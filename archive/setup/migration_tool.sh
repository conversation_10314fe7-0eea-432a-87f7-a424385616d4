#!/bin/bash

set -e
echo "Project Migration Tool"
echo "----------------------"

# Define source and destination
SOURCE_DIR="/root/project"
TARGET_SERVER="root@***************"
TARGET_DIR="/root/project"

# Create directories for documentation
mkdir -p "$SOURCE_DIR/migration_docs"

echo "1. Documenting current system environment..."
# Document system info
{
  echo "# System Information"
  echo "Date: $(date)"
  echo "Hostname: $(hostname)"
  echo "Kernel: $(uname -r)"
  echo "OS: $(cat /etc/os-release | grep PRETTY_NAME | cut -d= -f2 | tr -d '"')"
  echo ""
  echo "# Python Environment"
  echo "Global Python: $(python3 --version 2>&1)"
  echo "Global Pip: $(pip3 --version 2>&1)"
  echo ""
  echo "# Node.js Environment"
  echo "Node.js: $(node --version 2>&1)"
  echo "NPM: $(npm --version 2>&1)"
  echo "PNPM: $(pnpm --version 2>&1)"
} > "$SOURCE_DIR/migration_docs/system_info.md"

# Document backend dependencies
echo "2. Documenting backend dependencies..."
cd "$SOURCE_DIR/backend"
# Ensure venv is activated
source venv/bin/activate
# Document Python version used in venv
{
  echo "# Backend Environment"
  echo "Python version: $(python --version 2>&1)"
  echo "Pip version: $(pip --version 2>&1)"
  echo ""
  echo "# Dependencies"
  pip freeze
} > "$SOURCE_DIR/migration_docs/backend_dependencies.md"

# Update requirements.txt with freeze output
pip freeze > "$SOURCE_DIR/backend/requirements.txt"
echo "✓ Updated backend/requirements.txt"

# Document frontend dependencies
echo "3. Documenting frontend dependencies..."
cd "$SOURCE_DIR/frontend"
# Create a backup of the original package.json
cp package.json package.json.bak

# Update package.json with actual installed versions
echo "4. Updating package.json with current versions..."
# Run the Node.js script to update package.json
chmod +x "$SOURCE_DIR/update_package_json.js"
node "$SOURCE_DIR/update_package_json.js"

# Document the front-end environment
{
  echo "# Frontend Environment"
  echo "Node.js: $(node --version 2>&1)"
  echo "PNPM: $(pnpm --version 2>&1)"
  echo ""
  echo "# Package.json vs Installed Versions"
  echo "\`\`\`"
  pnpm list
  echo "\`\`\`"
} > "$SOURCE_DIR/migration_docs/frontend_dependencies.md"

echo "5. Preparing for transfer..."
# Create a tar archive excluding node_modules and venv
cd "$SOURCE_DIR"
tar --exclude="*/node_modules" \
    --exclude="*/venv" \
    --exclude="*/__pycache__" \
    --exclude="*/.git" \
    --exclude="*/build" \
    --exclude="*/dist" \
    -czf "/tmp/project_migration.tar.gz" .

echo "6. Transferring to new server..."
# Transfer the archive to the new server
scp "/tmp/project_migration.tar.gz" "$TARGET_SERVER:/tmp/"

# Extract on the new server
ssh "$TARGET_SERVER" "mkdir -p $TARGET_DIR && tar -xzf /tmp/project_migration.tar.gz -C $TARGET_DIR && rm /tmp/project_migration.tar.gz"

# Create instructions file for setting up the new server
cat > "$SOURCE_DIR/migration_docs/setup_instructions.md" << 'EOF'
# Setup Instructions for New Server

## 1. Backend Setup
```bash
cd /root/project/backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## 2. Frontend Setup
```bash
cd /root/project/frontend
# Install pnpm if not already installed
npm install -g pnpm@9.15.4
pnpm install
```

## 3. Configure NGINX
- Edit the NGINX configuration based on the files in frontend/nginx.conf.txt
- Update domain names and SSL certificate paths as needed
```bash
sudo cp /root/project/frontend/nginx.conf.txt /etc/nginx/sites-available/your-domain.conf
sudo ln -s /etc/nginx/sites-available/your-domain.conf /etc/nginx/sites-enabled/
sudo nginx -t  # Test configuration
sudo systemctl restart nginx
```

## 4. Set up SSL certificates
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 5. Set up systemd services
Create a systemd service file for the backend:
```bash
sudo nano /etc/systemd/system/vpn-backend.service
```

Add the following content:
```
[Unit]
Description=VPN Dashboard Backend
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/root/project/backend
Environment="PATH=/root/project/backend/venv/bin"
ExecStart=/root/project/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable vpn-backend
sudo systemctl start vpn-backend
```

## 6. Final steps
- Check logs to verify everything is working:
  - Backend: `sudo journalctl -u vpn-backend -f`
  - NGINX: `sudo tail -f /var/log/nginx/error.log`
- Set proper permissions:
  `sudo chown -R www-data:www-data /root/project`
- Configure firewall:
  ```bash
  sudo ufw allow 22/tcp
  sudo ufw allow 80/tcp
  sudo ufw allow 443/tcp
  sudo ufw allow 8443/tcp
  sudo ufw enable
  ```
EOF

# Copy instructions to the new server
scp "$SOURCE_DIR/migration_docs/setup_instructions.md" "$TARGET_SERVER:$TARGET_DIR/SETUP_INSTRUCTIONS.md"

echo "✓ Migration complete!"
echo "✓ Setup instructions available at: $TARGET_DIR/SETUP_INSTRUCTIONS.md on the new server"
echo ""
echo "Next steps:"
echo "1. Connect to the new server: ssh $TARGET_SERVER"
echo "2. Follow the setup instructions in $TARGET_DIR/SETUP_INSTRUCTIONS.md" 