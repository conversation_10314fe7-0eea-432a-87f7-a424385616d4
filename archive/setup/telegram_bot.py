import telebot
import logging
import os
from dotenv import load_dotenv
import sys
import threading
import time

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO,
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('telegram_bot.log')
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
if not BOT_TOKEN:
    logger.critical("❌ TELEGRAM_BOT_TOKEN not found in environment variables!")
    raise ValueError("TELEGRAM_BOT_TOKEN must be set in environment variables")

WEBAPP_URL = os.getenv('WEBAPP_URL', 'https://dev.atlasvip.cloud')
ADMIN_ID = 6488419998

class TelegramBot:
    def __init__(self):
        self.bot = telebot.TeleBot(BOT_TOKEN)
        self._setup_handlers()
        self._is_running = False
        
    def _setup_handlers(self):
        @self.bot.message_handler(commands=['start'])
        def start(message):
            markup = telebot.types.ReplyKeyboardMarkup(resize_keyboard=True)
            webapp_btn = telebot.types.KeyboardButton(
                text="Open AtlasVIP WebApp",
                web_app=telebot.types.WebAppInfo(url=WEBAPP_URL)
            )
            markup.add(webapp_btn)
            
            inline_markup = telebot.types.InlineKeyboardMarkup()
            inline_markup.row(
                telebot.types.InlineKeyboardButton("💫 Check Status", callback_data='check_status'),
                telebot.types.InlineKeyboardButton("📊 My Account", callback_data='account')
            )
            
            self.bot.reply_to(
                message,
                "Welcome to AtlasVIP! 🚀\n\n",
                reply_markup=inline_markup
            )
            
        @self.bot.callback_query_handler(func=lambda call: True)
        def callback_handler(call):
            if call.data == 'check_status':
                self.bot.answer_callback_query(call.id, "Checking status...")
            elif call.data == 'account':
                self.bot.answer_callback_query(call.id, "Fetching account details...")

    def start(self):
        """Start the bot"""
        if self._is_running:
            return
        
        logger.info("🚀 Starting bot...")
        self._is_running = True
        
        try:
            self.bot.infinity_polling(
                timeout=10,
                long_polling_timeout=5,
                logger_level=logging.INFO
            )
        except Exception as e:
            self._is_running = False
            logger.error(f"❌ Bot error: {str(e)}", exc_info=True)
            raise

    def stop(self):
        """Stop the bot"""
        if not self._is_running:
            return
            
        logger.info("🛑 Stopping bot...")
        try:
            self._is_running = False
            self.bot.stop_polling()
            time.sleep(0.5)  # Give time for the polling to stop
            logger.info("✅ Bot stopped successfully")
        except Exception as e:
            logger.error(f"❌ Error stopping bot: {str(e)}", exc_info=True)
            raise


