#!/usr/bin/env python3
"""
Script to check user aminshafigh in database and add mock data for services and subscriptions
"""
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import bcrypt
import random
import string

# Import models and database functions - these imports will work when run from backend directory
from database import get_db_context, init_db, Base
from models import (
    User, VPNPackage, VPNSubscription, Transaction, 
    Role, TransactionType
)

def generate_random_string(length=8):
    """Generate a random string for subscription URLs"""
    letters = string.ascii_letters + string.digits
    return ''.join(random.choice(letters) for _ in range(length))

def hash_password(password: str) -> str:
    """Generate password hash"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def check_create_vpn_package(db: Session) -> VPNPackage:
    """Check if VPN package exists, if not create it"""
    # Check if package exists
    package = db.query(VPNPackage).filter(VPNPackage.name == "Premium VPN").first()
    
    if not package:
        print("Creating new VPN package...")
        package = VPNPackage(
            name="Premium VPN",
            data_limit=107374182400,  # 100 GB in bytes
            expire_days=30,
            price=9.99,
            description="Premium VPN package with 100GB data and 30 days validity",
            is_active=True
        )
        db.add(package)
        db.commit()
        db.refresh(package)
        print(f"Created VPN package: {package.name} (ID: {package.id})")
    else:
        print(f"VPN package already exists: {package.name} (ID: {package.id})")
    
    return package

def check_create_user(db: Session) -> User:
    """Check if user exists, if not create it"""
    # Check if user exists
    user = db.query(User).filter(User.username == "aminshafigh").first()
    
    if not user:
        print("Creating new user: aminshafigh...")
        
        # Generate a unique referral code
        referral_code = generate_random_string()
        
        # Create user
        user = User(
            username="aminshafigh",
            hashed_password=hash_password("securepassword"),
            role=Role.user,
            wallet_balance=50.0,
            first_name="Amin",
            last_name="Shafigh",
            email="<EMAIL>",
            is_active=True,
            discount_percent=0.0,
            referral_code=referral_code,
            created_at=datetime.utcnow()
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        print(f"Created user: {user.username} (ID: {user.id})")
    else:
        print(f"User already exists: {user.username} (ID: {user.id})")
    
    return user

def check_create_subscription(db: Session, user: User, package: VPNPackage) -> VPNSubscription:
    """Check if subscription exists, if not create it"""
    # Check if subscription exists
    subscription = db.query(VPNSubscription).filter(
        VPNSubscription.user_id == user.id,
        VPNSubscription.package_id == package.id
    ).first()
    
    if not subscription:
        print("Creating new VPN subscription...")
        
        # Create mock data
        marzban_username = f"aminshafigh_{user.id}"
        subscription_url = f"vless://user-{generate_random_string(12)}@example.com:443?encryption=none&security=tls&sni=vpn.example.com&fp=chrome&type=ws&path=%2F{generate_random_string(8)}#Premium_VPN"
        
        # Calculate expiry date (30 days from now)
        expiry_date = datetime.utcnow() + timedelta(days=package.expire_days)
        
        # Create subscription
        subscription = VPNSubscription(
            user_id=user.id,
            package_id=package.id,
            marzban_username=marzban_username,
            subscription_url=subscription_url,
            data_limit=package.data_limit,
            data_used=1073741824,  # 1 GB used
            expires_at=expiry_date,
            created_at=datetime.utcnow(),
            is_active=True
        )
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        print(f"Created VPN subscription: {subscription.id}")
    else:
        print(f"Subscription already exists: {subscription.id}")
    
    return subscription

def create_transaction(db: Session, user: User, package: VPNPackage, subscription: VPNSubscription):
    """Create transaction record for the subscription"""
    # Check if transaction exists
    transaction = db.query(Transaction).filter(
        Transaction.user_id == user.id,
        Transaction.subscription_id == subscription.id
    ).first()
    
    if not transaction:
        print("Creating transaction record...")
        
        # Create transaction
        transaction = Transaction(
            user_id=user.id,
            type=TransactionType.subscription_purchase,
            amount=-package.price,
            created_at=datetime.utcnow(),
            description=f"VPN subscription purchase: {package.name}",
            balance_after=user.wallet_balance,
            package_name=package.name,
            package_price=package.price,
            package_data_limit=package.data_limit,
            package_expire_days=package.expire_days,
            subscription_id=subscription.id,
            status="completed"
        )
        db.add(transaction)
        db.commit()
        db.refresh(transaction)
        print(f"Created transaction record: {transaction.id}")
    else:
        print(f"Transaction already exists: {transaction.id}")

def main():
    """Main function to run the script"""
    print("Initializing database...")
    init_db()
    
    with get_db_context() as db:
        # Check/create VPN package
        package = check_create_vpn_package(db)
        
        # Check/create user
        user = check_create_user(db)
        
        # Check/create subscription
        subscription = check_create_subscription(db, user, package)
        
        # Create transaction
        create_transaction(db, user, package, subscription)
        
        # Final summary
        print("\nSummary:")
        print(f"User: {user.username} (ID: {user.id})")
        print(f"Balance: ${user.wallet_balance}")
        print(f"VPN Package: {package.name} (ID: {package.id})")
        print(f"Subscription: ID {subscription.id}, expires on {subscription.expires_at}")
        print(f"Data limit: {subscription.data_limit/1073741824} GB, used: {subscription.data_used/1073741824} GB")
        
        # Fetch all user's transactions
        transactions = db.query(Transaction).filter(Transaction.user_id == user.id).all()
        print(f"\nTransactions ({len(transactions)}):")
        for t in transactions:
            print(f"  ID: {t.id}, Type: {t.type}, Amount: ${t.amount}, Date: {t.created_at}")

if __name__ == "__main__":
    main() 