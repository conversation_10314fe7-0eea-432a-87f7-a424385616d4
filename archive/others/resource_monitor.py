#!/usr/bin/env python3
"""
Resource Monitor for Atlas VPN Backend

This script monitors system resources and application-specific metrics
to help identify resource leaks, particularly file descriptor leaks.
"""

import os
import sys
import time
import logging
import psutil
import argparse
import asyncio
import json
import socket
from datetime import datetime
import signal
import traceback
from typing import Dict, List, Any, Optional
import sqlite3
import aiohttp

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/resource_monitor.log')
    ]
)
logger = logging.getLogger("resource_monitor")

class ResourceMonitor:
    def __init__(self, pid: Optional[int] = None, interval: int = 60, threshold: int = 80):
        self.target_pid = pid
        self.interval = interval  # seconds
        self.threshold = threshold  # percentage
        self.process = None
        self.running = False
        self.last_stats = {}
        self.high_fd_threshold = 1000  # Alert if process has more than this many FDs
        self._setup_process()
        
    def _setup_process(self):
        """Set up the process to monitor, either using provided PID or finding by name"""
        if self.target_pid:
            try:
                self.process = psutil.Process(self.target_pid)
                logger.info(f"Monitoring process with PID {self.target_pid} ({self.process.name()})")
            except psutil.NoSuchProcess:
                logger.error(f"Process with PID {self.target_pid} not found")
                self.process = None
        else:
            # Try to find Atlas VPN process by name
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if any('uvicorn' in cmd for cmd in proc.info['cmdline'] if cmd) and \
                   any('main:app' in cmd for cmd in proc.info['cmdline'] if cmd):
                    self.process = proc
                    self.target_pid = proc.pid
                    logger.info(f"Found Atlas VPN process with PID {self.target_pid}")
                    break
            
            if not self.process:
                logger.error("Atlas VPN process not found")
    
    async def start_monitoring(self):
        """Start monitoring resources"""
        logger.info("Starting resource monitoring...")
        self.running = True
        
        # Register signal handlers
        signal.signal(signal.SIGINT, self._handle_signal)
        signal.signal(signal.SIGTERM, self._handle_signal)
        
        try:
            while self.running:
                try:
                    # Ensure process is still running
                    if not self.process or not self.process.is_running():
                        self._setup_process()
                        if not self.process:
                            logger.warning("Target process not found. Waiting before retry...")
                            await asyncio.sleep(self.interval)
                            continue
                    
                    # Collect and analyze metrics
                    await self._collect_metrics()
                    
                    # Sleep until next check
                    await asyncio.sleep(self.interval)
                except psutil.NoSuchProcess:
                    logger.warning(f"Process with PID {self.target_pid} no longer exists")
                    self.process = None
                except Exception as e:
                    logger.error(f"Error during monitoring: {str(e)}")
                    traceback.print_exc()
                    await asyncio.sleep(self.interval)
        except asyncio.CancelledError:
            logger.info("Monitoring task cancelled")
        finally:
            logger.info("Resource monitoring stopped")
    
    def _handle_signal(self, signum, frame):
        """Handle termination signals"""
        logger.info(f"Received signal {signum}, stopping monitoring")
        self.running = False
    
    async def _collect_metrics(self):
        """Collect system and process metrics"""
        if not self.process:
            return
        
        # Get process info
        try:
            stats = {}
            
            # Basic process info
            stats["timestamp"] = datetime.now().isoformat()
            stats["pid"] = self.process.pid
            stats["name"] = self.process.name()
            stats["status"] = self.process.status()
            stats["cpu_percent"] = self.process.cpu_percent(interval=1.0)
            stats["memory_percent"] = self.process.memory_percent()
            stats["memory_info"] = self.process.memory_info()._asdict()
            
            # Count open files
            open_files = self.process.open_files()
            stats["open_files_count"] = len(open_files)
            
            # Count network connections
            connections = self.process.connections()
            stats["connections_count"] = len(connections)
            stats["connections_by_status"] = {}
            for conn in connections:
                status = conn.status
                if status not in stats["connections_by_status"]:
                    stats["connections_by_status"][status] = 0
                stats["connections_by_status"][status] += 1
            
            # System-wide stats
            stats["system"] = {
                "cpu_percent": psutil.cpu_percent(interval=None),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_usage_percent": psutil.disk_usage('/').percent
            }
            
            # Analyze for potential issues
            await self._analyze_metrics(stats)
            
            # Store for comparison
            self.last_stats = stats
            
        except psutil.NoSuchProcess:
            logger.warning(f"Process {self.target_pid} no longer exists")
            self.process = None
        except psutil.AccessDenied:
            logger.error(f"Access denied to process information for PID {self.target_pid}")
        except Exception as e:
            logger.error(f"Error collecting metrics: {str(e)}")
    
    async def _analyze_metrics(self, stats: Dict[str, Any]):
        """Analyze collected metrics for potential issues"""
        # Check for high file descriptor usage
        if stats.get("open_files_count", 0) > self.high_fd_threshold:
            logger.warning(
                f"HIGH FILE DESCRIPTOR USAGE: {stats['open_files_count']} open files. "
                f"This may indicate a file descriptor leak."
            )
            # Get more detailed info about open files
            await self._log_detailed_fd_info()
        
        # Check if process is using too much memory
        if stats.get("memory_percent", 0) > self.threshold:
            logger.warning(
                f"HIGH MEMORY USAGE: {stats['memory_percent']:.1f}% of system memory. "
                f"RSS: {stats['memory_info']['rss'] / (1024*1024):.1f} MB."
            )
        
        # Check for too many network connections
        if stats.get("connections_count", 0) > 500:  # Arbitrary threshold
            logger.warning(
                f"HIGH CONNECTION COUNT: {stats['connections_count']} network connections. "
                f"Status breakdown: {stats['connections_by_status']}"
            )
        
        # Check for increase in resource usage from last check
        if self.last_stats:
            # Check file descriptor growth
            last_fd_count = self.last_stats.get("open_files_count", 0)
            current_fd_count = stats.get("open_files_count", 0)
            if current_fd_count > last_fd_count * 1.2 and current_fd_count - last_fd_count > 50:
                logger.warning(
                    f"RAPID FD GROWTH: Open files increased from {last_fd_count} to {current_fd_count} "
                    f"({current_fd_count - last_fd_count} new files)"
                )
            
            # Check connection growth
            last_conn_count = self.last_stats.get("connections_count", 0)
            current_conn_count = stats.get("connections_count", 0)
            if current_conn_count > last_conn_count * 1.5 and current_conn_count - last_conn_count > 20:
                logger.warning(
                    f"RAPID CONNECTION GROWTH: Network connections increased from {last_conn_count} to {current_conn_count} "
                    f"({current_conn_count - last_conn_count} new connections)"
                )
    
    async def _log_detailed_fd_info(self):
        """Log detailed information about open file descriptors"""
        try:
            # Get detailed open files information
            open_files = self.process.open_files()
            
            # Group files by path
            file_groups = {}
            for f in open_files:
                path = f.path
                if path not in file_groups:
                    file_groups[path] = 0
                file_groups[path] += 1
            
            # Find the top 10 most frequent paths
            top_files = sorted(file_groups.items(), key=lambda x: x[1], reverse=True)[:10]
            
            logger.info("Top open file paths:")
            for path, count in top_files:
                logger.info(f"  {count} instances of: {path}")
            
            # Get database connection activity if available
            try:
                db_path = "./users.db"
                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA journal_mode;")
                    journal_mode = cursor.fetchone()[0]
                    cursor.execute("PRAGMA database_list;")
                    database_list = cursor.fetchall()
                    conn.close()
                    
                    logger.info(f"Database journal mode: {journal_mode}")
                    logger.info(f"Attached databases: {database_list}")
            except Exception as db_error:
                logger.warning(f"Could not check database status: {str(db_error)}")
                
        except Exception as e:
            logger.error(f"Error getting detailed file descriptor info: {str(e)}")
    
    async def check_http_connections(self):
        """Check HTTP connections to identify potential connection leaks"""
        try:
            # Try to connect to the application API endpoint
            base_url = "http://localhost:8000"  # Adjust if needed
            health_endpoint = "/api/health"
            
            async with aiohttp.ClientSession() as session:
                try:
                    start_time = time.time()
                    async with session.get(f"{base_url}{health_endpoint}", timeout=5) as response:
                        elapsed = time.time() - start_time
                        data = await response.text()
                        logger.info(f"Health check: {response.status}, time: {elapsed:.3f}s, data: {data[:100]}")
                except Exception as req_error:
                    logger.warning(f"Health check failed: {str(req_error)}")
        except Exception as e:
            logger.error(f"Error during HTTP connection check: {str(e)}")
    
    async def check_database_connections(self):
        """Check database connection pool for leaks"""
        try:
            db_path = "./users.db"
            if os.path.exists(db_path):
                # Just check if we can connect to the database
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                # Check if the database is not locked
                try:
                    cursor.execute("SELECT 1;")
                    cursor.fetchone()
                    logger.info("Database connection successful")
                except sqlite3.Error as sql_error:
                    logger.warning(f"Database error: {str(sql_error)}")
                finally:
                    conn.close()
        except Exception as e:
            logger.error(f"Error checking database connections: {str(e)}")

async def main():
    parser = argparse.ArgumentParser(description="Monitor system resources for Atlas VPN backend")
    parser.add_argument("-p", "--pid", type=int, help="Process ID to monitor (default: auto-detect)")
    parser.add_argument("-i", "--interval", type=int, default=60, help="Monitoring interval in seconds (default: 60)")
    parser.add_argument("-t", "--threshold", type=int, default=80, help="Alert threshold percentage (default: 80)")
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    # Create and start the monitor
    monitor = ResourceMonitor(pid=args.pid, interval=args.interval, threshold=args.threshold)
    
    # Run periodic tasks
    tasks = [
        monitor.start_monitoring()
    ]
    
    # Add HTTP connection check every 5 minutes
    if not args.pid:  # Only if monitoring Atlas backend
        tasks.append(run_periodic(monitor.check_http_connections, 300))
        tasks.append(run_periodic(monitor.check_database_connections, 300))
    
    await asyncio.gather(*tasks)

async def run_periodic(func, interval):
    """Run a function periodically"""
    while True:
        try:
            await func()
        except Exception as e:
            logger.error(f"Error in periodic task {func.__name__}: {str(e)}")
        await asyncio.sleep(interval)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")
    except Exception as e:
        logger.critical(f"Unhandled exception: {str(e)}")
        traceback.print_exc()
        sys.exit(1) 