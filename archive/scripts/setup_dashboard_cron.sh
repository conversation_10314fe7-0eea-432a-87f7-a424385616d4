#!/bin/bash
# Setup Security Dashboard Cron Job
# This script sets up a cron job to run the security dashboard generator periodically

# Default values
CRON_SCHEDULE="0 */6 * * *"  # Every 6 hours
SCRIPT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DASHBOARD_SCRIPT="$SCRIPT_PATH/run_dashboard.sh"
LOG_FILE="/var/log/security_dashboard_cron.log"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --schedule)
      CRON_SCHEDULE="$2"
      shift 2
      ;;
    --log-file)
      LOG_FILE="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --schedule SCHEDULE   Cron schedule expression (default: 0 */6 * * *)"
      echo "  --log-file FILE       Log file for cron output (default: /var/log/security_dashboard_cron.log)"
      echo "  --help                Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Make sure the dashboard script is executable
chmod +x "$DASHBOARD_SCRIPT"

# Create the cron job
CRON_JOB="$CRON_SCHEDULE cd $SCRIPT_PATH/.. && $DASHBOARD_SCRIPT >> $LOG_FILE 2>&1"

# Check if cron job already exists
EXISTING_CRON=$(crontab -l 2>/dev/null | grep -F "$DASHBOARD_SCRIPT")

if [ -n "$EXISTING_CRON" ]; then
  echo "Cron job for security dashboard already exists. Updating..."
  # Remove existing cron job
  crontab -l 2>/dev/null | grep -v -F "$DASHBOARD_SCRIPT" | crontab -
else
  echo "Setting up new cron job for security dashboard..."
fi

# Add new cron job
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

echo "Cron job set up successfully!"
echo "Schedule: $CRON_SCHEDULE"
echo "Dashboard script: $DASHBOARD_SCRIPT"
echo "Log file: $LOG_FILE"
echo ""
echo "To view current cron jobs, run: crontab -l" 