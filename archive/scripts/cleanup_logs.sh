#!/bin/bash

# Cleanup script for test logs and artifacts
# Removes old logs and test reports from the backend folder
# Organizes necessary logs in the scripts/logs directory

echo "Cleaning up test logs and artifacts..."

# Create logs directory in scripts folder if it doesn't exist
mkdir -p $(dirname "$0")/logs

# Remove log files from backend folder
echo "Removing old log files from backend folder..."
find $(dirname "$0")/.. -maxdepth 1 -name "*.log" -type f -delete

# Remove test report files from backend folder
echo "Removing old test report files from backend folder..."
find $(dirname "$0")/.. -maxdepth 1 -name "*_report_*.json" -type f -delete

# Remove temporary database files if they exist
echo "Removing temporary database files..."
find $(dirname "$0")/.. -maxdepth 1 -name "test_*.db" -type f -delete
find $(dirname "$0")/.. -maxdepth 1 -name "test_*.db-journal" -type f -delete

echo "Cleanup complete!"
echo "All tests should now be run from the scripts folder using './run_task_tests.sh'" 