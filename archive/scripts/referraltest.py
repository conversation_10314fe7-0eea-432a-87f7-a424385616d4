#!/usr/bin/env python3
"""
Referral System Test Script

This script tests the Atlas VPN referral system by:
1. Creating an initial user
2. Getting their referral code
3. Creating multiple users using that referral code
4. Verifying the referral relationships are properly established
5. Checking if referral tasks are properly tracked
"""

import requests
import json
import random
import string
import time
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"  # Change to your API address
TOTAL_USERS = 5  # Number of referred users to create
VERBOSE = True  # Print detailed logs

# Helper functions
def log(message):
    """Print log message if VERBOSE is True"""
    if VERBOSE:
        print(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")

def random_telegram_id():
    """Generate a random Telegram ID"""
    return str(random.randint(10000000, 999999999))

def random_username():
    """Generate a random username"""
    return f"user_{''.join(random.choices(string.ascii_lowercase + string.digits, k=8))}"

# 1. Create the initial user (referrer)
def create_referrer():
    log("Creating initial referrer user...")
    
    # Generate random Telegram data for the initial user
    telegram_id = random_telegram_id()
    username = random_username()
    
    # Mock the Telegram init data (in real systems this would be signed by Telegram)
    init_data = f"id={telegram_id}&first_name=Test&username={username}"
    
    # Register using the Telegram registration endpoint
    response = requests.post(
        f"{BASE_URL}/auth/telegram/register",
        json={
            "init_data": init_data,
            "username": username,
            "selected_avatar": "default.jpg",
            "device_id": f"device_{telegram_id}",
            "platform": "web"
        }
    )
    
    if response.status_code != 200:
        log(f"Failed to create referrer: {response.status_code} {response.text}")
        return None, None
    
    # Save cookies for authentication
    cookies = response.cookies
    
    # Get the referrer information
    referral_info_response = requests.get(
        f"{BASE_URL}/api/referrals/info",
        cookies=cookies
    )
    
    if referral_info_response.status_code != 200:
        log(f"Failed to get referral info: {referral_info_response.status_code} {referral_info_response.text}")
        return None, None
    
    referral_info = referral_info_response.json()
    log(f"Created referrer: {username} with code: {referral_info['referral_code']}")
    
    return {
        "username": username,
        "telegram_id": telegram_id,
        "referral_code": referral_info["referral_code"],
        "referral_link": referral_info["referral_link"],
        "cookies": cookies
    }, cookies

# 2. Create referred users using the referral code
def create_referred_users(referrer_data, count=5):
    log(f"Creating {count} referred users...")
    referred_users = []
    
    referral_code = referrer_data["referral_code"]
    
    for i in range(count):
        # Generate random data for this user
        telegram_id = random_telegram_id()
        username = random_username()
        
        # Create init_data with start parameter containing referral code
        # This simulates a user clicking a Telegram referral link
        init_data = f"id={telegram_id}&first_name=Referred&username={username}&start_param=ref_{referral_code}"
        
        response = requests.post(
            f"{BASE_URL}/auth/telegram/register",
            json={
                "init_data": init_data,
                "username": username,
                "selected_avatar": "default.jpg",
                "device_id": f"device_{telegram_id}",
                "platform": "web",
                "referral_code": referral_code  # Pass referral code directly
            }
        )
        
        if response.status_code != 200:
            log(f"Failed to create referred user {i+1}: {response.status_code} {response.text}")
            continue
        
        cookies = response.cookies
        log(f"Created referred user: {username}")
        
        referred_users.append({
            "username": username,
            "telegram_id": telegram_id,
            "cookies": cookies
        })
        
        # Small delay to prevent rate limiting
        time.sleep(1)
    
    return referred_users

# 3. Verify referral relationships
def verify_referrals(referrer_data):
    log("Verifying referral relationships...")
    
    # Get the list of referred users
    referred_users_response = requests.get(
        f"{BASE_URL}/api/referrals/users",
        cookies=referrer_data["cookies"]
    )
    
    if referred_users_response.status_code != 200:
        log(f"Failed to get referred users: {referred_users_response.status_code} {referred_users_response.text}")
        return False
    
    referred_users = referred_users_response.json()
    log(f"Found {len(referred_users)} referred users")
    
    # Get updated referral info
    referral_info_response = requests.get(
        f"{BASE_URL}/api/referrals/info",
        cookies=referrer_data["cookies"]
    )
    
    if referral_info_response.status_code != 200:
        log(f"Failed to get updated referral info: {referral_info_response.status_code} {referral_info_response.text}")
        return False
    
    referral_info = referral_info_response.json()
    
    log(f"Referral statistics:")
    log(f"- Total referrals: {referral_info['total_referrals']}")
    log(f"- Active referrals: {referral_info['active_referrals']}")
    log(f"- Total earnings: ${referral_info['total_earnings']}")
    
    return True

# 4. Check if referral tasks are tracked
def check_referral_tasks(referrer_data):
    log("Checking if referral tasks are tracked...")
    
    # Get tasks for the referrer
    tasks_response = requests.get(
        f"{BASE_URL}/api/tasks",
        cookies=referrer_data["cookies"]
    )
    
    if tasks_response.status_code != 200:
        log(f"Failed to get tasks: {tasks_response.status_code} {tasks_response.text}")
        return False
    
    tasks = tasks_response.json()
    
    # Look for referral-type tasks
    referral_tasks = [task for task in tasks if task.get("type") == "REFERRAL"]
    
    if not referral_tasks:
        log("No referral tasks found for this user")
        return False
    
    log(f"Found {len(referral_tasks)} referral tasks")
    
    # For each referral task, check its completion status
    for task in referral_tasks:
        task_id = task["id"]
        log(f"Checking status of referral task {task_id}: {task['name']}")
        
        # Get detailed status for this task
        task_status_response = requests.get(
            f"{BASE_URL}/api/tasks/{task_id}/referral-status",
            cookies=referrer_data["cookies"]
        )
        
        if task_status_response.status_code != 200:
            log(f"Failed to get task status: {task_status_response.status_code} {task_status_response.text}")
            continue
            
        task_status = task_status_response.json()
        log(f"Task status: {task_status['status']}")
        log(f"Progress: {task_status['currentProgress']}/{task_status['target']}")
        log(f"Completed: {task_status['isCompleted']}")
        
        if task_status['isCompleted'] and not task_status['isClaimed']:
            # Claim the completed task reward
            log(f"Claiming reward for completed task {task_id}")
            claim_response = requests.post(
                f"{BASE_URL}/api/tasks/{task_id}/claim",
                cookies=referrer_data["cookies"]
            )
            
            if claim_response.status_code == 200:
                log(f"Successfully claimed reward: {claim_response.json()}")
            else:
                log(f"Failed to claim reward: {claim_response.status_code} {claim_response.text}")
    
    return True

# Main test function
def test_referral_system():
    log("Starting referral system test")
    
    # Step 1: Create referrer
    referrer_data, cookies = create_referrer()
    if not referrer_data:
        log("Test failed: Could not create referrer")
        return False
    
    # Step 2: Create referred users
    referred_users = create_referred_users(referrer_data, TOTAL_USERS)
    if not referred_users or len(referred_users) < TOTAL_USERS:
        log(f"Warning: Created only {len(referred_users)} out of {TOTAL_USERS} referred users")
    
    # Give the system a moment to process referrals
    log("Waiting for system to process referrals...")
    time.sleep(2)
    
    # Step 3: Verify referrals
    if not verify_referrals(referrer_data):
        log("Test warning: Referral verification failed")
    
    # Step 4: Check referral tasks
    if not check_referral_tasks(referrer_data):
        log("Test warning: Referral task check failed")
    
    log("Referral system test completed")
    return True

if __name__ == "__main__":
    test_referral_system()
