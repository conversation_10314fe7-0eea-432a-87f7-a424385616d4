#!/usr/bin/env python3
"""
Card Manager - A tool for managing the card catalog
"""
import sys
import os
import json
import argparse
from datetime import datetime

# Add backend directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from models import CardCatalog, UserCard
    from database import get_db
except ImportError as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)

def list_cards():
    """List all cards in the catalog"""
    db = next(get_db())
    cards = db.query(CardCatalog).all()
    
    if not cards:
        print("No cards found in the catalog.")
        return
    
    print("\n=== Card Catalog ===")
    print(f"Total Cards: {len(cards)}")
    print("-" * 80)
    print(f"{'ID':^5} | {'Name':<25} | {'Rarity':<10} | {'Base Profit':^12} | {'Price':^10} | {'Max Level':^9}")
    print("-" * 80)
    
    for card in cards:
        try:
            level_profits = json.loads(card.level_profits_json) if card.level_profits_json else []
            level_costs = json.loads(card.level_costs_json) if card.level_costs_json else []
            
            base_profit = level_profits[0] if level_profits else 0
            base_cost = level_costs[0] if level_costs else 0
            
            print(f"{card.id:^5} | {card.name:<25} | {card.rarity:<10} | {base_profit:^12.4f} | {base_cost:^10.2f} | {card.max_level:^9}")
        except Exception as e:
            print(f"{card.id:^5} | {card.name:<25} | {card.rarity:<10} | ERROR: {str(e)}")
    
    print()

def add_card(name, rarity, description, profits, costs, max_level, image_url=None):
    """Add a new card to the catalog"""
    db = next(get_db())
    
    # Validate inputs
    valid_rarities = ['common', 'rare', 'epic', 'legendary', 'mythic']
    if rarity not in valid_rarities:
        print(f"Error: Rarity must be one of {valid_rarities}")
        return
    
    # Check if a card with this name already exists
    existing = db.query(CardCatalog).filter(CardCatalog.name == name).first()
    if existing:
        print(f"Error: A card named '{name}' already exists (ID: {existing.id})")
        return
    
    # Convert profit and cost lists to JSON
    try:
        profits_list = [float(p) for p in profits.split(',')]
        costs_list = [float(c) for c in costs.split(',')]
        
        if len(profits_list) != max_level:
            print(f"Warning: Profit list length ({len(profits_list)}) doesn't match max_level ({max_level})")
        
        if len(costs_list) != max_level:
            print(f"Warning: Cost list length ({len(costs_list)}) doesn't match max_level ({max_level})")
        
        level_profits_json = json.dumps(profits_list)
        level_costs_json = json.dumps(costs_list)
    except Exception as e:
        print(f"Error parsing profit/cost values: {e}")
        return
    
    # Create new card
    new_card = CardCatalog(
        name=name,
        rarity=rarity,
        description=description,
        level_profits_json=level_profits_json,
        level_costs_json=level_costs_json,
        max_level=max_level,
        image_url=image_url or f"/img/cards/{name.lower().replace(' ', '-')}.jpg",
        created_at=datetime.utcnow()
    )
    
    try:
        db.add(new_card)
        db.commit()
        db.refresh(new_card)
        print(f"Successfully added new card: {name} (ID: {new_card.id})")
    except Exception as e:
        db.rollback()
        print(f"Error adding card: {e}")

def update_card(card_id, name=None, rarity=None, description=None, profits=None, costs=None, max_level=None, image_url=None):
    """Update an existing card in the catalog"""
    db = next(get_db())
    
    # Find the card
    card = db.query(CardCatalog).filter(CardCatalog.id == card_id).first()
    if not card:
        print(f"Error: Card with ID {card_id} not found")
        return
    
    # Update fields if provided
    if name:
        card.name = name
    
    if rarity:
        valid_rarities = ['common', 'rare', 'epic', 'legendary', 'mythic']
        if rarity not in valid_rarities:
            print(f"Error: Rarity must be one of {valid_rarities}")
            return
        card.rarity = rarity
    
    if description:
        card.description = description
    
    if profits:
        try:
            profits_list = [float(p) for p in profits.split(',')]
            card.level_profits_json = json.dumps(profits_list)
        except Exception as e:
            print(f"Error parsing profit values: {e}")
            return
    
    if costs:
        try:
            costs_list = [float(c) for c in costs.split(',')]
            card.level_costs_json = json.dumps(costs_list)
        except Exception as e:
            print(f"Error parsing cost values: {e}")
            return
    
    if max_level:
        card.max_level = max_level
    
    if image_url:
        card.image_url = image_url
    
    try:
        db.commit()
        print(f"Successfully updated card: {card.name} (ID: {card.id})")
    except Exception as e:
        db.rollback()
        print(f"Error updating card: {e}")

def main():
    parser = argparse.ArgumentParser(description="Card Catalog Manager")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List all cards")
    
    # Add command
    add_parser = subparsers.add_parser("add", help="Add a new card")
    add_parser.add_argument("--name", required=True, help="Card name")
    add_parser.add_argument("--rarity", required=True, choices=['common', 'rare', 'epic', 'legendary', 'mythic'], help="Card rarity")
    add_parser.add_argument("--description", required=True, help="Card description")
    add_parser.add_argument("--profits", required=True, help="Comma-separated list of hourly profits for each level")
    add_parser.add_argument("--costs", required=True, help="Comma-separated list of costs for each level")
    add_parser.add_argument("--max-level", required=True, type=int, help="Maximum level")
    add_parser.add_argument("--image-url", help="Image URL (defaults to /img/cards/card-name.jpg)")
    
    # Update command
    update_parser = subparsers.add_parser("update", help="Update an existing card")
    update_parser.add_argument("--id", required=True, type=int, help="Card ID")
    update_parser.add_argument("--name", help="Card name")
    update_parser.add_argument("--rarity", choices=['common', 'rare', 'epic', 'legendary', 'mythic'], help="Card rarity")
    update_parser.add_argument("--description", help="Card description")
    update_parser.add_argument("--profits", help="Comma-separated list of hourly profits for each level")
    update_parser.add_argument("--costs", help="Comma-separated list of costs for each level")
    update_parser.add_argument("--max-level", type=int, help="Maximum level")
    update_parser.add_argument("--image-url", help="Image URL")
    
    args = parser.parse_args()
    
    if args.command == "list":
        list_cards()
    elif args.command == "add":
        add_card(
            name=args.name,
            rarity=args.rarity,
            description=args.description,
            profits=args.profits,
            costs=args.costs,
            max_level=args.max_level,
            image_url=args.image_url
        )
    elif args.command == "update":
        update_card(
            card_id=args.id,
            name=args.name,
            rarity=args.rarity,
            description=args.description,
            profits=args.profits,
            costs=args.costs,
            max_level=args.max_level,
            image_url=args.image_url
        )
    else:
        parser.print_help()

if __name__ == "__main__":
    main() 