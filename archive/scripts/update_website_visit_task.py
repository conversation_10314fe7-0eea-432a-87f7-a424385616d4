#!/usr/bin/env python
"""
Update Website Visit Task

This script updates the WEBSITE_VISIT task to ensure it has the required duration 
and any other necessary fields that might be needed for the platform link button to show.
"""
import sys
import os
from pathlib import Path
import logging
import json
import traceback
import sqlite3

# Add backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Import database modules
from database import init_db, get_db_context
from models import Task, TaskType
from sqlalchemy import update

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def update_task_with_sqlalchemy():
    """Update website visit task using SQLAlchemy"""
    try:
        with get_db_context() as db:
            # Find the WEBSITE_VISIT task
            website_task = db.query(Task).filter(
                Task.type == TaskType.WEBSITE_VISIT
            ).first()
            
            if not website_task:
                logger.error("No WEBSITE_VISIT task found in the database")
                return False
            
            logger.info(f"Found WEBSITE_VISIT task: ID {website_task.id}, Name: {website_task.name}")
            
            # Print current values
            logger.info(f"Current platform_url: {website_task.platform_url}")
            logger.info(f"Current platform_id: {website_task.platform_id}")
            
            # Update the task with all necessary fields
            website_task.platform_url = "https://ourwebsite.com/features"
            website_task.platform_id = "features_page"
            website_task.verify_key = "WEBSITE123"  # Add a verification key for testing
            website_task.max_verification_attempts = 5
            website_task.verification_cooldown = 30
            
            # Set description with duration
            website_task.description = "Visit our website and stay for at least 30 seconds. The platform link will appear when the task is started."
            
            # Commit the changes
            db.commit()
            
            logger.info("Successfully updated WEBSITE_VISIT task")
            return True
            
    except Exception as e:
        logger.error(f"Error updating task: {str(e)}")
        logger.error(traceback.format_exc())
        return False

def update_with_direct_sqlite():
    """Update task using direct SQLite for maximum compatibility"""
    db_path = Path(__file__).parent.parent.parent / "users.db"
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Find WEBSITE_VISIT task
        cursor.execute("SELECT id, name, platform_url FROM tasks WHERE type = 'WEBSITE_VISIT'")
        task = cursor.fetchone()
        
        if not task:
            logger.error("No WEBSITE_VISIT task found in SQLite database")
            return False
            
        task_id = task[0]
        logger.info(f"Found WEBSITE_VISIT task with ID {task_id}, Name: {task[1]}")
        
        # Create a JSON string with the duration
        social_task_json = json.dumps({
            "required_duration": 30,
            "platform_url": "https://ourwebsite.com/features",
            "verification_method": "duration"
        })
        
        # Check if verification_data column exists
        cursor.execute("PRAGMA table_info(tasks)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if "verification_data" in columns:
            # Update the verification_data field if it exists
            cursor.execute(
                "UPDATE tasks SET verification_data = ? WHERE id = ?",
                (social_task_json, task_id)
            )
            logger.info("Updated verification_data field")
        
        # Update other fields
        cursor.execute("""
            UPDATE tasks SET 
            platform_url = ?,
            description = ?,
            verify_key = ?
            WHERE id = ?
        """, (
            "https://ourwebsite.com/features",
            "Visit our website and stay for at least 30 seconds. The platform link will appear when the task is started.",
            "WEBSITE123",
            task_id
        ))
        
        # Commit changes
        conn.commit()
        logger.info("Successfully updated WEBSITE_VISIT task with direct SQLite")
        
        # Verify the update
        cursor.execute("SELECT platform_url FROM tasks WHERE id = ?", (task_id,))
        updated_url = cursor.fetchone()[0]
        logger.info(f"Updated platform_url: {updated_url}")
        
        return True
        
    except sqlite3.Error as e:
        logger.error(f"SQLite error: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()

def main():
    """Main function"""
    logger.info("Starting update of WEBSITE_VISIT task...")
    
    # Try both methods
    sqlalchemy_success = update_task_with_sqlalchemy()
    sqlite_success = update_with_direct_sqlite()
    
    if sqlalchemy_success or sqlite_success:
        logger.info("Successfully updated WEBSITE_VISIT task using at least one method")
    else:
        logger.error("Failed to update WEBSITE_VISIT task with both methods")

if __name__ == "__main__":
    main() 