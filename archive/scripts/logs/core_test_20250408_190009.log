
Starting Core Task API Tests...
2025-04-08 19:00:23,466 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:00:23,469 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:00:24,607 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:00:24,647 - INFO - Admin login successful
2025-04-08 19:00:24,705 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:00:24,714 - INFO - Retrieved CSRF token for user login
2025-04-08 19:00:26,881 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:00:26,883 - INFO - User login successful
2025-04-08 19:00:27,473 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:00:27,474 - INFO - Created YOUTUBE_VIEW task with ID: 396
2025-04-08 19:00:27,582 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:00:27,625 - INFO - Created TELEGRAM_CHANNEL task with ID: 397
2025-04-08 19:00:27,737 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/start "HTTP/1.1 200 OK"
2025-04-08 19:00:27,785 - INFO - Started task 396
2025-04-08 19:00:27,933 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/verify "HTTP/1.1 200 OK"
2025-04-08 19:00:28,458 - INFO - Verified task 396: True
2025-04-08 19:00:29,590 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/verify "HTTP/1.1 200 OK"
2025-04-08 19:00:29,591 - INFO - Verified task 396: True
2025-04-08 19:00:29,722 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/claim "HTTP/1.1 200 OK"
2025-04-08 19:00:29,765 - INFO - Claimed reward for task 396
2025-04-08 19:00:29,766 - INFO - YouTube task flow completed successfully
2025-04-08 19:00:29,901 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/397/start "HTTP/1.1 200 OK"
2025-04-08 19:00:29,950 - INFO - Started task 397
2025-04-08 19:00:30,746 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/397/verify "HTTP/1.1 200 OK"
2025-04-08 19:00:30,789 - INFO - Verified task 397: True
2025-04-08 19:00:30,861 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/397/claim "HTTP/1.1 200 OK"
2025-04-08 19:00:30,914 - INFO - Claimed reward for task 397
2025-04-08 19:00:30,915 - INFO - Telegram task flow completed successfully
2025-04-08 19:00:32,479 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/396/toggle "HTTP/1.1 200 OK"
2025-04-08 19:00:32,481 - INFO - Toggled task 396 active status to False
2025-04-08 19:00:32,608 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/396/toggle "HTTP/1.1 200 OK"
2025-04-08 19:00:32,649 - INFO - Toggled task 396 active status to True
2025-04-08 19:00:32,737 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/start "HTTP/1.1 400 Bad Request"
2025-04-08 19:00:32,778 - ERROR - Failed to start task 396: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-08 19:00:32,778 - INFO - Admin functions tested successfully
  Setup completed 
  Running tests...

Task API Test Report
       Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 3       │
│ Passed       │ 3       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘

Report saved to /root/project/backend/scripts/logs/task_api_test_report_20250408_190032.json
