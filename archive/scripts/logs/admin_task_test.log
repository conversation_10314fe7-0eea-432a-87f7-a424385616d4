2025-04-05 21:00:50,798 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-05 21:00:50,800 - ERROR - Admin login failed: 403 {"detail":"CSRF token missing or invalid"}
2025-04-05 21:00:50,837 - ERROR - Admin login failed, cannot continue tests
2025-04-05 22:15:23,754 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:15:23,762 - INFO - Retrieved CSRF token for admin login
2025-04-05 22:15:24,821 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:15:24,823 - INFO - Admin login successful
2025-04-05 22:15:24,948 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:24,949 - INFO - Created test task 0 with ID: 391
2025-04-05 22:15:25,500 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,541 - INFO - Created test task 1 with ID: 392
2025-04-05 22:15:25,610 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,653 - INFO - Created test task 2 with ID: 393
2025-04-05 22:15:25,722 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,761 - INFO - Created test task 3 with ID: 394
2025-04-05 22:15:25,835 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,884 - INFO - Created test task 4 with ID: 395
2025-04-05 22:15:25,884 - INFO - Test environment setup complete
2025-04-05 22:15:26,633 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-05 22:15:26,685 - INFO - Admin list tasks returned 365 tasks
2025-04-05 22:15:26,913 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-05 22:15:27,565 - INFO - HTTP Request: PATCH http://localhost:8000/api/tasks/admin/tasks/391 "HTTP/1.1 200 OK"
2025-04-05 22:15:27,569 - INFO - Admin update task successful: Updated Admin Test Task
2025-04-05 22:15:27,569 - INFO - Update verification successful
2025-04-05 22:15:27,664 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks/392 "HTTP/1.1 204 No Content"
2025-04-05 22:15:27,665 - INFO - Admin delete task successful for task ID: 392
2025-04-05 22:15:30,948 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-05 22:15:31,482 - INFO - Delete verification successful
2025-04-05 22:15:31,636 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks?ids=393,394 "HTTP/1.1 204 No Content"
2025-04-05 22:15:31,637 - INFO - Admin mass delete tasks successful for task IDs: [393, 394]
2025-04-05 22:15:31,805 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-05 22:15:31,826 - INFO - Mass delete verification successful
2025-04-05 22:15:33,525 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/stats "HTTP/1.1 200 OK"
2025-04-05 22:15:33,562 - INFO - Admin stats returned successfully: {
  "total_completions": 69,
  "completed_tasks": 49,
  "completion_rate": 71.01,
  "reward_distribution": {
    "RewardType.DATA_BONUS": 0,
    "RewardType.DAYS_BONUS": 0,
    "RewardType.WALLET_BONUS": 49
  },
  "task_type_distribution": {
    "TaskType.DAILY_CHECKIN": 11,
    "TaskType.YOUTUBE_VIEW": 23,
    "TaskType.INSTAGRAM_FOLLOW": 0,
    "TaskType.INSTAGRAM_VIEW": 0,
    "TaskType.TELEGRAM_CHANNEL": 15,
    "TaskType.TWITTER_FOLLOW": 0,
    "TaskType.WEBSITE_VISIT": 0,
    "TaskType.REFERRAL": 0
  },
  "daily_stats": [
    {
      "date": "2025-04-05",
      "completions": 4
    },
    {
      "date": "2025-04-04",
      "completions": 0
    },
    {
      "date": "2025-04-03",
      "completions": 0
    },
    {
      "date": "2025-04-02",
      "completions": 0
    },
    {
      "date": "2025-04-01",
      "completions": 0
    },
    {
      "date": "2025-03-31",
      "completions": 0
    },
    {
      "date": "2025-03-30",
      "completions": 0
    }
  ]
}
2025-04-05 22:15:33,592 - INFO - Stats verification successful
2025-04-05 22:15:33,681 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/395/start "HTTP/1.1 200 OK"
2025-04-05 22:15:35,557 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/395/reset "HTTP/1.1 200 OK"
2025-04-05 22:15:35,597 - INFO - Task reset successful: 70
2025-04-05 22:15:35,620 - INFO - Reset verification successful
2025-04-08 19:08:46,190 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:08:46,199 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:08:47,644 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:08:47,686 - INFO - Admin login successful
2025-04-08 19:08:47,801 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:47,803 - INFO - Created test task 0 with ID: 402
2025-04-08 19:08:47,908 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:47,949 - INFO - Created test task 1 with ID: 403
2025-04-08 19:08:49,483 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:49,525 - INFO - Created test task 2 with ID: 404
2025-04-08 19:08:49,689 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:49,729 - INFO - Created test task 3 with ID: 405
2025-04-08 19:08:49,860 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:49,901 - INFO - Created test task 4 with ID: 406
2025-04-08 19:08:49,902 - INFO - Test environment setup complete
2025-04-08 19:08:51,455 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 19:08:51,539 - INFO - Admin list tasks returned 372 tasks
2025-04-08 19:08:51,844 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 19:08:52,475 - INFO - HTTP Request: PATCH http://localhost:8000/api/tasks/admin/tasks/402 "HTTP/1.1 200 OK"
2025-04-08 19:08:52,513 - INFO - Admin update task successful: Updated Admin Test Task
2025-04-08 19:08:52,513 - INFO - Update verification successful
2025-04-08 19:08:52,821 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks/403 "HTTP/1.1 204 No Content"
2025-04-08 19:08:52,823 - INFO - Admin delete task successful for task ID: 403
2025-04-08 19:08:57,684 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 19:08:57,717 - INFO - Delete verification successful
2025-04-08 19:08:57,848 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks?ids=404,405 "HTTP/1.1 204 No Content"
2025-04-08 19:08:57,850 - INFO - Admin mass delete tasks successful for task IDs: [404, 405]
2025-04-08 19:08:58,689 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 19:08:58,698 - INFO - Mass delete verification successful
2025-04-08 19:08:58,847 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/stats "HTTP/1.1 200 OK"
2025-04-08 19:08:58,890 - INFO - Admin stats returned successfully: {
  "total_completions": 75,
  "completed_tasks": 54,
  "completion_rate": 72.0,
  "reward_distribution": {
    "RewardType.DATA_BONUS": 0,
    "RewardType.DAYS_BONUS": 0,
    "RewardType.WALLET_BONUS": 54
  },
  "task_type_distribution": {
    "TaskType.DAILY_CHECKIN": 12,
    "TaskType.YOUTUBE_VIEW": 25,
    "TaskType.INSTAGRAM_FOLLOW": 0,
    "TaskType.INSTAGRAM_VIEW": 0,
    "TaskType.TELEGRAM_CHANNEL": 17,
    "TaskType.TWITTER_FOLLOW": 0,
    "TaskType.WEBSITE_VISIT": 0,
    "TaskType.REFERRAL": 0
  },
  "daily_stats": [
    {
      "date": "2025-04-08",
      "completions": 4
    },
    {
      "date": "2025-04-07",
      "completions": 0
    },
    {
      "date": "2025-04-06",
      "completions": 0
    },
    {
      "date": "2025-04-05",
      "completions": 4
    },
    {
      "date": "2025-04-04",
      "completions": 0
    },
    {
      "date": "2025-04-03",
      "completions": 0
    },
    {
      "date": "2025-04-02",
      "completions": 0
    }
  ]
}
2025-04-08 19:08:58,890 - INFO - Stats verification successful
2025-04-08 19:08:59,542 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/406/start "HTTP/1.1 200 OK"
2025-04-08 19:08:59,897 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/406/reset "HTTP/1.1 200 OK"
2025-04-08 19:08:59,941 - INFO - Task reset successful: 76
2025-04-08 19:08:59,941 - INFO - Reset verification successful
2025-04-08 20:11:43,775 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 20:11:43,781 - INFO - Retrieved CSRF token for admin login
2025-04-08 20:11:47,913 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 20:11:47,945 - INFO - Admin login successful
2025-04-08 20:11:49,762 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 20:11:49,764 - INFO - Created test task 0 with ID: 407
2025-04-08 20:11:49,857 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 20:11:49,893 - INFO - Created test task 1 with ID: 408
2025-04-08 20:11:50,486 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 20:11:50,529 - INFO - Created test task 2 with ID: 409
2025-04-08 20:11:50,605 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 20:11:50,649 - INFO - Created test task 3 with ID: 410
2025-04-08 20:11:50,718 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 20:11:50,761 - INFO - Created test task 4 with ID: 411
2025-04-08 20:11:50,762 - INFO - Test environment setup complete
2025-04-08 20:11:51,545 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 20:11:51,610 - INFO - Admin list tasks returned 374 tasks
2025-04-08 20:11:51,758 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 20:11:51,920 - INFO - HTTP Request: PATCH http://localhost:8000/api/tasks/admin/tasks/407 "HTTP/1.1 200 OK"
2025-04-08 20:11:52,455 - INFO - Admin update task successful: Updated Admin Test Task
2025-04-08 20:11:52,456 - INFO - Update verification successful
2025-04-08 20:11:52,568 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks/408 "HTTP/1.1 204 No Content"
2025-04-08 20:11:52,570 - INFO - Admin delete task successful for task ID: 408
2025-04-08 20:11:55,945 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 20:11:56,462 - INFO - Delete verification successful
2025-04-08 20:11:56,721 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks?ids=409,410 "HTTP/1.1 204 No Content"
2025-04-08 20:11:56,722 - INFO - Admin mass delete tasks successful for task IDs: [409, 410]
2025-04-08 20:11:57,599 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 20:11:57,607 - INFO - Mass delete verification successful
2025-04-08 20:11:57,765 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/stats "HTTP/1.1 200 OK"
2025-04-08 20:11:57,766 - INFO - Admin stats returned successfully: {
  "total_completions": 76,
  "completed_tasks": 54,
  "completion_rate": 71.05,
  "reward_distribution": {
    "RewardType.DATA_BONUS": 0,
    "RewardType.DAYS_BONUS": 0,
    "RewardType.WALLET_BONUS": 54
  },
  "task_type_distribution": {
    "TaskType.DAILY_CHECKIN": 12,
    "TaskType.YOUTUBE_VIEW": 25,
    "TaskType.INSTAGRAM_FOLLOW": 0,
    "TaskType.INSTAGRAM_VIEW": 0,
    "TaskType.TELEGRAM_CHANNEL": 17,
    "TaskType.TWITTER_FOLLOW": 0,
    "TaskType.WEBSITE_VISIT": 0,
    "TaskType.REFERRAL": 0
  },
  "daily_stats": [
    {
      "date": "2025-04-08",
      "completions": 4
    },
    {
      "date": "2025-04-07",
      "completions": 0
    },
    {
      "date": "2025-04-06",
      "completions": 0
    },
    {
      "date": "2025-04-05",
      "completions": 4
    },
    {
      "date": "2025-04-04",
      "completions": 0
    },
    {
      "date": "2025-04-03",
      "completions": 0
    },
    {
      "date": "2025-04-02",
      "completions": 0
    }
  ]
}
2025-04-08 20:11:57,766 - INFO - Stats verification successful
2025-04-08 20:11:57,924 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/411/start "HTTP/1.1 200 OK"
2025-04-08 20:11:58,557 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/411/reset "HTTP/1.1 200 OK"
2025-04-08 20:11:58,601 - INFO - Task reset successful: 77
2025-04-08 20:11:58,601 - INFO - Reset verification successful
2025-04-08 21:41:33,934 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:41:34,473 - INFO - Retrieved CSRF token for admin login
2025-04-08 21:41:35,516 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:41:35,518 - INFO - Admin login successful
2025-04-08 21:41:35,587 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:35,588 - INFO - Created test task 0 with ID: 416
2025-04-08 21:41:35,724 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:35,765 - INFO - Created test task 1 with ID: 417
2025-04-08 21:41:35,875 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:35,917 - INFO - Created test task 2 with ID: 418
2025-04-08 21:41:36,947 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:37,460 - INFO - Created test task 3 with ID: 419
2025-04-08 21:41:37,551 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:37,593 - INFO - Created test task 4 with ID: 420
2025-04-08 21:41:37,593 - INFO - Test environment setup complete
2025-04-08 21:41:37,885 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 21:41:44,575 - INFO - Admin list tasks returned 379 tasks
2025-04-08 21:41:44,899 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 21:41:45,548 - INFO - HTTP Request: PATCH http://localhost:8000/api/tasks/admin/tasks/416 "HTTP/1.1 200 OK"
2025-04-08 21:41:45,549 - INFO - Admin update task successful: Updated Admin Test Task
2025-04-08 21:41:45,551 - INFO - Update verification successful
2025-04-08 21:41:45,624 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks/417 "HTTP/1.1 204 No Content"
2025-04-08 21:41:45,625 - INFO - Admin delete task successful for task ID: 417
2025-04-08 21:41:50,282 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 21:41:50,552 - INFO - Delete verification successful
2025-04-08 21:41:50,751 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks?ids=418,419 "HTTP/1.1 204 No Content"
2025-04-08 21:41:50,752 - INFO - Admin mass delete tasks successful for task IDs: [418, 419]
2025-04-08 21:41:51,479 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 21:41:51,491 - INFO - Mass delete verification successful
2025-04-08 21:41:51,632 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/stats "HTTP/1.1 200 OK"
2025-04-08 21:41:51,673 - INFO - Admin stats returned successfully: {
  "total_completions": 80,
  "completed_tasks": 57,
  "completion_rate": 71.25,
  "reward_distribution": {
    "RewardType.DATA_BONUS": 0,
    "RewardType.DAYS_BONUS": 0,
    "RewardType.WALLET_BONUS": 57
  },
  "task_type_distribution": {
    "TaskType.DAILY_CHECKIN": 13,
    "TaskType.YOUTUBE_VIEW": 26,
    "TaskType.INSTAGRAM_FOLLOW": 0,
    "TaskType.INSTAGRAM_VIEW": 0,
    "TaskType.TELEGRAM_CHANNEL": 18,
    "TaskType.TWITTER_FOLLOW": 0,
    "TaskType.WEBSITE_VISIT": 0,
    "TaskType.REFERRAL": 0
  },
  "daily_stats": [
    {
      "date": "2025-04-08",
      "completions": 6
    },
    {
      "date": "2025-04-07",
      "completions": 0
    },
    {
      "date": "2025-04-06",
      "completions": 0
    },
    {
      "date": "2025-04-05",
      "completions": 4
    },
    {
      "date": "2025-04-04",
      "completions": 0
    },
    {
      "date": "2025-04-03",
      "completions": 0
    },
    {
      "date": "2025-04-02",
      "completions": 0
    }
  ]
}
2025-04-08 21:41:51,675 - INFO - Stats verification successful
2025-04-08 21:41:51,761 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/420/start "HTTP/1.1 200 OK"
2025-04-08 21:41:51,878 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/420/reset "HTTP/1.1 200 OK"
2025-04-08 21:41:51,921 - INFO - Task reset successful: 81
2025-04-08 21:41:51,921 - INFO - Reset verification successful
