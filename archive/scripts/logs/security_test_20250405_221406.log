
Starting Security Task Tests...
2025-04-05 22:14:26,647 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:14:26,682 - INFO - Retrieved CSRF token for admin login
2025-04-05 22:14:27,807 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:14:27,816 - INFO - Admin login successful
2025-04-05 22:14:27,947 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:14:27,959 - INFO - Created test task with ID: 390
2025-04-05 22:14:27,961 - INFO - Test environment setup complete
2025-04-05 22:14:28,533 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:28,586 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/390/verify "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:14:28,686 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:28,748 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/390/claim "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:14:28,829 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/claim "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:28,903 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,491 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,534 - INFO - All unauthorized access tests passed
2025-04-05 22:14:30,581 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,625 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,698 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,782 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,867 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,950 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,522 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,621 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,721 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,789 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,865 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,934 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:32,470 - INFO - All invalid token tests passed
2025-04-05 22:14:37,711 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-05 22:14:37,844 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/390/toggle "HTTP/1.1 200 OK"
2025-04-05 22:14:37,915 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/analytics "HTTP/1.1 404 Not Found"
2025-04-05 22:14:37,957 - ERROR - Admin access to /api/tasks/admin/analytics failed with status 404
2025-04-05 22:14:37,957 - WARNING - TEST_MODE is enabled, rate limiting may not be active
2025-04-05 22:14:44,653 - INFO - For accurate rate limiting tests, run with TEST_MODE=false
2025-04-05 22:14:44,721 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:44,831 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:45,545 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:45,725 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:45,858 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:46,530 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:46,654 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:46,776 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:46,896 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:47,684 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:47,861 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:49,513 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:49,641 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:49,763 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:49,871 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:51,545 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,477 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,596 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,687 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,774 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,873 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:53,895 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,523 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,639 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,739 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,831 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,925 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,526 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,635 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,743 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,842 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,944 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,547 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,643 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,741 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,853 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,465 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,603 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,738 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,859 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,497 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,619 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,746 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,875 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,465 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,615 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,768 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,906 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,567 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,719 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,772 - WARNING - Rate limiting not detected - this might be expected in test mode
2025-04-05 22:15:02,850 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,851 - ERROR - Input validation failed for {'verification_code': ''}. Got status 500
2025-04-05 22:15:02,907 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/claim "HTTP/1.1 400 Bad Request"
2025-04-05 22:15:03,516 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:03,561 - ERROR - Verify without starting didn't return error. Got 500
2025-04-05 22:15:03,596 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/start "HTTP/1.1 401 Unauthorized"
2025-04-05 22:15:03,691 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 401 Unauthorized"
2025-04-05 22:15:03,832 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/claim "HTTP/1.1 401 Unauthorized"
2025-04-05 22:15:03,873 - INFO - CSRF protection test passed
2025-04-05 22:15:03,875 - INFO - Header injection was prevented by the HTTP library: Illegal header value b'127.0.0.1\r\nX-Injected-Header: value'
2025-04-05 22:15:03,876 - INFO - This is good security practice and the test is considered passed
  Setup completed 
  Running tests...

Security Task Test Report
      Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━┓
┃ Metric       ┃ Value  ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━┩
│ Total Tests  │ 8      │
│ Passed       │ 5      │
│ Failed       │ 0      │
│ Success Rate │ 62.50% │
└──────────────┴────────┘

Report saved to /root/project/backend/scripts/logs/security_task_test_report_20250405_221503.json
