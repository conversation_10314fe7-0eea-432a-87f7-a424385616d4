Starting Admin Task Tests...
2025-04-08 19:08:46,190 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:08:46,199 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:08:47,644 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:08:47,686 - INFO - Admin login successful
2025-04-08 19:08:47,801 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:47,803 - INFO - Created test task 0 with ID: 402
2025-04-08 19:08:47,908 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:47,949 - INFO - Created test task 1 with ID: 403
2025-04-08 19:08:49,483 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:49,525 - INFO - Created test task 2 with ID: 404
2025-04-08 19:08:49,689 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:49,729 - INFO - Created test task 3 with ID: 405
2025-04-08 19:08:49,860 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:08:49,901 - INFO - Created test task 4 with ID: 406
2025-04-08 19:08:49,902 - INFO - Test environment setup complete
  Setup completed 
  Running tests...
2025-04-08 19:08:51,455 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 19:08:51,539 - INFO - Admin list tasks returned 372 tasks
2025-04-08 19:08:51,844 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 19:08:52,475 - INFO - HTTP Request: PATCH http://localhost:8000/api/tasks/admin/tasks/402 "HTTP/1.1 200 OK"
2025-04-08 19:08:52,513 - INFO - Admin update task successful: Updated Admin Test Task
2025-04-08 19:08:52,513 - INFO - Update verification successful
2025-04-08 19:08:52,821 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks/403 "HTTP/1.1 204 No Content"
2025-04-08 19:08:52,823 - INFO - Admin delete task successful for task ID: 403
2025-04-08 19:08:57,684 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 19:08:57,717 - INFO - Delete verification successful
2025-04-08 19:08:57,848 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks?ids=404,405 "HTTP/1.1 204 No Content"
2025-04-08 19:08:57,850 - INFO - Admin mass delete tasks successful for task IDs: [404, 405]
2025-04-08 19:08:58,689 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 19:08:58,698 - INFO - Mass delete verification successful
2025-04-08 19:08:58,847 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/stats "HTTP/1.1 200 OK"
2025-04-08 19:08:58,890 - INFO - Admin stats returned successfully: {
  "total_completions": 75,
  "completed_tasks": 54,
  "completion_rate": 72.0,
  "reward_distribution": {
    "RewardType.DATA_BONUS": 0,
    "RewardType.DAYS_BONUS": 0,
    "RewardType.WALLET_BONUS": 54
  },
  "task_type_distribution": {
    "TaskType.DAILY_CHECKIN": 12,
    "TaskType.YOUTUBE_VIEW": 25,
    "TaskType.INSTAGRAM_FOLLOW": 0,
    "TaskType.INSTAGRAM_VIEW": 0,
    "TaskType.TELEGRAM_CHANNEL": 17,
    "TaskType.TWITTER_FOLLOW": 0,
    "TaskType.WEBSITE_VISIT": 0,
    "TaskType.REFERRAL": 0
  },
  "daily_stats": [
    {
      "date": "2025-04-08",
      "completions": 4
    },
    {
      "date": "2025-04-07",
      "completions": 0
    },
    {
      "date": "2025-04-06",
      "completions": 0
    },
    {
      "date": "2025-04-05",
      "completions": 4
    },
    {
      "date": "2025-04-04",
      "completions": 0
    },
    {
      "date": "2025-04-03",
      "completions": 0
    },
    {
      "date": "2025-04-02",
      "completions": 0
    }
  ]
}
2025-04-08 19:08:58,890 - INFO - Stats verification successful
2025-04-08 19:08:59,542 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/406/start "HTTP/1.1 200 OK"
2025-04-08 19:08:59,897 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/406/reset "HTTP/1.1 200 OK"
2025-04-08 19:08:59,941 - INFO - Task reset successful: 76
2025-04-08 19:08:59,941 - INFO - Reset verification successful
  Admin Task Test Report  
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 6       │
│ Passed       │ 6       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘
Report saved to /root/project/backend/scripts/logs/admin_task_test_report_20250408_190859.json
