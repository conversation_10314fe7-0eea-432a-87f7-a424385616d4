
Starting Security Task Tests...
2025-04-08 21:40:45,745 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:40:45,747 - INFO - Retrieved CSRF token for admin login
2025-04-08 21:40:47,727 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:40:47,769 - INFO - Admin login successful
2025-04-08 21:40:47,836 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:40:47,838 - INFO - Created test task with ID: 415
2025-04-08 21:40:47,838 - INFO - Test environment setup complete
2025-04-08 21:40:47,873 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:47,909 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/415/verify "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:40:48,827 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:48,890 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/415/claim "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:40:49,492 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,554 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,639 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,682 - INFO - All unauthorized access tests passed
2025-04-08 21:40:49,730 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,782 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,872 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,950 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,498 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,590 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,680 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,770 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,854 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,938 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:51,498 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:51,585 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:51,629 - INFO - All invalid token tests passed
2025-04-08 21:40:53,872 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 21:40:54,786 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/415/toggle "HTTP/1.1 200 OK"
2025-04-08 21:40:54,838 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/analytics "HTTP/1.1 404 Not Found"
2025-04-08 21:40:54,881 - ERROR - Admin access to /api/tasks/admin/analytics failed with status 404
2025-04-08 21:40:54,881 - WARNING - TEST_MODE is enabled, rate limiting may not be active
2025-04-08 21:40:54,881 - INFO - For accurate rate limiting tests, run with TEST_MODE=false
2025-04-08 21:40:54,931 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:55,622 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:55,754 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:55,882 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:56,522 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:40:56,652 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:56,760 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:56,895 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,508 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,605 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:40:58,699 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,793 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,896 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:41:00,488 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:41:00,603 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:00,707 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:00,845 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,551 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,664 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,768 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,882 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,465 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,585 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,718 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,850 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:04,722 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:04,808 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:04,912 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,554 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,675 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,790 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,910 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,534 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,668 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,808 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,940 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:07,565 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:07,765 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:07,930 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:15,591 - ERROR - Rate limiting test error: 
2025-04-08 21:41:15,664 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:15,665 - ERROR - Input validation failed for {'verification_code': ''}. Got status 500
2025-04-08 21:41:15,704 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/claim "HTTP/1.1 400 Bad Request"
2025-04-08 21:41:15,833 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:15,877 - ERROR - Verify without starting didn't return error. Got 500
2025-04-08 21:41:15,914 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/start "HTTP/1.1 401 Unauthorized"
2025-04-08 21:41:16,531 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 21:41:16,577 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 21:41:16,617 - INFO - CSRF protection test passed
2025-04-08 21:41:16,619 - INFO - Header injection was prevented by the HTTP library: Illegal header value b'127.0.0.1\r\nX-Injected-Header: value'
2025-04-08 21:41:16,619 - INFO - This is good security practice and the test is considered passed
  Setup completed 
  Running tests...

Security Task Test Report
      Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━┓
┃ Metric       ┃ Value  ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━┩
│ Total Tests  │ 8      │
│ Passed       │ 4      │
│ Failed       │ 1      │
│ Success Rate │ 50.00% │
└──────────────┴────────┘
 Errors  
┏━━━━━━━┓
┃ Error ┃
┡━━━━━━━┩
│       │
└───────┘

Report saved to /root/project/backend/scripts/logs/security_task_test_report_20250408_214116.json
