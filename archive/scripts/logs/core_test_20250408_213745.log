
Starting Core Task API Tests...
2025-04-08 21:37:58,704 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:37:58,707 - INFO - Retrieved CSRF token for admin login
2025-04-08 21:38:00,627 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:38:00,670 - INFO - Admin login successful
2025-04-08 21:38:00,705 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:38:00,712 - INFO - Retrieved CSRF token for user login
2025-04-08 21:38:01,733 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:38:01,774 - INFO - User login successful
2025-04-08 21:38:01,909 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:38:01,910 - INFO - Created YOUTUBE_VIEW task with ID: 412
2025-04-08 21:38:02,934 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:38:03,466 - INFO - Created TELEGRAM_CHANNEL task with ID: 413
2025-04-08 21:38:03,570 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/start "HTTP/1.1 200 OK"
2025-04-08 21:38:03,613 - INFO - Started task 412
2025-04-08 21:38:03,797 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/verify "HTTP/1.1 200 OK"
2025-04-08 21:38:03,837 - INFO - Verified task 412: True
2025-04-08 21:38:04,901 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/verify "HTTP/1.1 200 OK"
2025-04-08 21:38:04,902 - INFO - Verified task 412: True
2025-04-08 21:38:05,518 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/claim "HTTP/1.1 200 OK"
2025-04-08 21:38:05,561 - INFO - Claimed reward for task 412
2025-04-08 21:38:05,562 - INFO - YouTube task flow completed successfully
2025-04-08 21:38:05,682 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/413/start "HTTP/1.1 200 OK"
2025-04-08 21:38:05,729 - INFO - Started task 413
2025-04-08 21:38:05,863 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/413/verify "HTTP/1.1 200 OK"
2025-04-08 21:38:05,905 - INFO - Verified task 413: True
2025-04-08 21:38:07,477 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/413/claim "HTTP/1.1 200 OK"
2025-04-08 21:38:07,517 - INFO - Claimed reward for task 413
2025-04-08 21:38:07,517 - INFO - Telegram task flow completed successfully
2025-04-08 21:38:07,588 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/412/toggle "HTTP/1.1 200 OK"
2025-04-08 21:38:07,629 - INFO - Toggled task 412 active status to False
2025-04-08 21:38:07,685 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/412/toggle "HTTP/1.1 200 OK"
2025-04-08 21:38:07,725 - INFO - Toggled task 412 active status to True
2025-04-08 21:38:07,771 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/start "HTTP/1.1 400 Bad Request"
2025-04-08 21:38:07,813 - ERROR - Failed to start task 412: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-08 21:38:07,814 - INFO - Admin functions tested successfully
  Setup completed 
  Running tests...

Task API Test Report
       Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 3       │
│ Passed       │ 3       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘

Report saved to /root/project/backend/scripts/logs/task_api_test_report_20250408_213807.json
