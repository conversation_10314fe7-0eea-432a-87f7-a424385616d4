Starting Admin Task Tests...
2025-04-05 22:15:23,754 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:15:23,762 - INFO - Retrieved CSRF token for admin login
2025-04-05 22:15:24,821 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:15:24,823 - INFO - Admin login successful
2025-04-05 22:15:24,948 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:24,949 - INFO - Created test task 0 with ID: 391
2025-04-05 22:15:25,500 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,541 - INFO - Created test task 1 with ID: 392
2025-04-05 22:15:25,610 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,653 - INFO - Created test task 2 with ID: 393
2025-04-05 22:15:25,722 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,761 - INFO - Created test task 3 with ID: 394
2025-04-05 22:15:25,835 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:15:25,884 - INFO - Created test task 4 with ID: 395
2025-04-05 22:15:25,884 - INFO - Test environment setup complete
  Setup completed 
  Running tests...
2025-04-05 22:15:26,633 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-05 22:15:26,685 - INFO - Admin list tasks returned 365 tasks
2025-04-05 22:15:26,913 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-05 22:15:27,565 - INFO - HTTP Request: PATCH http://localhost:8000/api/tasks/admin/tasks/391 "HTTP/1.1 200 OK"
2025-04-05 22:15:27,569 - INFO - Admin update task successful: Updated Admin Test Task
2025-04-05 22:15:27,569 - INFO - Update verification successful
2025-04-05 22:15:27,664 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks/392 "HTTP/1.1 204 No Content"
2025-04-05 22:15:27,665 - INFO - Admin delete task successful for task ID: 392
2025-04-05 22:15:30,948 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-05 22:15:31,482 - INFO - Delete verification successful
2025-04-05 22:15:31,636 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks?ids=393,394 "HTTP/1.1 204 No Content"
2025-04-05 22:15:31,637 - INFO - Admin mass delete tasks successful for task IDs: [393, 394]
2025-04-05 22:15:31,805 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-05 22:15:31,826 - INFO - Mass delete verification successful
2025-04-05 22:15:33,525 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/stats "HTTP/1.1 200 OK"
2025-04-05 22:15:33,562 - INFO - Admin stats returned successfully: {
  "total_completions": 69,
  "completed_tasks": 49,
  "completion_rate": 71.01,
  "reward_distribution": {
    "RewardType.DATA_BONUS": 0,
    "RewardType.DAYS_BONUS": 0,
    "RewardType.WALLET_BONUS": 49
  },
  "task_type_distribution": {
    "TaskType.DAILY_CHECKIN": 11,
    "TaskType.YOUTUBE_VIEW": 23,
    "TaskType.INSTAGRAM_FOLLOW": 0,
    "TaskType.INSTAGRAM_VIEW": 0,
    "TaskType.TELEGRAM_CHANNEL": 15,
    "TaskType.TWITTER_FOLLOW": 0,
    "TaskType.WEBSITE_VISIT": 0,
    "TaskType.REFERRAL": 0
  },
  "daily_stats": [
    {
      "date": "2025-04-05",
      "completions": 4
    },
    {
      "date": "2025-04-04",
      "completions": 0
    },
    {
      "date": "2025-04-03",
      "completions": 0
    },
    {
      "date": "2025-04-02",
      "completions": 0
    },
    {
      "date": "2025-04-01",
      "completions": 0
    },
    {
      "date": "2025-03-31",
      "completions": 0
    },
    {
      "date": "2025-03-30",
      "completions": 0
    }
  ]
}
2025-04-05 22:15:33,592 - INFO - Stats verification successful
2025-04-05 22:15:33,681 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/395/start "HTTP/1.1 200 OK"
2025-04-05 22:15:35,557 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/395/reset "HTTP/1.1 200 OK"
2025-04-05 22:15:35,597 - INFO - Task reset successful: 70
2025-04-05 22:15:35,620 - INFO - Reset verification successful
  Admin Task Test Report  
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 6       │
│ Passed       │ 6       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘
Report saved to /root/project/backend/scripts/logs/admin_task_test_report_20250405_221535.json
