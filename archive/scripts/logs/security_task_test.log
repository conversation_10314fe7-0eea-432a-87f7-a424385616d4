2025-04-01 22:04:00,731 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-01 22:04:00,733 - ERROR - Admin login failed: {"detail": "CAPTCHA verification required"}
2025-04-01 22:04:00,734 - ERROR - Admin login failed, cannot continue tests
2025-04-02 19:40:21,887 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-02 19:40:21,931 - ERROR - Admin login failed: {"detail":"CSRF token missing or invalid"}
2025-04-02 19:40:21,933 - ERROR - Admin login failed, cannot continue tests
2025-04-05 21:00:17,751 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-05 21:00:17,784 - ERROR - Admin login failed: {"detail":"CSRF token missing or invalid"}
2025-04-05 21:00:17,785 - ERROR - Admin login failed, cannot continue tests
2025-04-05 22:14:26,647 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:14:26,682 - INFO - Retrieved CSRF token for admin login
2025-04-05 22:14:27,807 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:14:27,816 - INFO - Admin login successful
2025-04-05 22:14:27,947 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:14:27,959 - INFO - Created test task with ID: 390
2025-04-05 22:14:27,961 - INFO - Test environment setup complete
2025-04-05 22:14:28,533 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:28,586 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/390/verify "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:14:28,686 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:28,748 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/390/claim "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:14:28,829 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/claim "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:28,903 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,491 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,534 - INFO - All unauthorized access tests passed
2025-04-05 22:14:30,581 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,625 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,698 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,782 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,867 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:30,950 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,522 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,621 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,721 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,789 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,865 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:31,934 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-05 22:14:32,470 - INFO - All invalid token tests passed
2025-04-05 22:14:37,711 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-05 22:14:37,844 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/390/toggle "HTTP/1.1 200 OK"
2025-04-05 22:14:37,915 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/analytics "HTTP/1.1 404 Not Found"
2025-04-05 22:14:37,957 - ERROR - Admin access to /api/tasks/admin/analytics failed with status 404
2025-04-05 22:14:37,957 - WARNING - TEST_MODE is enabled, rate limiting may not be active
2025-04-05 22:14:44,653 - INFO - For accurate rate limiting tests, run with TEST_MODE=false
2025-04-05 22:14:44,721 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:44,831 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:45,545 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:45,725 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:45,858 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:46,530 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:46,654 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:46,776 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:46,896 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:47,684 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:47,861 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:49,513 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:49,641 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 400 Bad Request"
2025-04-05 22:14:49,763 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:49,871 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:51,545 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,477 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,596 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,687 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,774 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:52,873 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:53,895 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,523 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,639 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,739 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,831 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:55,925 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,526 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,635 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,743 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,842 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:56,944 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,547 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,643 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,741 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:57,853 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,465 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,603 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,738 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:58,859 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,497 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,619 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,746 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:14:59,875 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,465 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,615 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,768 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:01,906 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,567 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,719 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,772 - WARNING - Rate limiting not detected - this might be expected in test mode
2025-04-05 22:15:02,850 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:02,851 - ERROR - Input validation failed for {'verification_code': ''}. Got status 500
2025-04-05 22:15:02,907 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/claim "HTTP/1.1 400 Bad Request"
2025-04-05 22:15:03,516 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 500 Internal Server Error"
2025-04-05 22:15:03,561 - ERROR - Verify without starting didn't return error. Got 500
2025-04-05 22:15:03,596 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/start "HTTP/1.1 401 Unauthorized"
2025-04-05 22:15:03,691 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/verify "HTTP/1.1 401 Unauthorized"
2025-04-05 22:15:03,832 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/390/claim "HTTP/1.1 401 Unauthorized"
2025-04-05 22:15:03,873 - INFO - CSRF protection test passed
2025-04-05 22:15:03,875 - INFO - Header injection was prevented by the HTTP library: Illegal header value b'127.0.0.1\r\nX-Injected-Header: value'
2025-04-05 22:15:03,876 - INFO - This is good security practice and the test is considered passed
2025-04-08 19:07:22,541 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:07:22,544 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:07:23,640 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:07:23,681 - INFO - Admin login successful
2025-04-08 19:07:23,746 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:07:23,748 - INFO - Created test task with ID: 401
2025-04-08 19:07:23,748 - INFO - Test environment setup complete
2025-04-08 19:07:23,848 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:23,921 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/401/verify "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:07:24,508 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,561 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/401/claim "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:07:24,632 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,796 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,878 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,967 - INFO - All unauthorized access tests passed
2025-04-08 19:07:25,503 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,544 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,618 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,694 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,768 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,835 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,901 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,961 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,535 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,577 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,647 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,727 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,769 - INFO - All invalid token tests passed
2025-04-08 19:07:42,599 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 19:07:42,660 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/401/toggle "HTTP/1.1 200 OK"
2025-04-08 19:07:42,731 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/analytics "HTTP/1.1 404 Not Found"
2025-04-08 19:07:42,773 - ERROR - Admin access to /api/tasks/admin/analytics failed with status 404
2025-04-08 19:07:42,773 - WARNING - TEST_MODE is enabled, rate limiting may not be active
2025-04-08 19:07:42,773 - INFO - For accurate rate limiting tests, run with TEST_MODE=false
2025-04-08 19:07:42,865 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:43,611 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:43,828 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:44,870 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:45,680 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:45,863 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:47,647 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:47,824 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:48,575 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:48,735 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:48,909 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:49,603 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:49,783 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:49,957 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:50,592 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:50,729 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:50,891 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:52,622 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:52,777 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,455 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,625 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,794 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,950 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:54,549 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:54,724 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:54,861 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:55,541 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:55,716 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:55,837 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:57,681 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:57,817 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:57,938 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:58,568 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:58,720 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:58,870 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:59,545 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:59,674 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:59,837 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:00,472 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:00,688 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:00,832 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:01,483 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:01,625 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:01,835 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:02,574 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:02,769 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:02,911 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,473 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,611 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,713 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,765 - WARNING - Rate limiting not detected - this might be expected in test mode
2025-04-08 19:08:04,854 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,855 - ERROR - Input validation failed for {'verification_code': ''}. Got status 500
2025-04-08 19:08:04,935 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/claim "HTTP/1.1 400 Bad Request"
2025-04-08 19:08:05,611 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:05,653 - ERROR - Verify without starting didn't return error. Got 500
2025-04-08 19:08:05,711 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/start "HTTP/1.1 401 Unauthorized"
2025-04-08 19:08:05,815 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 19:08:05,901 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 19:08:05,941 - INFO - CSRF protection test passed
2025-04-08 19:08:05,944 - INFO - Header injection was prevented by the HTTP library: Illegal header value b'127.0.0.1\r\nX-Injected-Header: value'
2025-04-08 19:08:05,945 - INFO - This is good security practice and the test is considered passed
2025-04-08 21:40:45,745 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:40:45,747 - INFO - Retrieved CSRF token for admin login
2025-04-08 21:40:47,727 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:40:47,769 - INFO - Admin login successful
2025-04-08 21:40:47,836 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:40:47,838 - INFO - Created test task with ID: 415
2025-04-08 21:40:47,838 - INFO - Test environment setup complete
2025-04-08 21:40:47,873 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:47,909 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/415/verify "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:40:48,827 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:48,890 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/415/claim "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:40:49,492 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,554 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,639 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,682 - INFO - All unauthorized access tests passed
2025-04-08 21:40:49,730 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,782 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,872 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:49,950 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,498 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,590 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,680 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,770 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,854 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:50,938 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:51,498 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:51,585 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 21:40:51,629 - INFO - All invalid token tests passed
2025-04-08 21:40:53,872 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 21:40:54,786 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/415/toggle "HTTP/1.1 200 OK"
2025-04-08 21:40:54,838 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/analytics "HTTP/1.1 404 Not Found"
2025-04-08 21:40:54,881 - ERROR - Admin access to /api/tasks/admin/analytics failed with status 404
2025-04-08 21:40:54,881 - WARNING - TEST_MODE is enabled, rate limiting may not be active
2025-04-08 21:40:54,881 - INFO - For accurate rate limiting tests, run with TEST_MODE=false
2025-04-08 21:40:54,931 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:55,622 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:55,754 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:55,882 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:56,522 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:40:56,652 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:56,760 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:56,895 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,508 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,605 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:40:58,699 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,793 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:40:58,896 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:41:00,488 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 400 Bad Request"
2025-04-08 21:41:00,603 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:00,707 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:00,845 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,551 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,664 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,768 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:01,882 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,465 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,585 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,718 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:03,850 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:04,722 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:04,808 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:04,912 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,554 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,675 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,790 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:05,910 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,534 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,668 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,808 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:06,940 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:07,565 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:07,765 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:07,930 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:15,591 - ERROR - Rate limiting test error: 
2025-04-08 21:41:15,664 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:15,665 - ERROR - Input validation failed for {'verification_code': ''}. Got status 500
2025-04-08 21:41:15,704 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/claim "HTTP/1.1 400 Bad Request"
2025-04-08 21:41:15,833 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 21:41:15,877 - ERROR - Verify without starting didn't return error. Got 500
2025-04-08 21:41:15,914 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/start "HTTP/1.1 401 Unauthorized"
2025-04-08 21:41:16,531 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 21:41:16,577 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/415/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 21:41:16,617 - INFO - CSRF protection test passed
2025-04-08 21:41:16,619 - INFO - Header injection was prevented by the HTTP library: Illegal header value b'127.0.0.1\r\nX-Injected-Header: value'
2025-04-08 21:41:16,619 - INFO - This is good security practice and the test is considered passed
