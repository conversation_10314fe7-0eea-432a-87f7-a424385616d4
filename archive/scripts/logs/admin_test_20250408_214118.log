Starting Admin Task Tests...
2025-04-08 21:41:33,934 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:41:34,473 - INFO - Retrieved CSRF token for admin login
2025-04-08 21:41:35,516 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:41:35,518 - INFO - Admin login successful
2025-04-08 21:41:35,587 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:35,588 - INFO - Created test task 0 with ID: 416
2025-04-08 21:41:35,724 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:35,765 - INFO - Created test task 1 with ID: 417
2025-04-08 21:41:35,875 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:35,917 - INFO - Created test task 2 with ID: 418
2025-04-08 21:41:36,947 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:37,460 - INFO - Created test task 3 with ID: 419
2025-04-08 21:41:37,551 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:41:37,593 - INFO - Created test task 4 with ID: 420
2025-04-08 21:41:37,593 - INFO - Test environment setup complete
  Setup completed 
  Running tests...
2025-04-08 21:41:37,885 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 21:41:44,575 - INFO - Admin list tasks returned 379 tasks
2025-04-08 21:41:44,899 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 21:41:45,548 - INFO - HTTP Request: PATCH http://localhost:8000/api/tasks/admin/tasks/416 "HTTP/1.1 200 OK"
2025-04-08 21:41:45,549 - INFO - Admin update task successful: Updated Admin Test Task
2025-04-08 21:41:45,551 - INFO - Update verification successful
2025-04-08 21:41:45,624 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks/417 "HTTP/1.1 204 No Content"
2025-04-08 21:41:45,625 - INFO - Admin delete task successful for task ID: 417
2025-04-08 21:41:50,282 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 21:41:50,552 - INFO - Delete verification successful
2025-04-08 21:41:50,751 - INFO - HTTP Request: DELETE http://localhost:8000/api/tasks/admin/tasks?ids=418,419 "HTTP/1.1 204 No Content"
2025-04-08 21:41:50,752 - INFO - Admin mass delete tasks successful for task IDs: [418, 419]
2025-04-08 21:41:51,479 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
2025-04-08 21:41:51,491 - INFO - Mass delete verification successful
2025-04-08 21:41:51,632 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/stats "HTTP/1.1 200 OK"
2025-04-08 21:41:51,673 - INFO - Admin stats returned successfully: {
  "total_completions": 80,
  "completed_tasks": 57,
  "completion_rate": 71.25,
  "reward_distribution": {
    "RewardType.DATA_BONUS": 0,
    "RewardType.DAYS_BONUS": 0,
    "RewardType.WALLET_BONUS": 57
  },
  "task_type_distribution": {
    "TaskType.DAILY_CHECKIN": 13,
    "TaskType.YOUTUBE_VIEW": 26,
    "TaskType.INSTAGRAM_FOLLOW": 0,
    "TaskType.INSTAGRAM_VIEW": 0,
    "TaskType.TELEGRAM_CHANNEL": 18,
    "TaskType.TWITTER_FOLLOW": 0,
    "TaskType.WEBSITE_VISIT": 0,
    "TaskType.REFERRAL": 0
  },
  "daily_stats": [
    {
      "date": "2025-04-08",
      "completions": 6
    },
    {
      "date": "2025-04-07",
      "completions": 0
    },
    {
      "date": "2025-04-06",
      "completions": 0
    },
    {
      "date": "2025-04-05",
      "completions": 4
    },
    {
      "date": "2025-04-04",
      "completions": 0
    },
    {
      "date": "2025-04-03",
      "completions": 0
    },
    {
      "date": "2025-04-02",
      "completions": 0
    }
  ]
}
2025-04-08 21:41:51,675 - INFO - Stats verification successful
2025-04-08 21:41:51,761 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/420/start "HTTP/1.1 200 OK"
2025-04-08 21:41:51,878 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/420/reset "HTTP/1.1 200 OK"
2025-04-08 21:41:51,921 - INFO - Task reset successful: 81
2025-04-08 21:41:51,921 - INFO - Reset verification successful
  Admin Task Test Report  
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 6       │
│ Passed       │ 6       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘
Report saved to /root/project/backend/scripts/logs/admin_task_test_report_20250408_214151.json
