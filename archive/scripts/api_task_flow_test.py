#!/usr/bin/env python
# API Task Flow Test
# This script tests the task API endpoints with real HTTP requests

import os
import sys
import json
import random
import asyncio
import logging
import httpx
from typing import Dict, List, Optional, Any
from datetime import datetime
from urllib.parse import urljoin

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('api_task_flow.log')
    ]
)
logger = logging.getLogger("APITaskFlowTest")

class APITaskFlowTester:
    """Test task flow using actual API endpoints"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
        self.access_token = None
        self.admin_token = None
        self.results = {
            "tests_run": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "detailed_results": []
        }
        
    async def close(self):
        """Close HTTP client"""
        await self.client.aclose()
        
    async def login(self, username: str, password: str) -> bool:
        """Login as user and get access token"""
        try:
            response = await self.client.post(
                urljoin(self.base_url, "/token"),
                data={"username": username, "password": password}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data["access_token"]
                logger.info(f"Successfully logged in as {username}")
                return True
            else:
                logger.error(f"Failed to login: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}")
            return False
            
    async def admin_login(self, username: str, password: str) -> bool:
        """Login as admin and get access token"""
        try:
            response = await self.client.post(
                urljoin(self.base_url, "/auth/token"),
                json={"username": username, "password": password}
            )
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data["access_token"]
                logger.info(f"Successfully logged in as admin {username}")
                return True
            else:
                logger.error(f"Failed to login as admin: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Admin login error: {str(e)}")
            return False

    async def create_test_task(self, task_name: str, task_type: str) -> Optional[int]:
        """Create a test task using admin API"""
        if not self.admin_token:
            logger.error("Admin not logged in, cannot create task")
            return None
            
        headers = {"Authorization": f"Bearer {self.admin_token}"}
        
        task_data = {
            "name": task_name,
            "description": f"Test task for {task_type}",
            "type": task_type,
            "reward_type": "WALLET_BONUS",
            "reward_value": 10.0,
            "target_value": 1,
            "is_active": True
        }
        
        # Add task-specific fields
        if task_type == "DAILY_CHECKIN":
            task_data.update({
                "cycle_length": 7,
                "daily_rewards": [10, 15, 20, 25, 30, 35, 50],
                "cycle_bonus_reward": 100,
                "reset_streak_after_hours": 48
            })
        elif task_type == "TELEGRAM_CHANNEL":
            task_data.update({
                "platform_id": "test_channel",
                "platform_url": "https://t.me/test_channel"
            })
        elif task_type == "YOUTUBE_VIEW":
            task_data.update({
                "platform_url": "https://youtube.com/watch?v=test",
                "verify_key": "TEST123"
            })
        elif task_type == "INSTAGRAM_FOLLOW":
            task_data.update({
                "platform_url": "https://instagram.com/test",
                "platform_id": "test_instagram"
            })
            
        try:
            response = await self.client.post(
                urljoin(self.base_url, "/api/tasks/admin/tasks/create"),
                json=task_data,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                task_id = data["id"]
                logger.info(f"Created task {task_id}: {task_name}")
                return task_id
            else:
                logger.error(f"Failed to create task: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error creating task: {str(e)}")
            return None

    async def get_available_tasks(self) -> List[Dict]:
        """Get available tasks for current user"""
        if not self.access_token:
            logger.error("User not logged in, cannot get tasks")
            return []
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            response = await self.client.get(
                urljoin(self.base_url, "/api/tasks/available"),
                headers=headers
            )
            
            if response.status_code == 200:
                tasks = response.json()
                logger.info(f"Got {len(tasks)} available tasks")
                return tasks
            else:
                logger.error(f"Failed to get tasks: {response.status_code} - {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting tasks: {str(e)}")
            return []

    async def start_task(self, task_id: int) -> Optional[Dict]:
        """Start a task using API"""
        if not self.access_token:
            logger.error("User not logged in, cannot start task")
            return None
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            response = await self.client.post(
                urljoin(self.base_url, f"/api/tasks/{task_id}/start"),
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Started task {task_id}")
                return data
            else:
                logger.error(f"Failed to start task: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error starting task: {str(e)}")
            return None

    async def verify_task(self, task_id: int, verification_data: Dict) -> Optional[Dict]:
        """Verify a task using API"""
        if not self.access_token:
            logger.error("User not logged in, cannot verify task")
            return None
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            response = await self.client.post(
                urljoin(self.base_url, f"/api/tasks/{task_id}/verify"),
                json=verification_data,
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Verified task {task_id}: {data['success']}")
                return data
            else:
                logger.error(f"Failed to verify task: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error verifying task: {str(e)}")
            return None

    async def claim_task_reward(self, task_id: int) -> Optional[Dict]:
        """Claim task reward using API"""
        if not self.access_token:
            logger.error("User not logged in, cannot claim reward")
            return None
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            response = await self.client.post(
                urljoin(self.base_url, f"/api/tasks/{task_id}/claim"),
                headers=headers
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Claimed reward for task {task_id}")
                return data
            else:
                logger.error(f"Failed to claim reward: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error claiming reward: {str(e)}")
            return None

    async def get_wallet_balance(self) -> Optional[float]:
        """Get current user's wallet balance"""
        if not self.access_token:
            logger.error("User not logged in, cannot get wallet balance")
            return None
            
        headers = {"Authorization": f"Bearer {self.access_token}"}
        
        try:
            response = await self.client.get(
                urljoin(self.base_url, "/users/wallet/"),
                headers=headers
            )
            
            if response.status_code == 200:
                balance = float(response.json())
                logger.info(f"Current wallet balance: {balance}")
                return balance
            else:
                logger.error(f"Failed to get wallet balance: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"Error getting wallet balance: {str(e)}")
            return None

    async def run_task_flow_test(self, task_type: str) -> Dict:
        """Run a complete task flow test"""
        result = {
            "task_type": task_type,
            "flow_steps": [],
            "success": False,
            "error": None
        }
        
        try:
            # Create a test task
            task_name = f"API Test {task_type} {random.randint(1000, 9999)}"
            task_id = await self.create_test_task(task_name, task_type)
            
            if not task_id:
                result["error"] = "Failed to create test task"
                return result
                
            result["task_id"] = task_id
            result["task_name"] = task_name
            
            # Get initial balance
            initial_balance = await self.get_wallet_balance()
            result["initial_balance"] = initial_balance
            
            # Step 1: Start task
            start_result = await self.start_task(task_id)
            
            if not start_result:
                result["error"] = "Failed to start task"
                return result
                
            result["flow_steps"].append({
                "step": "start_task",
                "success": True,
                "completion_id": start_result.get("id")
            })
            
            # Step 2: Verify task
            verification_data = self._get_verification_data_for_task_type(task_type)
            verify_result = await self.verify_task(task_id, verification_data)
            
            if not verify_result:
                result["error"] = "Failed to verify task"
                return result
                
            result["flow_steps"].append({
                "step": "verify_task",
                "success": verify_result.get("success"),
                "status": verify_result.get("status"),
                "message": verify_result.get("message")
            })
            
            # Only proceed to claim if verification was successful
            if verify_result.get("success"):
                # Wait briefly to ensure claim delay has passed 
                # (this would normally be overridden in the server in test mode)
                logger.info("Waiting briefly before claiming reward...")
                await asyncio.sleep(1)
                
                # Step 3: Claim reward
                claim_result = await self.claim_task_reward(task_id)
                
                if not claim_result:
                    result["error"] = "Failed to claim reward"
                    return result
                    
                result["flow_steps"].append({
                    "step": "claim_reward",
                    "success": True,
                    "claimed_at": claim_result.get("claimed_at")
                })
                
                # Get final balance
                final_balance = await self.get_wallet_balance()
                result["final_balance"] = final_balance
                result["balance_change"] = final_balance - initial_balance if initial_balance is not None and final_balance is not None else None
                
                result["success"] = True
            else:
                result["error"] = f"Verification failed: {verify_result.get('message')}"
                
        except Exception as e:
            logger.error(f"Error in task flow test: {str(e)}")
            result["error"] = str(e)
            
        return result

    def _get_verification_data_for_task_type(self, task_type: str) -> Dict:
        """Generate task-specific verification data"""
        if task_type == "YOUTUBE_VIEW":
            return {"verification_code": "TEST123"}
        elif task_type == "TELEGRAM_CHANNEL":
            return {"channel_id": "test_channel"}
        elif task_type == "INSTAGRAM_FOLLOW":
            return {"username": "test_user"}
        elif task_type == "DAILY_CHECKIN":
            return {"check_in": True}
        else:
            return {"test": "data"}

    async def run_all_task_type_tests(self) -> Dict:
        """Run task flow tests for all task types"""
        task_types = [
            "DAILY_CHECKIN",
            "YOUTUBE_VIEW",
            "TELEGRAM_CHANNEL",
            "INSTAGRAM_FOLLOW",
            "WEBSITE_VISIT",
            "TWITTER_FOLLOW"
        ]
        
        for task_type in task_types:
            logger.info(f"Testing task flow for {task_type}")
            result = await self.run_task_flow_test(task_type)
            self.results["tests_run"] += 1
            
            if result["success"]:
                self.results["successful_tests"] += 1
            else:
                self.results["failed_tests"] += 1
                
            self.results["detailed_results"].append(result)
            
        return self.results

    def generate_report(self, report_path: Optional[str] = None) -> None:
        """Generate a JSON report of API test results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = report_path or f"test_reports/api_task_flow_test_{timestamp}.json"
        
        # Create report directory if it doesn't exist
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        # Add summary statistics
        self.results["summary"] = {
            "total_tests": self.results["tests_run"],
            "success_rate": round((self.results["successful_tests"] / self.results["tests_run"]) * 100, 2) if self.results["tests_run"] > 0 else 0,
            "run_time": datetime.now().isoformat()
        }
        
        # Write report to file
        with open(report_path, 'w') as f:
            json.dump(self.results, f, indent=2)
            
        logger.info(f"API test report generated: {report_path}")


async def main():
    # Initialize API tester
    base_url = os.environ.get("API_BASE_URL", "http://localhost:8000")
    tester = APITaskFlowTester(base_url)
    
    try:
        # Login as admin (replace with actual credentials)
        admin_username = os.environ.get("ADMIN_USERNAME", "admin")
        admin_password = os.environ.get("ADMIN_PASSWORD", "admin")
        
        admin_login_success = await tester.admin_login(admin_username, admin_password)
        if not admin_login_success:
            logger.error("Admin login failed, cannot continue")
            return
            
        # Login as regular user (replace with actual credentials)
        user_username = os.environ.get("USER_USERNAME", "user")
        user_password = os.environ.get("USER_PASSWORD", "user")
        
        user_login_success = await tester.login(user_username, user_password)
        if not user_login_success:
            logger.error("User login failed, cannot continue")
            return
            
        # Run all task flow tests
        logger.info("Starting API task flow tests...")
        results = await tester.run_all_task_type_tests()
        
        # Generate report
        tester.generate_report()
        
        # Log summary results
        success_rate = round((results["successful_tests"] / results["tests_run"]) * 100, 2) if results["tests_run"] > 0 else 0
        logger.info(f"API task flow tests completed:")
        logger.info(f"  Tests run: {results['tests_run']}")
        logger.info(f"  Successful: {results['successful_tests']}")
        logger.info(f"  Failed: {results['failed_tests']}")
        logger.info(f"  Success rate: {success_rate}%")
        
    finally:
        # Close HTTP client
        await tester.close()


if __name__ == "__main__":
    # Set test mode environment variable
    os.environ["TEST_MODE"] = "true"
    
    # Run the async main function
    asyncio.run(main())