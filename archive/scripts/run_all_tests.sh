#!/bin/bash

# <PERSON>ript to run all task API tests in sequence

# Use direct path references
BACKEND_DIR="/root/project/backend"
SCRIPTS_DIR="$BACKEND_DIR/scripts"
LOGS_DIR="$SCRIPTS_DIR/logs"

# Try to activate virtual environment in specific locations
if [ -d "$BACKEND_DIR/venv" ]; then
    echo "Activating virtual environment from $BACKEND_DIR/venv..."
    source "$BACKEND_DIR/venv/bin/activate"
else
    echo "Warning: Virtual environment not found. Using system Python."
fi

# Check if required packages are installed
if ! python -c "import httpx, rich" &> /dev/null; then
    echo "Installing required packages..."
    pip install httpx rich
fi

# Export environment variables
export API_BASE_URL=${API_BASE_URL:-"http://localhost:8000"}
export TEST_MODE=${TEST_MODE:-"true"}

# Create logs directory if it doesn't exist
mkdir -p "$LOGS_DIR"

# Create a timestamp for this test run
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
LOG_FILE="$LOGS_DIR/all_tests_$TIMESTAMP.log"

# Header for the log file
echo "=== Task API Test Suite - $(date) ===" | tee "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# Function to run a test script and log results
run_test() {
    local script=$1
    local script_name=$(basename "$script")
    
    echo "=== Running $script_name ===" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    
    # Change to backend directory for each test
    cd "$BACKEND_DIR"
    
    # Run the test script
    python "scripts/$script" 2>&1 | tee -a "$LOG_FILE"
    
    # Get exit code
    local exit_code=${PIPESTATUS[0]}
    
    echo "" | tee -a "$LOG_FILE"
    if [ $exit_code -eq 0 ]; then
        echo "✅ $script_name completed successfully" | tee -a "$LOG_FILE"
    else
        echo "❌ $script_name failed with exit code $exit_code" | tee -a "$LOG_FILE"
    fi
    echo "" | tee -a "$LOG_FILE"
    echo "-------------------------------------------" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
    
    return $exit_code
}

# Array of test scripts to run
TEST_SCRIPTS=(
    "task_api_test.py"
    "api_task_flow_test.py"
    "task_performance_test.py"
)

# Run all test scripts
FAILED_TESTS=0
for script in "${TEST_SCRIPTS[@]}"; do
    if [ -f "$SCRIPTS_DIR/$script" ]; then
        run_test "$script"
        if [ $? -ne 0 ]; then
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo "⚠️ Warning: Test script $script not found" | tee -a "$LOG_FILE"
    fi
done

# Print summary
echo "=== Test Suite Summary ===" | tee -a "$LOG_FILE"
echo "Total scripts: ${#TEST_SCRIPTS[@]}" | tee -a "$LOG_FILE"
echo "Failed scripts: $FAILED_TESTS" | tee -a "$LOG_FILE"
echo "Log file: $LOG_FILE" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# Deactivate virtual environment if it was activated
if [ -n "$VIRTUAL_ENV" ]; then
    deactivate
fi

# Exit with appropriate code
if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\033[0;32mAll tests completed successfully!\033[0m" | tee -a "$LOG_FILE"
    exit 0
else
    echo -e "\033[0;31m$FAILED_TESTS test scripts failed!\033[0m" | tee -a "$LOG_FILE"
    exit 1
fi 