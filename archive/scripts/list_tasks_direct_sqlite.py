#!/usr/bin/env python
"""
List Tasks Directly from SQLite Database

This script queries the SQLite database directly to list all tasks.
"""
import sqlite3
import os
from pathlib import Path

# Database file path is in project root
DB_PATH = Path(__file__).parent.parent.parent / "users.db"

def main():
    """Query SQLite database directly to list tasks"""
    if not os.path.exists(DB_PATH):
        print(f"Database file not found at: {DB_PATH}")
        return
    
    print(f"Connecting to database: {DB_PATH}")
    
    try:
        # Connect to SQLite database
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Get results as dictionaries
        cursor = conn.cursor()
        
        # Check if tasks table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='tasks'")
        if not cursor.fetchone():
            print("Tasks table does not exist in the database!")
            return
        
        # Get count of tasks
        cursor.execute("SELECT COUNT(*) as count FROM tasks")
        count = cursor.fetchone()['count']
        print(f"Total tasks in database: {count}")
        
        # Get list of all tasks
        cursor.execute("""
            SELECT id, name, type, platform_url, is_active 
            FROM tasks 
            ORDER BY id 
            LIMIT 20
        """)
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("No tasks found in the database.")
            return
        
        print("\nTasks in database:")
        print("-" * 80)
        for task in tasks:
            print(f"ID: {task['id']}")
            print(f"Name: {task['name']}")
            print(f"Type: {task['type']}")
            print(f"Platform URL: {task['platform_url']}")
            print(f"Active: {'Yes' if task['is_active'] else 'No'}")
            print("-" * 80)
            
        # If there are more than 20 tasks, indicate there are more
        if count > 20:
            print(f"... and {count - 20} more tasks")
            
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    main() 