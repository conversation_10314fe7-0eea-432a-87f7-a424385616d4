#!/bin/bash
# Security Dashboard Generator Runner
# This script runs the security dashboard generator with proper parameters

# Default values
LOG_DIR="logs"
OUTPUT_DIR="reports"
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""
DAYS=7
SCRIPT_DIR=$(dirname "$0")

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --log-dir)
      LOG_DIR="$2"
      shift 2
      ;;
    --output-dir)
      OUTPUT_DIR="$2"
      shift 2
      ;;
    --redis-host)
      REDIS_HOST="$2"
      shift 2
      ;;
    --redis-port)
      REDIS_PORT="$2"
      shift 2
      ;;
    --redis-password)
      REDIS_PASSWORD="$2"
      shift 2
      ;;
    --days)
      DAYS="$2"
      shift 2
      ;;
    --help)
      echo "Usage: $0 [options]"
      echo "Options:"
      echo "  --log-dir DIR         Directory containing log files (default: logs)"
      echo "  --output-dir DIR      Directory to save dashboard (default: reports)"
      echo "  --redis-host HOST     Redis host (default: localhost)"
      echo "  --redis-port PORT     Redis port (default: 6379)"
      echo "  --redis-password PWD  Redis password (default: none)"
      echo "  --days DAYS           Number of days to analyze (default: 7)"
      echo "  --help                Show this help message"
      exit 0
      ;;
    *)
      echo "Unknown option: $1"
      echo "Use --help for usage information"
      exit 1
      ;;
  esac
done

# Create directories if they don't exist
mkdir -p "$LOG_DIR"
mkdir -p "$OUTPUT_DIR"

# Check if virtual environment exists and activate it
if [ -d "$SCRIPT_DIR/../venv" ]; then
  echo "Activating virtual environment..."
  source "$SCRIPT_DIR/../venv/bin/activate"
  echo "Virtual environment activated successfully"
else
  echo "Virtual environment not found at $SCRIPT_DIR/../venv"
  echo "Using system Python..."
fi

# Check for required Python packages
echo "Checking dependencies..."
required_packages=("pandas" "matplotlib" "seaborn" "redis")
missing_packages=()

for package in "${required_packages[@]}"; do
  if ! python3 -c "import $package" &>/dev/null; then
    missing_packages+=("$package")
  fi
done

if [ ${#missing_packages[@]} -gt 0 ]; then
  echo "Missing required packages: ${missing_packages[*]}"
  echo "Installing missing packages..."
  pip install "${missing_packages[@]}"
else
  echo "All required packages are installed."
fi

# Set Redis password argument if provided
REDIS_PWD_ARG=""
if [ -n "$REDIS_PASSWORD" ]; then
  REDIS_PWD_ARG="--redis-password $REDIS_PASSWORD"
fi

echo "Generating security dashboard..."
echo "Log directory: $LOG_DIR"
echo "Output directory: $OUTPUT_DIR"
echo "Redis host: $REDIS_HOST"
echo "Redis port: $REDIS_PORT"
echo "Days to analyze: $DAYS"

# Set PYTHONPATH to include the parent directory
export PYTHONPATH="$SCRIPT_DIR/..:$PYTHONPATH"

# Run the dashboard generator
python3 "$SCRIPT_DIR/security_dashboard.py" \
  --log-dir "$LOG_DIR" \
  --output-dir "$OUTPUT_DIR" \
  --redis-host "$REDIS_HOST" \
  --redis-port "$REDIS_PORT" \
  $REDIS_PWD_ARG \
  --days "$DAYS"

# Check if dashboard was generated successfully
if [ $? -eq 0 ]; then
  echo "Dashboard generated successfully!"
  echo "You can access the dashboard at: http://localhost:8000/api/security/dashboard"
else
  echo "Failed to generate dashboard. Check the logs for errors."
  exit 1
fi 