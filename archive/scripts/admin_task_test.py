#!/usr/bin/env python
"""
Admin Task API Test
Tests admin-specific task functionality: PATCH, DELETE, mass DELETE, and stats endpoints.
"""
import sys
import os
from pathlib import Path
import asyncio
import logging
from datetime import datetime, timedelta
import json
import httpx
import time
import traceback
from typing import Dict, List, Optional, Any, Tuple
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
import random
import string

# Add backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logs directory
SCRIPT_DIR = Path(__file__).parent
LOGS_DIR = SCRIPT_DIR / "logs"
LOGS_DIR.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOGS_DIR / 'admin_task_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)
console = Console()

class AdminTaskTester:
    """Test admin task endpoints"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.admin_token = None
        self.admin_headers = None
        self.test_tasks = []
        self.results = {
            "tests_run": 0,
            "tests_passed": 0,
            "tests_failed": 0,
            "errors": []
        }
        self.client = httpx.AsyncClient(timeout=30.0, verify=False)
        
        # Admin credentials (should already exist in the system)
        self.admin_credentials = {
            "username": "admin_test",
            "password": "admin123"
        }
    
    async def setup(self):
        """Initialize test environment"""
        try:
            # Login as admin
            success = await self.admin_login()
            if not success:
                logger.error("Admin login failed, cannot continue tests")
                return False
            
            # Create test tasks
            await self.create_test_tasks()
            
            logger.info("Test environment setup complete")
            return True
        except Exception as e:
            logger.error(f"Setup error: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    async def admin_login(self):
        """Login as admin"""
        try:
            # First make a GET request to get the CSRF token
            auth_url = f"{self.base_url}/auth/token"
            
            # Manual GET request to get CSRF token
            async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
                response = await client.get(auth_url)
                csrf_token = response.cookies.get("csrf_token")
                
                if csrf_token:
                    logger.info(f"Retrieved CSRF token for admin login")
                    # Now make the POST request with the CSRF token
                    headers = {
                        "Content-Type": "application/json",
                        "X-CSRF-Token": csrf_token,
                        "X-Captcha-Token": "bypass_captcha_in_test_mode"  # Test CAPTCHA token in header
                    }
                    
                    response = await client.post(
                        auth_url,
                        json={
                            "username": self.admin_credentials["username"],
                            "password": self.admin_credentials["password"]
                        },
                        headers=headers,
                        cookies={"csrf_token": csrf_token}  # Include in cookies
                    )
                else:
                    # Fallback to no CSRF token
                    logger.warning("No CSRF token found for admin login")
                    response = await self.client.post(
                        auth_url,
                        json={
                            "username": self.admin_credentials["username"],
                            "password": self.admin_credentials["password"]
                        },
                        headers={"X-Captcha-Token": "bypass_captcha_in_test_mode"}
                    )
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data["access_token"]
                self.admin_headers = {"Authorization": f"Bearer {self.admin_token}"}
                logger.info("Admin login successful")
                return True
            else:
                logger.error(f"Admin login failed: {response.status_code} {response.text}")
                return False
        except Exception as e:
            logger.error(f"Admin login error: {str(e)}")
            return False
    
    async def create_test_tasks(self):
        """Create test tasks for admin operations"""
        try:
            # Create multiple test tasks for different operations
            for i in range(5):
                # Create task data with social_task structure
                task_data = {
                    "name": f"Admin Test Task {i}",
                    "description": f"Test task for admin operations {i}",
                    "type": "WEBSITE_VISIT",
                    "reward_type": "WALLET_BONUS",
                    "reward_value": 10.0,
                    "target_value": 1,
                    "is_active": True,
                    "social_task": {
                        "platform": "website",
                        "platform_url": "https://example.com",
                        "platform_id": f"admin_test_{i}",
                        "verify_key": "TEST123",
                        "required_duration": 30,
                        "max_verification_attempts": 3,
                        "verification_interval": 60
                    }
                }
                
                # Transform for API compatibility
                api_task_data = task_data.copy()
                if "social_task" in api_task_data:
                    social_task = api_task_data.pop("social_task")
                    api_task_data["platform_url"] = social_task.get("platform_url", "")
                    api_task_data["platform_id"] = social_task.get("platform_id", "")
                    api_task_data["verify_key"] = social_task.get("verify_key", "")
                    api_task_data["max_verification_attempts"] = social_task.get("max_verification_attempts", 3)
                    api_task_data["verification_cooldown"] = social_task.get("verification_interval", 60)
                    
                    if "required_duration" in social_task:
                        api_task_data["required_duration"] = social_task["required_duration"]
                
                response = await self.client.post(
                    f"{self.base_url}/api/tasks/admin/tasks/create",
                    headers=self.admin_headers,
                    json=api_task_data
                )
                
                if response.status_code == 200:
                    task = response.json()
                    self.test_tasks.append(task)
                    logger.info(f"Created test task {i} with ID: {task['id']}")
                else:
                    logger.error(f"Failed to create test task {i}: {response.status_code} {response.text}")
            
            return len(self.test_tasks) > 0
        except Exception as e:
            logger.error(f"Create test tasks error: {str(e)}")
            return False
    
    async def test_admin_list_tasks(self):
        """Test admin list tasks endpoint"""
        try:
            # Use the available endpoint instead of admin/tasks/list
            response = await self.client.get(
                f"{self.base_url}/api/tasks/available",
                headers=self.admin_headers
            )
            
            if response.status_code == 200:
                tasks = response.json()
                logger.info(f"Admin list tasks returned {len(tasks)} tasks")
                self.results["tests_passed"] += 1
                return True
            else:
                logger.error(f"Admin list tasks failed: {response.status_code} {response.text}")
                self.results["tests_failed"] += 1
                return False
        except Exception as e:
            logger.error(f"Admin list tasks error: {str(e)}")
            self.results["tests_failed"] += 1
            self.results["errors"].append(str(e))
            return False
        finally:
            self.results["tests_run"] += 1
    
    async def test_admin_update_task(self):
        """Test admin update task endpoint"""
        try:
            if not self.test_tasks:
                logger.error("No test tasks available for update test")
                self.results["tests_failed"] += 1
                return False
            
            task_id = self.test_tasks[0]["id"]
            
            # Get the original task first to include all required fields
            get_response = await self.client.get(
                f"{self.base_url}/api/tasks/available",
                headers=self.admin_headers
            )
            
            if get_response.status_code != 200:
                logger.error(f"Failed to get task details: {get_response.status_code}")
                self.results["tests_failed"] += 1
                return False
                
            tasks = get_response.json()
            original_task = None
            for task in tasks:
                if task["id"] == task_id:
                    original_task = task
                    break
                    
            if not original_task:
                logger.error(f"Could not find task with ID {task_id}")
                self.results["tests_failed"] += 1
                return False
            
            # Include all required fields in the update
            update_data = {
                "name": "Updated Admin Test Task",
                "description": "Updated description for admin test",
                "type": original_task["type"],
                "reward_type": original_task["reward_type"],
                "reward_value": 15.0,
                "target_value": original_task["target_value"],
                "platform_url": "https://updated-example.com",
                "platform_id": original_task.get("platform_id", ""),
                "verify_key": "UPDATED123",
                "max_verification_attempts": original_task.get("max_verification_attempts", 3)
            }
            
            response = await self.client.patch(
                f"{self.base_url}/api/tasks/admin/tasks/{task_id}",
                headers=self.admin_headers,
                json=update_data
            )
            
            if response.status_code == 200:
                updated_task = response.json()
                logger.info(f"Admin update task successful: {updated_task['name']}")
                
                # Verify the update was applied
                if (updated_task["name"] == update_data["name"] and 
                    updated_task["description"] == update_data["description"] and
                    updated_task["reward_value"] == update_data["reward_value"]):
                    logger.info("Update verification successful")
                    self.results["tests_passed"] += 1
                    return True
                else:
                    logger.error("Update verification failed: values don't match")
                    self.results["tests_failed"] += 1
                    return False
            else:
                logger.error(f"Admin update task failed: {response.status_code} {response.text}")
                self.results["tests_failed"] += 1
                return False
        except Exception as e:
            logger.error(f"Admin update task error: {str(e)}")
            self.results["tests_failed"] += 1
            self.results["errors"].append(str(e))
            return False
        finally:
            self.results["tests_run"] += 1
    
    async def test_admin_delete_task(self):
        """Test admin delete task endpoint"""
        try:
            if len(self.test_tasks) < 2:
                logger.error("Not enough test tasks available for delete test")
                self.results["tests_failed"] += 1
                return False
            
            task_id = self.test_tasks[1]["id"]
            
            response = await self.client.delete(
                f"{self.base_url}/api/tasks/admin/tasks/{task_id}",
                headers=self.admin_headers
            )
            
            if response.status_code == 204:
                logger.info(f"Admin delete task successful for task ID: {task_id}")
                
                # Verify the task was deleted
                verify_response = await self.client.get(
                    f"{self.base_url}/api/tasks/admin/tasks/list",
                    headers=self.admin_headers
                )
                
                if verify_response.status_code == 200:
                    tasks = verify_response.json()
                    task_ids = [task["id"] for task in tasks]
                    
                    if task_id not in task_ids:
                        logger.info("Delete verification successful")
                        self.results["tests_passed"] += 1
                        return True
                    else:
                        logger.error("Delete verification failed: task still exists")
                        self.results["tests_failed"] += 1
                        return False
                else:
                    logger.error(f"Delete verification failed: {verify_response.status_code}")
                    self.results["tests_failed"] += 1
                    return False
            else:
                logger.error(f"Admin delete task failed: {response.status_code} {response.text}")
                self.results["tests_failed"] += 1
                return False
        except Exception as e:
            logger.error(f"Admin delete task error: {str(e)}")
            self.results["tests_failed"] += 1
            self.results["errors"].append(str(e))
            return False
        finally:
            self.results["tests_run"] += 1
    
    async def test_admin_mass_delete_tasks(self):
        """Test admin mass delete tasks endpoint"""
        try:
            if len(self.test_tasks) < 4:
                logger.error("Not enough test tasks available for mass delete test")
                self.results["tests_failed"] += 1
                return False
            
            # Select two tasks to delete
            task_ids = [self.test_tasks[2]["id"], self.test_tasks[3]["id"]]
            
            # Use query parameters as expected by the updated endpoint
            task_ids_str = ",".join(str(task_id) for task_id in task_ids)
            response = await self.client.delete(
                f"{self.base_url}/api/tasks/admin/tasks?ids={task_ids_str}",
                headers=self.admin_headers
            )
            
            if response.status_code == 204:
                logger.info(f"Admin mass delete tasks successful for task IDs: {task_ids}")
                
                # Verify the tasks were deleted using the available endpoint
                verify_response = await self.client.get(
                    f"{self.base_url}/api/tasks/available",
                    headers=self.admin_headers
                )
                
                if verify_response.status_code == 200:
                    tasks = verify_response.json()
                    remaining_task_ids = [task["id"] for task in tasks]
                    
                    deleted_count = 0
                    for task_id in task_ids:
                        if task_id not in remaining_task_ids:
                            deleted_count += 1
                    
                    if deleted_count == len(task_ids):
                        logger.info("Mass delete verification successful")
                        self.results["tests_passed"] += 1
                        return True
                    else:
                        logger.error(f"Mass delete verification failed: {deleted_count}/{len(task_ids)} tasks deleted")
                        self.results["tests_failed"] += 1
                        return False
                else:
                    logger.error(f"Mass delete verification failed: {verify_response.status_code}")
                    self.results["tests_failed"] += 1
                    return False
            else:
                logger.error(f"Admin mass delete tasks failed: {response.status_code} {response.text}")
                self.results["tests_failed"] += 1
                return False
        except Exception as e:
            logger.error(f"Admin mass delete tasks error: {str(e)}")
            self.results["tests_failed"] += 1
            self.results["errors"].append(str(e))
            return False
        finally:
            self.results["tests_run"] += 1
    
    async def test_admin_stats(self):
        """Test admin stats endpoint"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/tasks/admin/stats",
                headers=self.admin_headers
            )
            
            if response.status_code == 200:
                stats = response.json()
                logger.info(f"Admin stats returned successfully: {json.dumps(stats, indent=2)}")
                
                # Verify the stats contain expected fields
                required_fields = ["total_completions", "completed_tasks", "completion_rate", "reward_distribution", "task_type_distribution", "daily_stats"]
                missing_fields = [field for field in required_fields if field not in stats]
                
                if not missing_fields:
                    logger.info("Stats verification successful")
                    self.results["tests_passed"] += 1
                    return True
                else:
                    logger.error(f"Stats verification failed: missing fields {missing_fields}")
                    self.results["tests_failed"] += 1
                    return False
            else:
                logger.error(f"Admin stats failed: {response.status_code} {response.text}")
                self.results["tests_failed"] += 1
                return False
        except Exception as e:
            logger.error(f"Admin stats error: {str(e)}")
            self.results["tests_failed"] += 1
            self.results["errors"].append(str(e))
            return False
        finally:
            self.results["tests_run"] += 1
    
    async def test_task_reset(self):
        """Test task reset endpoint"""
        try:
            if not self.test_tasks:
                logger.error("No test tasks available for reset test")
                self.results["tests_failed"] += 1
                return False
            
            task_id = self.test_tasks[4]["id"] if len(self.test_tasks) > 4 else self.test_tasks[0]["id"]
            
            # First start the task
            start_response = await self.client.post(
                f"{self.base_url}/api/tasks/{task_id}/start",
                headers=self.admin_headers
            )
            
            if start_response.status_code != 200:
                logger.error(f"Failed to start task for reset test: {start_response.status_code}")
                self.results["tests_failed"] += 1
                return False
            
            # Now reset the task
            reset_response = await self.client.post(
                f"{self.base_url}/api/tasks/{task_id}/reset",
                headers=self.admin_headers
            )
            
            if reset_response.status_code == 200:
                reset_data = reset_response.json()
                logger.info(f"Task reset successful: {reset_data['id']}")
                
                # Verify the reset was applied
                if reset_data["verification_attempts"] == 0:
                    logger.info("Reset verification successful")
                    self.results["tests_passed"] += 1
                    return True
                else:
                    logger.error("Reset verification failed: verification attempts not reset")
                    self.results["tests_failed"] += 1
                    return False
            else:
                logger.error(f"Task reset failed: {reset_response.status_code} {reset_response.text}")
                self.results["tests_failed"] += 1
                return False
        except Exception as e:
            logger.error(f"Task reset error: {str(e)}")
            self.results["tests_failed"] += 1
            self.results["errors"].append(str(e))
            return False
        finally:
            self.results["tests_run"] += 1
    
    async def run_tests(self):
        """Run all admin task tests"""
        console.print("[bold blue]Starting Admin Task Tests...[/bold blue]")
        
        # Setup test environment
        setup_success = await self.setup()
        if not setup_success:
            console.print("[bold red]Setup failed, cannot continue tests[/bold red]")
            return False
        
        console.print("  Setup completed ")
        
        # Run tests
        console.print("  Running tests...")
        
        await self.test_admin_list_tasks()
        await self.test_admin_update_task()
        await self.test_admin_delete_task()
        await self.test_admin_mass_delete_tasks()
        await self.test_admin_stats()
        await self.test_task_reset()
        
        # Generate report
        self.generate_report()
        
        return self.results["tests_failed"] == 0
    
    def generate_report(self):
        """Generate test report"""
        # Calculate success rate
        total_tests = self.results["tests_run"]
        success_rate = (self.results["tests_passed"] / total_tests * 100) if total_tests > 0 else 0
        
        # Create report table
        table = Table(title="Admin Task Test Report")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Total Tests", str(total_tests))
        table.add_row("Passed", str(self.results["tests_passed"]))
        table.add_row("Failed", str(self.results["tests_failed"]))
        table.add_row("Success Rate", f"{success_rate:.2f}%")
        
        console.print(table)
        
        # Save report to file
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "passed": self.results["tests_passed"],
            "failed": self.results["tests_failed"],
            "success_rate": success_rate,
            "errors": self.results["errors"]
        }
        
        report_file = LOGS_DIR / f"admin_task_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, "w") as f:
            json.dump(report_data, f, indent=2)
        
        console.print(f"Report saved to {report_file}")

async def main():
    """Main function"""
    # Get API base URL from environment variable or use default
    api_base_url = os.environ.get("API_BASE_URL", "http://localhost:8000")
    
    # Create tester instance
    tester = AdminTaskTester(base_url=api_base_url)
    
    # Run tests
    await tester.run_tests()
    
    # Close client
    await tester.client.aclose()

if __name__ == "__main__":
    asyncio.run(main()) 