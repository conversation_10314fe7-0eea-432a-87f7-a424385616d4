"""
Database verification and repair script.
Checks if database structure matches models and fixes any inconsistencies.
"""
import sys
import os
from pathlib import Path
from datetime import datetime
import json
from typing import List, Dict, <PERSON>, <PERSON><PERSON>, Set

# Add the parent directory to sys.path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy import inspect, text, Index
from models import Base, TaskCompletion, Task, User, SecurityLog, TokenBlacklist
from database import engine, SessionLocal
import logging
from redis import Redis
from config.redis_config import RedisClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_model_columns(table) -> Dict[str, Dict[str, Any]]:
    """Get column definitions from SQLAlchemy model"""
    return {
        column.name: {
            'type': str(column.type),
            'nullable': column.nullable,
            'default': str(column.default) if column.default else None,
            'primary_key': column.primary_key
        }
        for column in table.columns
    }

def get_db_columns(table_name: str) -> Dict[str, Dict[str, Any]]:
    """Get column definitions from database"""
    inspector = inspect(engine)
    columns = {}
    for column in inspector.get_columns(table_name):
        columns[column['name']] = {
            'type': str(column['type']),
            'nullable': column['nullable'],
            'default': str(column['default']) if 'default' in column else None,
            'primary_key': column.get('primary_key', False)
        }
    return columns

def check_indexes(table_name: str) -> List[str]:
    """Check if all required indexes exist"""
    inspector = inspect(engine)
    existing_indexes = inspector.get_indexes(table_name)
    model_indexes = getattr(Base.metadata.tables[table_name], 'indexes', set())
    
    missing_indexes = []
    for idx in model_indexes:
        found = False
        for existing_idx in existing_indexes:
            if set(idx.columns.keys()) == set(existing_idx['column_names']):
                found = True
                break
        if not found:
            missing_indexes.append(f"Index on {', '.join(idx.columns.keys())}")
    
    return missing_indexes

def verify_table_structure(table_name: str) -> Tuple[List[str], List[str], List[str]]:
    """Verify table structure against model"""
    model_columns = get_model_columns(Base.metadata.tables[table_name])
    db_columns = get_db_columns(table_name)
    
    missing_columns = []
    type_mismatches = []
    constraint_issues = []
    
    # Check for missing columns
    for col_name, model_col in model_columns.items():
        if col_name not in db_columns:
            missing_columns.append(col_name)
        else:
            db_col = db_columns[col_name]
            # Check type matches
            if model_col['type'] != db_col['type']:
                type_mismatches.append(f"{col_name}: expected {model_col['type']}, got {db_col['type']}")
            # Check constraints
            if model_col['nullable'] != db_col['nullable']:
                constraint_issues.append(f"{col_name}: nullable mismatch")
            if model_col['primary_key'] != db_col['primary_key']:
                constraint_issues.append(f"{col_name}: primary key mismatch")
    
    return missing_columns, type_mismatches, constraint_issues

def backup_table(table_name: str, timestamp: str = None) -> bool:
    """Create a backup of the table"""
    try:
        timestamp = timestamp or datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_table = f"{table_name}_backup_{timestamp}"
        
        # Check if backup already exists
        inspector = inspect(engine)
        if inspector.has_table(backup_table):
            logger.info(f"Backup table {backup_table} already exists, skipping backup")
            return True
            
        with engine.connect() as conn:
            conn.execute(text(f"CREATE TABLE {backup_table} AS SELECT * FROM {table_name}"))
            conn.commit()
            logger.info(f"Created backup table: {backup_table}")
        return True
    except Exception as e:
        logger.error(f"Failed to create backup: {str(e)}")
        return False

def fix_table_structure(table_name: str, missing_columns: List[str], type_mismatches: List[str], constraint_issues: List[str], timestamp: str = None) -> bool:
    """Fix table structure issues"""
    try:
        timestamp = timestamp or datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create backup first
        if not backup_table(table_name, timestamp):
            return False
            
        with engine.connect() as conn:
            # Add missing columns
            for column_name in missing_columns:
                column = Base.metadata.tables[table_name].columns[column_name]
                sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column.type}"
                if not column.nullable:
                    if column.default is not None:
                        sql += f" DEFAULT {column.default.arg}"
                    sql += " NOT NULL"
                conn.execute(text(sql))
                conn.commit()
                logger.info(f"Added column {column_name} to {table_name}")
            
            # Fix type mismatches and constraints
            if type_mismatches or constraint_issues:
                # For SQLite, we need to create a new table and copy data
                if engine.dialect.name == 'sqlite':
                    # Create temporary table with correct structure
                    temp_table = f"{table_name}_temp_{timestamp}"
                    
                    # Get all columns for SELECT
                    all_columns = [c.name for c in Base.metadata.tables[table_name].columns]
                    columns_str = ", ".join(all_columns)
                    
                    # Create temp table with correct structure
                    Base.metadata.tables[table_name].tometadata(Base.metadata).create(bind=engine, checkfirst=True)
                    conn.execute(text(f"ALTER TABLE {table_name} RENAME TO {temp_table}"))
                    
                    # Create new table with correct structure
                    Base.metadata.tables[table_name].create(bind=engine, checkfirst=True)
                    
                    # Copy data
                    conn.execute(text(f"INSERT INTO {table_name} SELECT {columns_str} FROM {temp_table}"))
                    
                    # Drop temporary table
                    conn.execute(text(f"DROP TABLE {temp_table}"))
                    conn.commit()
                    
                    logger.info(f"Fixed type and constraint issues in {table_name}")
                
        return True
    except Exception as e:
        logger.error(f"Failed to fix table structure: {str(e)}")
        return False

def fix_indexes(table_name: str) -> bool:
    """Fix missing indexes"""
    try:
        with engine.connect() as conn:
            inspector = inspect(engine)
            existing_indexes = {
                tuple(idx['column_names']): idx['name'] 
                for idx in inspector.get_indexes(table_name)
            }
            
            model_indexes = Base.metadata.tables[table_name].indexes
            
            for idx in model_indexes:
                columns = tuple(idx.columns.keys())
                if columns not in existing_indexes:
                    # Create index
                    idx_name = f"ix_{table_name}_{'_'.join(columns)}"
                    columns_str = ", ".join(columns)
                    conn.execute(text(f"CREATE INDEX {idx_name} ON {table_name} ({columns_str})"))
                    conn.commit()
                    logger.info(f"Created index {idx_name} on {table_name}")
            
            return True
    except Exception as e:
        logger.error(f"Failed to fix indexes: {str(e)}")
        return False

# Add Redis verification
def verify_redis_connection() -> bool:
    """Verify Redis connection and configuration"""
    try:
        redis_client = RedisClient.get_client()
        redis_client.ping()
        
        # Verify Redis configuration
        config = redis_client.config_get('*')
        required_configs = {
            'maxmemory': '100mb',
            'maxmemory-policy': 'volatile-ttl'
        }
        
        for key, value in required_configs.items():
            if config.get(key) != value:
                logger.warning(f"Redis config mismatch for {key}: expected {value}, got {config.get(key)}")
                return False
                
        return True
    except Exception as e:
        logger.error(f"Redis verification failed: {str(e)}")
        return False

def verify_security_tables() -> Tuple[List[str], List[str], List[str]]:
    """Verify security-related tables"""
    logger.info("Starting security tables verification...")
    
    security_tables = {
        'security_logs': SecurityLog,
        'token_blacklist': TokenBlacklist
    }
    
    missing_tables = []
    structure_issues = []
    index_issues = []
    
    inspector = inspect(engine)
    
    for table_name, model in security_tables.items():
        logger.info(f"Checking table: {table_name}")
        try:
            # Try to get columns - this will fail if table doesn't exist
            inspector.get_columns(table_name)
            logger.info(f"Table {table_name} exists")
            
            logger.info(f"Checking structure for {table_name}")
            # Check columns
            missing_cols, type_mismatches, constraint_issues = verify_table_structure(table_name)
            if any([missing_cols, type_mismatches, constraint_issues]):
                logger.warning(f"Found structure issues in {table_name}")
                if missing_cols:
                    logger.warning(f"Missing columns: {', '.join(missing_cols)}")
                if type_mismatches:
                    logger.warning(f"Type mismatches: {', '.join(type_mismatches)}")
                if constraint_issues:
                    logger.warning(f"Constraint issues: {', '.join(constraint_issues)}")
                    
                structure_issues.append({
                    'table': table_name,
                    'missing_columns': missing_cols,
                    'type_mismatches': type_mismatches,
                    'constraint_issues': constraint_issues
                })
                
            logger.info(f"Checking indexes for {table_name}")
            # Check indexes
            missing_indexes = check_indexes(table_name)
            if missing_indexes:
                logger.warning(f"Missing indexes in {table_name}: {', '.join(missing_indexes)}")
                index_issues.append({
                    'table': table_name,
                    'missing_indexes': missing_indexes
                })
        except Exception as e:
            logger.warning(f"Table {table_name} does not exist: {str(e)}")
            missing_tables.append(table_name)
            
    if missing_tables:
        logger.info(f"Total missing tables: {len(missing_tables)}")
    if structure_issues:
        logger.info(f"Total tables with structure issues: {len(structure_issues)}")
    if index_issues:
        logger.info(f"Total tables with missing indexes: {len(index_issues)}")
            
    return missing_tables, structure_issues, index_issues

def create_security_tables():
    """Create missing security tables"""
    try:
        logger.info("Creating security tables using SQL...")
        
        with engine.connect() as conn:
            # Create SecurityLog table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS security_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action_type VARCHAR(50) NOT NULL,
                    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent VARCHAR(255),
                    status VARCHAR(20) NOT NULL,
                    details JSON,
                    risk_level VARCHAR(10) NOT NULL,
                    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    attempt_count INTEGER DEFAULT 1,
                    window_start TIMESTAMP,
                    blocked_until TIMESTAMP,
                    is_blocked BOOLEAN DEFAULT FALSE
                )
            """))
            logger.info("Created SecurityLog table")
            
            # Create TokenBlacklist table
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS token_blacklist (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    token VARCHAR(500) NOT NULL UNIQUE,
                    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    expires_at TIMESTAMP NOT NULL,
                    revoked_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    revocation_reason VARCHAR(200) NOT NULL,
                    revoked_by_ip VARCHAR(45) NOT NULL
                )
            """))
            logger.info("Created TokenBlacklist table")
            
            # Create indexes
            logger.info("Creating indexes for SecurityLog table...")
            index_statements = [
                "CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs (user_id)",
                "CREATE INDEX IF NOT EXISTS idx_security_logs_action_type ON security_logs (action_type)",
                "CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs (created_at)",
                "CREATE INDEX IF NOT EXISTS idx_security_logs_risk_level ON security_logs (risk_level)",
                "CREATE INDEX IF NOT EXISTS idx_security_logs_ip_action ON security_logs (ip_address, action_type)",
                "CREATE INDEX IF NOT EXISTS idx_security_logs_blocked ON security_logs (is_blocked, blocked_until)",
                "CREATE INDEX IF NOT EXISTS idx_security_logs_rate_limit ON security_logs (action_type, ip_address, user_id, window_start)"
            ]
            
            for stmt in index_statements:
                try:
                    logger.info(f"Executing: {stmt}")
                    conn.execute(text(stmt))
                    logger.info("Index created successfully")
                except Exception as e:
                    logger.error(f"Error creating index: {str(e)}")
                    
            logger.info("Creating indexes for TokenBlacklist table...")
            blacklist_index_statements = [
                "CREATE INDEX IF NOT EXISTS idx_token_blacklist_token ON token_blacklist (token)",
                "CREATE INDEX IF NOT EXISTS idx_token_blacklist_user_id ON token_blacklist (user_id)",
                "CREATE INDEX IF NOT EXISTS idx_token_blacklist_expires_at ON token_blacklist (expires_at)"
            ]
            
            for stmt in blacklist_index_statements:
                try:
                    logger.info(f"Executing: {stmt}")
                    conn.execute(text(stmt))
                    logger.info("Index created successfully")
                except Exception as e:
                    logger.error(f"Error creating index: {str(e)}")
            
            conn.commit()
            logger.info("All security tables and indexes created successfully")
            return True
        
    except Exception as e:
        logger.error(f"Failed to create security tables: {str(e)}")
        logger.exception("Detailed error:")
        return False

def verify_and_fix_security_setup() -> bool:
    """Verify and fix security-related database setup"""
    try:
        logger.info("Verifying security setup...")
        
        # Check Redis first
        if not verify_redis_connection():
            logger.error("Redis verification failed")
            logger.info("Continuing with security table verification...")
            
        # Check security tables
        missing_tables, structure_issues, index_issues = verify_security_tables()
        
        # Create missing tables automatically
        if missing_tables:
            logger.warning(f"Missing security tables: {', '.join(missing_tables)}")
            if create_security_tables():
                logger.info("Successfully created missing security tables")
            else:
                logger.error("Failed to create security tables")
                return False
                
        # Fix structure issues automatically
        if structure_issues:
            logger.warning("Structure issues found in security tables:")
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            for issue in structure_issues:
                table_name = issue['table']
                logger.info(f"Fixing structure issues in {table_name}...")
                if fix_table_structure(
                    table_name,
                    issue['missing_columns'],
                    issue['type_mismatches'],
                    issue['constraint_issues'],
                    timestamp
                ):
                    logger.info(f"Successfully fixed structure issues in {table_name}")
                else:
                    logger.error(f"Failed to fix structure issues in {table_name}")
                    
        # Fix index issues automatically
        if index_issues:
            logger.warning("Missing indexes in security tables:")
            for issue in index_issues:
                table_name = issue['table']
                logger.info(f"Creating missing indexes in {table_name}...")
                if fix_indexes(table_name):
                    logger.info(f"Successfully created indexes in {table_name}")
                else:
                    logger.error(f"Failed to create indexes in {table_name}")
                    
        return True
        
    except Exception as e:
        logger.error(f"Security setup verification failed: {str(e)}")
        return False

# Update main function to include security verification
def main():
    """Main verification function"""
    logger.info("Starting database verification...")
    
    # Verify security setup first
    logger.info("\nVerifying security setup...")
    if not verify_and_fix_security_setup():
        logger.error("Security setup verification failed")
        logger.info("Continuing with general verification...")
    
    # Tables to check
    tables = ['task_completions', 'tasks', 'users', 'task_verifications', 
              'security_logs', 'token_blacklist']
    
    issues_found = False
    fixes_needed = []
    indexes_needed = set()
    
    inspector = inspect(engine)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    for table_name in tables:
        logger.info(f"\nChecking table: {table_name}")
        
        # Verify table exists
        if not inspector.has_table(table_name):
            logger.error(f"Table {table_name} does not exist!")
            continue
            
        # Check structure
        missing_cols, type_mismatches, constraint_issues = verify_table_structure(table_name)
        
        if missing_cols:
            logger.warning(f"Missing columns in {table_name}: {', '.join(missing_cols)}")
            issues_found = True
            fixes_needed.append((table_name, 'structure', (missing_cols, type_mismatches, constraint_issues)))
            
        if type_mismatches:
            logger.warning(f"Type mismatches in {table_name}: {', '.join(type_mismatches)}")
            issues_found = True
            
        if constraint_issues:
            logger.warning(f"Constraint issues in {table_name}: {', '.join(constraint_issues)}")
            issues_found = True
            
        # Check indexes
        missing_indexes = check_indexes(table_name)
        if missing_indexes:
            logger.warning(f"Missing indexes in {table_name}: {', '.join(missing_indexes)}")
            issues_found = True
            indexes_needed.add(table_name)
            
    if issues_found:
        logger.info("\nAttempting to fix issues automatically...")
        
        # Fix structure issues first
        for table_name, issue_type, issues in fixes_needed:
            if issue_type == 'structure':
                missing_cols, type_mismatches, constraint_issues = issues
                if fix_table_structure(table_name, missing_cols, type_mismatches, constraint_issues, timestamp):
                    logger.info(f"Successfully fixed structure issues in {table_name}")
                else:
                    logger.error(f"Failed to fix structure issues in {table_name}")
        
        # Then fix indexes
        for table_name in indexes_needed:
            if fix_indexes(table_name):
                logger.info(f"Successfully fixed indexes in {table_name}")
            else:
                logger.error(f"Failed to fix indexes in {table_name}")
    else:
        logger.info("\nNo issues found. Database structure matches models.")

if __name__ == "__main__":
    main() 