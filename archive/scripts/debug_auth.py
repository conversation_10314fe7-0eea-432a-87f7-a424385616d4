#!/usr/bin/env python
"""
Authentication Debugging Script
This script attempts to diagnose authentication issues in the backend.
"""

import os
import sys
from pathlib import Path
import httpx
import asyncio
import json
import logging
from contextlib import contextmanager
import sqlite3

# Add backend directory to the Python path
backend_dir = Path(__file__).parent.parent.absolute()
sys.path.append(str(backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)
logger = logging.getLogger("auth_debug")

# Set up database access - but use the actual database path
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models import User, Role, Base
from auth import verify_password

# Create a direct connection to the correct database
DB_PATH = "/root/project/backend/users.db"
DATABASE_URL = f"sqlite:///{DB_PATH}"
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@contextmanager
def session_scope():
    """Provide a transactional scope around a series of operations."""
    session = SessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        logger.error(f"Database error: {str(e)}")
        session.rollback()
        raise
    finally:
        session.close()

def check_db_exists():
    """Check if the database file exists"""
    if os.path.exists(DB_PATH):
        logger.info(f"✅ Database file exists at {DB_PATH}")
        # Check file size
        size = os.path.getsize(DB_PATH)
        logger.info(f"Database size: {size / 1024:.2f} KB")
        return True
    else:
        logger.error(f"❌ Database file does not exist at {DB_PATH}")
        return False

def list_tables():
    """List all tables in the database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"Tables in database: {[t[0] for t in tables]}")
        conn.close()
        return [t[0] for t in tables]
    except Exception as e:
        logger.error(f"Error listing tables: {str(e)}")
        return []

async def test_direct_auth():
    """Test authentication directly with the database"""
    logger.info("Testing direct authentication against database...")
    
    # First check database file
    if not check_db_exists():
        return False
        
    # Check tables
    tables = list_tables()
    if "users" not in tables:
        logger.error("❌ No 'users' table found in the database")
        return False
    
    username = "admin_test"
    password = "admin123"
    
    with session_scope() as db:
        # Look up user in the database
        try:
            logger.info("Querying 'users' table...")
            user = db.query(User).filter(User.username == username).first()
            
            if not user:
                logger.error(f"User '{username}' not found in database")
                return False
            
            logger.info(f"Found user: {username}, role: {user.role}")
            
            # Test password verification
            if verify_password(password, user.hashed_password):
                logger.info("✅ Password verification successful")
                return True
            else:
                logger.error("❌ Password verification failed")
                return False
                
        except Exception as e:
            logger.error(f"Error querying user: {str(e)}")
            return False

async def test_api_auth():
    """Test authentication via the API"""
    logger.info("Testing authentication via API...")
    
    base_url = os.environ.get("API_BASE_URL", "http://localhost:8000")
    logger.info(f"Using API URL: {base_url}")
    
    async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
        # Test /auth/token endpoint
        logger.info("Testing /auth/token endpoint...")
        try:
            response = await client.post(
                f"{base_url}/auth/token",
                json={
                    "username": "admin_test",
                    "password": "admin123"
                }
            )
            
            logger.info(f"Response status: {response.status_code}")
            logger.info(f"Response headers: {dict(response.headers)}")
            logger.info(f"Response body: {response.text}")
            
            if response.status_code == 200:
                logger.info("✅ API authentication successful")
                token = response.json()["access_token"]
                logger.info(f"Token: {token[:20]}...")
                return True
            else:
                logger.error(f"❌ API authentication failed: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error during API authentication: {str(e)}")
            return False

async def test_api_tasks_access():
    """Test access to tasks API with authenticated token"""
    logger.info("Testing tasks API with authenticated token...")
    
    base_url = os.environ.get("API_BASE_URL", "http://localhost:8000")
    logger.info(f"Using API URL: {base_url}")
    
    async with httpx.AsyncClient(timeout=10.0, verify=False) as client:
        # 1. Login and get token
        logger.info("1. Getting authentication token...")
        response = await client.post(
            f"{base_url}/auth/token",
            json={
                "username": "admin_test",
                "password": "admin123"
            }
        )
        
        if response.status_code != 200:
            logger.error(f"Authentication failed: {response.status_code} - {response.text}")
            return False
            
        token = response.json()["access_token"]
        auth_headers = {"Authorization": f"Bearer {token}"}
        logger.info(f"✅ Authentication successful, token: {token[:20]}...")
        
        # 2. Create a test task
        logger.info("2. Creating a test task...")
        task_data = {
            "name": "Test Task",
            "description": "This is a test task created by debug_auth.py",
            "type": "YOUTUBE_VIEW",
            "reward_type": "WALLET_BONUS",
            "reward_value": 10.0,
            "target_value": 1,
            "is_active": True
        }
        
        task_response = await client.post(
            f"{base_url}/api/tasks/admin/tasks/create",
            json=task_data,
            headers=auth_headers
        )
        
        if task_response.status_code != 200:
            logger.error(f"Task creation failed: {task_response.status_code} - {task_response.text}")
            return False
            
        task = task_response.json()
        task_id = task["id"]
        logger.info(f"✅ Test task created successfully: Task ID {task_id}")
        
        # 3. Toggle task status
        logger.info("3. Testing task toggle...")
        toggle_response = await client.post(
            f"{base_url}/api/tasks/admin/tasks/{task_id}/toggle",
            headers=auth_headers
        )
        
        if toggle_response.status_code != 200:
            logger.error(f"Task toggle failed: {toggle_response.status_code} - {toggle_response.text}")
            return False
            
        toggled_task = toggle_response.json()
        logger.info(f"✅ Task toggle successful: Task is now {toggled_task['is_active']}")
        
        # 4. Get task list
        logger.info("4. Getting task list...")
        list_response = await client.get(
            f"{base_url}/api/tasks/admin/tasks/list",
            headers=auth_headers
        )
        
        if list_response.status_code != 200:
            logger.error(f"Task list failed: {list_response.status_code} - {list_response.text}")
            return False
            
        tasks = list_response.json()
        logger.info(f"✅ Got task list: {len(tasks)} tasks found")
        
        return True

async def main():
    """Main function to run the tests"""
    logger.info("Starting authentication debugging...")
    
    # Test direct auth
    direct_auth_success = await test_direct_auth()
    
    # Test API auth
    api_auth_success = await test_api_auth()
    
    # Test tasks API access
    task_api_success = await test_api_tasks_access()
    
    # Print summary
    logger.info("\n=== Authentication Test Summary ===")
    logger.info(f"Direct Auth: {'✅ SUCCESS' if direct_auth_success else '❌ FAILED'}")
    logger.info(f"API Auth: {'✅ SUCCESS' if api_auth_success else '❌ FAILED'}")
    logger.info(f"Task API: {'✅ SUCCESS' if task_api_success else '❌ FAILED'}")
    
    if not api_auth_success and direct_auth_success:
        logger.info(
            "\nDiagnosis: The database authentication works, but the API authentication fails. "
            "This indicates a problem with the API endpoint implementation or middleware."
        )
    elif not direct_auth_success:
        logger.info(
            "\nDiagnosis: The database authentication fails. "
            "This indicates a problem with the user record or password verification."
        )
    else:
        logger.info("\nDiagnosis: Both authentication methods work.")
        
    if not task_api_success:
        logger.info(
            "\nDiagnosis: API authentication works but task API access fails. "
            "This could indicate a problem with the task API implementation or permissions."
        )
    else:
        logger.info("\nTask API access works correctly.")

if __name__ == "__main__":
    asyncio.run(main()) 