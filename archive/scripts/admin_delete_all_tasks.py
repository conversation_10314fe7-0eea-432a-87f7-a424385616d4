#!/usr/bin/env python
"""
Admin Delete All Tasks Script

This script connects to the API as an admin and deletes all tasks.
It uses the mass delete endpoint from task_router.py.
"""
import sys
import os
from pathlib import Path
import asyncio
import logging
from datetime import datetime
import json
import httpx
import time
import traceback
from typing import Dict, List, Optional, Any

# Add backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logs directory
SCRIPT_DIR = Path(__file__).parent
LOGS_DIR = SCRIPT_DIR / "logs"
LOGS_DIR.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOGS_DIR / 'admin_delete_all_tasks.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AdminTaskDeleter:
    """Connect as admin and delete all tasks"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.admin_token = None
        self.admin_headers = None
        self.client = httpx.AsyncClient(timeout=30.0, verify=False)
        
        # Admin credentials (should already exist in the system)
        self.admin_credentials = {
            "username": "admin_test",
            "password": "admin123"
        }
        
        # Stats about the operation
        self.stats = {
            "total_tasks": 0,
            "deleted_tasks": 0,
            "failed_deletes": 0,
            "errors": []
        }
    
    async def admin_login(self):
        """Login as admin"""
        try:
            # First make a GET request to get the CSRF token
            auth_url = f"{self.base_url}/auth/token"
            
            # Manual GET request to get CSRF token
            async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
                response = await client.get(auth_url)
                csrf_token = response.cookies.get("csrf_token")
                
                if csrf_token:
                    logger.info(f"Retrieved CSRF token for admin login")
                    # Now make the POST request with the CSRF token
                    headers = {
                        "Content-Type": "application/json",
                        "X-CSRF-Token": csrf_token,
                        "X-Captcha-Token": "bypass_captcha_in_test_mode"  # Test CAPTCHA token in header
                    }
                    
                    response = await client.post(
                        auth_url,
                        json={
                            "username": self.admin_credentials["username"],
                            "password": self.admin_credentials["password"]
                        },
                        headers=headers,
                        cookies={"csrf_token": csrf_token}  # Include in cookies
                    )
                else:
                    # Fallback to no CSRF token
                    logger.warning("No CSRF token found for admin login")
                    response = await self.client.post(
                        auth_url,
                        json={
                            "username": self.admin_credentials["username"],
                            "password": self.admin_credentials["password"]
                        },
                        headers={"X-Captcha-Token": "bypass_captcha_in_test_mode"}
                    )
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data["access_token"]
                self.admin_headers = {"Authorization": f"Bearer {self.admin_token}"}
                logger.info("Admin login successful")
                return True
            else:
                logger.error(f"Admin login failed: {response.status_code} {response.text}")
                return False
        except Exception as e:
            logger.error(f"Admin login error: {str(e)}")
            return False
    
    async def get_all_tasks(self):
        """Get list of all tasks"""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/tasks/admin/tasks/list",
                headers=self.admin_headers
            )
            
            if response.status_code == 200:
                tasks = response.json()
                logger.info(f"Found {len(tasks)} tasks")
                return tasks
            else:
                logger.error(f"Failed to get task list: {response.status_code} {response.text}")
                return []
        except Exception as e:
            logger.error(f"Error getting tasks: {str(e)}")
            return []
    
    async def delete_all_tasks(self, task_ids: List[int]):
        """Delete all tasks using mass delete endpoint"""
        try:
            if not task_ids:
                logger.warning("No task IDs provided for deletion")
                return True
            
            # Use the mass delete endpoint with query parameter
            task_ids_str = ",".join(str(task_id) for task_id in task_ids)
            response = await self.client.delete(
                f"{self.base_url}/api/tasks/admin/tasks?ids={task_ids_str}",
                headers=self.admin_headers
            )
            
            if response.status_code == 204:
                logger.info(f"Successfully deleted {len(task_ids)} tasks")
                self.stats["deleted_tasks"] = len(task_ids)
                return True
            else:
                logger.error(f"Failed to delete tasks: {response.status_code} {response.text}")
                self.stats["failed_deletes"] += len(task_ids)
                return False
        except Exception as e:
            logger.error(f"Error deleting tasks: {str(e)}")
            self.stats["errors"].append(str(e))
            self.stats["failed_deletes"] += len(task_ids)
            return False
    
    async def verify_deletion(self):
        """Verify all tasks were deleted"""
        tasks = await self.get_all_tasks()
        if tasks:
            logger.warning(f"There are still {len(tasks)} tasks remaining")
            return False
        else:
            logger.info("All tasks have been deleted successfully")
            return True
    
    def print_report(self):
        """Print final report"""
        logger.info("=" * 50)
        logger.info("Task Deletion Report")
        logger.info("=" * 50)
        logger.info(f"Total tasks found: {self.stats['total_tasks']}")
        logger.info(f"Tasks deleted: {self.stats['deleted_tasks']}")
        logger.info(f"Failed deletes: {self.stats['failed_deletes']}")
        
        if self.stats['errors']:
            logger.info(f"Errors encountered: {len(self.stats['errors'])}")
            for error in self.stats['errors'][:5]:  # Show only first 5 errors
                logger.info(f"  - {error}")
                
        logger.info("=" * 50)
    
    async def run(self):
        """Main function to delete all tasks"""
        try:
            logger.info("Starting task deletion process...")
            
            # Login as admin
            success = await self.admin_login()
            if not success:
                logger.error("Admin login failed, cannot continue")
                return False
            
            # Get all tasks
            tasks = await self.get_all_tasks()
            self.stats["total_tasks"] = len(tasks)
            
            if not tasks:
                logger.info("No tasks found, nothing to delete")
                return True
            
            # Extract task IDs
            task_ids = [task["id"] for task in tasks]
            
            # Delete tasks in batches of 50 to avoid request size limits
            batch_size = 50
            for i in range(0, len(task_ids), batch_size):
                batch = task_ids[i:i + batch_size]
                logger.info(f"Deleting batch of {len(batch)} tasks (IDs {batch[0]} to {batch[-1]})")
                await self.delete_all_tasks(batch)
                # Small delay between batches
                await asyncio.sleep(0.5)
            
            # Verify deletion
            await self.verify_deletion()
            
            # Print report
            self.print_report()
            
            return True
        except Exception as e:
            logger.error(f"Error in deletion process: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            await self.client.aclose()

async def main():
    """Main function"""
    # Get API base URL from environment variable or use default
    api_base_url = os.environ.get("API_BASE_URL", "http://localhost:8000")
    
    # Create task deleter
    task_deleter = AdminTaskDeleter(base_url=api_base_url)
    
    # Run deletion process
    await task_deleter.run()

if __name__ == "__main__":
    asyncio.run(main()) 