#!/usr/bin/env python3
"""
Simple Security Dashboard Generator

This script generates a basic security dashboard HTML file with security metrics.
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta
import redis
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('security_dashboard')

def generate_dashboard(redis_host='localhost', redis_port=6379, redis_password=None, output_dir='reports'):
    """Generate a basic security dashboard HTML file"""
    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Connect to Redis
        r = redis.Redis(
            host=redis_host,
            port=redis_port,
            password=redis_password,
            decode_responses=True
        )
        
        # Get security metrics from Redis
        metrics = {
            'rate_limit': {
                'active_limits': len(r.keys('rate_limit:*')),
                'blocked_ips': len(r.keys('rate_limit:blocked:*')),
            },
            'bot_detection': {
                'suspicious_ips': len(r.keys('bot_detection:suspicious:*')),
                'captcha_challenges': len(r.keys('captcha:challenge:*')),
                'required_captchas': len(r.keys('captcha:required:*')),
            },
            'timestamp': datetime.now().isoformat()
        }
        
        # Generate HTML
        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Security Dashboard</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .header {{
                    background-color: #2c3e50;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    margin-bottom: 20px;
                }}
                .summary-cards {{
                    display: flex;
                    flex-wrap: wrap;
                    gap: 20px;
                    margin-bottom: 20px;
                }}
                .card {{
                    background-color: white;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                    padding: 20px;
                    flex: 1;
                    min-width: 200px;
                }}
                .card h3 {{
                    margin-top: 0;
                    color: #2c3e50;
                }}
                .card.warning {{
                    border-left: 4px solid #f39c12;
                }}
                .card.danger {{
                    border-left: 4px solid #e74c3c;
                }}
                .card.info {{
                    border-left: 4px solid #3498db;
                }}
                .card.success {{
                    border-left: 4px solid #2ecc71;
                }}
                .metrics-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                .metrics-table th, .metrics-table td {{
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }}
                .metrics-table th {{
                    background-color: #f2f2f2;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 20px;
                    padding: 20px;
                    color: #7f8c8d;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Security Dashboard</h1>
                <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="container">
                <div class="summary-cards">
                    <div class="card warning">
                        <h3>Bot Detection</h3>
                        <p style="font-size: 24px;">{metrics['bot_detection']['suspicious_ips']}</p>
                        <p>Suspicious IPs</p>
                    </div>
                    
                    <div class="card danger">
                        <h3>Rate Limiting</h3>
                        <p style="font-size: 24px;">{metrics['rate_limit']['blocked_ips']}</p>
                        <p>Blocked IPs</p>
                    </div>
                    
                    <div class="card info">
                        <h3>CAPTCHA</h3>
                        <p style="font-size: 24px;">{metrics['bot_detection']['captcha_challenges']}</p>
                        <p>Active Challenges</p>
                    </div>
                </div>
                
                <div class="card">
                    <h3>Current Security Metrics</h3>
                    <table class="metrics-table">
                        <tr>
                            <th>Metric</th>
                            <th>Value</th>
                        </tr>
                        <tr>
                            <td>Active Rate Limits</td>
                            <td>{metrics['rate_limit']['active_limits']}</td>
                        </tr>
                        <tr>
                            <td>Blocked IPs</td>
                            <td>{metrics['rate_limit']['blocked_ips']}</td>
                        </tr>
                        <tr>
                            <td>Suspicious IPs</td>
                            <td>{metrics['bot_detection']['suspicious_ips']}</td>
                        </tr>
                        <tr>
                            <td>Active CAPTCHA Challenges</td>
                            <td>{metrics['bot_detection']['captcha_challenges']}</td>
                        </tr>
                        <tr>
                            <td>Required CAPTCHAs</td>
                            <td>{metrics['bot_detection']['required_captchas']}</td>
                        </tr>
                    </table>
                </div>
                
                <div class="card">
                    <h3>Security Recommendations</h3>
                    <ul>
                        <li>Regularly review blocked IPs for false positives</li>
                        <li>Monitor suspicious activity patterns</li>
                        <li>Update bot detection patterns as needed</li>
                        <li>Adjust rate limit thresholds based on traffic patterns</li>
                    </ul>
                </div>
                
                <div class="footer">
                    <p>Security Dashboard | Generated by Security Dashboard Generator</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        # Save dashboard
        dashboard_path = os.path.join(output_dir, 'security_dashboard.html')
        with open(dashboard_path, 'w') as f:
            f.write(html)
            
        logger.info(f"Generated security dashboard: {dashboard_path}")
        return dashboard_path
        
    except Exception as e:
        logger.error(f"Error generating dashboard: {str(e)}")
        return None

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Generate security dashboard')
    parser.add_argument('--redis-host', default='localhost', help='Redis host')
    parser.add_argument('--redis-port', type=int, default=6379, help='Redis port')
    parser.add_argument('--redis-password', help='Redis password')
    parser.add_argument('--output-dir', default='reports', help='Output directory')
    
    args = parser.parse_args()
    
    # Generate dashboard
    dashboard_path = generate_dashboard(
        redis_host=args.redis_host,
        redis_port=args.redis_port,
        redis_password=args.redis_password,
        output_dir=args.output_dir
    )
    
    if dashboard_path:
        print(f"Dashboard generated successfully: {dashboard_path}")
        print(f"Access the dashboard at: http://localhost:8000/api/security/dashboard")
        return 0
    else:
        print("Failed to generate dashboard. Check the logs for errors.")
        return 1

if __name__ == '__main__':
    sys.exit(main()) 