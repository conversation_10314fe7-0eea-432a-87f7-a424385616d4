#!/usr/bin/env python
"""
Count Tasks in Database
"""
import sys
import os
from pathlib import Path
import logging

# Add backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Import database and models
from database import init_db, get_db_context
from models import Task
from sqlalchemy import func

def main():
    """Count tasks in database"""
    try:
        # Initialize database
        init_db()
        
        # Count tasks
        with get_db_context() as db:
            task_count = db.query(func.count(Task.id)).scalar()
            print(f"Total tasks in database: {task_count}")
            
            # Get a few sample tasks to verify content
            sample_tasks = db.query(Task).limit(5).all()
            print("\nSample tasks:")
            for task in sample_tasks:
                print(f"ID: {task.id}, Name: {task.name}, Type: {task.type}, Platform URL: {task.platform_url}")
    
    except Exception as e:
        print(f"Error counting tasks: {str(e)}")

if __name__ == "__main__":
    main() 