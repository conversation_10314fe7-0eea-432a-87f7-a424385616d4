#!/usr/bin/env python3
"""
Verify and initialize card catalog data in the database.
"""
import os
import sys
import json
from datetime import datetime
from sqlalchemy import create_engine, func, select, inspect
from sqlalchemy.orm import sessionmaker, Session

# Add parent directory to path so we can import our app modules
backend_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, backend_dir)

try:
    from models import CardCatalog, UserCard, User
    from database import get_db, Base
except ImportError as e:
    print(f"Error: Could not import required modules: {e}")
    print("Make sure you're running this script from within the virtual environment.")
    sys.exit(1)

def get_session() -> Session:
    """Create a database session."""
    try:
        # Use the get_db function to get the database path and session
        db = next(get_db())
        print("Successfully connected to database")
        return db
    except Exception as e:
        print(f"Error connecting to database: {e}")
        sys.exit(1)

def check_card_tables(session: Session) -> tuple:
    """Check if card tables exist and have data."""
    inspector = inspect(engine)
    
    # Check if tables exist
    has_card_catalog_table = "card_catalog" in inspector.get_table_names()
    has_user_cards_table = "user_cards" in inspector.get_table_names()
    
    catalog_count = 0
    user_cards_count = 0
    
    if has_card_catalog_table:
        catalog_count = session.query(func.count(CardCatalog.id)).scalar()
    
    if has_user_cards_table:
        user_cards_count = session.query(func.count(UserCard.id)).scalar()
    
    return has_card_catalog_table, has_user_cards_table, catalog_count, user_cards_count

def create_example_cards(session: Session) -> list:
    """Create example cards for the catalog."""
    example_cards = [
        {
            "name": "Digital Guardian",
            "rarity": "common",
            "description": "A basic protection card that generates a small but steady income.",
            "image_url": "/img/cards/digital-guardian.jpg",
            "level_profits_json": json.dumps([0.01, 0.015, 0.02, 0.025, 0.03]),
            "level_costs_json": json.dumps([100, 150, 225, 337, 500]),
            "max_level": 5
        },
        {
            "name": "Cyber Shield",
            "rarity": "rare",
            "description": "Defends against common cyber threats while generating passive income.",
            "image_url": "/img/cards/cyber-shield.jpg",
            "level_profits_json": json.dumps([0.02, 0.03, 0.05, 0.08, 0.12]),
            "level_costs_json": json.dumps([250, 375, 562, 843, 1250]),
            "max_level": 5
        },
        {
            "name": "Data Miner",
            "rarity": "epic",
            "description": "Efficiently generates income by processing data streams.",
            "image_url": "/img/cards/data-miner.jpg",
            "level_profits_json": json.dumps([0.05, 0.08, 0.12, 0.18, 0.25]),
            "level_costs_json": json.dumps([500, 750, 1125, 1687, 2500]),
            "max_level": 5
        },
        {
            "name": "Quantum Vault",
            "rarity": "legendary",
            "description": "Implements quantum encryption to secure high-value assets and generate significant return.",
            "image_url": "/img/cards/quantum-vault.jpg",
            "level_profits_json": json.dumps([0.1, 0.15, 0.22, 0.33, 0.5]),
            "level_costs_json": json.dumps([1000, 1500, 2250, 3375, 5000]),
            "max_level": 5
        },
        {
            "name": "Blockchain Node",
            "rarity": "mythic",
            "description": "A rare blockchain node that provides exceptional returns.",
            "image_url": "/img/cards/blockchain-node.jpg",
            "level_profits_json": json.dumps([0.2, 0.3, 0.45, 0.67, 1.0]),
            "level_costs_json": json.dumps([2000, 3000, 4500, 6750, 10000]),
            "max_level": 5
        }
    ]
    
    created_cards = []
    for card_data in example_cards:
        # Check if this card already exists by name
        existing = session.execute(
            select(CardCatalog).where(CardCatalog.name == card_data["name"])
        ).scalar_one_or_none()
        
        if existing:
            print(f"Card '{card_data['name']}' already exists, skipping.")
            continue
        
        # Create new card
        card = CardCatalog(
            name=card_data["name"],
            rarity=card_data["rarity"],
            description=card_data["description"],
            image_url=card_data["image_url"],
            level_profits_json=card_data["level_profits_json"],
            level_costs_json=card_data["level_costs_json"],
            max_level=card_data["max_level"],
            created_at=datetime.utcnow()
        )
        session.add(card)
        created_cards.append(card)
    
    # Commit all new cards
    if created_cards:
        try:
            session.commit()
            print(f"Created {len(created_cards)} new cards.")
        except Exception as e:
            session.rollback()
            print(f"Error creating cards: {e}")
    
    return created_cards

def print_card_catalog(session: Session):
    """Print out the current card catalog."""
    cards = session.execute(select(CardCatalog)).scalars().all()
    
    if not cards:
        print("No cards found in the catalog.")
        return
    
    print("\n=== Current Card Catalog ===")
    print(f"Total Cards: {len(cards)}")
    print("-" * 50)
    
    for card in cards:
        print(f"ID: {card.id} | Name: {card.name} | Rarity: {card.rarity}")
        print(f"Max Level: {card.max_level}")
        try:
            level_profits = json.loads(card.level_profits_json)
            level_costs = json.loads(card.level_costs_json)
            print(f"Level 1 Profit: {level_profits[0]} | Level 1 Cost: {level_costs[0]}")
        except (json.JSONDecodeError, IndexError, TypeError) as e:
            print(f"Error parsing card data: {e}")
        print("-" * 50)

def main():
    """Main function to verify and initialize card database."""
    print("=== Card Database Verification Tool ===")
    
    session = get_session()
    try:
        # Check tables
        has_catalog, has_user_cards, catalog_count, user_cards_count = check_card_tables(session)
        
        print("\n=== Database Status ===")
        print(f"CardCatalog table exists: {has_catalog}")
        print(f"UserCard table exists: {has_user_cards}")
        print(f"Number of cards in catalog: {catalog_count}")
        print(f"Number of user cards: {user_cards_count}")
        
        # Create example cards if needed
        if catalog_count == 0:
            print("\nNo cards found in catalog. Creating example cards...")
            create_example_cards(session)
        
        # Print current catalog
        print_card_catalog(session)
        
    except Exception as e:
        print(f"An error occurred: {e}")
    finally:
        session.close()

if __name__ == "__main__":
    main() 