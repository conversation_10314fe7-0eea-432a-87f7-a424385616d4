#!/bin/bash
# Setup script for file descriptor monitoring

set -e

# Define colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Setting up file descriptor monitoring tools...${NC}"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
  echo -e "${RED}Please run as root${NC}"
  exit 1
fi

# Install dependencies
echo -e "${YELLOW}Installing dependencies...${NC}"
apt-get update
apt-get install -y python3-pip python3-psutil lsof

# Install Python dependencies
echo -e "${YELLOW}Installing Python dependencies...${NC}"
pip3 install psutil typing_extensions

# Create directories for logs
echo -e "${YELLOW}Creating log directories...${NC}"
mkdir -p /var/log/fd_monitor

# Make scripts executable
echo -e "${YELLOW}Making scripts executable...${NC}"
chmod +x /opt/atlasvpn/backend/scripts/cleanup_connections.py
chmod +x /opt/atlasvpn/backend/scripts/monitor_connections.py

# Install systemd service
echo -e "${YELLOW}Installing systemd service...${NC}"
cp /opt/atlasvpn/backend/scripts/fd-monitor.service /etc/systemd/system/
systemctl daemon-reload
systemctl enable fd-monitor.service

# Set up cron job for the cleanup script
echo -e "${YELLOW}Setting up cron job for cleanup script...${NC}"
(crontab -l 2>/dev/null || echo "") | grep -v "cleanup_connections.py" | { cat; echo "0 */2 * * * /opt/atlasvpn/backend/scripts/cleanup_connections.py --close-idle --reset-db >> /var/log/fd_cleanup.log 2>&1"; } | crontab -

# Increase system file descriptor limits
echo -e "${YELLOW}Increasing system file descriptor limits...${NC}"

# Check if limits are already set
if grep -q "fs.file-max" /etc/sysctl.conf; then
  echo -e "${GREEN}System file descriptor limits already configured in sysctl.conf${NC}"
else
  echo "fs.file-max = 2097152" >> /etc/sysctl.conf
  sysctl -p
fi

# Set limits in limits.conf
if grep -q "nofile" /etc/security/limits.conf; then
  echo -e "${GREEN}User file descriptor limits already configured in limits.conf${NC}"
else
  cat >> /etc/security/limits.conf << EOF
*               soft    nofile          65535
*               hard    nofile          65535
root            soft    nofile          65535
root            hard    nofile          65535
EOF
fi

# Update pam limits
if ! grep -q "pam_limits.so" /etc/pam.d/common-session; then
  echo "session required pam_limits.so" >> /etc/pam.d/common-session
fi

# Increase limits for systemd services
mkdir -p /etc/systemd/system.conf.d/
cat > /etc/systemd/system.conf.d/limits.conf << EOF
[Manager]
DefaultLimitNOFILE=65535
EOF

# Configure the atlasvpn service to use these limits
mkdir -p /etc/systemd/system/atlasvpn.service.d/
cat > /etc/systemd/system/atlasvpn.service.d/limits.conf << EOF
[Service]
LimitNOFILE=65535
EOF

systemctl daemon-reload

# Configure ulimit in the application startup scripts
echo -e "${YELLOW}Configuring application startup scripts...${NC}"

# Get the application start script
APP_START_SCRIPT=$(find /opt/atlasvpn -name "start.sh" -o -name "run.sh" | head -n 1)

if [ -n "$APP_START_SCRIPT" ]; then
  if ! grep -q "ulimit -n" "$APP_START_SCRIPT"; then
    # Add ulimit to the script
    sed -i '1s/^/# Set file descriptor limits\nulimit -n 65535\n\n/' "$APP_START_SCRIPT"
    echo -e "${GREEN}Added ulimit command to $APP_START_SCRIPT${NC}"
  else
    echo -e "${GREEN}ulimit already configured in $APP_START_SCRIPT${NC}"
  fi
else
  echo -e "${YELLOW}Could not find application startup script. You may need to add 'ulimit -n 65535' manually.${NC}"
fi

# Start the monitoring service
echo -e "${YELLOW}Starting the monitoring service...${NC}"
systemctl start fd-monitor.service

# Display status
echo -e "${YELLOW}Checking service status...${NC}"
systemctl status fd-monitor.service

echo -e "\n${GREEN}Setup completed successfully!${NC}"
echo -e "${YELLOW}Current file descriptor limits:${NC}"
ulimit -n
echo -e "${YELLOW}Current system file-max:${NC}"
cat /proc/sys/fs/file-max

echo -e "\n${GREEN}Next steps:${NC}"
echo -e "1. Restart your application: ${YELLOW}systemctl restart atlasvpn${NC}"
echo -e "2. Monitor file descriptors: ${YELLOW}/opt/atlasvpn/backend/scripts/monitor_connections.py${NC}"
echo -e "3. View logs: ${YELLOW}tail -f /var/log/fd_monitor.log${NC}" 