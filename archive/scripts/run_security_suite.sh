#!/bin/bash

# Comprehensive Security Test Suite Runner
# This script runs all security tests and generates a comprehensive report

# Set default values
API_BASE_URL=${API_BASE_URL:-"http://localhost:8000"}
TEST_MODE=${TEST_MODE:-"true"}
SCRIPT_DIR=$(dirname "$0")
SECURITY_TESTS_DIR="$SCRIPT_DIR/../security_tests"
REPORTS_DIR="$SECURITY_TESTS_DIR/reports"
LOGS_DIR="$SECURITY_TESTS_DIR/logs"
DETAILED_OUTPUT=false
GENERATE_REPORT=true
SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL:-""}
EMAIL_RECIPIENT=${EMAIL_RECIPIENT:-""}

# Display help message
usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  --url <url>          Set API base URL (default: http://localhost:8000)"
    echo "  --test-mode <bool>   Set test mode (true/false, default: true)"
    echo "  --detailed           Show detailed output"
    echo "  --no-report          Skip report generation"
    echo "  --slack <webhook>    Send results to Slack webhook"
    echo "  --email <address>    Send results to email address"
    echo "  --help               Display this help message"
    echo
    echo "Examples:"
    echo "  $0 --url https://api.example.com --test-mode false"
    echo "  $0 --detailed --slack https://hooks.slack.com/services/XXX/YYY/ZZZ"
    echo
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --url)
                API_BASE_URL="$2"
                shift 2
                ;;
            --test-mode)
                TEST_MODE="$2"
                shift 2
                ;;
            --detailed)
                DETAILED_OUTPUT=true
                shift
                ;;
            --no-report)
                GENERATE_REPORT=false
                shift
                ;;
            --slack)
                SLACK_WEBHOOK_URL="$2"
                shift 2
                ;;
            --email)
                EMAIL_RECIPIENT="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
}

# Create directories
create_dirs() {
    mkdir -p "$REPORTS_DIR"
    mkdir -p "$LOGS_DIR"
    echo "Reports will be saved to $REPORTS_DIR"
    echo "Logs will be saved to $LOGS_DIR"
}

# Check if security tests directory exists
check_security_tests_dir() {
    if [ ! -d "$SECURITY_TESTS_DIR" ]; then
        echo "Error: Security tests directory not found at $SECURITY_TESTS_DIR"
        exit 1
    fi
}

# Run security headers test
run_headers_test() {
    echo "----------------------------------------------"
    echo "Running Security Headers Test"
    echo "----------------------------------------------"
    
    # Set environment variables
    export API_BASE_URL=$API_BASE_URL
    export TEST_MODE=$TEST_MODE
    
    # Run the test
    timestamp=$(date +"%Y%m%d_%H%M%S")
    log_file="$LOGS_DIR/headers_test_${timestamp}.log"
    
    if [ "$DETAILED_OUTPUT" = true ]; then
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --headers --url "$API_BASE_URL" --test-mode "$TEST_MODE" --detailed 2>&1 | tee "$log_file"
    else
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --headers --url "$API_BASE_URL" --test-mode "$TEST_MODE" > "$log_file" 2>&1
        
        # Check exit status
        status=$?
        if [ $status -eq 0 ]; then
            echo "✅ Security Headers Test passed"
        else
            echo "❌ Security Headers Test failed"
            echo "Last 5 lines of log:"
            tail -n 5 "$log_file"
        fi
    fi
    
    return ${PIPESTATUS[0]}
}

# Run rate limit test
run_rate_limit_test() {
    echo "----------------------------------------------"
    echo "Running Rate Limit Test"
    echo "----------------------------------------------"
    
    # Set environment variables
    export API_BASE_URL=$API_BASE_URL
    export TEST_MODE=$TEST_MODE
    
    # Run the test
    timestamp=$(date +"%Y%m%d_%H%M%S")
    log_file="$LOGS_DIR/rate_limit_test_${timestamp}.log"
    
    if [ "$DETAILED_OUTPUT" = true ]; then
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --rate-limit --url "$API_BASE_URL" --test-mode "$TEST_MODE" --detailed 2>&1 | tee "$log_file"
    else
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --rate-limit --url "$API_BASE_URL" --test-mode "$TEST_MODE" > "$log_file" 2>&1
        
        # Check exit status
        status=$?
        if [ $status -eq 0 ]; then
            echo "✅ Rate Limit Test passed"
        else
            echo "❌ Rate Limit Test failed"
            echo "Last 5 lines of log:"
            tail -n 5 "$log_file"
        fi
    fi
    
    return ${PIPESTATUS[0]}
}

# Run CAPTCHA test
run_captcha_test() {
    echo "----------------------------------------------"
    echo "Running CAPTCHA Test"
    echo "----------------------------------------------"
    
    # Set environment variables
    export API_BASE_URL=$API_BASE_URL
    export TEST_MODE=$TEST_MODE
    export ENABLE_CLOUDFLARE_CAPTCHA=true
    
    # Run the test
    timestamp=$(date +"%Y%m%d_%H%M%S")
    log_file="$LOGS_DIR/captcha_test_${timestamp}.log"
    
    if [ "$DETAILED_OUTPUT" = true ]; then
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --captcha --cloudflare --url "$API_BASE_URL" --test-mode "$TEST_MODE" --detailed 2>&1 | tee "$log_file"
    else
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --captcha --cloudflare --url "$API_BASE_URL" --test-mode "$TEST_MODE" > "$log_file" 2>&1
        
        # Check exit status
        status=$?
        if [ $status -eq 0 ]; then
            echo "✅ CAPTCHA Test passed"
        else
            echo "❌ CAPTCHA Test failed"
            echo "Last 5 lines of log:"
            tail -n 5 "$log_file"
        fi
    fi
    
    return ${PIPESTATUS[0]}
}

# Run bot detection test
run_bot_detection_test() {
    echo "----------------------------------------------"
    echo "Running Bot Detection Test"
    echo "----------------------------------------------"
    
    # Set environment variables
    export API_BASE_URL=$API_BASE_URL
    export TEST_MODE=$TEST_MODE
    
    # Run the test
    timestamp=$(date +"%Y%m%d_%H%M%S")
    log_file="$LOGS_DIR/bot_detection_test_${timestamp}.log"
    
    if [ "$DETAILED_OUTPUT" = true ]; then
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --bot-detection --url "$API_BASE_URL" --test-mode "$TEST_MODE" --detailed 2>&1 | tee "$log_file"
    else
        "$SECURITY_TESTS_DIR/run_security_tests.sh" --bot-detection --url "$API_BASE_URL" --test-mode "$TEST_MODE" > "$log_file" 2>&1
        
        # Check exit status
        status=$?
        if [ $status -eq 0 ]; then
            echo "✅ Bot Detection Test passed"
        else
            echo "❌ Bot Detection Test failed"
            echo "Last 5 lines of log:"
            tail -n 5 "$log_file"
        fi
    fi
    
    return ${PIPESTATUS[0]}
}

# Generate comprehensive report
generate_report() {
    if [ "$GENERATE_REPORT" = false ]; then
        return 0
    fi
    
    echo "----------------------------------------------"
    echo "Generating Comprehensive Security Report"
    echo "----------------------------------------------"
    
    timestamp=$(date +"%Y%m%d_%H%M%S")
    report_file="$REPORTS_DIR/security_report_${timestamp}.html"
    
    # Run the report generator
    python3 "$SECURITY_TESTS_DIR/run_security_tests.py" --output "$report_file"
    
    echo "Report generated: $report_file"
    return 0
}

# Send report to Slack
send_to_slack() {
    if [ -z "$SLACK_WEBHOOK_URL" ]; then
        return 0
    fi
    
    echo "----------------------------------------------"
    echo "Sending Report to Slack"
    echo "----------------------------------------------"
    
    # Create a simple message
    message="Security Test Results for $API_BASE_URL\n"
    message+="Headers Test: $([ $headers_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')\n"
    message+="Rate Limit Test: $([ $rate_limit_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')\n"
    message+="CAPTCHA Test: $([ $captcha_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')\n"
    message+="Bot Detection Test: $([ $bot_detection_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')\n"
    message+="Overall Status: $([ $overall_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')\n"
    
    # Send to Slack
    curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$message\"}" "$SLACK_WEBHOOK_URL"
    
    echo "Report sent to Slack"
    return 0
}

# Send report by email
send_by_email() {
    if [ -z "$EMAIL_RECIPIENT" ]; then
        return 0
    fi
    
    echo "----------------------------------------------"
    echo "Sending Report by Email"
    echo "----------------------------------------------"
    
    # Create a simple message
    subject="Security Test Results for $API_BASE_URL"
    body="Security Test Results for $API_BASE_URL\n\n"
    body+="Headers Test: $([ $headers_status -eq 0 ] && echo 'PASSED' || echo 'FAILED')\n"
    body+="Rate Limit Test: $([ $rate_limit_status -eq 0 ] && echo 'PASSED' || echo 'FAILED')\n"
    body+="CAPTCHA Test: $([ $captcha_status -eq 0 ] && echo 'PASSED' || echo 'FAILED')\n"
    body+="Bot Detection Test: $([ $bot_detection_status -eq 0 ] && echo 'PASSED' || echo 'FAILED')\n"
    body+="Overall Status: $([ $overall_status -eq 0 ] && echo 'PASSED' || echo 'FAILED')\n"
    
    # Send email
    echo -e "$body" | mail -s "$subject" "$EMAIL_RECIPIENT"
    
    echo "Report sent to $EMAIL_RECIPIENT"
    return 0
}

# Main function
main() {
    # Parse arguments
    parse_args "$@"
    
    # Check security tests directory
    check_security_tests_dir
    
    # Create directories
    create_dirs
    
    # Tracking overall status
    overall_status=0
    
    # Run security headers test
    run_headers_test
    headers_status=$?
    [ $headers_status -ne 0 ] && overall_status=1
    
    # Run rate limit test
    run_rate_limit_test
    rate_limit_status=$?
    [ $rate_limit_status -ne 0 ] && overall_status=1
    
    # Run CAPTCHA test
    run_captcha_test
    captcha_status=$?
    [ $captcha_status -ne 0 ] && overall_status=1
    
    # Run bot detection test
    run_bot_detection_test
    bot_detection_status=$?
    [ $bot_detection_status -ne 0 ] && overall_status=1
    
    # Generate comprehensive report
    generate_report
    
    # Send report to Slack
    send_to_slack
    
    # Send report by email
    send_by_email
    
    # Display summary
    echo "----------------------------------------------"
    echo "Security Test Summary:"
    echo "----------------------------------------------"
    echo "Headers Test:        $([ $headers_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
    echo "Rate Limit Test:     $([ $rate_limit_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
    echo "CAPTCHA Test:        $([ $captcha_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
    echo "Bot Detection Test:  $([ $bot_detection_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
    echo "----------------------------------------------"
    echo "Overall Status:      $([ $overall_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
    echo "----------------------------------------------"
    
    return $overall_status
}

# Run main function
main "$@"
exit $? 