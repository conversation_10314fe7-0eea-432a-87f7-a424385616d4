2025-04-10 20:51:35,886 - __main__ - INFO - Backup bot started
2025-04-10 20:51:36,207 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 20:51:36,229 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 20:51:36,229 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 20:51:36,229 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 20:51:36,272 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 20:51:36,274 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 20:56:36,404 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 20:56:36,404 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 20:56:36,405 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 20:56:36,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 20:56:36,531 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:01:36,685 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:01:36,686 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:01:36,686 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:01:36,791 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:01:36,794 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:06:36,909 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:06:36,909 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:06:36,910 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:06:37,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:06:37,023 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:11:37,148 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:11:37,148 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:11:37,148 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:11:37,307 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:11:37,309 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:16:37,436 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:16:37,436 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:16:37,437 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:16:37,585 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:16:37,588 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:21:37,716 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:21:37,717 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:21:37,717 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:21:37,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:21:37,994 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:26:38,081 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:26:38,082 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:26:38,082 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:26:38,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:26:38,196 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:31:38,323 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:31:38,324 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:31:38,324 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:31:38,457 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:31:38,466 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:36:38,569 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:36:38,570 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:36:38,570 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:36:38,668 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:36:38,671 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:41:38,776 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:41:38,776 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:41:38,776 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:41:38,871 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:41:38,875 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:46:39,003 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:46:39,004 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:46:39,004 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:46:39,106 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:46:39,108 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:51:39,291 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:51:39,291 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:51:39,292 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:51:39,469 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:51:39,472 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 21:56:39,557 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 21:56:39,558 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 21:56:39,558 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 21:56:39,667 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 21:56:39,670 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:01:39,779 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:01:39,780 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:01:39,780 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:01:39,899 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:01:39,903 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:06:40,032 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:06:40,033 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:06:40,033 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:06:40,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:06:40,127 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:11:40,244 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:11:40,244 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:11:40,244 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:11:40,340 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:11:40,343 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:16:40,469 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:16:40,470 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:16:40,470 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:16:40,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:16:40,583 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:21:40,701 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:21:40,702 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:21:40,702 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:21:40,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:21:40,859 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:26:40,977 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:26:40,978 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:26:40,978 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:26:41,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:26:41,223 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:31:41,314 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:31:41,314 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:31:41,314 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:31:41,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:31:41,416 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:36:41,534 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:36:41,534 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:36:41,534 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:36:41,622 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:36:41,626 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:41:41,747 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:41:41,747 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:41:41,747 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:41:41,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:41:41,852 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:46:41,983 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:46:41,983 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:46:41,984 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:46:42,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:46:42,080 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:51:42,208 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:51:42,208 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:51:42,209 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:51:42,306 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:51:42,308 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 22:56:42,420 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 22:56:42,421 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 22:56:42,421 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 22:56:42,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 22:56:42,539 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:01:42,661 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:01:42,662 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:01:42,662 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:01:42,741 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:01:42,743 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:06:42,871 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:06:42,871 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:06:42,871 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:06:42,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:06:42,974 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:11:43,090 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:11:43,091 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:11:43,091 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:11:43,185 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:11:43,188 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:16:43,214 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:16:43,214 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:16:43,214 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:16:43,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:16:43,297 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:21:43,423 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:21:43,424 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:21:43,424 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:21:43,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:21:43,515 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:26:43,608 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:26:43,608 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:26:43,608 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:26:43,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:26:43,694 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:31:43,820 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:31:43,821 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:31:43,821 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:31:43,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:31:43,901 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:36:44,027 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:36:44,027 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:36:44,028 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:36:44,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:36:44,151 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:41:44,277 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:41:44,278 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:41:44,278 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:41:44,368 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:41:44,371 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:46:44,484 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:46:44,484 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:46:44,484 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:46:44,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:46:44,564 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:51:44,690 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:51:44,690 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:51:44,690 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:51:44,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:51:44,781 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-10 23:56:44,878 - __main__ - ERROR - No backup files found after running backup script
2025-04-10 23:56:44,878 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-10 23:56:44,879 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-10 23:56:44,957 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-10 23:56:44,960 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-11 00:01:45,089 - __main__ - ERROR - No backup files found after running backup script
2025-04-11 00:01:45,089 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-11 00:01:45,089 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-11 00:01:45,167 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-11 00:01:45,169 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-11 00:06:45,292 - __main__ - ERROR - No backup files found after running backup script
2025-04-11 00:06:45,292 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-11 00:06:45,292 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-11 00:06:45,367 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-11 00:06:45,370 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-11 00:11:45,549 - __main__ - ERROR - No backup files found after running backup script
2025-04-11 00:11:45,550 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-11 00:11:45,550 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-11 00:11:45,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-11 00:11:45,652 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-11 00:16:45,771 - __main__ - ERROR - No backup files found after running backup script
2025-04-11 00:16:45,771 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-11 00:16:45,771 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-11 00:16:45,850 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-11 00:16:45,852 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-12 01:11:28,700 - __main__ - INFO - Backup bot started
2025-04-12 01:11:28,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-12 01:11:29,014 - __main__ - ERROR - No backup files found after running backup script
2025-04-12 01:11:29,014 - __main__ - ERROR - Error creating backup: No backup files found after running backup script
2025-04-12 01:11:29,014 - __main__ - ERROR - ❌ Error during backup process: No backup files found after running backup script
2025-04-12 01:11:29,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-12 01:11:29,056 - __main__ - INFO - Waiting 5 minutes before retrying...
2025-04-12 01:14:49,782 - __main__ - INFO - Backup bot started
2025-04-12 01:14:50,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-12 01:14:52,349 - __main__ - INFO - Backup created successfully: backups/project_backup_20250412_011450.zip
2025-04-12 01:14:52,390 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-12 01:14:52,709 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-12 01:14:52,711 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250412_011450.zip
2025-04-12 01:14:52,712 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250412_011450.zip
2025-04-12 01:14:52,712 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-16 22:36:34,802 - __main__ - INFO - Backup bot started
2025-04-16 22:36:35,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-16 22:36:37,111 - __main__ - INFO - Backup created successfully: backups/project_backup_20250416_223635.zip
2025-04-16 22:36:37,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-16 22:36:37,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-16 22:36:37,547 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250416_223635.zip
2025-04-16 22:36:37,548 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250416_223635.zip
2025-04-16 22:36:37,549 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-17 00:02:24,692 - __main__ - INFO - Backup bot started
2025-04-17 00:02:24,957 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-17 00:02:26,966 - __main__ - INFO - Backup created successfully: backups/project_backup_20250417_000224.zip
2025-04-17 00:02:27,004 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-17 00:02:27,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-17 00:02:27,385 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250417_000224.zip
2025-04-17 00:02:27,386 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250417_000224.zip
2025-04-17 00:02:27,386 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-18 01:26:54,473 - __main__ - INFO - Backup bot started
2025-04-18 01:26:54,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-18 01:26:57,041 - __main__ - INFO - Backup created successfully: backups/project_backup_20250418_012654.zip
2025-04-18 01:26:57,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-18 01:26:57,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-18 01:26:57,406 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250418_012654.zip
2025-04-18 01:26:57,407 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250418_012654.zip
2025-04-18 01:26:57,407 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-18 02:26:59,670 - __main__ - INFO - Backup created successfully: backups/project_backup_20250418_022657.zip
2025-04-18 02:26:59,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-18 02:27:00,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-18 02:27:00,079 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250418_022657.zip
2025-04-18 02:27:00,081 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250418_022657.zip
2025-04-18 02:27:00,081 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-19 02:33:45,833 - __main__ - INFO - Backup bot started
2025-04-19 02:33:46,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-19 02:33:48,222 - __main__ - INFO - Backup created successfully: backups/project_backup_20250419_023346.zip
2025-04-19 02:33:48,259 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-19 02:33:48,559 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-19 02:33:48,562 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250419_023346.zip
2025-04-19 02:33:48,563 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250419_023346.zip
2025-04-19 02:33:48,563 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-19 03:33:50,705 - __main__ - INFO - Backup created successfully: backups/project_backup_20250419_033348.zip
2025-04-19 03:33:50,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-19 03:33:51,103 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-19 03:33:51,105 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250419_033348.zip
2025-04-19 03:33:51,107 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250419_033348.zip
2025-04-19 03:33:51,107 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-19 04:33:53,225 - __main__ - INFO - Backup created successfully: backups/project_backup_20250419_043351.zip
2025-04-19 04:33:53,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-19 04:33:53,732 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-19 04:33:53,734 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250419_043351.zip
2025-04-19 04:33:53,735 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250419_043351.zip
2025-04-19 04:33:53,736 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-19 05:33:55,928 - __main__ - INFO - Backup created successfully: backups/project_backup_20250419_053353.zip
2025-04-19 05:33:56,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-19 05:33:56,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-19 05:33:56,546 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250419_053353.zip
2025-04-19 05:33:56,547 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250419_053353.zip
2025-04-19 05:33:56,547 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-20 00:17:33,414 - __main__ - INFO - Backup bot started
2025-04-20 00:17:33,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 00:17:36,354 - __main__ - INFO - Backup created successfully: backups/project_backup_20250420_001733.zip
2025-04-20 00:17:36,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 00:17:36,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-20 00:17:36,739 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250420_001733.zip
2025-04-20 00:17:36,741 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250420_001733.zip
2025-04-20 00:17:36,741 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-20 01:17:39,198 - __main__ - INFO - Backup created successfully: backups/project_backup_20250420_011736.zip
2025-04-20 01:17:39,318 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 01:17:39,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-20 01:17:39,687 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250420_011736.zip
2025-04-20 01:17:39,688 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250420_011736.zip
2025-04-20 01:17:39,688 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-20 02:17:41,957 - __main__ - INFO - Backup created successfully: backups/project_backup_20250420_021739.zip
2025-04-20 02:17:42,065 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 02:17:42,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-20 02:17:42,362 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250420_021739.zip
2025-04-20 02:17:42,364 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250420_021739.zip
2025-04-20 02:17:42,364 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-20 03:17:44,547 - __main__ - INFO - Backup created successfully: backups/project_backup_20250420_031742.zip
2025-04-20 03:17:44,645 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 03:17:44,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-20 03:17:44,973 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250420_031742.zip
2025-04-20 03:17:44,975 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250420_031742.zip
2025-04-20 03:17:44,976 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-20 04:17:47,026 - __main__ - INFO - Backup created successfully: backups/project_backup_20250420_041745.zip
2025-04-20 04:17:47,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 04:17:47,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-20 04:17:47,606 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250420_041745.zip
2025-04-20 04:17:47,608 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250420_041745.zip
2025-04-20 04:17:47,608 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-20 05:17:49,787 - __main__ - INFO - Backup created successfully: backups/project_backup_20250420_051747.zip
2025-04-20 05:17:49,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 05:17:50,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-20 05:17:50,327 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250420_051747.zip
2025-04-20 05:17:50,329 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250420_051747.zip
2025-04-20 05:17:50,329 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-20 06:16:26,348 - __main__ - INFO - Backup bot started
2025-04-20 06:16:26,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 06:16:28,868 - __main__ - INFO - Backup created successfully: backups/project_backup_20250420_061626.zip
2025-04-20 06:16:28,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-20 06:16:29,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-20 06:16:29,274 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250420_061626.zip
2025-04-20 06:16:29,275 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250420_061626.zip
2025-04-20 06:16:29,276 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-21 03:20:45,006 - __main__ - INFO - Backup bot started
2025-04-21 03:20:45,268 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 03:20:48,765 - __main__ - INFO - Backup created successfully: backups/project_backup_20250421_032045.zip
2025-04-21 03:20:48,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 03:20:49,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-21 03:20:49,226 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250421_032045.zip
2025-04-21 03:20:49,227 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250421_032045.zip
2025-04-21 03:20:49,227 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-21 04:20:51,358 - __main__ - INFO - Backup created successfully: backups/project_backup_20250421_042049.zip
2025-04-21 04:20:51,456 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 04:20:51,759 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-21 04:20:51,760 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250421_042049.zip
2025-04-21 04:20:51,761 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250421_042049.zip
2025-04-21 04:20:51,762 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-21 05:20:53,883 - __main__ - INFO - Backup created successfully: backups/project_backup_20250421_052051.zip
2025-04-21 05:20:53,992 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 05:20:54,308 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-21 05:20:54,310 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250421_052051.zip
2025-04-21 05:20:54,311 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250421_052051.zip
2025-04-21 05:20:54,311 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-21 06:20:56,461 - __main__ - INFO - Backup created successfully: backups/project_backup_20250421_062054.zip
2025-04-21 06:20:56,570 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 06:20:56,937 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-21 06:20:56,939 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250421_062054.zip
2025-04-21 06:20:56,940 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250421_062054.zip
2025-04-21 06:20:56,940 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-21 17:49:12,432 - __main__ - INFO - Backup bot started
2025-04-21 17:49:12,719 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 17:49:14,996 - __main__ - INFO - Backup created successfully: backups/project_backup_20250421_174912.zip
2025-04-21 17:49:15,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 17:49:15,460 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-21 17:49:15,462 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250421_174912.zip
2025-04-21 17:49:15,463 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250421_174912.zip
2025-04-21 17:49:15,463 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-21 18:49:17,791 - __main__ - INFO - Backup created successfully: backups/project_backup_20250421_184915.zip
2025-04-21 18:49:17,908 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-21 18:49:18,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-21 18:49:18,322 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250421_184915.zip
2025-04-21 18:49:18,323 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250421_184915.zip
2025-04-21 18:49:18,324 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-25 01:53:39,044 - __main__ - INFO - Backup bot started
2025-04-25 01:53:39,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-25 01:53:41,380 - __main__ - INFO - Backup created successfully: backups/project_backup_20250425_015339.zip
2025-04-25 01:53:41,415 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-25 01:53:41,737 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-25 01:53:41,739 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250425_015339.zip
2025-04-25 01:53:41,740 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250425_015339.zip
2025-04-25 01:53:41,740 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-25 02:53:44,190 - __main__ - INFO - Backup created successfully: backups/project_backup_20250425_025341.zip
2025-04-25 02:53:44,289 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-25 02:53:44,664 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-25 02:53:44,666 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250425_025341.zip
2025-04-25 02:53:44,669 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250425_025341.zip
2025-04-25 02:53:44,669 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-25 03:53:46,858 - __main__ - INFO - Backup created successfully: backups/project_backup_20250425_035344.zip
2025-04-25 03:53:46,990 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-25 03:53:47,314 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-25 03:53:47,317 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250425_035344.zip
2025-04-25 03:53:47,318 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250425_035344.zip
2025-04-25 03:53:47,318 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-25 04:53:49,528 - __main__ - INFO - Backup created successfully: backups/project_backup_20250425_045347.zip
2025-04-25 04:53:49,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-25 04:53:49,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-25 04:53:49,946 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250425_045347.zip
2025-04-25 04:53:49,947 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250425_045347.zip
2025-04-25 04:53:49,947 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-25 05:17:17,352 - __main__ - INFO - Backup bot started
2025-04-25 05:17:17,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-25 05:17:19,834 - __main__ - INFO - Backup created successfully: backups/project_backup_20250425_051717.zip
2025-04-25 05:17:19,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-25 05:17:20,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-25 05:17:20,221 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250425_051717.zip
2025-04-25 05:17:20,223 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250425_051717.zip
2025-04-25 05:17:20,223 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-27 00:50:21,646 - __main__ - INFO - Backup bot started
2025-04-27 00:50:21,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-27 00:50:24,129 - __main__ - INFO - Backup created successfully: backups/project_backup_20250427_005021.zip
2025-04-27 00:50:24,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-27 00:50:24,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-27 00:50:24,559 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250427_005021.zip
2025-04-27 00:50:24,561 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250427_005021.zip
2025-04-27 00:50:24,561 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-27 01:50:26,821 - __main__ - INFO - Backup created successfully: backups/project_backup_20250427_015024.zip
2025-04-27 01:50:26,926 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-27 01:50:27,420 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-27 01:50:27,422 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250427_015024.zip
2025-04-27 01:50:27,424 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250427_015024.zip
2025-04-27 01:50:27,424 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-27 02:50:29,765 - __main__ - INFO - Backup created successfully: backups/project_backup_20250427_025027.zip
2025-04-27 02:50:29,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-27 02:50:30,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-27 02:50:30,241 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250427_025027.zip
2025-04-27 02:50:30,244 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250427_025027.zip
2025-04-27 02:50:30,244 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-27 03:50:32,490 - __main__ - INFO - Backup created successfully: backups/project_backup_20250427_035030.zip
2025-04-27 03:50:32,590 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-27 03:50:32,921 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-27 03:50:32,923 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250427_035030.zip
2025-04-27 03:50:32,924 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250427_035030.zip
2025-04-27 03:50:32,924 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-28 01:04:46,297 - __main__ - INFO - Backup bot started
2025-04-28 01:04:46,622 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-28 01:04:48,857 - __main__ - INFO - Backup created successfully: backups/project_backup_20250428_010446.zip
2025-04-28 01:04:48,889 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-28 01:04:49,337 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-28 01:04:49,338 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250428_010446.zip
2025-04-28 01:04:49,340 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250428_010446.zip
2025-04-28 01:04:49,340 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-28 02:04:51,959 - __main__ - INFO - Backup created successfully: backups/project_backup_20250428_020449.zip
2025-04-28 02:04:52,058 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-28 02:04:52,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-28 02:04:52,394 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250428_020449.zip
2025-04-28 02:04:52,396 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250428_020449.zip
2025-04-28 02:04:52,397 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-28 03:04:55,058 - __main__ - INFO - Backup created successfully: backups/project_backup_20250428_030452.zip
2025-04-28 03:04:55,169 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-28 03:04:55,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-28 03:04:55,566 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250428_030452.zip
2025-04-28 03:04:55,568 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250428_030452.zip
2025-04-28 03:04:55,568 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-04-29 03:50:00,242 - __main__ - INFO - Backup bot started
2025-04-29 03:50:00,592 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-29 03:50:03,848 - __main__ - INFO - Backup created successfully: backups/project_backup_20250429_035000.zip
2025-04-29 03:50:03,896 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-04-29 03:50:04,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-04-29 03:50:04,658 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250429_035000.zip
2025-04-29 03:50:04,660 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250429_035000.zip
2025-04-29 03:50:04,660 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-01 01:59:35,149 - __main__ - INFO - Backup bot started
2025-05-01 01:59:35,426 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-01 01:59:38,688 - __main__ - INFO - Backup created successfully: backups/project_backup_20250501_015935.zip
2025-05-01 01:59:38,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-01 01:59:39,197 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-01 01:59:39,199 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250501_015935.zip
2025-05-01 01:59:39,201 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250501_015935.zip
2025-05-01 01:59:39,202 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-01 22:12:22,579 - __main__ - INFO - Backup bot started
2025-05-01 22:12:22,936 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-01 22:12:26,247 - __main__ - INFO - Backup created successfully: backups/project_backup_20250501_221222.zip
2025-05-01 22:12:26,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-01 22:12:26,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-01 22:12:26,915 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250501_221222.zip
2025-05-01 22:12:26,917 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250501_221222.zip
2025-05-01 22:12:26,917 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-01 23:12:29,860 - __main__ - INFO - Backup created successfully: backups/project_backup_20250501_231226.zip
2025-05-01 23:12:29,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-01 23:12:30,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-01 23:12:30,497 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250501_231226.zip
2025-05-01 23:12:30,499 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250501_231226.zip
2025-05-01 23:12:30,499 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-02 00:12:33,551 - __main__ - INFO - Backup created successfully: backups/project_backup_20250502_001230.zip
2025-05-02 00:12:33,707 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-02 00:12:34,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-02 00:12:34,153 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250502_001230.zip
2025-05-02 00:12:34,155 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250502_001230.zip
2025-05-02 00:12:34,155 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-02 01:12:37,412 - __main__ - INFO - Backup created successfully: backups/project_backup_20250502_011234.zip
2025-05-02 01:12:37,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-02 01:12:38,029 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-02 01:12:38,031 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250502_011234.zip
2025-05-02 01:12:38,033 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250502_011234.zip
2025-05-02 01:12:38,034 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-02 02:12:41,089 - __main__ - INFO - Backup created successfully: backups/project_backup_20250502_021238.zip
2025-05-02 02:12:41,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-02 02:12:41,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-02 02:12:41,681 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250502_021238.zip
2025-05-02 02:12:41,683 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250502_021238.zip
2025-05-02 02:12:41,683 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-02 03:12:44,934 - __main__ - INFO - Backup created successfully: backups/project_backup_20250502_031241.zip
2025-05-02 03:12:45,040 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-02 03:12:45,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-02 03:12:45,491 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250502_031241.zip
2025-05-02 03:12:45,493 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250502_031241.zip
2025-05-02 03:12:45,493 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-02 04:12:48,519 - __main__ - INFO - Backup created successfully: backups/project_backup_20250502_041245.zip
2025-05-02 04:12:48,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-02 04:12:49,200 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-02 04:12:49,202 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250502_041245.zip
2025-05-02 04:12:49,204 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250502_041245.zip
2025-05-02 04:12:49,204 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-02 05:12:52,122 - __main__ - INFO - Backup created successfully: backups/project_backup_20250502_051249.zip
2025-05-02 05:12:52,234 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-02 05:12:52,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-02 05:12:52,680 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250502_051249.zip
2025-05-02 05:12:52,683 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250502_051249.zip
2025-05-02 05:12:52,683 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-04 23:49:37,168 - __main__ - INFO - Backup bot started
2025-05-04 23:49:37,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-04 23:49:41,119 - __main__ - INFO - Backup created successfully: backups/project_backup_20250504_234937.zip
2025-05-04 23:49:41,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-04 23:49:41,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-04 23:49:41,727 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250504_234937.zip
2025-05-04 23:49:41,732 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250504_234937.zip
2025-05-04 23:49:41,733 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-07 23:20:30,448 - __main__ - INFO - Backup bot started
2025-05-07 23:20:31,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-07 23:20:40,913 - __main__ - INFO - Backup created successfully: backups/project_backup_20250507_232031.zip
2025-05-07 23:20:41,050 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-07 23:20:41,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-07 23:20:41,838 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250507_232031.zip
2025-05-07 23:20:41,840 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250507_232031.zip
2025-05-07 23:20:41,840 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-08 00:20:47,231 - __main__ - INFO - Backup created successfully: backups/project_backup_20250508_002041.zip
2025-05-08 00:20:47,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-08 00:20:47,978 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-08 00:20:47,980 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250508_002041.zip
2025-05-08 00:20:47,982 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250508_002041.zip
2025-05-08 00:20:47,982 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-08 01:20:52,245 - __main__ - INFO - Backup created successfully: backups/project_backup_20250508_012048.zip
2025-05-08 01:20:52,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-08 01:20:53,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-08 01:20:53,350 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250508_012048.zip
2025-05-08 01:20:53,379 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250508_012048.zip
2025-05-08 01:20:53,379 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-10 01:00:26,189 - __main__ - INFO - Backup bot started
2025-05-10 01:00:26,767 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-10 01:00:37,942 - __main__ - INFO - Backup created successfully: backups/project_backup_20250510_010026.zip
2025-05-10 01:00:38,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-10 01:00:38,790 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-10 01:00:38,792 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250510_010026.zip
2025-05-10 01:00:38,793 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250510_010026.zip
2025-05-10 01:00:38,793 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-10 02:00:42,137 - __main__ - INFO - Backup created successfully: backups/project_backup_20250510_020038.zip
2025-05-10 02:00:42,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-10 02:00:42,654 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-10 02:00:42,656 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250510_020038.zip
2025-05-10 02:00:42,659 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250510_020038.zip
2025-05-10 02:00:42,659 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-10 03:00:47,691 - __main__ - INFO - Backup created successfully: backups/project_backup_20250510_030042.zip
2025-05-10 03:00:47,796 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-10 03:00:48,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-10 03:00:48,269 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250510_030042.zip
2025-05-10 03:00:48,271 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250510_030042.zip
2025-05-10 03:00:48,271 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-14 05:39:17,377 - __main__ - INFO - Backup bot started
2025-05-14 05:39:17,831 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-14 05:39:28,153 - __main__ - INFO - Backup created successfully: backups/project_backup_20250514_053917.zip
2025-05-14 05:39:28,229 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-14 05:39:29,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-14 05:39:29,423 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250514_053917.zip
2025-05-14 05:39:29,427 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250514_053917.zip
2025-05-14 05:39:29,428 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-17 06:26:50,199 - __main__ - INFO - Backup bot started
2025-05-17 06:26:50,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-17 06:26:57,534 - __main__ - INFO - Backup created successfully: backups/project_backup_20250517_062650.zip
2025-05-17 06:26:57,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-17 06:26:58,642 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-17 06:26:58,644 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250517_062650.zip
2025-05-17 06:26:58,648 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250517_062650.zip
2025-05-17 06:26:58,648 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-18 18:56:36,398 - __main__ - INFO - Backup bot started
2025-05-18 18:56:36,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-18 18:56:43,210 - __main__ - INFO - Backup created successfully: backups/project_backup_20250518_185636.zip
2025-05-18 18:56:43,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-18 18:56:44,779 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-18 18:56:44,781 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250518_185636.zip
2025-05-18 18:56:44,787 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250518_185636.zip
2025-05-18 18:56:44,787 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-23 00:57:22,093 - __main__ - INFO - Backup bot started
2025-05-23 00:57:22,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-23 00:57:28,212 - __main__ - INFO - Backup created successfully: backups/project_backup_20250523_005722.zip
2025-05-23 00:57:28,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-23 00:57:29,320 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-23 00:57:29,322 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250523_005722.zip
2025-05-23 00:57:29,328 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250523_005722.zip
2025-05-23 00:57:29,328 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-23 01:57:34,684 - __main__ - INFO - Backup created successfully: backups/project_backup_20250523_015729.zip
2025-05-23 01:57:34,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-23 01:57:35,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-23 01:57:35,675 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250523_015729.zip
2025-05-23 01:57:35,680 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250523_015729.zip
2025-05-23 01:57:35,680 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-23 02:57:41,162 - __main__ - INFO - Backup created successfully: backups/project_backup_20250523_025735.zip
2025-05-23 02:57:41,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-23 02:57:42,074 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-23 02:57:42,076 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250523_025735.zip
2025-05-23 02:57:42,082 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250523_025735.zip
2025-05-23 02:57:42,082 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-23 03:57:47,361 - __main__ - INFO - Backup created successfully: backups/project_backup_20250523_035742.zip
2025-05-23 03:57:47,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-23 03:57:48,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-23 03:57:48,243 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250523_035742.zip
2025-05-23 03:57:48,248 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250523_035742.zip
2025-05-23 03:57:48,248 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-27 08:32:51,376 - __main__ - INFO - Backup bot started
2025-05-27 08:32:51,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-27 08:32:55,435 - __main__ - INFO - Backup created successfully: backups/project_backup_20250527_083251.zip
2025-05-27 08:32:55,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-27 08:32:56,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-27 08:32:56,104 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250527_083251.zip
2025-05-27 08:32:56,106 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250527_083251.zip
2025-05-27 08:32:56,107 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-27 09:32:59,106 - __main__ - INFO - Backup created successfully: backups/project_backup_20250527_093256.zip
2025-05-27 09:32:59,210 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-27 09:32:59,686 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-27 09:32:59,689 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250527_093256.zip
2025-05-27 09:32:59,690 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250527_093256.zip
2025-05-27 09:32:59,690 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-27 10:33:02,601 - __main__ - INFO - Backup created successfully: backups/project_backup_20250527_103259.zip
2025-05-27 10:33:02,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-27 10:33:03,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-27 10:33:03,285 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250527_103259.zip
2025-05-27 10:33:03,288 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250527_103259.zip
2025-05-27 10:33:03,288 - __main__ - INFO - Waiting 60 minutes until next backup...
2025-05-27 11:33:06,805 - __main__ - INFO - Backup created successfully: backups/project_backup_20250527_113303.zip
2025-05-27 11:33:06,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendMessage "HTTP/1.1 200 OK"
2025-05-27 11:33:07,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7721541231:AAGEwOJ9HdRHbX0gBq9zyF7eMLDpTeb3Nwg/sendDocument "HTTP/1.1 200 OK"
2025-05-27 11:33:07,590 - __main__ - INFO - Backup sent successfully: backups/project_backup_20250527_113303.zip
2025-05-27 11:33:07,592 - __main__ - INFO - Cleaned up old backup: backups/project_backup_20250527_113303.zip
2025-05-27 11:33:07,592 - __main__ - INFO - Waiting 60 minutes until next backup...
