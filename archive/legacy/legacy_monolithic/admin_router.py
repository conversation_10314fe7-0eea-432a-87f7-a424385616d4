from fastapi import APIRouter, Depends, HTTPException, status, Request, Response, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, update, desc, asc, and_, String
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import SQLAlchemyError
from typing import Dict, Optional, List, Any
from datetime import datetime, timedelta, timezone
import logging
import secrets
import string
from pydantic import BaseModel

from database import get_async_db
from models import (
    User, Role, VPNPackage, MarzbanPanel, VPNSubscription, Transaction, 
    TransactionType, SystemConfig, UserCard, TaskCompletion, Task,
    DailyTaskStreak, ChatMessage, TaskType, RewardType, TaskStatus,
    CardCatalog, TaskVerification, SecurityLog, TokenBlacklist, OnlineUser,
    ChatConversation, DailyCycle
)
from schemas import (
    UserCreate, UserOut, UserUpdate, UserProfile, UserProfileUpdate,
    VPNPackageCreate, VPNPackageOut, UserVPNUpdate,
    MarzbanPanelCreate, MarzbanPanelUpdate, MarzbanPanelOut, VPNSubscriptionOut,
    VPNSubscriptionCreate, VPNPackageUpdate, AdminUserUpdate,
    TaskCreate, TaskOut, AdminTaskOut, TaskStatistics, UnifiedCardResponse,
    UserCardCreate, UserCardOut
)
from auth import get_current_admin, get_password_hash
from services.marzban_service import get_marzban_service
from routers.chat.websocket import connection_manager

router = APIRouter(
    tags=["admin"]
)

logger = logging.getLogger(__name__)

# Admin User Management Routes
@router.get("/admin/users/", response_model=List[UserOut])
async def get_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    sort: str = "newest"
):
    """Get all users with proper sorting"""
    stmt = select(User).options(
        selectinload(User.user_cards).selectinload(UserCard.card_catalog),
        selectinload(User.vpn_subscriptions).options(
            selectinload(VPNSubscription.package).selectinload(VPNPackage.allowed_panels)
        ),
        selectinload(User.task_completions).selectinload(TaskCompletion.task),
        selectinload(User.daily_streak)
        # Add other relationships if needed by UserOut indirectly
    )
    if sort == "newest":
        stmt = stmt.order_by(User.created_at.desc())
    elif sort == "oldest":
        stmt = stmt.order_by(User.created_at.asc())
    elif sort == "balance-high":
        stmt = stmt.order_by(User.wallet_balance.desc())
    elif sort == "balance-low":
        stmt = stmt.order_by(User.wallet_balance.asc())
    
    result = await db.execute(stmt)
    users = result.scalars().all()
    
    # Ensure dates are properly formatted in ISO format
    for user in users:
        if user.created_at:
            user.created_at = user.created_at.isoformat()
    
    return users

@router.post("/admin/users/", response_model=UserOut)
async def create_user_admin(
    user: UserCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    logger.info(f"Creating user with data: {user.dict()}")
    try:
        # Check if username or email already exists
        stmt = select(User).filter(
            or_(User.username == user.username, User.email == user.email)
        )
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="Username or email already registered"
            )

        # Generate unique referral code
        while True:
            referral_code = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
            ref_stmt = select(User).filter(User.referral_code == referral_code)
            ref_result = await db.execute(ref_stmt)
            existing = ref_result.scalar_one_or_none()
            if not existing:
                break

        # Create new user with UTC timestamp
        user_data = user.dict(exclude={'password'})  # Exclude password from dict
        new_user = User(
            **user_data,
            created_at=datetime.now(timezone.utc),  # Explicitly use UTC
            hashed_password=get_password_hash(user.password),  # Hash the password
            referral_code=referral_code
        )
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # Format the created_at time in ISO format with timezone
        new_user.created_at = new_user.created_at.isoformat()
        return new_user

    except SQLAlchemyError as e:
        await db.rollback()
        logger.error(f"Database error while creating user: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"Database error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create user: {str(e)}"
        )

@router.put("/admin/users/{user_id}", response_model=UserOut)
async def update_user(
    user_id: int,
    user_update: AdminUserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update user details"""
    try:
        logger.info(f"Received update request for user {user_id}")
        logger.info(f"Update data received: {user_update.dict()}")
        
        # Get user, EAGERLY LOADING relationships needed for the UserOut response
        stmt = select(User).where(User.id == user_id).options(
            selectinload(User.user_cards).selectinload(UserCard.card_catalog),
            selectinload(User.vpn_subscriptions).options( 
                selectinload(VPNSubscription.package).selectinload(VPNPackage.allowed_panels)
            ),
            selectinload(User.task_completions).selectinload(TaskCompletion.task),
            selectinload(User.daily_streak)
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Convert update data to dict
        # Use exclude_unset=True to only update fields explicitly provided in the request
        update_data = user_update.dict(exclude_unset=True) 
        logger.info(f"Processed update data (excluding unset): {update_data}")

        # Handle Telegram ID update
        if 'telegram_id' in update_data:
            telegram_id = update_data['telegram_id']
            logger.info(f"Processing telegram_id update: {telegram_id}")

            if telegram_id is not None:
                # Check if ID is already in use by another user
                existing_stmt = select(User).where(
                    User.telegram_id == telegram_id,
                    User.id != user_id
                )
                existing_result = await db.execute(existing_stmt)
                existing = existing_result.scalar_one_or_none()
                
                if existing:
                    raise HTTPException(
                        status_code=400,
                        detail="Telegram ID is already in use"
                    )

            user.telegram_id = telegram_id # Assign even if None to clear it
            logger.info(f"Updated telegram_id to {user.telegram_id}")

        # Update all other fields included in the request
        for key, value in update_data.items():
            # Skip telegram_id as it's handled above
            # Also skip fields not present in the User model to avoid errors
            if key != 'telegram_id' and hasattr(user, key):
                setattr(user, key, value)
                logger.info(f"Updated {key} to {value}")

        try:
            db.add(user) # Ensure the modified user is tracked by the session
            await db.commit()
            await db.refresh(user) # Refresh to get committed state
            logger.info(f"Successfully updated user {user_id}")
            return user
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Database error updating user {user_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Database error: {str(e)}"
            )

    except HTTPException: # Re-raise HTTPExceptions directly
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating user {user_id}: {str(e)}", exc_info=True)
        # Attempt rollback on generic errors
        try: 
             await db.rollback()
        except Exception as rb_err:
             logger.error(f"Error during rollback after update user exception: {rb_err}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update user: {str(e)}"
        )

@router.get("/admin/users/{user_id}", response_model=UserOut)
async def get_user_details(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    stmt = select(User).filter(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

@router.put("/admin/users/{user_id}/discount", response_model=UserOut)
async def update_user_discount(
    user_id: int,
    discount: UserVPNUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    stmt = select(User).filter(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    if discount.discount_percent is not None:
        user.discount_percent = max(0, min(100, discount.discount_percent))
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    return user

@router.put("/admin/users/{user_id}/wallet/charge", response_model=UserOut)
async def charge_user_wallet(
    user_id: int,
    charge_data: AdminUserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Charge or deduct from user wallet by admin"""
    try:
        # Fetch user with relationships needed for UserOut response model
        stmt = select(User).where(User.id == user_id).options(
            selectinload(User.user_cards).selectinload(UserCard.card_catalog), # Load cards just in case UserOut needs it indirectly
            selectinload(User.vpn_subscriptions).options( # Load subscriptions and nested package/panels
                selectinload(VPNSubscription.package).selectinload(VPNPackage.allowed_panels)
            ),
            selectinload(User.task_completions).selectinload(TaskCompletion.task), # Load completions and nested task
            selectinload(User.daily_streak) # Load daily streak
        )
        result = await db.execute(stmt) 
        user = result.scalar_one_or_none()
        
        if not user:
            logger.error(f"User {user_id} not found for wallet charge")
            raise HTTPException(status_code=404, detail="User not found")
        
        if charge_data.wallet_charge is None:
            raise HTTPException(status_code=400, detail="Charge amount (wallet_charge) is required")
            
        # Use Decimal for currency potentially
        try:
            charge_amount = float(charge_data.wallet_charge)
        except (ValueError, TypeError):
             raise HTTPException(status_code=400, detail="Invalid charge amount format")

        old_balance = user.wallet_balance
        new_balance = old_balance + charge_amount
        
        # Prevent negative balance
        if new_balance < 0:
            raise HTTPException(status_code=400, detail="Operation would result in negative balance")
        
        # Set description based on positive or negative charge
        if charge_amount > 0:
            description = f"Wallet increased by admin {current_user.username}"
        else:
            description = f"Wallet decreased by admin {current_user.username}"
        
        # Create transaction record
        transaction = Transaction(
            user_id=user.id,
            amount=charge_amount,
            type=TransactionType.admin_adjustment,
            description=description,
            reseller_id=current_user.id,
            balance_after=new_balance,
            status="completed"
        )
        db.add(transaction)
        
        # Update user's wallet balance
        user.wallet_balance = new_balance
        db.add(user) # Add the modified user back to the session for update
        
        # Log with appropriate message based on charge type
        if charge_amount > 0:
            logger.info(f"Wallet charged for user {user_id}: {old_balance} → {new_balance} (+{charge_amount})")
        else:
            logger.info(f"Wallet deducted for user {user_id}: {old_balance} → {new_balance} ({charge_amount})")

        try:
            await db.commit()
            # Refresh the user object AFTER commit to get the latest state including any triggers/defaults
            # The relationships are already loaded from the initial query
            await db.refresh(user) 
            return user
        except SQLAlchemyError as e:
            await db.rollback()
            logger.error(f"Database error during wallet charge commit for user {user_id}: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Database error: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing wallet operation for user {user_id}: {str(e)}", exc_info=True)
        # Attempt rollback on generic errors too
        try:
            await db.rollback()
        except Exception as rb_error:
             logger.error(f"Error during rollback after wallet operation exception for user {user_id}: {rb_error}")
        raise HTTPException(status_code=500, detail=f"Failed to process wallet operation: {str(e)}")

@router.get("/admin/users/{user_id}/transactions")
async def get_user_transactions(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get user transactions (admin only)"""
    try:
        # Check if user exists
        stmt_user = select(User).filter(User.id == user_id)
        result_user = await db.execute(stmt_user)
        user = result_user.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Get user's transactions ordered by created_at desc
        stmt_transactions = (
            select(Transaction)
            .filter(Transaction.user_id == user_id)
            .order_by(Transaction.created_at.desc())
        )
        result_transactions = await db.execute(stmt_transactions)
        transactions = result_transactions.scalars().all()

        return transactions

    except SQLAlchemyError as e:
        logger.error(f"Database error while fetching transactions: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"Database error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error fetching transactions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch transactions: {str(e)}"
        )

@router.put("/admin/users/{user_id}/password")
async def update_user_password(
    user_id: int,
    data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update user password by admin"""
    try:
        stmt = select(User).filter(User.id == user_id)
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
            
        if not data.get("new_password"):
            raise HTTPException(status_code=400, detail="New password is required")
            
        # Hash the new password
        user.hashed_password = get_password_hash(data["new_password"])
        db.add(user)
        
        await db.commit()
        return {"message": "Password updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating password: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update password: {str(e)}"
        )

@router.get("/admin/users/check/{field}/{value}")
async def check_user_field(
    field: str,
    value: str,
    db: AsyncSession = Depends(get_async_db)
) -> Dict[str, bool]:
    """Check if username, email, or telegram ID exists"""
    try:
        if field == "username":
            stmt = select(User).filter(User.username == value)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            return {"exists": user is not None}
            
        elif field == "email":
            if not value:
                return {"exists": False}
            stmt = select(User).filter(User.email == value)
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            return {"exists": user is not None}
            
        elif field == "telegram":
            if not value.isdigit():
                raise HTTPException(
                    status_code=400, 
                    detail="Telegram ID must be numeric"
                )
            
            stmt = select(User).filter(User.telegram_id == int(value))
            result = await db.execute(stmt)
            user = result.scalar_one_or_none()
            return {"exists": user is not None}
            
        else:
            raise HTTPException(
                status_code=400,
                detail="Invalid field to check"
            )
            
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error checking {field}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error checking {field}"
        )

@router.get("/admin/users/check/telegram/{telegram_id}")
async def check_telegram_id(
    telegram_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Check if telegram ID exists and is valid"""
    try:
        # Check if telegram_id exists in database
        stmt = select(User).filter(User.telegram_id == telegram_id) 
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        # Log the check
        logger.info(f"Checking Telegram ID {telegram_id}: {'exists' if existing_user else 'available'}")
        
        return {
            "exists": existing_user is not None,
            "current_user": existing_user.id if existing_user else None
        }
    except Exception as e:
        logger.error(f"Error checking Telegram ID: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to check Telegram ID"
        )

# --- VPN Package Routes ---

@router.get("/admin/packages/", response_model=List[VPNPackageOut])
async def list_packages(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100
):
    """List all VPN packages"""
    try:
        stmt = select(VPNPackage).options(
            selectinload(VPNPackage.allowed_panels)
        ).offset(skip).limit(limit)
        result = await db.execute(stmt)
        packages = result.scalars().all()
        return packages
    except Exception as e:
        logger.error(f"Failed to list packages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/packages/", response_model=VPNPackageOut)
async def create_package(
    package: VPNPackageCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Create a new VPN package"""
    try:
        # Verify panel exists
        panel_stmt = select(MarzbanPanel).filter(MarzbanPanel.id == package.panel_id)
        panel_result = await db.execute(panel_stmt)
        panel = panel_result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")
        
        # Create package
        db_package = VPNPackage(**package.dict())
        db.add(db_package)
        await db.commit()
        await db.refresh(db_package)
        
        return db_package
    except Exception as e:
        logger.error(f"Failed to create package: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/packages/{package_id}", response_model=VPNPackageOut)
async def get_package(
    package_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get a specific VPN package"""
    try:
        stmt = select(VPNPackage).filter(VPNPackage.id == package_id)
        result = await db.execute(stmt)
        package = result.scalar_one_or_none()
        
        if not package:
            raise HTTPException(status_code=404, detail="Package not found")
            
        return package
    except Exception as e:
        logger.error(f"Failed to get package: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/admin/packages/{package_id}", response_model=VPNPackageOut)
async def update_package(
    package_id: int,
    package_update: VPNPackageUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update a VPN package"""
    try:
        # Get package
        stmt = select(VPNPackage).filter(VPNPackage.id == package_id)
        result = await db.execute(stmt)
        db_package = result.scalar_one_or_none()
        
        if not db_package:
            raise HTTPException(status_code=404, detail="Package not found")
            
        # Verify panel exists if changing
        if package_update.panel_id is not None:
            panel_stmt = select(MarzbanPanel).filter(MarzbanPanel.id == package_update.panel_id)
            panel_result = await db.execute(panel_stmt)
            panel = panel_result.scalar_one_or_none()
            
            if not panel:
                raise HTTPException(status_code=404, detail="Panel not found")
        
        # Update package fields
        update_data = package_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(db_package, key, value)
            
        await db.commit()
        await db.refresh(db_package)
        
        return db_package
    except Exception as e:
        logger.error(f"Failed to update package: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/admin/packages/{package_id}")
async def delete_package(
    package_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a VPN package"""
    try:
        # Check for existing subscriptions
        subs_stmt = select(VPNSubscription).filter(VPNSubscription.package_id == package_id)
        subs_result = await db.execute(subs_stmt)
        if subs_result.first():
            raise HTTPException(
                status_code=400, 
                detail="Cannot delete package with existing subscriptions"
            )
        
        # Get package
        stmt = select(VPNPackage).filter(VPNPackage.id == package_id)
        result = await db.execute(stmt)
        package = result.scalar_one_or_none()
        
        if not package:
            raise HTTPException(status_code=404, detail="Package not found")
            
        # Delete package
        await db.delete(package)
        await db.commit()
        
        return {"message": "Package deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete package: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# --- Marzban Panel Admin Routes ---

@router.get("/admin/marzban-panels/", response_model=List[MarzbanPanelOut])
async def list_panels(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """List all Marzban panels"""
    try:
        stmt = select(MarzbanPanel)
        result = await db.execute(stmt)
        panels = result.scalars().all()
        return panels
    except Exception as e:
        logger.error(f"Failed to list panels: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/marzban-panels/", response_model=MarzbanPanelOut)
async def create_panel(
    panel: MarzbanPanelCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Create a new Marzban panel"""
    try:
        # Create panel with encrypted password
        db_panel = MarzbanPanel(
            name=panel.name,
            url=panel.url,
            username=panel.username,
            password=get_password_hash(panel.password),  # Encrypt password
            is_active=panel.is_active
        )
        db.add(db_panel)
        await db.commit()
        await db.refresh(db_panel)
        
        return db_panel
    except Exception as e:
        logger.error(f"Failed to create panel: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/marzban-panels/{panel_id}/test")
async def test_panel_connection(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Test connection to a Marzban panel"""
    try:
        # Get panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")
            
        # Test connection
        service = get_marzban_service(db)
        is_connected = await service.test_connection(panel)
        
        if not is_connected:
            return JSONResponse(
                status_code=400,
                content={"message": "Failed to connect to panel"}
            )
            
        return {"message": "Connection successful"}
    except Exception as e:
        logger.error(f"Failed to test panel connection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/admin/marzban-panels/{panel_id}", response_model=MarzbanPanelOut)
async def update_panel(
    panel_id: int,
    panel_update: MarzbanPanelUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update a Marzban panel"""
    try:
        # Get panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        db_panel = result.scalar_one_or_none()
        
        if not db_panel:
            raise HTTPException(status_code=404, detail="Panel not found")
            
        # Update panel fields
        update_data = panel_update.dict(exclude_unset=True)
        if "password" in update_data and update_data["password"]:
            update_data["password"] = get_password_hash(update_data["password"])
            
        for key, value in update_data.items():
            setattr(db_panel, key, value)
            
        await db.commit()
        await db.refresh(db_panel)
        
        return db_panel
    except Exception as e:
        logger.error(f"Failed to update panel: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/admin/marzban-panels/{panel_id}/")
async def delete_panel(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a Marzban panel"""
    try:
        # Check for existing packages
        package_stmt = select(VPNPackage).filter(VPNPackage.panel_id == panel_id)
        package_result = await db.execute(package_stmt)
        if package_result.first():
            raise HTTPException(
                status_code=400, 
                detail="Cannot delete panel with existing packages"
            )
        
        # Get panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")
            
        # Delete panel
        await db.delete(panel)
        await db.commit()
        
        return {"message": "Panel deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete panel: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/marzban-panels/{panel_id}/stats")
async def get_panel_stats(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get statistics from a Marzban panel"""
    try:
        # Get panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")
            
        # Get stats from panel
        service = get_marzban_service(db)
        stats = await service.get_panel_stats(panel)
        
        return stats
    except Exception as e:
        logger.error(f"Failed to get panel stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# --- Subscriptions Admin Routes ---

@router.get("/admin/subscriptions/", response_model=List[VPNSubscriptionOut])
async def list_subscriptions(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    sort_by: str = "created_at",
    sort_dir: str = "desc"
):
    """List all VPN subscriptions with filtering and sorting"""
    try:
        # Build query
        query = select(VPNSubscription).options(
            selectinload(VPNSubscription.user),
            selectinload(VPNSubscription.package).selectinload(VPNPackage.allowed_panels)
        )
        
        # Apply filters
        if user_id:
            query = query.filter(VPNSubscription.user_id == user_id)
        if is_active is not None:
            query = query.filter(VPNSubscription.is_active == is_active)
            
        # Apply sorting
        if sort_dir.lower() == "asc":
            query = query.order_by(asc(getattr(VPNSubscription, sort_by)))
        else:
            query = query.order_by(desc(getattr(VPNSubscription, sort_by)))
            
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        # Execute query
        result = await db.execute(query)
        subscriptions = result.scalars().all()
        
        return subscriptions
    except Exception as e:
        logger.error(f"Failed to list subscriptions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/subscriptions/renew/{subscription_id}")
async def renew_subscription(
    subscription_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    force: bool = False
):
    """Renew a VPN subscription"""
    try:
        # Get subscription with package
        subscription_stmt = select(VPNSubscription).filter(
            VPNSubscription.id == subscription_id
        ).options(
            selectinload(VPNSubscription.package),
            selectinload(VPNSubscription.user)
        )
        subscription_result = await db.execute(subscription_stmt)
        subscription = subscription_result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")
        
        # TODO: Implement renewal logic
        # This might include:
        # 1. Calculate new expiry date
        # 2. Check user wallet balance if not force=True
        # 3. Create transaction if payment needed
        # 4. Update subscription record
        # 5. Update Marzban user via API
        
        # Placeholder logic - this should be replaced with actual implementation
        if not force:
            # Check if user has enough balance
            if subscription.user.wallet_balance < subscription.package.price:
                raise HTTPException(
                    status_code=400, 
                    detail="Insufficient wallet balance for renewal"
                )
                
            # Deduct from wallet
            subscription.user.wallet_balance -= subscription.package.price
            
            # Create transaction record
            transaction = Transaction(
                user_id=subscription.user_id,
                amount=subscription.package.price,
                type=TransactionType.vpn_purchase,
                description=f"Renewal of {subscription.package.name} package",
                status="completed"
            )
            db.add(transaction)
        
        # Update subscription
        new_expiry = max(
            datetime.now() + timedelta(days=subscription.package.duration_days),
            subscription.expiry_date if subscription.expiry_date > datetime.now() else datetime.now()
        )
        subscription.expiry_date = new_expiry
        subscription.is_active = True
        
        # TODO: Update Marzban user via API
        service = get_marzban_service(db)
        panel_stmt = select(MarzbanPanel).filter(
            MarzbanPanel.id == subscription.package.panel_id
        )
        panel_result = await db.execute(panel_stmt)
        panel = panel_result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")
            
        # Update user on panel
        updated = await service.update_user(
            panel=panel,
            username=subscription.marzban_username,
            data={"expire": int(new_expiry.timestamp())}
        )
        
        if not updated:
            logger.warning(f"Failed to update user on panel: {subscription.marzban_username}")
            
        await db.commit()
        
        return {"message": "Subscription renewed successfully", "expiry_date": new_expiry}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to renew subscription: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/subscriptions/create/{user_id}")
async def admin_create_subscription(
    user_id: int,
    package_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days_override: Optional[int] = None,
    charge_wallet: bool = False
):
    """Create a VPN subscription for a user by admin"""
    try:
        # Get user
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
            
        # Get package
        package_stmt = select(VPNPackage).filter(VPNPackage.id == package_id)
        package_result = await db.execute(package_stmt)
        package = package_result.scalar_one_or_none()
        
        if not package:
            raise HTTPException(status_code=404, detail="Package not found")
            
        # Check if user already has an active subscription
        sub_stmt = select(VPNSubscription).filter(
            VPNSubscription.user_id == user_id,
            VPNSubscription.is_active == True
        )
        sub_result = await db.execute(sub_stmt)
        existing_sub = sub_result.scalar_one_or_none()
        
        if existing_sub:
            raise HTTPException(
                status_code=400, 
                detail="User already has an active subscription"
            )
            
        # Check wallet balance if charging
        if charge_wallet and user.wallet_balance < package.price:
            raise HTTPException(
                status_code=400, 
                detail="Insufficient wallet balance"
            )
            
        # Get panel
        panel_stmt = select(MarzbanPanel).filter(MarzbanPanel.id == package.panel_id)
        panel_result = await db.execute(panel_stmt)
        panel = panel_result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")
            
        # Create user on panel
        service = get_marzban_service(db)
        duration = days_override or package.duration_days
        expiry_date = datetime.now() + timedelta(days=duration)
        
        # Create unique username for Marzban
        username = f"user_{user.id}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        created = await service.create_user(
            panel=panel,
            username=username,
            data={
                "expire": int(expiry_date.timestamp()),
                "data_limit": package.data_limit_gb * 1024 * 1024 * 1024,  # Convert GB to bytes
                "proxies": {
                    "vmess": {"flow": ""},
                    "vless": {"flow": ""},
                    "trojan": {},
                    "shadowsocks": {}
                }
            }
        )
        
        if not created:
            raise HTTPException(
                status_code=500, 
                detail="Failed to create user on VPN panel"
            )
            
        # Charge wallet if needed
        if charge_wallet:
            user.wallet_balance -= package.price
            
            # Create transaction record
            transaction = Transaction(
                user_id=user.id,
                amount=package.price,
                type=TransactionType.vpn_purchase,
                description=f"Purchase of {package.name} package by admin",
                status="completed"
            )
            db.add(transaction)
            
        # Create subscription record
        subscription = VPNSubscription(
            user_id=user.id,
            package_id=package.id,
            expiry_date=expiry_date,
            is_active=True,
            marzban_username=username
        )
        db.add(subscription)
        
        await db.commit()
        await db.refresh(subscription)
        
        return {
            "message": "Subscription created successfully", 
            "subscription_id": subscription.id,
            "expiry_date": expiry_date,
            "marzban_username": username
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create subscription: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# --- System Config Routes ---

@router.get("/admin/system/config")
async def get_system_config(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get system configuration"""
    try:
        stmt = select(SystemConfig)
        result = await db.execute(stmt)
        configs = result.scalars().all()
        
        # Convert to dict
        config_dict = {config.key: config.value for config in configs}
        return config_dict
    except Exception as e:
        logger.error(f"Failed to get system config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/admin/system/config")
async def update_system_config(
    config: Dict[str, Any],
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update system configuration"""
    try:
        for key, value in config.items():
            # Check if config exists
            stmt = select(SystemConfig).filter(SystemConfig.key == key)
            result = await db.execute(stmt)
            db_config = result.scalar_one_or_none()
            
            if db_config:
                # Update existing config
                db_config.value = value
            else:
                # Create new config
                db_config = SystemConfig(key=key, value=value)
                db.add(db_config)
                
        await db.commit()
        return {"message": "System configuration updated successfully"}
    except Exception as e:
        logger.error(f"Failed to update system config: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/stats/users")
async def get_user_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get user statistics"""
    try:
        # Total users
        total_users_stmt = select(func.count(User.id))
        total_users_result = await db.execute(total_users_stmt)
        total_users = total_users_result.scalar()
        
        # Active users with subscriptions
        active_users_stmt = select(func.count(User.id)).join(
            VPNSubscription, User.id == VPNSubscription.user_id
        ).filter(VPNSubscription.is_active == True)
        active_users_result = await db.execute(active_users_stmt)
        active_users = active_users_result.scalar()
        
        # Total wallet balance
        total_balance_stmt = select(func.sum(User.wallet_balance))
        total_balance_result = await db.execute(total_balance_stmt)
        total_balance = total_balance_result.scalar() or 0
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "total_wallet_balance": total_balance
        }
    except Exception as e:
        logger.error(f"Failed to get user stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/subscriptions/{subscription_id}/stats")
async def get_subscription_stats(
    subscription_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get usage statistics for a specific VPN subscription"""
    try:
        # Get subscription with package info
        subscription_stmt = select(VPNSubscription).filter(
            VPNSubscription.id == subscription_id
        ).options(
            selectinload(VPNSubscription.package).selectinload(VPNPackage.panel)
        )
        subscription_result = await db.execute(subscription_stmt)
        subscription = subscription_result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")
            
        if not subscription.package:
            raise HTTPException(status_code=404, detail="Subscription has no associated package")
            
        if not subscription.package.panel:
            raise HTTPException(status_code=404, detail="Package has no associated panel")
        
        # Get user stats from Marzban panel
        service = get_marzban_service(db)
        user_stats = await service.get_user_stats(
            panel=subscription.package.panel,
            username=subscription.marzban_username
        )
        
        if not user_stats:
            raise HTTPException(
                status_code=404, 
                detail="User not found on VPN panel or error retrieving stats"
            )
            
        # Combine subscription info with user stats
        return {
            "subscription_id": subscription.id,
            "username": subscription.marzban_username,
            "package": subscription.package.name,
            "is_active": subscription.is_active,
            "expiry_date": subscription.expiry_date,
            "data_limit": user_stats.get("data_limit", 0),
            "used_traffic": user_stats.get("used_traffic", 0),
            "usage_percent": (
                user_stats.get("used_traffic", 0) / user_stats.get("data_limit", 1) * 100
                if user_stats.get("data_limit", 0) > 0 else 0
            ),
            "status": user_stats.get("status", "unknown"),
            "last_online": user_stats.get("last_online", None),
            "subscription_url": user_stats.get("subscription_url", "")
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get subscription stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/admin/subscriptions/{subscription_id}")
async def delete_subscription(
    subscription_id: int,
    delete_from_panel: bool = Query(True, description="Whether to delete the user from Marzban panel"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a VPN subscription"""
    try:
        # Get subscription with package info
        subscription_stmt = select(VPNSubscription).filter(
            VPNSubscription.id == subscription_id
        ).options(
            selectinload(VPNSubscription.package).selectinload(VPNPackage.panel)
        )
        subscription_result = await db.execute(subscription_stmt)
        subscription = subscription_result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")
            
        # If requested, try to delete user from the panel
        if delete_from_panel:
            # Check if package and panel exist
            if subscription.package and subscription.package.panel:
                service = get_marzban_service(db)
                
                # Delete user from panel
                success = await service.delete_user(
                    panel=subscription.package.panel,
                    username=subscription.marzban_username
                )
                
                if not success:
                    logger.warning(
                        f"Failed to delete user {subscription.marzban_username} from panel. "
                        "Continuing with database deletion."
                    )
            else:
                logger.warning(
                    "Cannot delete user from panel: subscription has no associated "
                    "package or panel. Continuing with database deletion."
                )
            
        # Delete subscription from database
        await db.delete(subscription)
        await db.commit()
        
        return {"message": "Subscription deleted successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete subscription: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/dashboard")
async def admin_dashboard(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get admin dashboard statistics"""
    try:
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Total registered users
        total_users_stmt = select(func.count(User.id))
        total_users_result = await db.execute(total_users_stmt)
        total_users = total_users_result.scalar() or 0
        
        # New users today
        new_users_today_stmt = select(func.count(User.id)).filter(
            User.created_at >= today
        )
        new_users_today_result = await db.execute(new_users_today_stmt)
        new_users_today = new_users_today_result.scalar() or 0
        
        # Active subscriptions
        active_subs_stmt = select(func.count(VPNSubscription.id)).filter(
            VPNSubscription.is_active == True
        )
        active_subs_result = await db.execute(active_subs_stmt)
        active_subscriptions = active_subs_result.scalar() or 0
        
        # Total revenue - Fixed TransactionType
        total_revenue_stmt = select(func.sum(Transaction.amount)).filter(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal,
                TransactionType.card_purchase,
                TransactionType.card_upgrade
            ]),
            Transaction.status == "completed"
        )
        total_revenue_result = await db.execute(total_revenue_stmt)
        total_revenue = total_revenue_result.scalar() or 0
        
        # Revenue today - Fixed TransactionType
        revenue_today_stmt = select(func.sum(Transaction.amount)).filter(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal,
                TransactionType.card_purchase,
                TransactionType.card_upgrade
            ]),
            Transaction.status == "completed",
            Transaction.created_at >= today
        )
        revenue_today_result = await db.execute(revenue_today_stmt)
        revenue_today = revenue_today_result.scalar() or 0
        
        # Package distribution
        package_distrib_stmt = select(
            VPNPackage.name,
            func.count(VPNSubscription.id).label("count")
        ).join(
            VPNSubscription, VPNPackage.id == VPNSubscription.package_id
        ).filter(
            VPNSubscription.is_active == True
        ).group_by(
            VPNPackage.id, VPNPackage.name
        )
        package_distrib_result = await db.execute(package_distrib_stmt)
        package_distribution = [
            {"name": name, "count": count} 
            for name, count in package_distrib_result.all()
        ]
        
        return {
            "total_users": total_users,
            "new_users_today": new_users_today,
            "active_subscriptions": active_subscriptions,
            "total_revenue": total_revenue,
            "revenue_today": revenue_today,
            "package_distribution": package_distribution
        }
    except Exception as e:
        logger.error(f"Failed to get admin dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/stats/online-users")
async def get_online_users_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get real-time online user statistics (Admin only - Privacy protected)"""
    try:
        # Get currently connected WebSocket users
        online_user_ids = await connection_manager.get_online_users()
        online_count = await connection_manager.get_online_count()
        
        # Get user details for admin analytics (usernames for identification)
        online_users_details = []
        if online_user_ids:
            users_query = await db.execute(
                select(User.id, User.username, User.created_at, User.last_login_at)
                .where(User.id.in_(online_user_ids))
            )
            
            for user_id, username, created_at, last_login_at in users_query:
                online_users_details.append({
                    "user_id": user_id,
                    "username": username,
                    "account_age_days": (datetime.utcnow() - created_at).days if created_at else 0,
                    "last_login": last_login_at.isoformat() if last_login_at else None
                })
        
        # Calculate peak hours analytics
        current_hour = datetime.utcnow().hour
        
        return {
            "current_online_count": online_count,
            "online_users": online_users_details,
            "timestamp": datetime.utcnow().isoformat(),
            "current_hour": current_hour,
            "analytics_note": "This data is for admin analytics only and not exposed to frontend users"
        }
        
    except Exception as e:
        logger.error(f"Failed to get online users stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/stats/chat-analytics")
async def get_chat_analytics(
    days: int = 7,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get chat usage analytics for the specified number of days (Admin only)"""
    try:
        # Calculate date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Total messages in period
        total_messages_query = await db.execute(
            select(func.count(ChatMessage.id))
            .where(ChatMessage.created_at >= start_date)
        )
        total_messages = total_messages_query.scalar() or 0
        
        # Public vs Private message breakdown
        public_messages_query = await db.execute(
            select(func.count(ChatMessage.id))
            .where(
                and_(
                    ChatMessage.created_at >= start_date,
                    ChatMessage.is_public == True
                )
            )
        )
        public_messages = public_messages_query.scalar() or 0
        
        private_messages = total_messages - public_messages
        
        # Active chat users (users who sent messages)
        active_chat_users_query = await db.execute(
            select(func.count(func.distinct(ChatMessage.sender_id)))
            .where(ChatMessage.created_at >= start_date)
        )
        active_chat_users = active_chat_users_query.scalar() or 0
        
        # Messages per day
        daily_messages_query = await db.execute(
            select(
                func.date(ChatMessage.created_at).label('date'),
                func.count(ChatMessage.id).label('count')
            )
            .where(ChatMessage.created_at >= start_date)
            .group_by(func.date(ChatMessage.created_at))
            .order_by(func.date(ChatMessage.created_at))
        )
        
        daily_breakdown = [
            {
                "date": str(date),
                "message_count": count
            }
            for date, count in daily_messages_query.all()
        ]
        
        # Top chat users (by message count)
        top_users_query = await db.execute(
            select(
                User.username,
                func.count(ChatMessage.id).label('message_count')
            )
            .join(ChatMessage, User.id == ChatMessage.sender_id)
            .where(ChatMessage.created_at >= start_date)
            .group_by(User.id, User.username)
            .order_by(func.count(ChatMessage.id).desc())
            .limit(10)
        )
        
        top_users = [
            {
                "username": username,
                "message_count": count
            }
            for username, count in top_users_query.all()
        ]
        
        return {
            "period_days": days,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "total_messages": total_messages,
            "public_messages": public_messages,
            "private_messages": private_messages,
            "active_chat_users": active_chat_users,
            "daily_breakdown": daily_breakdown,
            "top_users": top_users,
            "avg_messages_per_day": round(total_messages / days, 2) if days > 0 else 0
        }
        
    except Exception as e:
        logger.error(f"Failed to get chat analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Task Management Admin Routes (moved from task_router.py)
@router.get("/admin/tasks/", response_model=List[AdminTaskOut])
async def get_admin_task_list(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all tasks with admin statistics"""
    try:
        # Get tasks with completion statistics
        stmt = (
            select(Task)
            .options(selectinload(Task.completions))
            .where(Task.is_active == True)
            .order_by(Task.created_at.desc())
        )
        result = await db.execute(stmt)
        tasks = result.scalars().all()
        
        admin_tasks = []
        for task in tasks:
            # Calculate statistics
            total_completions = len(task.completions)
            completed_count = len([c for c in task.completions if c.status == TaskStatus.COMPLETED])
            pending_count = len([c for c in task.completions if c.status == TaskStatus.PENDING])
            failed_count = len([c for c in task.completions if c.status == TaskStatus.FAILED])
            
            # Calculate average completion time for completed tasks
            completed_tasks = [c for c in task.completions if c.status == TaskStatus.COMPLETED and c.completed_at and c.started_at]
            avg_completion_time = None
            if completed_tasks:
                total_time = sum((c.completed_at - c.started_at).total_seconds() for c in completed_tasks)
                avg_completion_time = total_time / len(completed_tasks)
            
            admin_task = AdminTaskOut(
                id=task.id,
                name=task.name,
                description=task.description,
                type=task.type,
                reward_type=task.reward_type,
                reward_value=task.reward_value,
                target_value=task.target_value,
                is_active=task.is_active,
                created_at=task.created_at,
                platform_url=task.platform_url,
                platform_id=task.platform_id,
                verify_key=task.verify_key,
                max_verification_attempts=task.max_verification_attempts,
                verification_cooldown=task.verification_cooldown,
                cycle_length=task.cycle_length,
                daily_rewards=task.daily_rewards,
                cycle_bonus_reward=task.cycle_bonus_reward,
                reset_streak_after_hours=task.reset_streak_after_hours,
                total_cycle_reward=task.total_cycle_reward,
                total_completions=total_completions,
                completed_count=completed_count,
                pending_count=pending_count,
                failed_count=failed_count,
                avg_completion_time=avg_completion_time
            )
            admin_tasks.append(admin_task)
        
        return admin_tasks
        
    except Exception as e:
        logger.error(f"Error fetching admin tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/tasks/", response_model=TaskOut)
async def create_task(
    task_data: TaskCreate,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
) -> TaskOut:
    """Create a new task (Admin only)"""
    try:
        # Create task with provided data
        task_dict = task_data.dict()
        
        # Calculate total cycle reward for daily tasks
        if task_dict.get('type') == TaskType.DAILY_CHECKIN and task_dict.get('daily_rewards'):
            try:
                daily_rewards_list = json.loads(task_dict['daily_rewards'])
                total_cycle_reward = sum(daily_rewards_list)
                if task_dict.get('cycle_bonus_reward'):
                    total_cycle_reward += task_dict['cycle_bonus_reward']
                task_dict['total_cycle_reward'] = total_cycle_reward
            except (json.JSONDecodeError, TypeError):
                task_dict['total_cycle_reward'] = task_dict.get('reward_value', 0)
        
        new_task = Task(**task_dict)
        db.add(new_task)
        await db.commit()
        await db.refresh(new_task)
        
        logger.info(f"Task created successfully: {new_task.id}")
        return TaskOut.from_orm(new_task)
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Error creating task: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create task: {str(e)}")

@router.put("/admin/tasks/{task_id}", response_model=TaskOut)
async def update_task(
    task_id: int,
    task_update: TaskCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update an existing task"""
    try:
        stmt = select(Task).where(Task.id == task_id)
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Update task fields
        update_data = task_update.dict(exclude_unset=True)
        
        # Recalculate total cycle reward for daily tasks
        if update_data.get('type') == TaskType.DAILY_CHECKIN and update_data.get('daily_rewards'):
            try:
                daily_rewards_list = json.loads(update_data['daily_rewards'])
                total_cycle_reward = sum(daily_rewards_list)
                if update_data.get('cycle_bonus_reward'):
                    total_cycle_reward += update_data['cycle_bonus_reward']
                update_data['total_cycle_reward'] = total_cycle_reward
            except (json.JSONDecodeError, TypeError):
                update_data['total_cycle_reward'] = update_data.get('reward_value', 0)
        
        for key, value in update_data.items():
            setattr(task, key, value)
        
        await db.commit()
        await db.refresh(task)
        
        return TaskOut.from_orm(task)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/admin/tasks/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_task(
    task_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a task"""
    try:
        stmt = select(Task).where(Task.id == task_id)
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        await db.delete(task)
        await db.commit()
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/tasks/{task_id}/toggle", response_model=TaskOut)
async def toggle_task_status(
    task_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Toggle task active status"""
    try:
        stmt = select(Task).where(Task.id == task_id)
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        task.is_active = not task.is_active
        await db.commit()
        await db.refresh(task)
        
        return TaskOut.from_orm(task)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error toggling task status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/tasks/stats", response_model=Dict)
async def get_task_stats(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get task statistics for admin dashboard"""
    try:
        # Total tasks
        total_tasks_stmt = select(func.count(Task.id))
        total_tasks_result = await db.execute(total_tasks_stmt)
        total_tasks = total_tasks_result.scalar() or 0
        
        # Active tasks
        active_tasks_stmt = select(func.count(Task.id)).where(Task.is_active == True)
        active_tasks_result = await db.execute(active_tasks_stmt)
        active_tasks = active_tasks_result.scalar() or 0
        
        # Total completions
        total_completions_stmt = select(func.count(TaskCompletion.id))
        total_completions_result = await db.execute(total_completions_stmt)
        total_completions = total_completions_result.scalar() or 0
        
        # Completed tasks
        completed_tasks_stmt = select(func.count(TaskCompletion.id)).where(
            TaskCompletion.status == TaskStatus.COMPLETED
        )
        completed_tasks_result = await db.execute(completed_tasks_stmt)
        completed_tasks = completed_tasks_result.scalar() or 0
        
        # Pending tasks
        pending_tasks_stmt = select(func.count(TaskCompletion.id)).where(
            TaskCompletion.status == TaskStatus.PENDING
        )
        pending_tasks_result = await db.execute(pending_tasks_stmt)
        pending_tasks = pending_tasks_result.scalar() or 0
        
        # Task type distribution
        task_type_stmt = select(
            Task.type,
            func.count(Task.id).label("count")
        ).group_by(Task.type)
        task_type_result = await db.execute(task_type_stmt)
        task_type_distribution = [
            {"type": task_type.value, "count": count}
            for task_type, count in task_type_result.all()
        ]
        
        return {
            "total_tasks": total_tasks,
            "active_tasks": active_tasks,
            "total_completions": total_completions,
            "completed_tasks": completed_tasks,
            "pending_tasks": pending_tasks,
            "completion_rate": round((completed_tasks / total_completions * 100), 2) if total_completions > 0 else 0,
            "task_type_distribution": task_type_distribution
        }
        
    except Exception as e:
        logger.error(f"Error getting task stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Card Management Admin Routes (moved from card_router.py)
@router.get("/admin/cards/catalog", response_model=List[Dict])
async def get_card_catalog(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all cards in the catalog for admin management"""
    try:
        stmt = select(CardCatalog).order_by(CardCatalog.rarity, CardCatalog.name)
        result = await db.execute(stmt)
        cards = result.scalars().all()
        
        catalog_data = []
        for card in cards:
            catalog_data.append({
                "id": card.id,
                "name": card.name,
                "description": card.description,
                "rarity": card.rarity,
                "max_level": card.max_level,
                "level_profits": card.level_profits,
                "level_costs": card.level_costs,
                "image_url": card.image_url,
                "level_images": card.level_images,
                "created_at": card.created_at.isoformat()
            })
        
        return catalog_data
        
    except Exception as e:
        logger.error(f"Error fetching card catalog: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/cards/users", response_model=List[Dict])
async def get_user_cards_overview(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get overview of all user cards for admin"""
    try:
        stmt = (
            select(UserCard, User.username, CardCatalog.name)
            .join(User, UserCard.user_id == User.id)
            .join(CardCatalog, UserCard.card_catalog_id == CardCatalog.id)
            .order_by(UserCard.acquired_at.desc())
        )
        result = await db.execute(stmt)
        user_cards = result.all()
        
        cards_data = []
        for user_card, username, card_name in user_cards:
            cards_data.append({
                "id": user_card.id,
                "user_id": user_card.user_id,
                "username": username,
                "card_name": card_name,
                "level": user_card.level,
                "current_hourly_profit": user_card.current_hourly_profit,
                "next_upgrade_cost": user_card.next_upgrade_cost,
                "last_claim_at": user_card.last_claim_at.isoformat(),
                "acquired_at": user_card.acquired_at.isoformat()
            })
        
        return cards_data
        
    except Exception as e:
        logger.error(f"Error fetching user cards overview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/cards/grant", response_model=UserCardOut)
async def grant_card_to_user(
    grant_request: UserCardCreate,
    user_id: int,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Grant a card to a user (Admin only)"""
    try:
        # Check if user exists
        user_stmt = select(User).where(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Check if card catalog exists
        card_stmt = select(CardCatalog).where(CardCatalog.id == grant_request.card_catalog_id)
        card_result = await db.execute(card_stmt)
        card_catalog = card_result.scalar_one_or_none()
        
        if not card_catalog:
            raise HTTPException(status_code=404, detail="Card not found in catalog")
        
        # Check if user already owns this card
        existing_stmt = select(UserCard).where(
            and_(
                UserCard.user_id == user_id,
                UserCard.card_catalog_id == grant_request.card_catalog_id
            )
        )
        existing_result = await db.execute(existing_stmt)
        existing_card = existing_result.scalar_one_or_none()
        
        if existing_card:
            raise HTTPException(status_code=400, detail="User already owns this card")
        
        # Create new user card
        new_user_card = UserCard(
            user_id=user_id,
            card_catalog_id=grant_request.card_catalog_id,
            level=grant_request.level or 1,
            last_claim_at=datetime.utcnow(),
            acquired_at=datetime.utcnow()
        )
        
        db.add(new_user_card)
        await db.commit()
        await db.refresh(new_user_card)
        
        # Create transaction record
        transaction = Transaction(
            user_id=user_id,
            type=TransactionType.card_purchase,
            amount=0.0,  # Admin granted, no cost
            description=f"Admin granted card: {card_catalog.name}",
            balance_after=user.wallet_balance,
            status="completed"
        )
        db.add(transaction)
        await db.commit()
        
        return UserCardOut.from_orm(new_user_card)
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error granting card to user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/cards/stats", response_model=Dict)
async def get_card_stats(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get card system statistics for admin dashboard"""
    try:
        # Total cards in catalog
        total_catalog_stmt = select(func.count(CardCatalog.id))
        total_catalog_result = await db.execute(total_catalog_stmt)
        total_catalog_cards = total_catalog_result.scalar() or 0
        
        # Total user cards
        total_user_cards_stmt = select(func.count(UserCard.id))
        total_user_cards_result = await db.execute(total_user_cards_stmt)
        total_user_cards = total_user_cards_result.scalar() or 0
        
        # Users with cards
        users_with_cards_stmt = select(func.count(func.distinct(UserCard.user_id)))
        users_with_cards_result = await db.execute(users_with_cards_stmt)
        users_with_cards = users_with_cards_result.scalar() or 0
        
        # Card rarity distribution
        rarity_stmt = select(
            CardCatalog.rarity,
            func.count(CardCatalog.id).label("count")
        ).group_by(CardCatalog.rarity)
        rarity_result = await db.execute(rarity_stmt)
        rarity_distribution = [
            {"rarity": rarity, "count": count}
            for rarity, count in rarity_result.all()
        ]
        
        # Total passive income rate - Simplified approach without complex JSON extraction
        # For now, let's just return 0 and implement proper calculation later
        total_passive_income = 0.0
        
        return {
            "total_catalog_cards": total_catalog_cards,
            "total_user_cards": total_user_cards,
            "users_with_cards": users_with_cards,
            "avg_cards_per_user": round(total_user_cards / users_with_cards, 2) if users_with_cards > 0 else 0,
            "rarity_distribution": rarity_distribution,
            "total_passive_income_rate": round(total_passive_income, 2)
        }
        
    except Exception as e:
        logger.error(f"Error getting card stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# --- Security & Audit Admin Routes ---

@router.get("/admin/security/logs")
async def get_security_logs(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100,
    action_type: Optional[str] = None,
    risk_level: Optional[str] = None,
    user_id: Optional[int] = None,
    days: int = 7
):
    """Get security logs with filtering"""
    try:
        # Build query
        query = select(SecurityLog).options(selectinload(SecurityLog.user))
        
        # Apply filters
        if action_type:
            query = query.filter(SecurityLog.action_type == action_type)
        if risk_level:
            query = query.filter(SecurityLog.risk_level == risk_level)
        if user_id:
            query = query.filter(SecurityLog.user_id == user_id)
            
        # Filter by date range
        start_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(SecurityLog.created_at >= start_date)
        
        # Order by most recent first
        query = query.order_by(desc(SecurityLog.created_at))
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        logs = result.scalars().all()
        
        # Convert to dict format
        logs_data = []
        for log in logs:
            logs_data.append({
                "id": log.id,
                "action_type": log.action_type,
                "user_id": log.user_id,
                "username": log.user.username if log.user else None,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "status": log.status,
                "risk_level": log.risk_level,
                "details": log.details,
                "location_info": log.location_info,
                "attempt_count": log.attempt_count,
                "is_blocked": log.is_blocked,
                "blocked_until": log.blocked_until.isoformat() if log.blocked_until else None,
                "created_at": log.created_at.isoformat()
            })
        
        return logs_data
        
    except Exception as e:
        logger.error(f"Error getting security logs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/security/stats")
async def get_security_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 7
):
    """Get security statistics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Total security events
        total_events_stmt = select(func.count(SecurityLog.id)).filter(
            SecurityLog.created_at >= start_date
        )
        total_events_result = await db.execute(total_events_stmt)
        total_events = total_events_result.scalar() or 0
        
        # High risk events
        high_risk_stmt = select(func.count(SecurityLog.id)).filter(
            SecurityLog.created_at >= start_date,
            SecurityLog.risk_level == "high"
        )
        high_risk_result = await db.execute(high_risk_stmt)
        high_risk_events = high_risk_result.scalar() or 0
        
        # Blocked IPs
        blocked_ips_stmt = select(func.count(func.distinct(SecurityLog.ip_address))).filter(
            SecurityLog.is_blocked == True,
            SecurityLog.blocked_until > datetime.utcnow()
        )
        blocked_ips_result = await db.execute(blocked_ips_stmt)
        blocked_ips = blocked_ips_result.scalar() or 0
        
        # Failed login attempts
        failed_logins_stmt = select(func.count(SecurityLog.id)).filter(
            SecurityLog.created_at >= start_date,
            SecurityLog.action_type == "login",
            SecurityLog.status == "failure"
        )
        failed_logins_result = await db.execute(failed_logins_stmt)
        failed_logins = failed_logins_result.scalar() or 0
        
        # Risk level distribution
        risk_distribution_stmt = select(
            SecurityLog.risk_level,
            func.count(SecurityLog.id).label("count")
        ).filter(
            SecurityLog.created_at >= start_date
        ).group_by(SecurityLog.risk_level)
        risk_distribution_result = await db.execute(risk_distribution_stmt)
        risk_distribution = [
            {"risk_level": risk, "count": count}
            for risk, count in risk_distribution_result.all()
        ]
        
        return {
            "total_events": total_events,
            "high_risk_events": high_risk_events,
            "blocked_ips": blocked_ips,
            "failed_logins": failed_logins,
            "risk_distribution": risk_distribution
        }
        
    except Exception as e:
        logger.error(f"Error getting security stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/security/blocked-tokens")
async def get_blocked_tokens(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100
):
    """Get list of revoked tokens"""
    try:
        query = select(TokenBlacklist).options(selectinload(TokenBlacklist.user))
        query = query.order_by(desc(TokenBlacklist.revoked_at))
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        tokens = result.scalars().all()
        
        tokens_data = []
        for token in tokens:
            tokens_data.append({
                "id": token.id,
                "user_id": token.user_id,
                "username": token.user.username if token.user else None,
                "revocation_reason": token.revocation_reason,
                "revoked_at": token.revoked_at.isoformat(),
                "expires_at": token.expires_at.isoformat(),
                "revoked_by_ip": token.revoked_by_ip
            })
        
        return tokens_data
        
    except Exception as e:
        logger.error(f"Error getting blocked tokens: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# --- System Configuration Admin Routes ---

@router.get("/admin/system/configs")
async def get_all_system_configs(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all system configurations"""
    try:
        stmt = select(SystemConfig).order_by(SystemConfig.key)
        result = await db.execute(stmt)
        configs = result.scalars().all()
        
        configs_data = []
        for config in configs:
            configs_data.append({
                "id": config.id,
                "key": config.key,
                "value": config.value,
                "description": config.description,
                "updated_at": config.updated_at.isoformat()
            })
        
        return configs_data
        
    except Exception as e:
        logger.error(f"Error getting system configs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/system/configs")
async def create_system_config(
    config_data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Create a new system configuration"""
    try:
        # Check if key already exists
        existing_stmt = select(SystemConfig).filter(SystemConfig.key == config_data["key"])
        existing_result = await db.execute(existing_stmt)
        existing_config = existing_result.scalar_one_or_none()
        
        if existing_config:
            raise HTTPException(status_code=400, detail="Configuration key already exists")
        
        new_config = SystemConfig(
            key=config_data["key"],
            value=config_data["value"],
            description=config_data.get("description", "")
        )
        
        db.add(new_config)
        await db.commit()
        await db.refresh(new_config)
        
        return {
            "id": new_config.id,
            "key": new_config.key,
            "value": new_config.value,
            "description": new_config.description,
            "updated_at": new_config.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error creating system config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/admin/system/configs/{config_id}")
async def update_system_config(
    config_id: int,
    config_data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update a system configuration"""
    try:
        stmt = select(SystemConfig).filter(SystemConfig.id == config_id)
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        config.value = config_data["value"]
        if "description" in config_data:
            config.description = config_data["description"]
        config.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(config)
        
        return {
            "id": config.id,
            "key": config.key,
            "value": config.value,
            "description": config.description,
            "updated_at": config.updated_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating system config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/admin/system/configs/{config_id}")
async def delete_system_config(
    config_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a system configuration"""
    try:
        stmt = select(SystemConfig).filter(SystemConfig.id == config_id)
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        await db.delete(config)
        await db.commit()
        
        return {"message": "Configuration deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting system config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# --- Chat Management Admin Routes ---

@router.get("/admin/chat/messages")
async def get_chat_messages(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100,
    is_public: Optional[bool] = None,
    sender_id: Optional[int] = None,
    days: int = 7
):
    """Get chat messages with filtering"""
    try:
        query = select(ChatMessage).options(
            selectinload(ChatMessage.sender),
            selectinload(ChatMessage.receiver)
        )
        
        # Apply filters
        if is_public is not None:
            query = query.filter(ChatMessage.is_public == is_public)
        if sender_id:
            query = query.filter(ChatMessage.sender_id == sender_id)
            
        # Filter by date range
        start_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(ChatMessage.created_at >= start_date)
        
        # Order by most recent first
        query = query.order_by(desc(ChatMessage.created_at))
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        messages = result.scalars().all()
        
        messages_data = []
        for message in messages:
            messages_data.append({
                "id": message.id,
                "sender_id": message.sender_id,
                "sender_username": message.sender.username if message.sender else None,
                "receiver_id": message.receiver_id,
                "receiver_username": message.receiver.username if message.receiver else None,
                "content": message.content,
                "is_public": message.is_public,
                "is_read": message.is_read,
                "conversation_id": message.conversation_id,
                "created_at": message.created_at.isoformat()
            })
        
        return messages_data
        
    except Exception as e:
        logger.error(f"Error getting chat messages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/admin/chat/messages/{message_id}")
async def delete_chat_message(
    message_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a chat message"""
    try:
        stmt = select(ChatMessage).filter(ChatMessage.id == message_id)
        result = await db.execute(stmt)
        message = result.scalar_one_or_none()
        
        if not message:
            raise HTTPException(status_code=404, detail="Message not found")
        
        await db.delete(message)
        await db.commit()
        
        return {"message": "Chat message deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting chat message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/chat/stats")
async def get_chat_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 7
):
    """Get chat system statistics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Total messages
        total_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.created_at >= start_date
        )
        total_messages_result = await db.execute(total_messages_stmt)
        total_messages = total_messages_result.scalar() or 0
        
        # Public messages
        public_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.created_at >= start_date,
            ChatMessage.is_public == True
        )
        public_messages_result = await db.execute(public_messages_stmt)
        public_messages = public_messages_result.scalar() or 0
        
        # Private messages
        private_messages = total_messages - public_messages
        
        # Active conversations
        active_conversations_stmt = select(func.count(func.distinct(ChatMessage.conversation_id))).filter(
            ChatMessage.created_at >= start_date,
            ChatMessage.conversation_id.isnot(None)
        )
        active_conversations_result = await db.execute(active_conversations_stmt)
        active_conversations = active_conversations_result.scalar() or 0
        
        # Online users
        online_users_stmt = select(func.count(OnlineUser.id)).filter(
            OnlineUser.last_active_at >= datetime.utcnow() - timedelta(minutes=5)
        )
        online_users_result = await db.execute(online_users_stmt)
        online_users = online_users_result.scalar() or 0
        
        return {
            "total_messages": total_messages,
            "public_messages": public_messages,
            "private_messages": private_messages,
            "active_conversations": active_conversations,
            "online_users": online_users
        }
        
    except Exception as e:
        logger.error(f"Error getting chat stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# --- Enhanced Transaction Analytics ---

@router.get("/admin/transactions/analytics")
async def get_transaction_analytics(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get detailed transaction analytics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Revenue by transaction type
        revenue_by_type_stmt = select(
            Transaction.type,
            func.sum(Transaction.amount).label("total_amount"),
            func.count(Transaction.id).label("count")
        ).filter(
            Transaction.created_at >= start_date,
            Transaction.amount > 0  # Only positive amounts (revenue)
        ).group_by(Transaction.type)
        
        revenue_by_type_result = await db.execute(revenue_by_type_stmt)
        revenue_by_type = [
            {
                "type": trans_type.value,
                "total_amount": float(total_amount),
                "count": count
            }
            for trans_type, total_amount, count in revenue_by_type_result.all()
        ]
        
        # Daily revenue trend
        daily_revenue_stmt = select(
            func.date(Transaction.created_at).label("date"),
            func.sum(Transaction.amount).label("revenue")
        ).filter(
            Transaction.created_at >= start_date,
            Transaction.amount > 0
        ).group_by(func.date(Transaction.created_at)).order_by(func.date(Transaction.created_at))
        
        daily_revenue_result = await db.execute(daily_revenue_stmt)
        daily_revenue = [
            {
                "date": date.isoformat(),
                "revenue": float(revenue)
            }
            for date, revenue in daily_revenue_result.all()
        ]
        
        # Top spending users
        top_users_stmt = select(
            User.id,
            User.username,
            func.sum(Transaction.amount).label("total_spent")
        ).join(Transaction, User.id == Transaction.user_id).filter(
            Transaction.created_at >= start_date,
            Transaction.amount < 0  # Negative amounts (spending)
        ).group_by(User.id, User.username).order_by(desc(func.sum(Transaction.amount))).limit(10)
        
        top_users_result = await db.execute(top_users_stmt)
        top_users = [
            {
                "user_id": user_id,
                "username": username,
                "total_spent": abs(float(total_spent))
            }
            for user_id, username, total_spent in top_users_result.all()
        ]
        
        return {
            "revenue_by_type": revenue_by_type,
            "daily_revenue": daily_revenue,
            "top_spending_users": top_users
        }
        
    except Exception as e:
        logger.error(f"Error getting transaction analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# --- Online Users Management ---

@router.get("/admin/users/online")
async def get_online_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get list of currently online users"""
    try:
        # Consider users online if they were active in the last 5 minutes
        cutoff_time = datetime.utcnow() - timedelta(minutes=5)
        
        stmt = select(OnlineUser).options(selectinload(OnlineUser.user)).filter(
            OnlineUser.last_active_at >= cutoff_time
        ).order_by(desc(OnlineUser.last_active_at))
        
        result = await db.execute(stmt)
        online_users = result.scalars().all()
        
        users_data = []
        for online_user in online_users:
            users_data.append({
                "user_id": online_user.user_id,
                "username": online_user.user.username if online_user.user else None,
                "last_active_at": online_user.last_active_at.isoformat(),
                "connection_id": online_user.connection_id
            })
        
        return users_data
        
    except Exception as e:
        logger.error(f"Error getting online users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 

# Enhanced User Management Routes - ADD AFTER EXISTING USER ROUTES

@router.get("/admin/users/{user_id}/activity")
async def get_user_activity(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get user activity timeline"""
    try:
        # Check if user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Get recent transactions
        transactions_stmt = select(Transaction).filter(
            Transaction.user_id == user_id,
            Transaction.created_at >= start_date
        ).order_by(Transaction.created_at.desc()).limit(50)
        transactions_result = await db.execute(transactions_stmt)
        transactions = transactions_result.scalars().all()
        
        # Get recent task completions
        task_completions_stmt = select(TaskCompletion).options(
            selectinload(TaskCompletion.task)
        ).filter(
            TaskCompletion.user_id == user_id,
            TaskCompletion.started_at >= start_date
        ).order_by(TaskCompletion.started_at.desc()).limit(50)
        task_completions_result = await db.execute(task_completions_stmt)
        task_completions = task_completions_result.scalars().all()
        
        # Get recent chat messages
        chat_messages_stmt = select(ChatMessage).filter(
            ChatMessage.sender_id == user_id,
            ChatMessage.created_at >= start_date
        ).order_by(ChatMessage.created_at.desc()).limit(50)
        chat_messages_result = await db.execute(chat_messages_stmt)
        chat_messages = chat_messages_result.scalars().all()
        
        # Get recent card activities
        card_activities_stmt = select(UserCard).options(
            selectinload(UserCard.card_catalog)
        ).filter(
            UserCard.user_id == user_id,
            UserCard.acquired_at >= start_date
        ).order_by(UserCard.acquired_at.desc()).limit(50)
        card_activities_result = await db.execute(card_activities_stmt)
        card_activities = card_activities_result.scalars().all()
        
        # Combine all activities into timeline
        activity_timeline = []
        
        # Add transactions
        for transaction in transactions:
            activity_timeline.append({
                "type": "transaction",
                "timestamp": transaction.created_at.isoformat(),
                "description": f"{transaction.type.value}: {transaction.amount}",
                "details": {
                    "amount": transaction.amount,
                    "type": transaction.type.value,
                    "balance_after": transaction.balance_after,
                    "description": transaction.description
                }
            })
        
        # Add task completions
        for completion in task_completions:
            activity_timeline.append({
                "type": "task_completion",
                "timestamp": completion.started_at.isoformat(),
                "description": f"Task: {completion.task.name} - {completion.status.value}",
                "details": {
                    "task_name": completion.task.name,
                    "status": completion.status.value,
                    "reward_amount": completion.reward_amount,
                    "completed_at": completion.completed_at.isoformat() if completion.completed_at else None
                }
            })
        
        # Add chat messages
        for message in chat_messages:
            activity_timeline.append({
                "type": "chat_message",
                "timestamp": message.created_at.isoformat(),
                "description": f"Chat message: {'Public' if message.is_public else 'Private'}",
                "details": {
                    "content": message.content[:100] + "..." if len(message.content) > 100 else message.content,
                    "is_public": message.is_public,
                    "receiver_id": message.receiver_id
                }
            })
        
        # Add card activities
        for card in card_activities:
            activity_timeline.append({
                "type": "card_activity",
                "timestamp": card.acquired_at.isoformat(),
                "description": f"Acquired card: {card.card_catalog.name}",
                "details": {
                    "card_name": card.card_catalog.name,
                    "level": card.level,
                    "current_profit": card.current_hourly_profit
                }
            })
        
        # Sort timeline by timestamp (most recent first)
        activity_timeline.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return {
            "user_id": user_id,
            "username": user.username,
            "period_days": days,
            "total_activities": len(activity_timeline),
            "activities": activity_timeline[:100]  # Limit to 100 most recent
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user activity: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/users/{user_id}/chat-history")
async def get_user_chat_history(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    limit: int = 100,
    is_public: Optional[bool] = None
):
    """Get user chat message history"""
    try:
        # Check if user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Build query for messages
        query = select(ChatMessage).options(
            selectinload(ChatMessage.receiver)
        ).filter(ChatMessage.sender_id == user_id)
        
        if is_public is not None:
            query = query.filter(ChatMessage.is_public == is_public)
        
        query = query.order_by(ChatMessage.created_at.desc()).limit(limit)
        
        result = await db.execute(query)
        messages = result.scalars().all()
        
        # Get message statistics
        total_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.sender_id == user_id
        )
        total_messages_result = await db.execute(total_messages_stmt)
        total_messages = total_messages_result.scalar() or 0
        
        public_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.sender_id == user_id,
            ChatMessage.is_public == True
        )
        public_messages_result = await db.execute(public_messages_stmt)
        public_messages = public_messages_result.scalar() or 0
        
        private_messages = total_messages - public_messages
        
        # Format messages
        formatted_messages = []
        for message in messages:
            formatted_messages.append({
                "id": message.id,
                "content": message.content,
                "is_public": message.is_public,
                "receiver_id": message.receiver_id,
                "receiver_username": message.receiver.username if message.receiver else None,
                "created_at": message.created_at.isoformat(),
                "is_read": message.is_read
            })
        
        return {
            "user_id": user_id,
            "username": user.username,
            "statistics": {
                "total_messages": total_messages,
                "public_messages": public_messages,
                "private_messages": private_messages
            },
            "messages": formatted_messages
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/users/{user_id}/task-history")
async def get_user_task_history(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    limit: int = 100,
    status: Optional[str] = None
):
    """Get user task completion history"""
    try:
        # Check if user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Build query for task completions
        query = select(TaskCompletion).options(
            selectinload(TaskCompletion.task)
        ).filter(TaskCompletion.user_id == user_id)
        
        if status:
            try:
                task_status = TaskStatus(status)
                query = query.filter(TaskCompletion.status == task_status)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid status value")
        
        query = query.order_by(TaskCompletion.started_at.desc()).limit(limit)
        
        result = await db.execute(query)
        completions = result.scalars().all()
        
        # Get task statistics
        total_completions_stmt = select(func.count(TaskCompletion.id)).filter(
            TaskCompletion.user_id == user_id
        )
        total_completions_result = await db.execute(total_completions_stmt)
        total_completions = total_completions_result.scalar() or 0
        
        completed_tasks_stmt = select(func.count(TaskCompletion.id)).filter(
            TaskCompletion.user_id == user_id,
            TaskCompletion.status == TaskStatus.COMPLETED
        )
        completed_tasks_result = await db.execute(completed_tasks_stmt)
        completed_tasks = completed_tasks_result.scalar() or 0
        
        total_rewards_stmt = select(func.sum(TaskCompletion.reward_amount)).filter(
            TaskCompletion.user_id == user_id,
            TaskCompletion.status == TaskStatus.COMPLETED
        )
        total_rewards_result = await db.execute(total_rewards_stmt)
        total_rewards = total_rewards_result.scalar() or 0
        
        # Format completions
        formatted_completions = []
        for completion in completions:
            formatted_completions.append({
                "id": completion.id,
                "task_id": completion.task_id,
                "task_name": completion.task.name,
                "task_type": completion.task.type.value,
                "status": completion.status.value,
                "reward_amount": completion.reward_amount,
                "started_at": completion.started_at.isoformat(),
                "completed_at": completion.completed_at.isoformat() if completion.completed_at else None,
                "claimed_at": completion.claimed_at.isoformat() if completion.claimed_at else None,
                "is_claimed": completion.is_claimed
            })
        
        return {
            "user_id": user_id,
            "username": user.username,
            "statistics": {
                "total_completions": total_completions,
                "completed_tasks": completed_tasks,
                "completion_rate": round((completed_tasks / total_completions * 100), 2) if total_completions > 0 else 0,
                "total_rewards": float(total_rewards)
            },
            "completions": formatted_completions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user task history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/users/{user_id}/card-history")
async def get_user_card_history(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get user card acquisition and upgrade history"""
    try:
        # Check if user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Get user cards
        cards_stmt = select(UserCard).options(
            selectinload(UserCard.card_catalog)
        ).filter(UserCard.user_id == user_id).order_by(UserCard.acquired_at.desc())
        
        cards_result = await db.execute(cards_stmt)
        user_cards = cards_result.scalars().all()
        
        # Get card-related transactions
        card_transactions_stmt = select(Transaction).filter(
            Transaction.user_id == user_id,
            Transaction.type.in_([
                TransactionType.card_purchase,
                TransactionType.card_upgrade,
                TransactionType.card_profit,
                TransactionType.card_profit_all
            ])
        ).order_by(Transaction.created_at.desc())
        
        card_transactions_result = await db.execute(card_transactions_stmt)
        card_transactions = card_transactions_result.scalars().all()
        
        # Calculate statistics
        total_cards = len(user_cards)
        total_spent_on_cards = sum(
            abs(t.amount) for t in card_transactions 
            if t.type in [TransactionType.card_purchase, TransactionType.card_upgrade]
        )
        total_earned_from_cards = sum(
            t.amount for t in card_transactions 
            if t.type in [TransactionType.card_profit, TransactionType.card_profit_all]
        )
        total_hourly_profit = sum(card.current_hourly_profit for card in user_cards)
        
        # Format cards
        formatted_cards = []
        for card in user_cards:
            formatted_cards.append({
                "id": card.id,
                "card_catalog_id": card.card_catalog_id,
                "card_name": card.card_catalog.name,
                "rarity": card.card_catalog.rarity,
                "level": card.level,
                "max_level": card.card_catalog.max_level,
                "current_hourly_profit": card.current_hourly_profit,
                "next_upgrade_cost": card.next_upgrade_cost,
                "acquired_at": card.acquired_at.isoformat(),
                "last_claim_at": card.last_claim_at.isoformat()
            })
        
        # Format transactions
        formatted_transactions = []
        for transaction in card_transactions:
            formatted_transactions.append({
                "id": transaction.id,
                "type": transaction.type.value,
                "amount": transaction.amount,
                "description": transaction.description,
                "created_at": transaction.created_at.isoformat()
            })
        
        return {
            "user_id": user_id,
            "username": user.username,
            "statistics": {
                "total_cards": total_cards,
                "total_spent": float(total_spent_on_cards),
                "total_earned": float(total_earned_from_cards),
                "net_profit": float(total_earned_from_cards - total_spent_on_cards),
                "total_hourly_profit": float(total_hourly_profit)
            },
            "cards": formatted_cards,
            "transactions": formatted_transactions
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user card history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/users/analytics")
async def get_user_analytics(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get comprehensive user analytics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Registration analytics
        daily_registrations_stmt = select(
            func.date(User.created_at).label('date'),
            func.count(User.id).label('count')
        ).filter(
            User.created_at >= start_date
        ).group_by(func.date(User.created_at)).order_by(func.date(User.created_at))
        
        daily_registrations_result = await db.execute(daily_registrations_stmt)
        daily_registrations = [
            {"date": str(date), "registrations": count}
            for date, count in daily_registrations_result.all()
        ]
        
        # User role distribution
        role_distribution_stmt = select(
            User.role,
            func.count(User.id).label('count')
        ).group_by(User.role)
        
        role_distribution_result = await db.execute(role_distribution_stmt)
        role_distribution = [
            {"role": role.value, "count": count}
            for role, count in role_distribution_result.all()
        ]
        
        # Activity analytics
        active_users_stmt = select(func.count(func.distinct(User.id))).join(
            Transaction, User.id == Transaction.user_id
        ).filter(Transaction.created_at >= start_date)
        
        active_users_result = await db.execute(active_users_stmt)
        active_users = active_users_result.scalar() or 0
        
        # Wallet balance distribution
        wallet_ranges = [
            (0, 10, "0-10"),
            (10, 50, "10-50"),
            (50, 100, "50-100"),
            (100, 500, "100-500"),
            (500, float('inf'), "500+")
        ]
        
        wallet_distribution = []
        for min_val, max_val, label in wallet_ranges:
            if max_val == float('inf'):
                count_stmt = select(func.count(User.id)).filter(User.wallet_balance >= min_val)
            else:
                count_stmt = select(func.count(User.id)).filter(
                    and_(User.wallet_balance >= min_val, User.wallet_balance < max_val)
                )
            
            count_result = await db.execute(count_stmt)
            count = count_result.scalar() or 0
            wallet_distribution.append({"range": label, "count": count})
        
        # Top users by activity
        top_users_stmt = select(
            User.id,
            User.username,
            func.count(Transaction.id).label('transaction_count'),
            func.sum(Transaction.amount).label('total_amount')
        ).join(Transaction, User.id == Transaction.user_id).filter(
            Transaction.created_at >= start_date
        ).group_by(User.id, User.username).order_by(
            func.count(Transaction.id).desc()
        ).limit(10)
        
        top_users_result = await db.execute(top_users_stmt)
        top_users = [
            {
                "user_id": user_id,
                "username": username,
                "transaction_count": count,
                "total_amount": float(amount) if amount else 0
            }
            for user_id, username, count, amount in top_users_result.all()
        ]
        
        return {
            "period_days": days,
            "daily_registrations": daily_registrations,
            "role_distribution": role_distribution,
            "active_users_in_period": active_users,
            "wallet_distribution": wallet_distribution,
            "top_active_users": top_users
        }
        
    except Exception as e:
        logger.error(f"Error getting user analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/users/export")
async def export_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    format: str = "csv",
    filters: Optional[dict] = None
):
    """Export user data to CSV or Excel"""
    try:
        # Build query with filters
        query = select(User).options(
            selectinload(User.vpn_subscriptions),
            selectinload(User.user_cards),
            selectinload(User.transactions)
        )
        
        # Apply filters if provided
        if filters:
            if filters.get("role"):
                query = query.filter(User.role == filters["role"])
            if filters.get("is_active") is not None:
                query = query.filter(User.is_active == filters["is_active"])
            if filters.get("created_after"):
                query = query.filter(User.created_at >= datetime.fromisoformat(filters["created_after"]))
            if filters.get("created_before"):
                query = query.filter(User.created_at <= datetime.fromisoformat(filters["created_before"]))
        
        result = await db.execute(query)
        users = result.scalars().all()
        
        # Prepare export data
        export_data = []
        for user in users:
            export_data.append({
                "ID": user.id,
                "Username": user.username,
                "Email": user.email or "",
                "First Name": user.first_name or "",
                "Last Name": user.last_name or "",
                "Role": user.role.value,
                "Wallet Balance": user.wallet_balance,
                "Created At": user.created_at.isoformat() if user.created_at else "",
                "Last Login": user.last_login.isoformat() if user.last_login else "",
                "Is Active": user.is_active,
                "Telegram ID": user.telegram_id or "",
                "Discount Percent": user.discount_percent,
                "Referral Code": user.referral_code,
                "VPN Subscriptions": len(user.vpn_subscriptions),
                "Cards Owned": len(user.user_cards),
                "Total Transactions": len(user.transactions)
            })
        
        # For now, return the data (in a real implementation, you'd generate and return a file)
        return {
            "format": format,
            "total_users": len(export_data),
            "exported_at": datetime.utcnow().isoformat(),
            "data": export_data[:1000]  # Limit for demo purposes
        }
        
    except Exception as e:
        logger.error(f"Error exporting users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Daily Task Management Admin Routes (consolidated from daily_task_router.py)

@router.get("/admin/daily-tasks/")
async def get_daily_tasks(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all daily tasks with statistics"""
    try:
        # Get daily tasks
        stmt = select(Task).options(
            selectinload(Task.completions),
            selectinload(Task.cycles)
        ).filter(Task.type == TaskType.DAILY_CHECKIN)
        
        result = await db.execute(stmt)
        daily_tasks = result.scalars().all()
        
        tasks_data = []
        for task in daily_tasks:
            # Calculate statistics
            total_users = len(set(c.user_id for c in task.completions))
            active_streaks = len([c for c in task.completions if c.status == TaskStatus.COMPLETED])
            total_cycles = len(task.cycles)
            completed_cycles = len([c for c in task.cycles if c.is_completed])
            
            # Calculate total rewards distributed
            total_rewards = sum(c.reward_amount for c in task.completions if c.is_claimed)
            
            tasks_data.append({
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "reward_value": task.reward_value,
                "cycle_length": task.cycle_length,
                "daily_rewards": task.get_daily_rewards,
                "cycle_bonus_reward": task.cycle_bonus_reward,
                "total_cycle_reward": task.total_cycle_reward,
                "reset_streak_after_hours": task.reset_streak_after_hours,
                "is_active": task.is_active,
                "created_at": task.created_at.isoformat(),
                "statistics": {
                    "total_users": total_users,
                    "active_streaks": active_streaks,
                    "total_cycles": total_cycles,
                    "completed_cycles": completed_cycles,
                    "completion_rate": round((completed_cycles / total_cycles * 100), 2) if total_cycles > 0 else 0,
                    "total_rewards_distributed": float(total_rewards)
                }
            })
        
        return tasks_data
        
    except Exception as e:
        logger.error(f"Error getting daily tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/daily-tasks/{task_id}/cycles")
async def get_daily_task_cycles(
    task_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    limit: int = 100
):
    """Get daily task cycles with user information"""
    try:
        # Verify task exists and is daily task
        task_stmt = select(Task).filter(
            Task.id == task_id,
            Task.type == TaskType.DAILY_CHECKIN
        )
        task_result = await db.execute(task_stmt)
        task = task_result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Daily task not found")
        
        # Get cycles with user information
        cycles_stmt = select(DailyCycle).options(
            selectinload(DailyCycle.user),
            selectinload(DailyCycle.completions)
        ).filter(
            DailyCycle.task_id == task_id
        ).order_by(DailyCycle.created_at.desc()).limit(limit)
        
        cycles_result = await db.execute(cycles_stmt)
        cycles = cycles_result.scalars().all()
        
        cycles_data = []
        for cycle in cycles:
            completed_days = cycle.get_completed_days
            
            cycles_data.append({
                "id": cycle.id,
                "user_id": cycle.user_id,
                "username": cycle.user.username,
                "cycle_number": cycle.cycle_number,
                "start_date": cycle.start_date.isoformat(),
                "end_date": cycle.end_date.isoformat() if cycle.end_date else None,
                "completed_days": completed_days,
                "days_completed": len(completed_days),
                "total_reward": cycle.total_reward,
                "is_completed": cycle.is_completed,
                "created_at": cycle.created_at.isoformat(),
                "progress_percentage": round((len(completed_days) / task.cycle_length * 100), 2)
            })
        
        return {
            "task_id": task_id,
            "task_name": task.name,
            "cycle_length": task.cycle_length,
            "total_cycles": len(cycles_data),
            "cycles": cycles_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting daily task cycles: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/admin/daily-tasks/reset-streak/{task_id}")
async def admin_reset_user_streak(
    task_id: int,
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Reset a user's daily task streak (Admin only)"""
    try:
        # Verify task exists and is daily task
        task_stmt = select(Task).filter(
            Task.id == task_id,
            Task.type == TaskType.DAILY_CHECKIN
        )
        task_result = await db.execute(task_stmt)
        task = task_result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Daily task not found")
        
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get user's daily streak
        streak_stmt = select(DailyTaskStreak).filter(
            DailyTaskStreak.user_id == user_id
        )
        streak_result = await db.execute(streak_stmt)
        streak = streak_result.scalar_one_or_none()
        
        if streak:
            # Reset streak
            streak.current_streak = 0
            streak.current_cycle_day = 1
            streak.last_streak_break = datetime.utcnow()
            
            # Reset any active task completions
            active_completion_stmt = select(TaskCompletion).filter(
                TaskCompletion.task_id == task_id,
                TaskCompletion.user_id == user_id,
                TaskCompletion.status.in_([TaskStatus.ACTIVE, TaskStatus.PENDING])
            )
            active_completion_result = await db.execute(active_completion_stmt)
            active_completions = active_completion_result.scalars().all()
            
            for completion in active_completions:
                completion.status = TaskStatus.FAILED
            
            await db.commit()
            
            return {
                "message": f"Successfully reset streak for user {user.username}",
                "user_id": user_id,
                "task_id": task_id,
                "previous_streak": streak.current_streak,
                "reset_at": datetime.utcnow().isoformat()
            }
        else:
            return {
                "message": f"No streak found for user {user.username}",
                "user_id": user_id,
                "task_id": task_id
            }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error resetting user streak: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/admin/daily-tasks/stats")
async def get_daily_task_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get comprehensive daily task statistics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Total daily tasks
        total_daily_tasks_stmt = select(func.count(Task.id)).filter(
            Task.type == TaskType.DAILY_CHECKIN
        )
        total_daily_tasks_result = await db.execute(total_daily_tasks_stmt)
        total_daily_tasks = total_daily_tasks_result.scalar() or 0
        
        # Active daily tasks
        active_daily_tasks_stmt = select(func.count(Task.id)).filter(
            Task.type == TaskType.DAILY_CHECKIN,
            Task.is_active == True
        )
        active_daily_tasks_result = await db.execute(active_daily_tasks_stmt)
        active_daily_tasks = active_daily_tasks_result.scalar() or 0
        
        # Users with active streaks
        active_streaks_stmt = select(func.count(DailyTaskStreak.id)).filter(
            DailyTaskStreak.current_streak > 0,
            DailyTaskStreak.last_check_in >= start_date
        )
        active_streaks_result = await db.execute(active_streaks_stmt)
        active_streaks = active_streaks_result.scalar() or 0
        
        # Total check-ins in period
        total_checkins_stmt = select(func.count(TaskCompletion.id)).join(
            Task, TaskCompletion.task_id == Task.id
        ).filter(
            Task.type == TaskType.DAILY_CHECKIN,
            TaskCompletion.started_at >= start_date,
            TaskCompletion.status == TaskStatus.COMPLETED
        )
        total_checkins_result = await db.execute(total_checkins_stmt)
        total_checkins = total_checkins_result.scalar() or 0
        
        # Completed cycles in period
        completed_cycles_stmt = select(func.count(DailyCycle.id)).filter(
            DailyCycle.is_completed == True,
            DailyCycle.end_date >= start_date
        )
        completed_cycles_result = await db.execute(completed_cycles_stmt)
        completed_cycles = completed_cycles_result.scalar() or 0
        
        # Total rewards distributed
        total_rewards_stmt = select(func.sum(TaskCompletion.reward_amount)).join(
            Task, TaskCompletion.task_id == Task.id
        ).filter(
            Task.type == TaskType.DAILY_CHECKIN,
            TaskCompletion.is_claimed == True,
            TaskCompletion.claimed_at >= start_date
        )
        total_rewards_result = await db.execute(total_rewards_stmt)
        total_rewards = total_rewards_result.scalar() or 0
        
        # Streak distribution
        streak_distribution_stmt = select(
            func.case(
                (DailyTaskStreak.current_streak == 0, "0"),
                (DailyTaskStreak.current_streak.between(1, 3), "1-3"),
                (DailyTaskStreak.current_streak.between(4, 7), "4-7"),
                (DailyTaskStreak.current_streak.between(8, 14), "8-14"),
                (DailyTaskStreak.current_streak.between(15, 30), "15-30"),
                else_="30+"
            ).label("streak_range"),
            func.count(DailyTaskStreak.id).label("count")
        ).group_by("streak_range")
        
        streak_distribution_result = await db.execute(streak_distribution_stmt)
        streak_distribution = [
            {"range": range_val, "count": count}
            for range_val, count in streak_distribution_result.all()
        ]
        
        # Daily check-in trend
        daily_checkins_stmt = select(
            func.date(TaskCompletion.started_at).label('date'),
            func.count(TaskCompletion.id).label('checkins')
        ).join(Task, TaskCompletion.task_id == Task.id).filter(
            Task.type == TaskType.DAILY_CHECKIN,
            TaskCompletion.started_at >= start_date,
            TaskCompletion.status == TaskStatus.COMPLETED
        ).group_by(func.date(TaskCompletion.started_at)).order_by(func.date(TaskCompletion.started_at))
        
        daily_checkins_result = await db.execute(daily_checkins_stmt)
        daily_checkins = [
            {"date": str(date), "checkins": checkins}
            for date, checkins in daily_checkins_result.all()
        ]
        
        return {
            "period_days": days,
            "total_daily_tasks": total_daily_tasks,
            "active_daily_tasks": active_daily_tasks,
            "users_with_active_streaks": active_streaks,
            "total_checkins": total_checkins,
            "completed_cycles": completed_cycles,
            "total_rewards_distributed": float(total_rewards),
            "streak_distribution": streak_distribution,
            "daily_checkin_trend": daily_checkins
        }
        
    except Exception as e:
        logger.error(f"Error getting daily task stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ======= CONSOLIDATED CARD CATALOG MANAGEMENT ROUTES =======

@router.post("/admin/cards/catalog")
async def create_card_catalog(
    card_data: dict,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new card in catalog"""
    try:
        import json
        
        card = CardCatalog(
            name=card_data["name"],
            description=card_data["description"],
            rarity=card_data["rarity"],
            max_level=card_data["max_level"],
            level_profits_json=json.dumps(card_data["level_profits"]),
            level_costs_json=json.dumps(card_data["level_costs"]),
            image_url=card_data.get("image_url")
        )
        
        db.add(card)
        await db.commit()
        await db.refresh(card)
        
        return {
            "id": card.id,
            "name": card.name,
            "description": card.description,
            "rarity": card.rarity,
            "max_level": card.max_level,
            "level_profits": card.level_profits,
            "level_costs": card.level_costs,
            "image_url": card.image_url,
            "created_at": card.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error creating card: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create card: {str(e)}"
        )

@router.put("/admin/cards/catalog/{card_id}")
async def update_card_catalog(
    card_id: int,
    card_data: dict,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Update an existing card in catalog"""
    try:
        import json
        
        stmt = select(CardCatalog).where(CardCatalog.id == card_id)
        result = await db.execute(stmt)
        card = result.scalar_one_or_none()
        
        if not card:
            raise HTTPException(status_code=404, detail="Card not found")
        
        card.name = card_data["name"]
        card.description = card_data["description"]
        card.rarity = card_data["rarity"]
        card.max_level = card_data["max_level"]
        card.level_profits_json = json.dumps(card_data["level_profits"])
        card.level_costs_json = json.dumps(card_data["level_costs"])
        card.image_url = card_data.get("image_url")
        
        await db.commit()
        await db.refresh(card)
        
        return {
            "id": card.id,
            "name": card.name,
            "description": card.description,
            "rarity": card.rarity,
            "max_level": card.max_level,
            "level_profits": card.level_profits,
            "level_costs": card.level_costs,
            "image_url": card.image_url,
            "created_at": card.created_at.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error updating card: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update card: {str(e)}"
        )

@router.delete("/admin/cards/catalog/{card_id}")
async def delete_card_catalog(
    card_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Delete a card from catalog"""
    try:
        stmt = select(CardCatalog).where(CardCatalog.id == card_id)
        result = await db.execute(stmt)
        card = result.scalar_one_or_none()
        
        if not card:
            raise HTTPException(status_code=404, detail="Card not found")
        
        await db.delete(card)
        await db.commit()
        
        return {"message": "Card deleted successfully"}
        
    except Exception as e:
        logger.error(f"Error deleting card: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete card: {str(e)}"
        )

# ======= REFERRAL MANAGEMENT ROUTES =======

@router.get("/admin/referrals/")
async def get_admin_referrals(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get all referral information for admin management"""
    try:
        # Get users with their referral data
        stmt = select(User).options(selectinload(User.referrals)).order_by(User.created_at.desc())
        result = await db.execute(stmt)
        users = result.scalars().all()
        
        referral_data = []
        
        for user in users:
            # Get referrer information if user was referred
            referrer = None
            if user.referred_by:
                stmt_referrer = select(User).where(User.id == user.referred_by)
                result_referrer = await db.execute(stmt_referrer)
                referrer = result_referrer.scalar_one_or_none()
            
            # Count direct referrals
            direct_referrals = len(user.referrals)
            
            # Calculate referral commissions received
            stmt_commissions = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == user.id,
                Transaction.type == TransactionType.referral_commission
            )
            result_commissions = await db.execute(stmt_commissions)
            total_commissions = result_commissions.scalar_one() or 0
            
            user_data = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "referral_code": user.referral_code,
                "referred_by_id": user.referred_by,
                "referred_by_username": referrer.username if referrer else None,
                "direct_referrals": direct_referrals,
                "total_commissions_earned": float(total_commissions),
                "created_at": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "is_active": user.is_active
            }
            
            referral_data.append(user_data)
        
        return referral_data
        
    except Exception as e:
        logger.error(f"Error getting admin referrals: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get referral data"
        )

@router.get("/admin/referrals/{user_id}/tree")
async def get_referral_tree(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get referral tree for a specific user"""
    try:
        # Get the root user
        stmt_root = select(User).where(User.id == user_id)
        result_root = await db.execute(stmt_root)
        root_user = result_root.scalar_one_or_none()
        
        if not root_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get all direct referrals
        stmt_referrals = select(User).where(User.referred_by == user_id).order_by(User.created_at.desc())
        result_referrals = await db.execute(stmt_referrals)
        direct_referrals = result_referrals.scalars().all()
        
        referral_tree = {
            "user_id": root_user.id,
            "username": root_user.username,
            "referral_code": root_user.referral_code,
            "direct_referrals": []
        }
        
        for referral in direct_referrals:
            # Get referral's referrals (second level)
            stmt_second_level = select(User).where(User.referred_by == referral.id)
            result_second_level = await db.execute(stmt_second_level)
            second_level_referrals = result_second_level.scalars().all()
            
            referral_data = {
                "user_id": referral.id,
                "username": referral.username,
                "email": referral.email,
                "created_at": referral.created_at.isoformat(),
                "is_active": referral.is_active,
                "second_level_count": len(second_level_referrals)
            }
            
            referral_tree["direct_referrals"].append(referral_data)
        
        return referral_tree
        
    except Exception as e:
        logger.error(f"Error getting referral tree: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get referral tree"
        )

@router.get("/admin/referrals/stats")
async def get_referral_stats(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get referral system statistics"""
    try:
        # Total users
        stmt_total_users = select(func.count(User.id))
        result_total_users = await db.execute(stmt_total_users)
        total_users = result_total_users.scalar_one()
        
        # Users with referrals
        stmt_users_with_referrals = select(func.count(User.id)).where(User.referred_by.isnot(None))
        result_users_with_referrals = await db.execute(stmt_users_with_referrals)
        users_with_referrals = result_users_with_referrals.scalar_one()
        
        # Total referral commissions
        stmt_total_commissions = select(func.sum(Transaction.amount)).where(
            Transaction.type == TransactionType.referral_commission
        )
        result_total_commissions = await db.execute(stmt_total_commissions)
        total_commissions = result_total_commissions.scalar_one() or 0
        
        # Top referrers (users with most direct referrals)
        stmt_top_referrers = select(
            User.id, 
            User.username, 
            func.count(User.id.label('referred_user_id')).label('referral_count')
        ).join(
            User.referrals
        ).group_by(User.id, User.username).order_by(
            func.count(User.id.label('referred_user_id')).desc()
        ).limit(10)
        
        result_top_referrers = await db.execute(stmt_top_referrers)
        top_referrers = result_top_referrers.all()
        
        # Calculate referral penetration rate
        referral_rate = (users_with_referrals / total_users * 100) if total_users > 0 else 0
        
        return {
            "total_users": total_users,
            "users_with_referrals": users_with_referrals,
            "referral_penetration_rate": round(referral_rate, 2),
            "total_commissions_paid": float(total_commissions),
            "avg_commission_per_referral": float(total_commissions / users_with_referrals) if users_with_referrals > 0 else 0,
            "top_referrers": [
                {
                    "user_id": user_id,
                    "username": username,
                    "referral_count": referral_count
                }
                for user_id, username, referral_count in top_referrers
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting referral stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get referral statistics"
        )

@router.post("/admin/referrals/{user_id}/commission")
async def grant_referral_commission(
    user_id: int,
    commission_data: dict,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Manually grant referral commission to a user"""
    try:
        # Get user
        stmt_user = select(User).where(User.id == user_id)
        result_user = await db.execute(stmt_user)
        user = result_user.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        commission_amount = float(commission_data["amount"])
        description = commission_data.get("description", "Manual referral commission by admin")
        
        # Update user balance
        user.wallet_balance += commission_amount
        
        # Create transaction record
        transaction = Transaction(
            user_id=user_id,
            type=TransactionType.referral_commission,
            amount=commission_amount,
            description=description,
            balance_after=user.wallet_balance,
            status="completed"
        )
        
        db.add(transaction)
        await db.commit()
        
        return {
            "message": "Referral commission granted successfully",
            "user_id": user_id,
            "amount": commission_amount,
            "new_balance": user.wallet_balance,
            "transaction_id": transaction.id
        }
        
    except Exception as e:
        logger.error(f"Error granting referral commission: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to grant referral commission: {str(e)}"
        )