# schemas.py
from services.marzban_service import MarzbanService
from pydantic import BaseModel, Field, ConfigDict, validator, field_validator, ValidationInfo, model_validator
from typing import Optional, List, ForwardRef, Dict, Any, ClassVar, Annotated, Union
from enum import Enum
from datetime import datetime
from models import Role, TaskStatus, RewardType, TaskType, SocialPlatform, TransactionType
import json
import ipaddress
import re
import html

# Base schemas first
class RoleEnum(str, Enum):
    admin = "admin"
    reseller = "reseller"
    user = "user"

    @classmethod
    def from_str(cls, value: str) -> 'Role':
        try:
            return cls(value.lower())
        except ValueError:
            raise ValueError(f"Invalid role value: {value}")

class BaseModelConfig(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid'
    )

    @field_validator('*')
    @classmethod
    def sanitize_strings(cls, v: Any, info: ValidationInfo) -> Any:
        if isinstance(v, str):
            # Remove HTML tags
            v = re.sub(r'<[^>]*?>', '', v)
            # Escape special characters
            v = html.escape(v)
            # Remove potentially dangerous patterns
            v = re.sub(r'javascript:', '', v, flags=re.IGNORECASE)
            v = re.sub(r'data:', '', v, flags=re.IGNORECASE)
            v = re.sub(r'vbscript:', '', v, flags=re.IGNORECASE)
            # Remove null bytes
            v = v.replace('\x00', '')
            # Normalize whitespace
            v = ' '.join(v.split())
        elif isinstance(v, dict):
            return {k: cls.sanitize_strings(val, info) for k, val in v.items()}
        elif isinstance(v, list):
            return [cls.sanitize_strings(item, info) for item in v]
        return v

class UserBase(BaseModelConfig):
    username: str
    telegram_id: Optional[int] = None
    role: Optional[RoleEnum] = None
    wallet_balance: Optional[float] = 0.0
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    telegram_photo_url: Optional[str] = None
    discount_percent: Optional[float] = 0.0

# Task schemas
class TaskBase(BaseModelConfig):
    name: str = Field(..., min_length=3, max_length=100)
    description: str = Field(..., min_length=10, max_length=500)
    type: TaskType
    reward_type: RewardType
    reward_value: float = Field(..., ge=0, le=1000)
    target_value: int = Field(1, ge=1, le=1000)
    is_active: bool = True
    # Daily task fields
    cycle_length: Optional[int] = Field(7, ge=1, le=30)
    daily_rewards: Optional[List[float]] = Field(None, max_length=30)
    cycle_bonus_reward: Optional[float] = Field(None, ge=0, le=1000)
    reset_streak_after_hours: Optional[int] = Field(48, ge=1, le=168)
    # Platform fields
    platform_url: Optional[str] = Field(None, max_length=500, pattern=r'^https?://.+')
    platform_id: Optional[str] = Field(None, max_length=100)
    verify_key: Optional[str] = Field(None, max_length=100)
    # Verification fields
    max_verification_attempts: Optional[int] = Field(3, ge=1, le=10)
    verification_cooldown: Optional[int] = Field(60, ge=30, le=3600)  # 30 seconds to 1 hour
    required_duration: Optional[int] = Field(None, ge=0)

    @field_validator('daily_rewards')
    @classmethod
    def validate_daily_rewards(cls, v: Optional[List[float]], info: ValidationInfo) -> Optional[List[float]]:
        if v is not None:
            if not isinstance(v, list):
                raise ValueError('Daily rewards must be a list of numbers')
            if len(v) != info.data.get('cycle_length', 7):
                raise ValueError(f'Daily rewards list length must match cycle_length ({info.data.get("cycle_length", 7)})')
            for reward in v:
                if not isinstance(reward, (int, float)) or reward < 0 or reward > 1000:
                    raise ValueError('Daily rewards must be non-negative numbers between 0 and 1000')
        return v

class TaskCreate(TaskBase):
    model_config = ConfigDict(extra='ignore')

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: Optional[str], info: ValidationInfo) -> Optional[str]:
        task_type = info.data.get('type')
        if task_type in [TaskType.YOUTUBE_VIEW, TaskType.TELEGRAM_CHANNEL, TaskType.INSTAGRAM_FOLLOW] and not v:
            raise ValueError(f'Platform URL is required for {task_type} tasks')
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Platform URL must start with http:// or https://')
        return v

    @field_validator('platform_id')
    @classmethod
    def validate_platform_id(cls, v: Optional[str], info: ValidationInfo) -> Optional[str]:
        task_type = info.data.get('type')
        if task_type in [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW] and not v:
            raise ValueError(f'Platform ID is required for {task_type} tasks')
        return v

    @field_validator('verify_key')
    @classmethod
    def validate_verify_key(cls, v: Optional[str], info: ValidationInfo) -> Optional[str]:
        task_type = info.data.get('type')
        if task_type in [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW, TaskType.YOUTUBE_VIEW] and not v:
            raise ValueError(f'Verification key is required for {task_type} tasks')
        return v

    @field_validator('max_verification_attempts')
    @classmethod
    def validate_max_attempts(cls, v: Optional[int], info: ValidationInfo) -> Optional[int]:
        if v is not None and (v < 1 or v > 10):
            raise ValueError("Max verification attempts must be between 1 and 10")
        return v

    @field_validator('verification_cooldown')
    @classmethod
    def validate_cooldown(cls, v: Optional[int], info: ValidationInfo) -> Optional[int]:
        if v is not None and (v < 30 or v > 3600):
            raise ValueError("Verification cooldown must be between 30 seconds and 1 hour")
        return v

class TaskOut(TaskBase):
    id: int
    created_at: datetime
    # Task status fields
    status: TaskStatus = TaskStatus.ACTIVE
    is_claimed: bool = False
    completion_id: Optional[int] = None
    started_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    last_verified_at: Optional[datetime] = None
    current_progress: Optional[int] = None
    verification_attempts: Optional[int] = None
    next_verification_time: Optional[datetime] = None
    verification_method: Optional[str] = None
    
    model_config = ConfigDict(
        from_attributes=True,
        extra='allow'  # Allow extra fields
    )

    @field_validator('daily_rewards', mode='before')
    def parse_daily_rewards(cls, v: Optional[str]) -> Optional[List[float]]:
        if v is None:
            return None
        try:
            return json.loads(v)
        except:
            return None

    @property
    def total_cycle_reward(self) -> float:
        """Calculate total cycle reward dynamically"""
        if self.type != TaskType.DAILY_CHECKIN:
            return self.reward_value
        
        daily_rewards = self.daily_rewards or [10, 15, 20, 25, 30, 35, 50]
        cycle_bonus = self.cycle_bonus_reward or 100
        return sum(daily_rewards) + cycle_bonus

# Task Completion schemas
class TaskCompletionBase(BaseModelConfig):
    task_id: int = Field(..., gt=0)
    user_id: int = Field(..., gt=0)
    status: str  # Changed from TaskStatus enum to str to fix serialization warnings
    started_at: datetime
    current_progress: int = Field(0, ge=0, le=1000)
    verification_attempts: int = Field(0, ge=0, le=10)
    is_claimed: bool = False
    expires_at: Optional[datetime] = None
    last_verified_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    claimed_at: Optional[datetime] = None
    verification_data: Optional[Dict] = None
    # Daily task fields
    streak_day: Optional[int] = Field(None, ge=0, le=365)
    cycle_day: Optional[int] = Field(None, ge=0, le=30)
    reward_multiplier: Optional[float] = Field(None, ge=0, le=10)
    # Security fields
    reward_amount: float = Field(0.0, ge=0, le=1000)
    verification_ip: Optional[str] = Field(None, max_length=45)
    verification_user_agent: Optional[str] = Field(None, max_length=255)
    last_verification_attempt: Optional[datetime] = None
    verification_cooldown: int = Field(60, ge=30, le=3600)
    max_verification_attempts: int = Field(3, ge=1, le=10)
    is_verified: bool = False
    verification_method: Optional[str] = Field(None, pattern=r'^(api|code|manual|auto)$')
    verification_token: Optional[str] = Field(None, max_length=100)

    @field_validator('verification_data')
    @classmethod
    def validate_verification_data(cls, v: Optional[Dict]) -> Optional[Dict]:
        if v is not None:
            if isinstance(v, str):
                try:
                    v = json.loads(v)
                except json.JSONDecodeError:
                    raise ValueError('Verification data must be valid JSON')
            if not isinstance(v, dict):
                raise ValueError('Verification data must be a dictionary')
            # Validate max size
            if len(json.dumps(v)) > 1000:
                raise ValueError('Verification data too large')
        return v

    @field_validator('expires_at')
    @classmethod
    def validate_expiry(cls, v: Optional[datetime]) -> Optional[datetime]:
        if v is not None and v < datetime.utcnow():
            raise ValueError('Expiry time cannot be in the past')
        return v

    @field_validator('verification_ip')
    @classmethod
    def validate_ip(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            try:
                ipaddress.ip_address(v)
            except ValueError:
                raise ValueError('Invalid IP address format')
        return v

    @field_validator('verification_user_agent')
    @classmethod
    def sanitize_user_agent(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            # Remove any non-printable characters
            v = ''.join(char for char in v if char.isprintable())
            # Truncate if too long
            if len(v) > 255:
                v = v[:255]
        return v

class TaskCompletionOut(TaskCompletionBase):
    id: int
    task: TaskOut
    
    model_config = ConfigDict(
        from_attributes=True,
        # Allow verifications field to pass through
        extra='allow'
    )

    @field_validator('verification_data', mode='before')
    def parse_verification_data(cls, v: Optional[str]) -> Optional[Dict]:
        if v is None:
            return None
        if isinstance(v, dict):
            return v
        try:
            return json.loads(v)
        except (json.JSONDecodeError, TypeError):
            return None

class TaskCompletionCreate(TaskCompletionBase):
    pass

class TaskCompletionUpdate(BaseModelConfig):
    status: Optional[str] = None  # Changed from TaskStatus enum to str
    current_progress: Optional[int] = None
    verification_attempts: Optional[int] = None
    is_claimed: Optional[bool] = None
    completed_at: Optional[datetime] = None
    claimed_at: Optional[datetime] = None
    verification_data: Optional[str] = None
    streak_day: Optional[int] = None
    cycle_day: Optional[int] = None
    reward_multiplier: Optional[float] = None

# Daily Task schemas
class DailyTaskStreakBase(BaseModelConfig):
    current_streak: int = 0
    longest_streak: int = 0
    total_check_ins: int = 0
    current_cycle_day: int = 1
    last_check_in: Optional[datetime] = None
    first_check_time: Optional[datetime] = None
    last_streak_break: Optional[datetime] = None

class DailyTaskStreakOut(DailyTaskStreakBase):
    id: int
    user_id: int
    
    model_config = ConfigDict(from_attributes=True)

class DailyCycleBase(BaseModelConfig):
    cycle_number: int = Field(1, ge=1)
    start_date: datetime
    end_date: Optional[datetime] = None
    completed_days: List[int] = Field(default_factory=list)
    total_reward: float = Field(0.0, ge=0)
    is_completed: bool = False

class DailyCycleCreate(DailyCycleBase):
    task_id: int
    user_id: int

class DailyCycleOut(DailyCycleBase):
    id: int
    task_id: int
    user_id: int
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('completed_days', mode='before')
    def parse_completed_days(cls, v: Optional[str]) -> List[int]:
        if isinstance(v, str):
            try:
                return json.loads(v)
            except:
                return []
        return v or []

class DailyCheckInResponse(BaseModelConfig):
    streak: DailyTaskStreakOut
    completion: TaskCompletionOut
    reward: float
    multiplier: float
    next_check_in: datetime
    cycle_completion: Optional[bool] = False
    cycle_reward: Optional[float] = None
    current_cycle: Optional[DailyCycleOut] = None
    
    model_config = ConfigDict(from_attributes=True)

class NextCheckInResponse(BaseModelConfig):
    can_check_in: bool
    next_check_in: Optional[datetime]
    time_left: int  # seconds until next check-in
    current_streak: Optional[int]
    current_cycle_day: Optional[int]
    
    model_config = ConfigDict(from_attributes=True)

# Define MarzbanPanelBase and related schemas first
class MarzbanPanelBase(BaseModelConfig):
    name: str = Field(..., min_length=1, max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., min_length=1, max_length=100)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')
        return v.rstrip('/')  # Remove trailing slashes

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Name cannot be empty')
        if len(v) > 100:
            raise ValueError('Name cannot be longer than 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Username cannot be empty')
        if len(v) > 100:
            raise ValueError('Username cannot be longer than 100 characters')
        return v

class MarzbanPanelCreate(BaseModelConfig):
    name: str = Field(..., min_length=1, max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., min_length=1, max_length=100)
    admin_password: str = Field(..., min_length=4)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')
        return v.rstrip('/')  # Remove trailing slashes

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Name cannot be empty')
        if len(v) > 100:
            raise ValueError('Name cannot be longer than 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Username cannot be empty')
        if len(v) > 100:
            raise ValueError('Username cannot be longer than 100 characters')
        return v

    @field_validator('admin_password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        if len(v) < 4:
            raise ValueError('Password must be at least 4 characters long')
        return v

class MarzbanPanelUpdate(BaseModelConfig):
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    api_url: Optional[str] = Field(None, pattern=r'^https?://.+')
    admin_username: Optional[str] = Field(None, min_length=1, max_length=100)
    admin_password: Optional[str] = Field(None, min_length=4)
    is_active: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            if not v.startswith(('http://', 'https://')):
                raise ValueError('API URL must start with http:// or https://')
            return v.rstrip('/')  # Remove trailing slashes
        return v

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            v = v.strip()
            if not v:
                raise ValueError('Name cannot be empty')
            if len(v) > 100:
                raise ValueError('Name cannot be longer than 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_username(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            v = v.strip()
            if not v:
                raise ValueError('Username cannot be empty')
            if len(v) > 100:
                raise ValueError('Username cannot be longer than 100 characters')
        return v

    @field_validator('admin_password')
    @classmethod
    def validate_password(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and len(v) < 4:
            raise ValueError('Password must be at least 4 characters long')
        return v

    def dict_with_encrypted_password(self, service: MarzbanService) -> dict:
        data = self.model_dump(exclude_unset=True)
        if 'admin_password' in data and data['admin_password']:
            data['admin_password'] = service.encrypt_password(data['admin_password'])
        return data

class MarzbanPanelOut(BaseModelConfig):
    id: int
    name: str = Field(..., max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., max_length=100)
    is_active: bool
    created_at: datetime
    
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Main Panel",
                "api_url": "https://api.example.com",
                "admin_username": "admin",
                "is_active": True,
                "created_at": "2024-02-05T12:00:00"
            }
        }
    )

# Now VPNPackage schemas can reference MarzbanPanelOut
class VPNPackageBase(BaseModelConfig):
    name: str = Field(..., min_length=3, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    data_limit: int = Field(..., gt=0)
    expire_days: int = Field(..., gt=0)
    price: float = Field(..., ge=0)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

class VPNPackageCreate(VPNPackageBase):
    allowed_panel_ids: List[int]

class VPNPackageUpdate(VPNPackageBase):
    name: Optional[str] = None
    description: Optional[str] = None
    data_limit: Optional[int] = None
    expire_days: Optional[int] = None
    price: Optional[float] = None
    is_active: Optional[bool] = None
    allowed_panel_ids: Optional[List[int]] = None

class VPNPackageOut(VPNPackageBase):
    id: int
    name: str
    data_limit: int
    expire_days: int
    price: float
    description: Optional[str]
    is_active: bool
    created_at: datetime
    allowed_panels: List[MarzbanPanelOut]
    
    model_config = ConfigDict(from_attributes=True)

# Now TaskPack schemas can reference VPNPackageOut
class TaskPackBase(BaseModelConfig):
    name: str = Field(..., min_length=3, max_length=100)
    description: str = Field(..., min_length=10, max_length=500)
    reward_package_id: int = Field(..., gt=0)
    is_lifetime: bool = False
    auto_renewal: bool = False
    required_duration: int = Field(..., gt=0)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

class TaskPackCreate(TaskPackBase):
    task_ids: List[int]

class TaskPackOut(TaskPackBase):
    id: int
    tasks: List["TaskOut"]
    reward_package: VPNPackageOut
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# Move SocialTask schemas before Task schemas
class SocialTaskBase(BaseModelConfig):
    platform: str = Field(..., max_length=50)
    platform_id: str = Field(..., max_length=100)
    platform_url: str = Field(..., pattern=r'^https?://.+')
    verify_key: Optional[str] = Field(None, max_length=100)
    max_verification_attempts: int = Field(3, ge=1, le=10)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

class SocialTaskCreate(BaseModelConfig):
    platform: str = Field(..., max_length=50)
    platform_id: str = Field(..., max_length=100)
    platform_url: str = Field(..., pattern=r'^https?://.+')
    verify_key: str = Field(..., max_length=100)
    max_verification_attempts: int = Field(3, ge=1, le=10)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

    @field_validator('platform')
    @classmethod
    def validate_platform(cls, v: str) -> str:
        if v.lower() not in ['youtube', 'instagram', 'telegram', 'twitter', 'website']:
            raise ValueError('Invalid platform')
        return v.lower()

    @field_validator('max_verification_attempts')
    @classmethod
    def validate_max_attempts(cls, v: int) -> int:
        if v < 1 or v > 10:
            raise ValueError('Max attempts must be between 1 and 10')
        return v

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Invalid platform URL')
        return v

class SocialTaskOut(SocialTaskBase):
    id: int
    task_id: int
    last_verification_time: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)

class SocialTaskUpdate(BaseModelConfig):
    platform_id: Optional[str] = Field(None, max_length=100)
    platform_url: Optional[str] = Field(None, pattern=r'^https?://.+')
    is_active: Optional[bool] = None
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and not v.startswith(('http://', 'https://')):
            raise ValueError('Invalid platform URL')
        return v

# Now Task schemas can reference SocialTaskCreate and SocialTaskUpdate
class TaskVerificationBase(BaseModelConfig):
    status: TaskStatus
    verification_data: Optional[Dict] = None
    verification_method: str  # "api", "code", "manual", "auto"
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    next_verification: Optional[datetime] = None

    @field_validator('verification_data', mode='before')
    def validate_verification_data(cls, v):
        if v is None:
            return None
        if isinstance(v, dict):
            return json.dumps(v)  # Convert dict to JSON string for storage
        if isinstance(v, str):
            try:
                json.loads(v)  # Validate JSON string
                return v
            except json.JSONDecodeError:
                return None
        return None

class TaskVerificationCreate(TaskVerificationBase):
    task_completion_id: int

class TaskVerificationOut(TaskVerificationBase):
    id: int
    task_completion_id: int
    verified_at: datetime
    verification_result: Optional[Dict] = None
    created_at: datetime

    @field_validator('verification_result', mode='before')
    def validate_verification_result(cls, v):
        if v is None:
            return None
        if isinstance(v, dict):
            return json.dumps(v)
        if isinstance(v, str):
            try:
                json.loads(v)
                return v
            except json.JSONDecodeError:
                return None
        return None

class TaskVerificationResult(BaseModelConfig):
    success: bool
    status: TaskStatus
    message: Optional[str] = Field(None, max_length=500)
    error_code: Optional[str] = Field(None, max_length=50)
    remaining_attempts: Optional[int] = Field(None, ge=0, le=10)
    verification_time_left: Optional[int] = Field(None, ge=0, le=3600)  # Seconds until next verification
    next_verification_time: Optional[datetime] = None
    verification_data: Optional[Dict] = None
    claim_available_at: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('verification_data')
    @classmethod
    def validate_verification_data(cls, v: Optional[Dict]) -> Optional[Dict]:
        if v is not None:
            if isinstance(v, str):
                try:
                    v = json.loads(v)
                except json.JSONDecodeError:
                    raise ValueError('Verification data must be valid JSON')
            if not isinstance(v, dict):
                raise ValueError('Verification data must be a dictionary')
            # Validate max size
            if len(json.dumps(v)) > 1000:
                raise ValueError('Verification data too large')
        return v

# Now Task Completion schemas can reference TaskVerificationOut
class RewardRevocationBase(BaseModelConfig):
    task_completion_id: int
    reason: str
    revoked_by_id: int
    revoked_at: datetime = Field(default_factory=datetime.utcnow)

class RewardRevocationCreate(RewardRevocationBase):
    model_config = ConfigDict(from_attributes=True)

class RewardRevocationOut(RewardRevocationBase):
    id: int
    task_completion_id: int
    revoked_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# NEW User Card Schemas
class UserCardBase(BaseModelConfig):
    # No Pydantic representation needed usually, properties handle calculation
    pass

class UserCardCreate(BaseModel):
    card_catalog_id: int = Field(..., gt=0)

# NEW Simple schema for nested card info in responses
class NestedCardInfo(BaseModel):
    id: int
    name: str
    rarity: str
    image_url: Optional[str] = None
    max_level: int
    description: Optional[str] = "No description available."

    model_config = ConfigDict(from_attributes=True)

# Schema for endpoints returning full card details (like /buy or /upgrade)
class UserCardOut(BaseModel):
    id: int
    user_id: int
    card_catalog_id: int
    level: int
    purchase_date: str # ISO format string
    last_profit_claim: str # ISO format string
    total_claimed_profit: float
    accumulated_profit: float
    current_hourly_profit: float
    next_upgrade_cost: Optional[float]
    card: NestedCardInfo # Reference the schema defined above

    model_config = ConfigDict(from_attributes=True)

class ClaimAllResponse(BaseModel):
    total_claimed: float

# NEW Response for buying a card (Now uses the defined UserCardOut)
class BuyCardResult(BaseModel):
    new_card: UserCardOut
    new_wallet_balance: float

# Now UserOut can reference TaskCompletionOut
class UserOut(BaseModelConfig):
    id: int
    username: str
    telegram_id: Optional[int] = None
    role: str  # Changed from Role enum to str to fix serialization warnings
    wallet_balance: float
    created_at: datetime
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    is_active: bool
    telegram_photo_url: Optional[str] = None
    discount_percent: float
    referral_code: str
    referred_by: Optional[int] = None
    vpn_subscriptions: List["VPNSubscriptionOut"] = []
    total_referrals: Optional[int] = None
    active_referrals: Optional[int] = None
    referral_earnings: Optional[float] = None
    task_completions: List[TaskCompletionOut] = []
    daily_streak: Optional[DailyTaskStreakOut] = None
    cards: List["UserCardOut"] = [] # Keep as string reference
    total_passive_hourly_income: float = 0.0
    
    model_config = ConfigDict(from_attributes=True)

class UserCreate(BaseModelConfig):
    username: str
    email: str
    password: str
    first_name: str
    last_name: str
    role: str = 'user'
    is_active: bool = True
    discount_percent: float = 0.0
    wallet_balance: float = 0.0
    wallet_charge: float = 0.0
    telegram_id: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if not v:
            raise ValueError('Username is required')
        if len(v) > 50:
            raise ValueError('Username cannot be longer than 50 characters')
        return v

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if not v:
            raise ValueError('Email is required')
        if len(v) > 100:
            raise ValueError('Email cannot be longer than 100 characters')
        return v

    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v):
        if not v:
            raise ValueError('Name is required')
        if len(v) > 50:
            raise ValueError('Name cannot be longer than 50 characters')
        return v

    @field_validator('discount_percent')
    @classmethod
    def validate_discount(cls, v):
        if v < 0 or v > 100:
            raise ValueError('Discount percentage must be between 0 and 100')
        return v

    @field_validator('wallet_balance')
    @classmethod
    def validate_balance(cls, v):
        if v < 0:
            raise ValueError('Wallet balance cannot be negative')
        return v

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        valid_roles = ['admin', 'reseller', 'user']
        if v not in valid_roles:
            raise ValueError(f'Role must be one of: {", ".join(valid_roles)}')
        return v

    @field_validator('wallet_charge')
    @classmethod
    def validate_charge(cls, v):
        if v is not None:
            if v == 0:
                raise ValueError('Charge amount cannot be zero')
        return v

    @field_validator('telegram_id')
    @classmethod
    def validate_telegram_id(cls, v):
        if v is not None and not v.isdigit():
            raise ValueError('Telegram ID must be a numeric string')
        return v

class UserLogin(BaseModelConfig):
    username: str
    password: str
    csrf_token: Optional[str] = None
    
    model_config = ConfigDict(
        from_attributes=True,
        extra='ignore'  # Ignore extra fields not defined in the model
    )

class Token(BaseModelConfig):
    access_token: str
    token_type: str
    role: str
    username: str
    id: int
    telegram_id: Optional[str] = None  # Keep as string for API responses

    model_config = ConfigDict(from_attributes=True)

class UserUpdate(BaseModelConfig):
    current_password: Optional[str] = None
    new_password: Optional[str] = None

class AdminUserUpdate(BaseModelConfig):
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    wallet_balance: Optional[float] = None
    wallet_charge: Optional[float] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None
    discount_percent: Optional[float] = None
    telegram_id: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if v is not None:
            if not v:  # Empty string
                return None
            # Add email format validation if needed
            if len(v) > 100:  # Match model constraint
                raise ValueError('Email cannot be longer than 100 characters')
        return v

    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v):
        if v is not None:
            if not v:  # Empty string
                return None
            if len(v) > 50:  # Match model constraint
                raise ValueError('Name cannot be longer than 50 characters')
        return v

    @field_validator('discount_percent')
    @classmethod
    def validate_discount(cls, v):
        if v is not None:
            if v < 0 or v > 100:
                raise ValueError('Discount percentage must be between 0 and 100')
        return v

    @field_validator('wallet_balance')
    @classmethod
    def validate_balance(cls, v):
        if v is not None:
            if v < 0:
                raise ValueError('Wallet balance cannot be negative')
        return v

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        if v is not None:
            valid_roles = ['admin', 'reseller', 'user']
            if v not in valid_roles:
                raise ValueError(f'Role must be one of: {", ".join(valid_roles)}')
        return v

    @field_validator('wallet_charge')
    @classmethod
    def validate_charge(cls, v):
        if v is not None:
            if v == 0:
                raise ValueError('Charge amount cannot be zero')
        return v

    @field_validator('telegram_id')
    @classmethod
    def validate_telegram_id(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Telegram ID must be a positive number')
        return v

class UserProfile(BaseModelConfig):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    telegram_photo_url: Optional[str] = None

class UserProfileUpdate(UserProfile):
    pass

class ResellerProfileUpdate(UserProfile):
    discount_percent: Optional[float] = None

class TelegramAuthData(BaseModelConfig):
    id: int
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    photo_url: Optional[str] = None
    auth_date: int
    hash: str

class UserVPNUpdate(BaseModelConfig):
    marzban_username: Optional[str] = None
    marzban_subscription_url: Optional[str] = None

class VPNSubscriptionBase(BaseModelConfig):
    package_id: int
    marzban_username: str
    subscription_url: str
    data_limit: int
    data_used: int = 0
    expires_at: datetime
    is_active: bool = True
    package: Optional[VPNPackageOut] = None

class VPNSubscriptionOut(VPNSubscriptionBase):
    id: int
    user_id: int
    created_at: datetime
    package: VPNPackageOut
    
    model_config = ConfigDict(from_attributes=True)

class VPNSubscriptionCreate(BaseModelConfig):
    package_id: int
    panel_id: int
    custom_name: str
    
    model_config = ConfigDict(from_attributes=True)

class TransactionBase(BaseModelConfig):
    amount: float
    type: str
    description: str
    balance_after: Optional[float] = None
    package_name: Optional[str] = None

class TransactionOut(TransactionBase):
    id: int
    user_id: int
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class ReferralInfo(BaseModelConfig):
    referral_code: str
    total_referrals: int
    active_referrals: int
    total_earnings: float
    referral_link: str

class ReferredUser(BaseModelConfig):
    username: str
    joined_at: datetime
    is_active: bool
    total_spent: float
    commission_earned: float
    telegram_photo_url: Optional[str] = None

class RewardRevocationBase(BaseModelConfig):
    reward_type: str
    revoked_amount: float
    reason: str
    wallet_balance_after: float

class RewardRevocationCreate(RewardRevocationBase):
    task_completion_id: int

class RewardRevocationOut(RewardRevocationBase):
    id: int
    task_completion_id: int
    revoked_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# Update forward references at the end of the file
UserOut.model_rebuild()
TaskOut.model_rebuild()
TaskCompletionOut.model_rebuild()
UserOut.update_forward_refs()

# Fix for Pydantic v2 forward reference resolution
UserOut.update_forward_refs()

# Add these new schemas
class TaskStatusOut(BaseModelConfig):
    status: TaskStatus
    verification_attempts: int = Field(..., ge=0, le=10)
    verification_time_left: int = Field(..., ge=0, le=3600)  # Seconds until next verification
    current_progress: int = Field(..., ge=0, le=1000)
    is_claimed: bool
    last_verified_at: Optional[datetime] = None
    next_verification_time: Optional[datetime] = None
    verification_method: Optional[str] = Field(None, pattern=r'^(api|code|manual|auto)$')
    
    model_config = ConfigDict(from_attributes=True)

class TaskRewardOut(BaseModelConfig):
    task_id: int
    task_name: str
    reward_type: RewardType
    reward_value: float
    claimed_at: datetime
    transaction_id: Optional[int]
    
    model_config = ConfigDict(from_attributes=True)

class YouTubeVerificationData(BaseModelConfig):
    verification_code: str

class SocialFollowVerificationData(BaseModelConfig):
    platform: SocialPlatform
    platform_id: str
    platform_url: str

class ConfigCreate(BaseModelConfig):
    name: str = Field(..., min_length=3, max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., min_length=4, max_length=50)
    admin_password: str = Field(..., min_length=8, max_length=100)
    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')
        return v.strip()

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        v = v.strip()
        if not v or len(v) < 3:
            raise ValueError('Name must be at least 3 characters long')
        if len(v) > 100:
            raise ValueError('Name cannot exceed 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_admin_username(cls, v: str) -> str:
        v = v.strip()
        if not v or len(v) < 4:
            raise ValueError('Admin username must be at least 4 characters long')
        if len(v) > 50:
            raise ValueError('Admin username cannot exceed 50 characters')
        if not v.isalnum():
            raise ValueError('Admin username must contain only letters and numbers')
        return v

    @field_validator('admin_password')
    @classmethod
    def validate_admin_password(cls, v: str) -> str:
        if not v or len(v) < 8:
            raise ValueError('Admin password must be at least 8 characters long')
        if len(v) > 100:
            raise ValueError('Admin password cannot exceed 100 characters')
        # Check for at least one uppercase, one lowercase, one number
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'\d', v):
            raise ValueError('Password must contain at least one number')
        return v

class TaskStatistics(BaseModelConfig):
    total_attempts: int = Field(..., ge=0)
    successful_completions: int = Field(..., ge=0)
    claimed_rewards: int = Field(..., ge=0)
    success_rate: float = Field(..., ge=0, le=100)
    active_users: int = Field(..., ge=0)
    
    
    model_config = ConfigDict(from_attributes=True)

class AdminTaskOut(TaskOut):
    statistics: Optional[TaskStatistics] = None
    total_verifications: Optional[int] = Field(None, ge=0)
    successful_verifications: Optional[int] = Field(None, ge=0)
    verification_success_rate: Optional[float] = Field(None, ge=0, le=100)
    average_completion_time: Optional[int] = Field(None, ge=0)  # In minutes
    
    model_config = ConfigDict(from_attributes=True)

class TaskStatusUpdate(BaseModelConfig):
    status: TaskStatus
    current_progress: Optional[int] = None
    verification_data: Optional[dict] = None
    
    model_config = ConfigDict(from_attributes=True)

class UpdateTaskData(BaseModelConfig):
    name: Optional[str] = Field(None, min_length=3, max_length=100)
    description: Optional[str] = Field(None, min_length=10)
    reward_type: Optional[RewardType] = None
    reward_value: Optional[float] = Field(None, ge=0, le=1000)
    target_value: Optional[int] = Field(None, ge=1)
    is_active: Optional[bool] = None
    platform_url: Optional[str] = None
    platform_id: Optional[str] = None
    verify_key: Optional[str] = None
    max_verification_attempts: Optional[int] = Field(None, ge=1, le=10)
    verification_cooldown: Optional[int] = Field(None, ge=30, le=3600)

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            if not v.startswith(('http://', 'https://')):
                raise ValueError('Platform URL must start with http:// or https://')
            if len(v) > 500:
                raise ValueError('Platform URL cannot exceed 500 characters')
        return v

    @field_validator('max_verification_attempts')
    @classmethod
    def validate_max_attempts(cls, v: Optional[int]) -> Optional[int]:
        if v is not None:
            if v < 1:
                raise ValueError('Maximum verification attempts must be at least 1')
            if v > 10:
                raise ValueError('Maximum verification attempts cannot exceed 10')
        return v

    @field_validator('verification_cooldown')
    @classmethod
    def validate_verification_cooldown(cls, v: Optional[int]) -> Optional[int]:
        if v is not None:
            if v < 30:
                raise ValueError('Verification cooldown must be at least 30 seconds')
            if v > 3600:
                raise ValueError('Verification cooldown cannot exceed 3600 seconds (1 hour)')
        return v

# Add security-related schemas
class SecurityLogCreate(BaseModelConfig):
    action_type: str = Field(..., max_length=50)
    user_id: Optional[int] = Field(None, gt=0)
    ip_address: str = Field(..., max_length=45)
    user_agent: Optional[str] = Field(None, max_length=255)
    status: str = Field(..., max_length=20)
    details: Optional[Dict[str, Any]] = None
    risk_level: str = Field(..., pattern=r'^(low|medium|high|critical)$')

    model_config = ConfigDict(from_attributes=True)

    @field_validator('action_type')
    @classmethod
    def validate_action_type(cls, v: str) -> str:
        valid_actions = {
            'login_attempt', 'password_reset', 'profile_update',
            'task_verification', 'token_blacklist', 'rate_limit',
            'suspicious_activity'
        }
        if v not in valid_actions:
            raise ValueError(f'Invalid action type. Must be one of: {", ".join(sorted(valid_actions))}')
        return v

    @field_validator('ip_address')
    @classmethod
    def validate_ip_address(cls, v: str) -> str:
        try:
            ipaddress.ip_address(v)
        except ValueError:
            raise ValueError('Invalid IP address format')
        return v

    @field_validator('user_agent')
    @classmethod
    def validate_user_agent(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            # Remove any non-printable characters
            v = ''.join(char for char in v if char.isprintable())
            # Truncate if too long
            if len(v) > 255:
                v = v[:255]
        return v

    @field_validator('status')
    @classmethod
    def validate_status(cls, v: str) -> str:
        valid_statuses = {'success', 'failure', 'blocked', 'warning', 'error'}
        if v not in valid_statuses:
            raise ValueError(f'Invalid status. Must be one of: {", ".join(sorted(valid_statuses))}')
        return v

    @field_validator('details')
    @classmethod
    def validate_details(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        if v is not None:
            # Convert to JSON and back to ensure serializability
            try:
                json_str = json.dumps(v)
                if len(json_str) > 1000:
                    raise ValueError('Details JSON cannot exceed 1000 characters')
                return json.loads(json_str)
            except (TypeError, json.JSONDecodeError):
                raise ValueError('Details must be JSON serializable')
        return v

    @field_validator('risk_level')
    @classmethod
    def validate_risk_level(cls, v: str) -> str:
        valid_levels = {'low', 'medium', 'high', 'critical'}
        if v not in valid_levels:
            raise ValueError(f'Invalid risk level. Must be one of: {", ".join(sorted(valid_levels))}')
        return v.lower()

class TokenBlacklistSchema(BaseModelConfig):
    token: str = Field(..., max_length=500)
    user_id: int = Field(..., gt=0)
    expires_at: datetime
    revoked_at: datetime = Field(default_factory=datetime.utcnow)
    revocation_reason: str = Field(..., max_length=200)
    revoked_by_ip: str = Field(..., max_length=45)

    model_config = ConfigDict(from_attributes=True)

    @field_validator('token')
    @classmethod
    def validate_token(cls, v: str) -> str:
        if not v:
            raise ValueError('Token is required')
        if len(v) > 500:
            raise ValueError('Token cannot exceed 500 characters')
        return v

    @field_validator('revocation_reason')
    @classmethod
    def validate_reason(cls, v: str) -> str:
        if not v:
            raise ValueError('Revocation reason is required')
        if len(v) > 200:
            raise ValueError('Revocation reason cannot exceed 200 characters')
        return v.strip()

    @field_validator('revoked_by_ip')
    @classmethod
    def validate_ip(cls, v: str) -> str:
        try:
            ipaddress.ip_address(v)
            return v
        except ValueError:
            raise ValueError('Invalid IP address format')

    @field_validator('expires_at')
    @classmethod
    def validate_expiry(cls, v: datetime) -> datetime:
        if v < datetime.utcnow():
            raise ValueError('Expiry time cannot be in the past')
        return v

    @field_validator('revoked_at')
    @classmethod
    def validate_revocation_time(cls, v: datetime, info: ValidationInfo) -> datetime:
        expires_at = info.data.get('expires_at')
        if expires_at and v > expires_at:
            raise ValueError('Revocation time cannot be after expiry time')
        return v

# NEW Card Catalog Schemas
class CardCatalogBase(BaseModelConfig):
    name: str = Field(..., min_length=1, max_length=100)
    rarity: str = Field("common", pattern=r"^(common|rare|epic|legendary|mythic)$")
    level_profits_json: str # Expecting a JSON string representing a list of floats
    level_costs_json: str   # Expecting a JSON string representing a list of floats
    max_level: int = Field(10, gt=1)
    image_url: Optional[str] = Field(None, max_length=255)

    @field_validator("level_profits_json", "level_costs_json")
    @classmethod
    def validate_level_json(cls, v: str, info: ValidationInfo) -> str:
        try:
            data = json.loads(v)
            if not isinstance(data, list):
                raise ValueError("JSON must represent a list")
            # Basic check: ensure all items are numbers (int or float)
            if not all(isinstance(item, (int, float)) for item in data):
                 raise ValueError("All items in the list must be numbers")

            # More specific checks based on field name and max_level (if available)
            # Note: max_level might not be available during base validation depending on order
            # We perform more robust checks in the model properties
            field_name = info.field_name
            # max_level_val = info.data.get("max_level", 10) # Might not work reliably here

            # if field_name == "level_profits_json" and len(data) != max_level_val:
            #     raise ValueError(f"Profits list must have {max_level_val} elements")
            # if field_name == "level_costs_json" and len(data) != max_level_val - 1:
            #     raise ValueError(f"Costs list must have {max_level_val - 1} elements")

            return v # Return the original JSON string if valid
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON string")
        except Exception as e:
             raise ValueError(f"Validation error for JSON list: {e}")

class CardCatalogCreate(CardCatalogBase):
    pass

class CardCatalogOut(CardCatalogBase):
    id: int
    created_at: datetime
    # initial_purchase_cost is already inherited from CardCatalogBase
    # Optionally parse the JSON for the response if needed by frontend directly
    # level_profits: List[float] = Field(default_factory=list)
    # level_costs: List[float] = Field(default_factory=list)
    # @field_validator("level_profits", "level_costs", mode=\"before\") ... add parsing logic

# Unified card response for combining catalog and ownership data
class UnifiedCardResponse(BaseModelConfig):
    id: int
    name: str = "Unknown Card"
    description: str = "No description available."
    image_url: Optional[str] = ""
    rarity: str = "common"
    max_level: int = 10
    profit_rate: float = 0.0
    price: float = 0.0
    level_profits: Optional[List[float]] = []
    next_level_profit_increase: Optional[float] = None
    is_owned: bool = False
    user_card: Optional[Dict[str, Any]] = None
    
    model_config = ConfigDict(from_attributes=True)

# Rebuild models that might depend on the changed schemas
UserOut.model_rebuild()
TaskOut.model_rebuild()
TaskCompletionOut.model_rebuild()

# Schema for Admin Login
class AdminLoginRequest(BaseModelConfig):
    username: str
    password: str
    csrf_token: Optional[str] = None # Allow csrf_token from frontend

# Add the ChatMessage schemas at the end of the file

# ----- Chat System Schemas -----

class ChatMessageBase(BaseModel):
    """Base schema for chat messages with privacy focus (no sender_username)"""
    content: str = Field(..., min_length=1, max_length=1000)
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """Sanitize and validate message content"""
        v = v.strip()
        if not v:
            raise ValueError("Message content cannot be empty")
        if len(v) > 1000:
            raise ValueError("Message content too long (max 1000 chars)")
        return v

class ChatMessageCreate(ChatMessageBase):
    """Schema for creating a new chat message"""
    receiver_id: Optional[int] = Field(None, description="User ID of the receiver, null for public messages")
    
    @field_validator('receiver_id')
    @classmethod
    def validate_receiver_id(cls, v: Optional[int]) -> Optional[int]:
        """Validate receiver ID if present"""
        if v is not None and v <= 0:
            raise ValueError("Invalid receiver ID")
        return v

class ChatMessageOut(ChatMessageBase):
    """Schema for returning chat messages (without usernames for privacy)"""
    id: int
    sender_id: int
    receiver_id: Optional[int] = None
    created_at: datetime
    is_public: bool
    sender_nickname: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

    @model_validator(mode='after')
    def compute_is_public(self) -> 'ChatMessageOut':
        self.is_public = self.receiver_id is None
        return self

class ChatConversationPartner(BaseModel):
    """Schema for returning conversation partners (with anonymous nicknames for the list view)"""
    id: int  # Anonymous chat ID, not the actual user ID
    username: str  # Anonymous nickname, not the actual username
    avatar_url: Optional[str] = None
    last_message_at: Optional[datetime] = None
    unread_count: Optional[int] = 0  # Default to 0, will be calculated
    is_online: bool = False  # Flag to indicate if user is online

    model_config = ConfigDict(from_attributes=True)

class UserSearchResult(BaseModel):
    """Schema for user search results (with usernames for selection)"""
    id: int
    username: str
    avatar_url: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

class UserDisplayInfo(BaseModel):
    """Schema for minimal user display info (no username for privacy)"""
    avatar_url: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

