from database import engine, Base
from models import SecurityLog  # Import the SecurityLog model specifically
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_database():
    try:
        # Create the security_logs table if it doesn't exist
        logger.info("Creating security_logs table...")
        Base.metadata.create_all(bind=engine, tables=[SecurityLog.__table__])
        logger.info("Security_logs table created successfully!")
        
    except Exception as e:
        logger.error(f"Error fixing database: {str(e)}")
        raise

if __name__ == "__main__":
    fix_database() 