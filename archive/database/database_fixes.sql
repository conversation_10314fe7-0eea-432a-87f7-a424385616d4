-- Add basic indexes to migrations_history
CREATE INDEX IF NOT EXISTS idx_migrations_history_id ON migrations_history(id);

-- Add basic indexes to system_configs
CREATE INDEX IF NOT EXISTS idx_system_configs_id ON system_configs(id);

-- Add basic indexes to task_packs
CREATE INDEX IF NOT EXISTS idx_task_packs_id ON task_packs(id);

-- Add index for task_id in social_tasks for better performance
CREATE INDEX IF NOT EXISTS idx_social_tasks_task_id ON social_tasks(task_id);

-- Add index for verification_type in social_tasks
CREATE INDEX IF NOT EXISTS idx_social_tasks_verification ON social_tasks(verification_type, is_active);

-- Add index for task_completion_id in task_verifications
CREATE INDEX IF NOT EXISTS idx_task_verifications_completion ON task_verifications(task_completion_id);

-- Add index for task_completion_id in reward_revocations
CREATE INDEX IF NOT EXISTS idx_reward_revocations_completion ON reward_revocations(task_completion_id);

-- Clean up orphaned records in transactions
DELETE FROM transactions WHERE id IN (
    SELECT t.id FROM transactions t
    LEFT JOIN users r ON t.user_id = r.id
    WHERE r.id IS NULL
);

-- Clean up orphaned records in transactions
DELETE FROM transactions WHERE id IN (
    SELECT t.id FROM transactions t
    LEFT JOIN vpn_subscriptions r ON t.subscription_id = r.id
    WHERE r.id IS NULL
);

-- Clean up orphaned records in users
DELETE FROM users WHERE id IN (
    SELECT t.id FROM users t
    LEFT JOIN users r ON t.referred_by = r.id
    WHERE r.id IS NULL AND t.referred_by IS NOT NULL
);

-- Add user_agent, request_method, and request_url columns to audit tables
ALTER TABLE users_audit 
ADD COLUMN user_agent TEXT,
ADD COLUMN request_method TEXT,
ADD COLUMN request_url TEXT;

ALTER TABLE transactions_audit 
ADD COLUMN user_agent TEXT,
ADD COLUMN request_method TEXT,
ADD COLUMN request_url TEXT;

ALTER TABLE vpn_subscriptions_audit 
ADD COLUMN user_agent TEXT,
ADD COLUMN request_method TEXT,
ADD COLUMN request_url TEXT;

ALTER TABLE marzban_panels_audit 
ADD COLUMN user_agent TEXT,
ADD COLUMN request_method TEXT,
ADD COLUMN request_url TEXT;

-- Create rate limits table
CREATE TABLE IF NOT EXISTS rate_limits (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    action_type TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_rate_limits_user ON rate_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_rate_limits_action ON rate_limits(action_type);
CREATE INDEX IF NOT EXISTS idx_rate_limits_timestamp ON rate_limits(timestamp);

-- Add indexes for audit tables
CREATE INDEX IF NOT EXISTS idx_users_audit_timestamp ON users_audit(timestamp);
CREATE INDEX IF NOT EXISTS idx_transactions_audit_timestamp ON transactions_audit(timestamp);
CREATE INDEX IF NOT EXISTS idx_vpn_subscriptions_audit_timestamp ON vpn_subscriptions_audit(timestamp);
CREATE INDEX IF NOT EXISTS idx_marzban_panels_audit_timestamp ON marzban_panels_audit(timestamp);