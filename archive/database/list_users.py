import sqlite3
import os

def list_users_with_telegram_id():
    # Try users.db first
    try:
        print("Checking users.db...")
        conn = sqlite3.connect('./users.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, telegram_id FROM users WHERE telegram_id IS NOT NULL LIMIT 10")
        users = cursor.fetchall()
        
        if users:
            print(f"Found {len(users)} users with Telegram IDs in users.db:")
            for user in users:
                print(f"ID={user[0]}, Username={user[1]}, Telegram ID={user[2]}")
        else:
            print("No users found with Telegram IDs in users.db")
        
        conn.close()
    except Exception as e:
        print(f"Error querying users.db: {e}")
    
    # Try sql_app.db next
    try:
        print("\nChecking sql_app.db...")
        conn = sqlite3.connect('./sql_app.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, telegram_id FROM users WHERE telegram_id IS NOT NULL LIMIT 10")
        users = cursor.fetchall()
        
        if users:
            print(f"Found {len(users)} users with Telegram IDs in sql_app.db:")
            for user in users:
                print(f"ID={user[0]}, Username={user[1]}, Telegram ID={user[2]}")
        else:
            print("No users found with Telegram IDs in sql_app.db")
        
        conn.close()
    except Exception as e:
        print(f"Error querying sql_app.db: {e}")

if __name__ == "__main__":
    list_users_with_telegram_id() 