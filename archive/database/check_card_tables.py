import os
import sqlalchemy
from sqlalchemy import create_engine, inspect
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# --- Configuration ---
# Assuming the script is run from the 'backend' directory where 'users.db' resides.
# Adjust the path if your database file is located elsewhere relative to the script.
DATABASE_URL = "sqlite:///users.db"
REQUIRED_TABLES = ["card_catalog", "user_cards"]
# --- End Configuration ---

def check_tables_exist():
    """
    Connects to the database and checks if the required tables exist.
    """
    logger.info(f"Attempting to connect to database: {DATABASE_URL}")
    engine = None
    try:
        engine = create_engine(DATABASE_URL)
        with engine.connect() as connection: # Test connection
             logger.info("Database connection successful.")
             inspector = inspect(engine)
             existing_tables = inspector.get_table_names()
             logger.info(f"Found tables: {existing_tables}")

             missing_tables = []
             found_tables = []

             for table_name in REQUIRED_TABLES:
                 if table_name in existing_tables:
                     logger.info(f"  [FOUND] Required table: '{table_name}'")
                     found_tables.append(table_name)
                 else:
                     logger.warning(f"  [MISSING] Required table: '{table_name}'")
                     missing_tables.append(table_name)

             print("-" * 30)
             if not missing_tables:
                 print("✅ Success: All required card tables exist.")
             else:
                 print(f"⚠️ Warning: The following required tables are missing: {', '.join(missing_tables)}")
                 print("   Please ensure you have run the necessary database schema creation/migration steps.")
             print("-" * 30)

             return not missing_tables # Return True if all tables exist, False otherwise

    except sqlalchemy.exc.SQLAlchemyError as e:
        logger.error(f"Database connection or inspection error: {e}", exc_info=True)
        print(f"❌ Error: Could not connect to or inspect the database at '{DATABASE_URL}'.")
        print(f"   Details: {e}")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}", exc_info=True)
        print(f"❌ Error: An unexpected error occurred: {e}")
        return False
    finally:
        if engine:
            engine.dispose()
            logger.info("Database engine disposed.")

if __name__ == "__main__":
    check_tables_exist() 