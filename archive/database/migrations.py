from sqlalchemy import create_engine, text, inspect
from database import DATABASE_URL, engine
import logging
from datetime import datetime
from models import (
    User, Role, VPNPackage, MarzbanPanel, VPNSubscription, Transaction,
    TransactionType, SystemConfig, Task, TaskType, TaskStatus,
    TaskVerification, TaskPack, TaskPackTask, DailyTaskStreak, 
    RewardType
)

logger = logging.getLogger(__name__)

class MigrationService:
    def __init__(self, connection):
        self.connection = connection
        self.migrations_table = "migrations_history"
        self._ensure_migrations_table()

    def _ensure_migrations_table(self):
        """Create migrations history table if it doesn't exist"""
        with self.connection.begin():
            self.connection.execute(text("""
                CREATE TABLE IF NOT EXISTS migrations_history (
                    id SERIAL PRIMARY KEY,
                    migration_name VARCHAR(255) NOT NULL,
                    applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    success BOOLEAN DEFAULT TRUE,
                    error_message TEXT
                )
            """))

    def has_migration_run(self, migration_name: str) -> bool:
        """Check if a specific migration has already been run"""
        with self.connection.begin():
            result = self.connection.execute(
                text("SELECT id FROM migrations_history WHERE migration_name = :name AND success = TRUE"),
                {"name": migration_name}
            )
            return result.fetchone() is not None

    def record_migration(self, migration_name: str, success: bool = True, error_message: str = None):
        """Record the execution of a migration"""
        with self.connection.begin():
            self.connection.execute(
                text("""
                    INSERT INTO migrations_history (migration_name, success, error_message)
                    VALUES (:name, :success, :error)
                """),
                {
                    "name": migration_name,
                    "success": success,
                    "error": error_message
                }
            )

    def run_migrations(self, migrations: dict):
        """Run all pending migrations"""
        logger.info("Starting migrations...")
        
        for migration_name, (upgrade_func, downgrade_func) in migrations.items():
            if self.has_migration_run(migration_name):
                logger.info(f"Migration {migration_name} has already been applied. Skipping...")
                continue

            logger.info(f"Running migration: {migration_name}")
            try:
                with self.connection.begin():
                    upgrade_func(self.connection)
                self.record_migration(migration_name, True)
                logger.info(f"Successfully completed migration: {migration_name}")
            except Exception as e:
                error_msg = str(e)
                logger.error(f"Error in migration {migration_name}: {error_msg}")
                self.record_migration(migration_name, False, error_msg)
                if downgrade_func:
                    try:
                        logger.info(f"Attempting to rollback migration: {migration_name}")
                        with self.connection.begin():
                            downgrade_func(self.connection)
                    except Exception as down_e:
                        logger.error(f"Error in rollback of {migration_name}: {str(down_e)}")
                raise

def has_table(connection, table_name):
    """Check if a table exists"""
    insp = inspect(connection)
    return table_name in insp.get_table_names()

def has_column(connection, table_name, column_name):
    """Check if a column exists in a table"""
    insp = inspect(connection)
    columns = [c["name"] for c in insp.get_columns(table_name)]
    return column_name in columns

def check_missing_tables_and_columns():
    """Check which tables and columns are missing"""
    connection = engine.connect()
    missing_tables = []
    missing_columns = {}
    
    tables = {
        "users": ["device_platform", "device_id", "last_login", "telegram_photo_url", 
                 "discount_percent", "marzban_username", "marzban_subscription_url", "referral_code"],
        "vpn_packages": [],
        "marzban_panels": [],
        "package_panel_association": [],
        "vpn_subscriptions": [],
        "transactions": [],
        "system_configs": [],
        "tasks": ["verification_frequency", "allow_reward_revocation", "minimum_duration", 
                 "daily_multiplier", "streak_requirement", "auto_verify_delay"],
        "social_tasks": ["verify_key", "required_duration", 
                        "auto_complete_after", "max_verification_attempts", 
                        "last_verification_time", "verification_interval"],
        "task_verifications": [],
        "reward_revocations": [],
        "task_completions": ["status", "auto_verify_at", "streak_day", 
                           "verification_attempts", "last_verified_at"],
        "task_packs": [],
        "task_pack_tasks": [],
        "daily_task_streaks": ["first_check_time"]
    }
    
    for table_name in tables.keys():
        if not has_table(connection, table_name):
            missing_tables.append(table_name)
    
    for table_name, columns in tables.items():
        if has_table(connection, table_name) and columns:
            missing_cols = []
            for col_name in columns:
                if not has_column(connection, table_name, col_name):
                    missing_cols.append(col_name)
            if missing_cols:
                missing_columns[table_name] = missing_cols
    
    connection.close()
    return missing_tables, missing_columns

def add_high_usage_indexes():
    """Add high-usage indexes to tables."""
    try:
        logger.info("Starting to add high-usage indexes...")
        
        with engine.connect() as connection:
            with connection.begin():
                # Task indexes for frequent queries
                logger.info("Adding task-related indexes...")
                statements = [
                    "CREATE INDEX IF NOT EXISTS idx_task_type_active ON tasks(type, is_active)",
                    "CREATE INDEX IF NOT EXISTS idx_task_reward ON tasks(reward_type, reward_value)",
                    
                    # Task completion indexes for status checks
                    "CREATE INDEX IF NOT EXISTS idx_user_task_status ON task_completions(user_id, task_id, status)",
                    "CREATE INDEX IF NOT EXISTS idx_user_completion ON task_completions(user_id, is_claimed, status)",
                    "CREATE INDEX IF NOT EXISTS idx_task_verification_completion ON task_completions(task_id, status, last_verified_at)",
                    
                    # Daily streak indexes for validation
                    "CREATE INDEX IF NOT EXISTS idx_user_streak ON daily_task_streaks(user_id, last_check_in)",
                    "CREATE INDEX IF NOT EXISTS idx_user_cycle ON daily_task_streaks(user_id, current_cycle_day, last_check_in)",
                    
                    # Task pack indexes for sequential access
                    'CREATE INDEX IF NOT EXISTS idx_pack_task_order ON task_pack_tasks(pack_id, "order")'
                ]
                
                for stmt in statements:
                    try:
                        connection.execute(text(stmt))
                        logger.info(f"Created index from statement: {stmt}")
                    except Exception as e:
                        logger.error(f"Error executing statement '{stmt}': {str(e)}")
                        raise
                
        logger.info("Successfully added all high-usage indexes")
            
    except Exception as e:
        logger.error(f"Error adding indexes: {str(e)}")
        raise

def migrate_database():
    """Run all database migrations."""
    try:
        logger.info("Starting database migration...")
        
        # Add high-usage indexes
        add_high_usage_indexes()
        
        logger.info("Database migration completed successfully")
        
    except Exception as e:
        logger.error(f"Database migration failed: {str(e)}")
        raise

if __name__ == "__main__":
    migrate_database()