import sqlite3
import sys
import os

def query_user_by_telegram_id(telegram_id):
    # Try users.db first
    try:
        print("Checking users.db...")
        conn = sqlite3.connect('./users.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, telegram_id FROM users WHERE telegram_id = ?", (telegram_id,))
        user = cursor.fetchone()
        
        if user:
            print(f"User found in users.db: ID={user[0]}, Username={user[1]}, Telegram ID={user[2]}")
            conn.close()
            return True
        else:
            print(f"No user found in users.db with Telegram ID: {telegram_id}")
            conn.close()
    except Exception as e:
        print(f"Error querying users.db: {e}")
    
    # Try sql_app.db next
    try:
        print("Checking sql_app.db...")
        conn = sqlite3.connect('./sql_app.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, username, telegram_id FROM users WHERE telegram_id = ?", (telegram_id,))
        user = cursor.fetchone()
        
        if user:
            print(f"User found in sql_app.db: ID={user[0]}, Username={user[1]}, Telegram ID={user[2]}")
            conn.close()
            return True
        else:
            print(f"No user found in sql_app.db with Telegram ID: {telegram_id}")
            conn.close()
    except Exception as e:
        print(f"Error querying sql_app.db: {e}")
    
    return False

if __name__ == "__main__":
    telegram_id = 5871603080
    print(f"Searching for user with Telegram ID: {telegram_id}")
    query_user_by_telegram_id(telegram_id) 