"""Database Debug and Security Check Tool"""
import logging
from sqlalchemy import inspect, text
from database import engine
from datetime import datetime, timedelta
import json
from typing import Dict, List, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseDebugger:
    def __init__(self, engine):
        self.engine = engine
        self.inspector = inspect(engine)
        self.results = {
            "tables": {},
            "security": {
                "vulnerabilities": [],
                "timing_attacks": [],
                "injection_tests": [],
                "password_security": {},
                "access_control": {},
                "data_exposure": []
            },
            "data_integrity": {},
            "performance": {},
            "timestamp": datetime.utcnow().isoformat()
        }

    def check_table_structure(self) -> None:
        """Check table structure, primary keys, and indexes."""
        logger.info("Checking table structure...")
        
        for table_name in self.inspector.get_table_names():
            table_info = {
                "columns": [],
                "primary_keys": [],
                "indexes": [],
                "foreign_keys": [],
                "issues": []
            }
            
            # Check columns
            for column in self.inspector.get_columns(table_name):
                table_info["columns"].append(column["name"])
            
            # Check primary keys
            pks = self.inspector.get_pk_constraint(table_name)
            if not pks.get("constrained_columns"):
                table_info["issues"].append("No primary key defined")
            else:
                table_info["primary_keys"] = pks["constrained_columns"]
            
            # Check indexes
            indexes = self.inspector.get_indexes(table_name)
            if not indexes:
                table_info["issues"].append("No indexes defined")
            else:
                for idx in indexes:
                    table_info["indexes"].append({
                        "name": idx["name"],
                        "columns": idx["column_names"],
                        "unique": idx["unique"]
                    })
            
            # Check foreign keys
            for fk in self.inspector.get_foreign_keys(table_name):
                table_info["foreign_keys"].append({
                    "referred_table": fk["referred_table"],
                    "referred_columns": fk["referred_columns"],
                    "constrained_columns": fk["constrained_columns"]
                })
            
            self.results["tables"][table_name] = table_info

    def simulate_timing_attacks(self) -> None:
        """Simulate timing attacks on critical operations."""
        logger.info("Simulating timing attacks...")
        
        with self.engine.connect() as conn:
            timing_tests = [
                {
                    "name": "Login timing variation",
                    "query": """
                        SELECT id FROM users 
                        WHERE email = :email 
                        AND password_hash = :password
                    """,
                    "params": [
                        {"email": "<EMAIL>", "password": "a" * 10},
                        {"email": "<EMAIL>", "password": "a" * 100},
                        {"email": "<EMAIL>", "password": "a" * 1000}
                    ]
                },
                {
                    "name": "User lookup timing",
                    "query": """
                        SELECT * FROM users 
                        WHERE telegram_id = :telegram_id
                    """,
                    "params": [
                        {"telegram_id": "123"},
                        {"telegram_id": "a" * 100},
                        {"telegram_id": "'; SELECT sleep(1);--"}
                    ]
                }
            ]

            for test in timing_tests:
                timings = []
                for params in test["params"]:
                    start_time = datetime.now()
                    try:
                        conn.execute(text(test["query"]), params)
                    except Exception:
                        pass
                    duration = (datetime.now() - start_time).total_seconds()
                    timings.append(duration)

                # Check for significant timing variations
                if max(timings) - min(timings) > 0.1:  # 100ms threshold
                    self.results["security"]["timing_attacks"].append({
                        "test": test["name"],
                        "timing_variation": max(timings) - min(timings),
                        "max_timing": max(timings),
                        "min_timing": min(timings)
                    })

    def simulate_sql_injection(self) -> None:
        """Test for SQL injection vulnerabilities."""
        logger.info("Testing SQL injection vulnerabilities...")
        
        injection_patterns = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT password_hash FROM users; --",
            "admin'--",
            "' OR id IS NOT NULL; --",
            "'; UPDATE users SET role='admin'--",
            "' AND (SELECT sleep(1))='",
            "' AND (SELECT COUNT(*) FROM users)>0--",
            "'; BEGIN TRANSACTION; ROLLBACK; --"
        ]

        test_queries = [
            ("User login", "SELECT id FROM users WHERE email = :input"),
            ("Task search", "SELECT id FROM tasks WHERE name LIKE :input"),
            ("Profile lookup", "SELECT * FROM users WHERE username = :input")
        ]

        with self.engine.connect() as conn:
            for test_name, query in test_queries:
                for pattern in injection_patterns:
                    try:
                        start_time = datetime.now()
                        conn.execute(text(query), {"input": pattern})
                        duration = (datetime.now() - start_time).total_seconds()

                        # If query executes without error, might be vulnerable
                        self.results["security"]["injection_tests"].append({
                            "test": test_name,
                            "pattern": pattern,
                            "status": "potentially_vulnerable",
                            "execution_time": duration
                        })
                    except Exception as e:
                        # Expected to fail for properly parameterized queries
                        if "syntax error" not in str(e).lower():
                            self.results["security"]["injection_tests"].append({
                                "test": test_name,
                                "pattern": pattern,
                                "status": "unexpected_error",
                                "error": str(e)
                            })

    def check_security(self) -> None:
        """Enhanced security vulnerability checks."""
        logger.info("Running comprehensive security checks...")
        
        with self.engine.connect() as conn:
            # Get user table columns
            user_columns = [col["name"] for col in self.inspector.get_columns("users")]
            
            # Password security checks
            security_checks = []
            if "password_hash" in user_columns:
                security_checks.append("COUNT(*) FILTER (WHERE password_hash IS NULL) as missing_hash")
                security_checks.append("COUNT(*) FILTER (WHERE LENGTH(password_hash) < 60) as weak_hash")
            if "last_password_change" in user_columns:
                security_checks.append("COUNT(*) FILTER (WHERE last_password_change < :old_date) as old_password")
            if "failed_login_attempts" in user_columns:
                security_checks.append("COUNT(*) FILTER (WHERE failed_login_attempts >= 5) as locked_accounts")
            
            if security_checks:
                try:
                    result = conn.execute(text(f"""
                        SELECT {', '.join(security_checks)}
                        FROM users
                    """), {
                        "old_date": datetime.utcnow() - timedelta(days=90)
                    })
                    row = result.fetchone()
                    
                    security_results = {}
                    if "password_hash" in user_columns:
                        security_results.update({
                            "missing_password_hashes": row.missing_hash if hasattr(row, 'missing_hash') else None,
                            "weak_password_hashes": row.weak_hash if hasattr(row, 'weak_hash') else None,
                        })
                    if "last_password_change" in user_columns:
                        security_results["old_passwords"] = row.old_password if hasattr(row, 'old_password') else None
                    if "failed_login_attempts" in user_columns:
                        security_results["locked_accounts"] = row.locked_accounts if hasattr(row, 'locked_accounts') else None
                    
                    self.results["security"]["password_security"].update(security_results)
                    
                    # Log missing security columns
                    missing_security_columns = []
                    if "password_hash" not in user_columns:
                        missing_security_columns.append("password_hash")
                    if "last_password_change" not in user_columns:
                        missing_security_columns.append("last_password_change")
                    if "failed_login_attempts" not in user_columns:
                        missing_security_columns.append("failed_login_attempts")
                    
                    if missing_security_columns:
                        self.results["security"]["vulnerabilities"].append({
                            "type": "missing_security_columns",
                            "message": f"Missing security columns in users table: {', '.join(missing_security_columns)}",
                            "severity": "high"
                        })
                except Exception as e:
                    logger.error(f"Error in password security checks: {str(e)}")
                    self.results["security"]["vulnerabilities"].append({
                        "type": "security_check_error",
                        "message": f"Error running password security checks: {str(e)}",
                        "severity": "high"
                    })

            # Check for sensitive data exposure
            sensitive_patterns = [
                r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # Email
                r'\b\d{16}\b',  # Possible credit card
                r'\b\d{3}-\d{2}-\d{4}\b',  # SSN pattern
                r'\b(?:\+\d{1,3}[-.]?)?\d{10}\b'  # Phone numbers
            ]

            for table_name in self.inspector.get_table_names():
                for column in self.inspector.get_columns(table_name):
                    if column["type"].__class__.__name__ in ("String", "Text", "VARCHAR"):
                        for pattern in sensitive_patterns:
                            try:
                                result = conn.execute(text(f"""
                                    SELECT COUNT(*) FROM {table_name} 
                                    WHERE CAST({column["name"]} AS TEXT) ~ :pattern
                                """), {"pattern": pattern})
                                count = result.scalar()
                                if count > 0:
                                    self.results["security"]["data_exposure"].append({
                                        "table": table_name,
                                        "column": column["name"],
                                        "pattern_type": pattern,
                                        "matches": count
                                    })
                            except Exception as e:
                                logger.debug(f"Error checking pattern in {table_name}.{column['name']}: {str(e)}")
                                continue

            # Access control checks
            self.results["security"]["access_control"].update({
                "public_tables": [
                    table for table in self.inspector.get_table_names()
                    if not any(fk["referred_table"] == "users" 
                             for fk in self.inspector.get_foreign_keys(table))
                ],
                "role_based_tables": [
                    table for table in self.inspector.get_table_names()
                    if "role" in [col["name"] for col in self.inspector.get_columns(table)]
                ]
            })

        # Run attack simulations
        self.simulate_timing_attacks()
        self.simulate_sql_injection()

    def check_data_integrity(self) -> None:
        """Enhanced data integrity checks."""
        logger.info("Running comprehensive data integrity checks...")
        
        with self.engine.connect() as conn:
            # Existing checks...
            
            # Check for duplicate records
            for table_name in self.inspector.get_table_names():
                try:
                    result = conn.execute(text(f"""
                        SELECT COUNT(*) - COUNT(DISTINCT id) as duplicate_count 
                        FROM {table_name}
                    """))
                    duplicate_count = result.scalar()
                    if duplicate_count > 0:
                        if "duplicate_records" not in self.results["data_integrity"]:
                            self.results["data_integrity"]["duplicate_records"] = {}
                        self.results["data_integrity"]["duplicate_records"][table_name] = duplicate_count
                except Exception:
                    continue

            # Check for orphaned records in all tables
            for table_name in self.inspector.get_table_names():
                for fk in self.inspector.get_foreign_keys(table_name):
                    try:
                        result = conn.execute(text(f"""
                            SELECT COUNT(*) FROM {table_name} t
                            LEFT JOIN {fk['referred_table']} r 
                            ON t.{fk['constrained_columns'][0]} = r.{fk['referred_columns'][0]}
                            WHERE r.{fk['referred_columns'][0]} IS NULL
                        """))
                        orphaned_count = result.scalar()
                        if orphaned_count > 0:
                            if "orphaned_records" not in self.results["data_integrity"]:
                                self.results["data_integrity"]["orphaned_records"] = {}
                            self.results["data_integrity"]["orphaned_records"][f"{table_name}_to_{fk['referred_table']}"] = orphaned_count
                    except Exception:
                        continue

    def check_performance(self) -> None:
        """Check database performance metrics."""
        logger.info("Checking performance metrics...")
        
        with self.engine.connect() as conn:
            # Check for tables without indexes
            for table_name in self.inspector.get_table_names():
                indexes = self.inspector.get_indexes(table_name)
                if not indexes:
                    if "tables_without_indexes" not in self.results["performance"]:
                        self.results["performance"]["tables_without_indexes"] = []
                    self.results["performance"]["tables_without_indexes"].append(table_name)

            # Check for large tables
            for table_name in self.inspector.get_table_names():
                result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                count = result.scalar()
                if count > 1000000:  # 1 million rows
                    if "large_tables" not in self.results["performance"]:
                        self.results["performance"]["large_tables"] = []
                    self.results["performance"]["large_tables"].append({
                        "table": table_name,
                        "row_count": count
                    })

    def run_all_checks(self) -> Dict[str, Any]:
        """Run all database checks."""
        logger.info("Running all database checks...")
        
        try:
            self.check_table_structure()
            self.check_data_integrity()
            self.check_security()
            self.check_performance()
            
            # Export results to file
            with open('database_debug_results.json', 'w') as f:
                json.dump(self.results, f, indent=2)
            
            return self.results
            
        except Exception as e:
            logger.error(f"Error during database checks: {str(e)}")
            raise

    def generate_fix_script(self) -> str:
        """Enhanced fix script generation."""
        logger.info("Generating comprehensive fix script...")
        
        fixes = []
        
        # Structure fixes
        fixes.extend(self._generate_structure_fixes())
        
        # Security fixes
        fixes.extend(self._generate_security_fixes())
        
        # Data integrity fixes
        fixes.extend(self._generate_integrity_fixes())
        
        # Performance fixes
        fixes.extend(self._generate_performance_fixes())
        
        # Write fix script to file
        script = "\n".join(fixes)
        with open('database_fixes.sql', 'w') as f:
            f.write(script)
        
        return script

    def _generate_structure_fixes(self) -> List[str]:
        """Generate fixes for structural issues."""
        fixes = []
        
        for table, info in self.results["tables"].items():
            if "No primary key defined" in info["issues"]:
                fixes.extend([
                    f"\n-- Add primary key to {table}",
                    f"ALTER TABLE {table} ADD COLUMN IF NOT EXISTS id SERIAL;",
                    f"UPDATE {table} SET id = DEFAULT WHERE id IS NULL;",
                    f"ALTER TABLE {table} ADD PRIMARY KEY (id);"
                ])
            
            if "No indexes defined" in info["issues"]:
                fixes.extend([
                    f"\n-- Add basic indexes to {table}",
                    f"CREATE INDEX IF NOT EXISTS idx_{table}_id ON {table}(id);"
                ])
        
        return fixes

    def _generate_security_fixes(self) -> List[str]:
        """Generate fixes for security issues."""
        fixes = []
        
        if self.results["security"]["password_security"].get("weak_password_hashes"):
            fixes.extend([
                "\n-- Update password security settings",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS password_version INTEGER DEFAULT 1;",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_password_change TIMESTAMP DEFAULT CURRENT_TIMESTAMP;"
            ])
        
        if self.results["security"]["timing_attacks"]:
            fixes.extend([
                "\n-- Add rate limiting columns",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0;",
                "ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_attempt TIMESTAMP;"
            ])
        
        return fixes

    def _generate_integrity_fixes(self) -> List[str]:
        """Generate fixes for data integrity issues."""
        fixes = []
        
        if "orphaned_records" in self.results["data_integrity"]:
            for relation, count in self.results["data_integrity"]["orphaned_records"].items():
                table, ref_table = relation.split("_to_")
                fixes.extend([
                    f"\n-- Clean up orphaned records in {table}",
                    f"DELETE FROM {table} WHERE id IN (",
                    f"    SELECT t.id FROM {table} t",
                    f"    LEFT JOIN {ref_table} r ON t.{ref_table[:-1]}_id = r.id",
                    f"    WHERE r.id IS NULL",
                    ");"
                ])
        
        return fixes

    def _generate_performance_fixes(self) -> List[str]:
        """Generate fixes for performance issues."""
        fixes = []
        
        if "large_tables" in self.results["performance"]:
            for table_info in self.results["performance"]["large_tables"]:
                fixes.extend([
                    f"\n-- Add performance indexes for large table {table_info['table']}",
                    f"CREATE INDEX IF NOT EXISTS idx_{table_info['table']}_created_at ON {table_info['table']}(created_at);",
                    f"ANALYZE {table_info['table']};"
                ])
        
        return fixes

def main():
    """Main function to run database debug checks."""
    logger.info("Starting database debug checks...")
    
    try:
        debugger = DatabaseDebugger(engine)
        results = debugger.run_all_checks()
        
        # Log detailed summary of findings
        logger.info("\n=== Database Debug Summary ===")
        
        # Table Structure Issues
        if results["tables"]:
            logger.info("\n[Table Structure Issues]")
            for table_name, info in results["tables"].items():
                if info["issues"]:
                    logger.warning(f"\nTable '{table_name}':")
                    for issue in info["issues"]:
                        logger.warning(f"- {issue}")
                    if info.get("indexes"):
                        logger.info(f"Current indexes: {[idx['name'] for idx in info['indexes']]}")

        # Security Issues
        if results["security"]["vulnerabilities"]:
            logger.info("\n[Security Vulnerabilities]")
            for vuln in results["security"]["vulnerabilities"]:
                logger.error(f"- {vuln['type']}: {vuln['message']} (Severity: {vuln['severity']})")

        if results["security"]["timing_attacks"]:
            logger.info("\n[Timing Attack Vulnerabilities]")
            for attack in results["security"]["timing_attacks"]:
                logger.warning(f"- {attack['test']}: {attack['timing_variation']:.3f}s variation")

        if results["security"]["injection_tests"]:
            logger.info("\n[SQL Injection Tests]")
            vulnerable_count = sum(1 for test in results["security"]["injection_tests"] 
                                 if test["status"] == "potentially_vulnerable")
            if vulnerable_count:
                logger.error(f"Found {vulnerable_count} potentially vulnerable queries")
                for test in results["security"]["injection_tests"]:
                    if test["status"] == "potentially_vulnerable":
                        logger.error(f"- {test['test']} vulnerable to: {test['pattern']}")

        # Data Integrity Issues
        if results["data_integrity"]:
            logger.info("\n[Data Integrity Issues]")
            if "duplicate_records" in results["data_integrity"]:
                logger.warning("\nDuplicate Records:")
                for table, count in results["data_integrity"]["duplicate_records"].items():
                    logger.warning(f"- Table '{table}': {count} duplicates")

            if "orphaned_records" in results["data_integrity"]:
                logger.warning("\nOrphaned Records:")
                for relation, count in results["data_integrity"]["orphaned_records"].items():
                    logger.warning(f"- {relation}: {count} orphaned records")

        # Performance Issues
        if results["performance"]:
            logger.info("\n[Performance Issues]")
            if "tables_without_indexes" in results["performance"]:
                logger.warning("\nTables Without Indexes:")
                for table in results["performance"]["tables_without_indexes"]:
                    logger.warning(f"- {table}")
                    logger.info(f"  Recommendation: Add indexes for frequently queried columns in {table}")

            if "large_tables" in results["performance"]:
                logger.warning("\nLarge Tables:")
                for table_info in results["performance"]["large_tables"]:
                    logger.warning(f"- {table_info['table']}: {table_info['row_count']:,} rows")
                    logger.info("  Recommendation: Consider table partitioning or archiving old data")
        
        # Generate fix script if issues were found
        if any(results.values()):
            fix_script = debugger.generate_fix_script()
            logger.info("\nFix script generated: database_fixes.sql")
            with open('database_fixes.sql', 'r') as f:
                logger.info("\nProposed fixes:")
                for line in f:
                    if line.strip() and not line.strip().startswith('--'):
                        logger.info(f"  {line.strip()}")
        
        logger.info("\n=== End of Debug Summary ===")
        
    except Exception as e:
        logger.error(f"Error running database debug: {str(e)}")
        raise

if __name__ == "__main__":
    main() 