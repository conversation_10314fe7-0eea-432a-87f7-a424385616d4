# create_db.py
from database import engine, Base, get_db
from models import User, Role, VPNPackage, MarzbanPanel, VPNSubscription, Transaction, SystemConfig
import logging
import secrets
import string
from auth import get_password_hash
from sqlalchemy import text

logger = logging.getLogger(__name__)

def generate_referral_code(length: int = 8) -> str:
    """Generate a random referral code"""
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(length))

def create_database():
    try:
        logger.info("Creating database tables...")
        # Drop all tables first to ensure clean state
        Base.metadata.drop_all(bind=engine)
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        # Create default admin user
        db = next(get_db())
        try:
            # Generate unique referral code for admin
            referral_code = generate_referral_code()
            
            # Create admin user
            admin = User(
                username="admin",
                hashed_password=get_password_hash("admin123"),  # Default password
                role=Role.admin,
                is_active=True,
                referral_code=referral_code,
                discount_percent=0.0,
                wallet_balance=0.0
            )
            
            # Create default system configurations
            default_configs = [
                SystemConfig(
                    key="REFERRAL_BONUS",
                    value="5.0",
                    description="Amount given to resellers for each new user referral"
                ),
                SystemConfig(
                    key="DEFAULT_USER_DISCOUNT",
                    value="0.0",
                    description="Default discount percentage for new users"
                ),
                SystemConfig(
                    key="DEFAULT_RESELLER_DISCOUNT",
                    value="10.0",
                    description="Default discount percentage for new resellers"
                ),
                SystemConfig(
                    key="SUBSCRIPTION_RENEWAL_DISCOUNT",
                    value="5.0",
                    description="Discount percentage applied to subscription renewals"
                ),
                SystemConfig(
                    key="MIN_WITHDRAWAL_AMOUNT",
                    value="10.0",
                    description="Minimum amount required for wallet withdrawal"
                ),
                SystemConfig(
                    key="RESELLER_COMMISSION_PERCENT",
                    value="20.0",
                    description="Percentage of subscription price given to reseller as commission"
                )
            ]
            
            # Add admin and configs to database
            db.add(admin)
            for config in default_configs:
                db.add(config)
            
            db.commit()
            
            logger.info("Created default admin user:")
            logger.info("Username: admin")
            logger.info("Password: admin123")
            logger.info("Referral Code: %s", referral_code)
            logger.info("Default system configurations created")
            
        except Exception as e:
            logger.error("Error creating initial data: %s", str(e))
            db.rollback()
            raise
        finally:
            db.close()
            
        # Create indexes for better performance
        with engine.connect() as connection:
            logger.info("Creating database indexes...")
            # User indexes
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_users_telegram_id ON users(telegram_id);
                CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
                CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);
                CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
            """))
            
            # VPN Package indexes
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_vpn_packages_name ON vpn_packages(name);
                CREATE INDEX IF NOT EXISTS idx_vpn_packages_is_active ON vpn_packages(is_active);
            """))
            
            # Subscription indexes
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_vpn_subscriptions_user_id ON vpn_subscriptions(user_id);
                CREATE INDEX IF NOT EXISTS idx_vpn_subscriptions_package_id ON vpn_subscriptions(package_id);
                CREATE INDEX IF NOT EXISTS idx_vpn_subscriptions_is_active ON vpn_subscriptions(is_active);
                CREATE INDEX IF NOT EXISTS idx_vpn_subscriptions_expires_at ON vpn_subscriptions(expires_at);
            """))
            
            # Transaction indexes
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
                CREATE INDEX IF NOT EXISTS idx_transactions_type ON transactions(type);
                CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
            """))
            
        logger.info("Database creation completed successfully!")
        
    except Exception as e:
        logger.error("Database creation failed: %s", str(e))
        raise

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    create_database()
