#!/bin/bash

# VS Code Tunnel Firewall Security Script
# This script configures firewall rules for secure tunnel operation

echo "🔥 Configuring firewall for VS Code tunnel security..."

# Enable UFW if not already enabled
if ! ufw status | grep -q "Status: active"; then
    echo "Enabling UFW firewall..."
    ufw --force enable
fi

# Default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (important - don't lock yourself out!)
ufw allow ssh

# Allow HTTPS outbound for tunnel communication
ufw allow out 443

# Allow DNS
ufw allow out 53

# Block all other incoming connections
ufw deny in on any

# Log denied connections for security monitoring
ufw logging on

# Show current status
echo ""
echo "🔒 Firewall Status:"
ufw status verbose

echo ""
echo "✅ Firewall configured for VS Code tunnel security"
echo "   - All incoming connections blocked (except SSH)"
echo "   - Only HTTPS and DNS outbound allowed"
echo "   - Tunnel uses secure outbound connections only" 