# main.py
from typing import Dict, Optional, List, Any
from fastapi import FastAPI, Depends, HTTPException, status, Form, Request, Response
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session, selectinload, joinedload
from sqlalchemy.future import select
from database import engine, get_db, get_init_db, init_db, close_db
from models import Base, User, Role, VPNPackage, MarzbanPanel, package_panel_association, VPNSubscription, Transaction, TransactionType, SystemConfig
from schemas import (
    UserCreate, UserLogin, Token, UserOut,
    UserUpdate, TelegramAuthData, UserProfile, UserProfileUpdate, ResellerProfileUpdate,
    VPNPackageCreate, VPNPackageOut, UserVPNUpdate,
    MarzbanPanelCreate, MarzbanPanelUpdate, MarzbanPanelOut, VPNSubscriptionOut,
    VPNSubscriptionCreate, VPNPackageUpdate, AdminUserUpdate,
    TaskCreate, TaskOut, TaskCompletionOut
)
from auth import (
    get_current_active_user, get_current_admin,
    get_current_reseller, create_access_token,
    verify_password, get_password_hash,
    verify_telegram_webapp_data,
    process_telegram_auth, get_current_user,
    COOKIE_NAME, ACCESS_TOKEN_EXPIRE_MINUTES
)
from datetime import timedelta, datetime, timezone
from fastapi.middleware.cors import CORSMiddleware
import os
import hashlib
import hmac
import urllib.parse
from sqlalchemy import or_, func
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager
from services.marzban_service import get_marzban_service
import secrets
import string
import logging
from pydantic import BaseModel
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
import random
from starlette.middleware.base import BaseHTTPMiddleware
from urllib.parse import parse_qs, unquote
import json
from dotenv import load_dotenv
import asyncio
import threading
import time
import gc  # Added missing import
from routers import (
    user_dashboard,
    payment,
    auth_router,
    task_router,
    referral_router,
    marzban_router,
    daily_task_router,
    monitoring_router,
    security_router,
    card_router
)
# Using the new organized admin router system
from routers.admin import admin_router
from cachetools import TTLCache
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import traceback
from config.redis_config import RedisClient
from middleware.rate_limit_middleware import RateLimitMiddleware
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.sessions import SessionMiddleware
from services.security_service import SecurityService
from services.rate_limit_service import RateLimitService
from services.verification_service import VerificationService
from services.cache_service import CacheService
from services.monitoring_service import MonitoringService
from middleware.security_headers_middleware import SecurityHeadersMiddleware
from middleware.bot_detection_middleware import BotDetectionMiddleware
from middleware.cloudflare_captcha_middleware import CloudflareCaptchaMiddleware
from services.bot_detection_service import BotDetectionService
from middleware.security_middleware import add_security_headers_middleware, add_ip_protection_middleware, add_csrf_middleware
from routers.chat import chat_router, websocket_router
from slowapi.middleware import SlowAPIMiddleware
import redis.asyncio as redis
from contextlib import asynccontextmanager

load_dotenv()

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Define LoggingMiddleware
class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = datetime.utcnow()
        request_id = str(start_time.timestamp())
        
        # Log request
        logger.info(f"[{request_id}] Started {request.method} {request.url.path}")
        
        try:
            response = await call_next(request)
            
            # Log response
            process_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            status_code = response.status_code
            logger.info(
                f"[{request_id}] Completed {status_code} in {process_time:.2f}ms"
            )
            
            return response
        except Exception as exc:
            logger.exception(f"[{request_id}] Request failed with exception: {str(exc)}")
            raise

# Initialize rate limiter using remote address as key
limiter = Limiter(key_func=get_remote_address, default_limits=["1000/minute"])

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0") # Default Redis URL

class RedisClientWrapper:
    def __init__(self, url):
        self._redis_url = url
        self._client: redis.Redis | None = None # Type hint for client

    async def connect(self):
        if not self._client:
            logger.info(f"Connecting to Redis at {self._redis_url}...")
            try:
                self._client = redis.from_url(self._redis_url, encoding="utf-8", decode_responses=True)
                await self._client.ping()
                logger.info("Successfully connected to Redis and received PONG.")
            except redis.exceptions.ConnectionError as e:
                logger.error(f"Failed to connect to Redis: {e}")
                self._client = None # Reset on failure
                raise # Re-raise the exception to be handled by lifespan or calling code
            except Exception as e:
                logger.error(f"An unexpected error occurred during Redis connection: {e}")
                self._client = None
                raise

    async def close(self):
        if self._client:
            logger.info("Closing Redis connection...")
            await self._client.close()
            self._client = None
            logger.info("Redis connection closed.")

    async def get_client(self) -> redis.Redis: # Ensure it returns the client or raises error
        if not self._client:
            logger.warning("Redis client accessed before explicit connect or after connection failure. Attempting to connect.")
            # In a lifespan context, connect should have been called.
            # If we are here, it means initial connection failed or was closed.
            # For a dependency, it's critical it's connected.
            await self.connect() # Try to reconnect
        
        # After attempting to connect (or if already connected), check again.
        if not self._client:
            logger.error("Redis client is not available after attempting connection.")
            raise RuntimeError("Redis client is not available. Check connection settings and Redis server.")
        return self._client

# Lifespan manager for application startup and shutdown events
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Initialize services
    logger.info("Application startup: Initializing Redis and services...")
    redis_wrapper = RedisClientWrapper(REDIS_URL)
    try:
        await redis_wrapper.connect()
        app.state.redis = redis_wrapper
        logger.info("Redis client initialized and stored in app.state.redis")
        
        # Initialize Redis client (legacy)
        await redis_client.connect()
        logger.info("Redis client connected successfully")
        
        # Initialize services in order
        services_to_initialize = [
            ("security_service", security_service),
            ("rate_limit_service", rate_limit_service),
            ("verification_service", verification_service),
            ("cache_service", cache_service),
            ("monitoring_service", monitoring_service),
            ("bot_detection_service", bot_detection_service)
        ]
        
        for service_name, service in services_to_initialize:
            try:
                if hasattr(service, 'initialize'):
                    logger.info(f"Initializing {service_name}...")
                    await service.initialize()
                    logger.info(f"{service_name} initialized successfully")
            except Exception as service_error:
                logger.error(f"Failed to initialize {service_name}: {str(service_error)}")
                raise
        
        # Store initialized services in app state
        app.state.security_service = security_service
        app.state.rate_limit_service = rate_limit_service
        app.state.verification_service = verification_service
        app.state.cache_service = cache_service
        app.state.monitoring_service = monitoring_service
        app.state.bot_detection_service = bot_detection_service
        
        # Log open file descriptors (debugging purposes)
        try:
            import resource
            soft, hard = resource.getrlimit(resource.RLIMIT_NOFILE)
            logger.info(f"File descriptor limits: soft={soft}, hard={hard}")
        except Exception as e:
            logger.warning(f"Could not get file descriptor limits: {str(e)}")
        
        logger.info("All services initialized successfully")
        
    except Exception as e:
        logger.error(f"Critical error: Failed to initialize during startup: {e}")
        app.state.redis = None

    yield  # Application is now running

    # Shutdown: Clean up resources
    logger.info("Application shutdown: Starting cleanup...")
    
    # Stop monitoring tasks first
    if hasattr(app.state, 'monitoring_service') and app.state.monitoring_service:
        try:
            await app.state.monitoring_service.stop_background_tasks()
            logger.info("Monitoring service background tasks stopped")
        except Exception as e:
            logger.error(f"Error stopping monitoring service tasks: {e}")

    # Clean up verification service
    if hasattr(app.state, 'verification_service') and app.state.verification_service:
        try:
            await app.state.verification_service.cleanup()
            logger.info("verification_service cleaned up successfully")
        except Exception as e:
            logger.error(f"Error cleaning up verification_service: {e}")
            
    # Close database connections
    try:
        logger.info("Closing database connection pools...")
        close_db() # Disposes synchronous engine pool
        logger.info("Synchronous database connection pool disposed")
        # Dispose the async engine pool as well
        from database import async_engine
        if async_engine:
           await async_engine.dispose()
           logger.info("Asynchronous database connection pool disposed")
    except Exception as e:
        logger.error(f"Error closing database connections: {e}")

    # Close Redis connections
    if hasattr(app.state, 'redis') and app.state.redis:
        try:
            await app.state.redis.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")

    # Explicitly run garbage collection
    try:
        import gc
        gc.collect()
        logger.info("Garbage collection completed")
    except Exception as e:
        logger.error(f"Error during garbage collection: {e}")

    logger.info("Application shutdown complete.")

# Initialize FastAPI app
app = FastAPI(
    title="Atlas VIP API",
    description="API for Atlas VIP services",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/openapi.json",
    lifespan=lifespan
)

# Initialize Redis client
redis_client = RedisClient()

# Add rate limiter state and handler to the app
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Initialize cache with 5 minute TTL
app.state.cache = TTLCache(maxsize=1000, ttl=300)  # 300 seconds = 5 minutes

Base.metadata.create_all(bind=engine)

# Store Redis client in app state for access in routes
app.state.redis = redis_client

# Initialize services
db = get_init_db()
security_service = SecurityService(db, redis_client)
rate_limit_service = RateLimitService(redis_client, db)
verification_service = VerificationService(db)
cache_service = CacheService(redis_client)
monitoring_service = MonitoringService(db=db, cache_service=cache_service, redis_client=redis_client)
bot_detection_service = BotDetectionService(db, redis_client)

# Store services in app state
app.state.security_service = security_service
app.state.rate_limit_service = rate_limit_service
app.state.cache_service = cache_service
app.state.monitoring_service = monitoring_service
app.state.bot_detection_service = bot_detection_service

#routers
app.include_router(user_dashboard.router)
app.include_router(payment.router)
app.include_router(auth_router.router, prefix="/auth", tags=["authentication"])
app.include_router(task_router.router, prefix="/api/tasks", tags=["tasks"])
app.include_router(referral_router.router)
app.include_router(marzban_router.router)
app.include_router(daily_task_router.router, prefix="/api/tasks/daily", tags=["daily_tasks"])
app.include_router(monitoring_router.router)
app.include_router(security_router.router)
app.include_router(card_router.router)
app.include_router(admin_router)
app.include_router(chat_router)
app.include_router(websocket_router)

# Add CORS middleware - Use environment variable for allowed origins
cors_origins = os.getenv(
    "CORS_ALLOWED_ORIGINS",
    "https://dev.atlasvip.cloud,http://localhost:3000,http://localhost:3005,http://localhost:5173,http://localhost:5174,http://localhost:5175,http://localhost:8080,http://***************:5173"
).split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=[origin.strip() for origin in cors_origins],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize and add security middlewares
app.state.rate_limit_service = None  # Will be initialized during startup

# Add middlewares in the correct order (from outermost to innermost)
app.add_middleware(LoggingMiddleware)
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("SECRET_KEY", "your-secret-key-if-not-set-in-env"),
    same_site="lax",
    https_only=True
)
app.add_middleware(CloudflareCaptchaMiddleware)  # Add Cloudflare CAPTCHA middleware
#app.add_middleware(BotDetectionMiddleware)  # Add bot detection middleware
app.add_middleware(SecurityHeadersMiddleware)  # Add security headers middleware
app.add_middleware(RateLimitMiddleware)  # Add rate limit middleware

# Add the security middleware to the FastAPI app
add_security_headers_middleware(app)
add_ip_protection_middleware(app)
add_csrf_middleware(app)

# Add middleware for documentation access without security restrictions
@app.middleware("http")
async def docs_middleware(request: Request, call_next):
    """Allow access to documentation with modified security headers but keep IP restrictions"""
    # List of paths that should have modified security settings
    docs_paths = ["/docs", "/redoc", "/openapi.json"]
    
    # Check if the request path is in the list of docs paths
    if any(request.url.path.startswith(path) for path in docs_paths):
        # For documentation endpoints, proceed with the request
        response = await call_next(request)
        
        # Add CORS headers for documentation endpoints
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        
        # Disable strict security headers for documentation - use del instead of pop
        if "Content-Security-Policy" in response.headers:
            del response.headers["Content-Security-Policy"]
        if "X-Frame-Options" in response.headers:
            del response.headers["X-Frame-Options"]
        
        # Add permissive CSP for Swagger UI/ReDoc
        response.headers["Content-Security-Policy"] = "default-src * 'unsafe-inline' 'unsafe-eval'; img-src * data: blob:; font-src * data:; connect-src *"
        
        return response
        
    # For other endpoints, continue with normal processing
    return await call_next(request)

async def init_redis(redis_client: RedisClient) -> None:
    """Initialize Redis connection"""
    try:
        await redis_client.initialize()
        logger.info("Successfully connected to Redis")
        
        # Test Redis connection
        redis = await redis_client.get_client()
        await redis.ping()
        logger.info("Redis connection verified")
        
    except Exception as e:
        logger.error(f"Failed to initialize Redis: {str(e)}")
        raise

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    """Handle validation errors"""
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": exc.errors()},
    )

# Add Cryptomus IP whitelist middleware
@app.middleware("http")
async def check_cryptomus_webhook(request: Request, call_next):
    # Check if the request is from Cryptomus webhook
    if request.url.path == "/payment/cryptomus/webhook":
        # Process the webhook directly, bypassing other checks
        return await call_next(request)
    return await call_next(request)

ACCESS_TOKEN_EXPIRE_MINUTES = 30

REFERRAL_BONUS = 5.0  # $5 bonus for each referral

@contextmanager
def transaction(db: Session):
    try:
        yield
        db.commit()
    except SQLAlchemyError as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Database error: {str(e)}"
        )

@app.post("/users/", response_model=UserOut)
async def create_user(
    user: UserCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_admin)
):
    """Create a new user"""
    try:
        logger.info(f"Creating user with data: {user.dict()}")
        
        # Check if username exists
        if db.query(User).filter(User.username == user.username).first():
            raise HTTPException(status_code=400, detail="Username already exists")
        
        # Check if email exists (if provided)
        if user.email and db.query(User).filter(User.email == user.email).first():
            raise HTTPException(status_code=400, detail="Email already exists")
        
        # Check if telegram_id exists (if provided)
        if user.telegram_id and db.query(User).filter(User.telegram_id == user.telegram_id).first():
            raise HTTPException(status_code=400, detail="Telegram ID already exists")

        # Generate unique referral code
        while True:
            referral_code = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
            if not db.query(User).filter(User.referral_code == referral_code).first():
                break

        # Create new user with hashed password and referral code
        hashed_password = get_password_hash(user.password)
        db_user = User(
            username=user.username,
            hashed_password=hashed_password,
            telegram_id=user.telegram_id,
            role=user.role or Role.user,
            wallet_balance=user.wallet_balance or 0.0,
            first_name=user.first_name,
            last_name=user.last_name,
            email=user.email,
            telegram_photo_url=user.telegram_photo_url,
            discount_percent=user.discount_percent or 0.0,
            is_active=user.is_active if user.is_active is not None else True,
            referral_code=referral_code,  # Add the generated referral code
            created_at=datetime.utcnow()  # Ensure UTC timestamp
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        return db_user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "ok",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0"
    }

@app.get("/")
async def read_root():
    """Root endpoint for API info"""
    return {
        "name": "Atlas VIP API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "operational"
    }

@app.exception_handler(StarletteHTTPException)
async def custom_http_exception_handler(request: Request, exc: StarletteHTTPException):
    if exc.status_code == 404:
        logger.error(f"404 Not Found: {request.method} {request.url.path}")
    else:
        logger.error(f"HTTP Exception {exc.status_code}: {request.method} {request.url.path} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
    )

# Define Base class for models using SQLAlchemy 2.0 style
# Base = declarative_base() # Assuming Base is defined elsewhere if needed for main

# Database configuration with optimized defaults
# SQLALCHEMY_DATABASE_URL = "sqlite:///./users.db" # Assuming DB URL is managed elsewhere or via env
