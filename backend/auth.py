from datetime import datetime, <PERSON><PERSON>ta
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from fastapi import Depends, HTTPException, status, Request, Response
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from sqlalchemy import select, update
from sqlalchemy.orm import Session
from database import get_db
from models import User, Role
from services.security_service import SecurityService
from config.redis_config import RedisClient
import os
from dotenv import load_dotenv
import hashlib
import base64
import secrets
import hmac
import json
import string
from urllib.parse import parse_qs, parse_qsl, unquote
import logging
from passlib.context import CryptContext
from starlette.middleware.base import BaseHTTPMiddleware

load_dotenv()

# Add logger configuration
logger = logging.getLogger(__name__)

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-if-not-set-in-env")
BOT_TOKEN = os.getenv("BOT_TOKEN")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60
REFRESH_TOKEN_EXPIRE_DAYS = 30
COOKIE_NAME = "auth_token"
REFRESH_TOKEN_COOKIE_NAME = "refresh_token"
DOMAIN = os.getenv("COOKIE_DOMAIN", None)

# Password hashing configuration
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto",
    bcrypt__rounds=12,  # Industry standard default
    bcrypt__ident="2b",  # Use the most secure version
    bcrypt__min_rounds=12,  # Minimum rounds
    bcrypt__max_rounds=31,  # Maximum rounds
    truncate_error=True  # Raise error if password is too long
)

# OAuth2 scheme for token extraction
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token", auto_error=False)

class Auth:
    """Centralized authentication class"""
    
    def __init__(self, db: Session):
        from sqlalchemy.orm import Session as SQLASession
        if not isinstance(db, SQLASession):
            raise ValueError(f"Expected SQLAlchemy Session but got {type(db)}")
            
        self.db = db
        self.redis_client = RedisClient()
        self.security_service = None
    
    @classmethod
    def create_token(cls, data: Dict[str, Any], token_type: str = "access") -> str:
        """Single source of truth for token creation"""
        expires_delta = (
            timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES) 
            if token_type == "access" 
            else timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        )
        
        to_encode = data.copy()
        expire = datetime.utcnow() + expires_delta
        to_encode.update({
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": token_type,
            "user_id": data.get("user_id"),
            "role": data.get("role"),
            "sub": data.get("sub")
        })
        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

    @classmethod
    def create_auth_tokens(cls, user: User) -> Tuple[str, str]:
        """Create both access and refresh tokens"""
        token_data = {
            "sub": user.username,
            "role": user.role.value,
            "user_id": user.id
        }
        
        access_token = cls.create_token(token_data, "access")
        refresh_token = cls.create_token(token_data, "refresh")
        
        return access_token, refresh_token

    @classmethod
    def set_auth_cookies(cls, response: Response, access_token: str, refresh_token: str):
        """Set authentication cookies optimized for Telegram WebApp"""
        common_settings = {
            "secure": True,
            "samesite": "none", # Required for Telegram WebApp embedding
            "domain": DOMAIN if DOMAIN else None,
        }

        # Access token cookie
        response.set_cookie(
            key=COOKIE_NAME,
            value=access_token,
            max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            path="/",
            httponly=True, # Secure against XSS
            **common_settings
        )

        # Refresh token cookie
        response.set_cookie(
            key=REFRESH_TOKEN_COOKIE_NAME,
            value=refresh_token,
            max_age=REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60,
            path="/auth", # Scoped to auth endpoints only
            httponly=True,
            **common_settings
        )

    @classmethod
    def clear_auth_cookies(cls, response: Response):
        """Clear authentication cookies"""
        response.delete_cookie(
            key=COOKIE_NAME,
            path="/",
            domain=DOMAIN
        )
        response.delete_cookie(
            key=REFRESH_TOKEN_COOKIE_NAME,
            path="/auth",
            domain=DOMAIN
        )

    @classmethod
    async def get_token_from_request(cls, request: Request) -> Optional[str]:
        """Extract token from request (header or cookie)"""
        # Try Authorization header first
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header.split(" ")[1]
        
        # Try cookie next
        return request.cookies.get(COOKIE_NAME)

    @classmethod
    async def validate_token(cls, token: str, db: Session, security_service: SecurityService) -> Optional[User]:
        """Validate token and return user, including blacklist check."""
        try:
            # --- Add Blacklist Check FIRST --- 
            if security_service and await security_service.is_token_blacklisted(token):
                logger.warning(f"Authentication attempt with blacklisted token: {token[:10]}...")
                return None
            # --- End Blacklist Check --- 

            # Decode token
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            token_type: str = payload.get("type")
            
            if not username or token_type != "access":
                return None
            
            # Get and validate user
            user = db.query(User).filter(User.username == username).first()
            if not user or not user.is_active:
                return None
                
            return user
            
        except JWTError:
            return None

    @classmethod
    async def validate_refresh_token(cls, token: str, db: Session) -> Optional[User]:
        """Validate refresh token and return user"""
        try:
            # Decode token
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            token_type: str = payload.get("type")
            
            if not username or token_type != "refresh":
                return None
            
            # Get and validate user
            user = db.query(User).filter(User.username == username).first()
            if not user or not user.is_active:
                return None
                
            return user
            
        except JWTError:
            return None
    
    async def authenticate_user(
        self,
        username: str,
        password: str,
        request: Request,
        is_admin: bool = False
    ) -> Tuple[User, str, str]:
        """
        Authenticate a user with username and password
        
        Args:
            username: The username to authenticate
            password: The password to verify
            request: The request object (for tracking failed attempts)
            is_admin: Whether this is an admin login
            
        Returns:
            Tuple containing the user, access token, and refresh token
            
        Raises:
            HTTPException: If authentication fails
        """
        try:
            if not self.db:
                logger.error("Database session not initialized")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Database error"
                )
                
            # Get user from database
            user = self.db.query(User).filter(User.username == username).first()
            
            # Check if user exists and password is correct
            if not user or not verify_password(password, user.hashed_password):
                # Track failed login attempt
                try:
                    # Get bot detection service
                    bot_detection_service = request.app.state.bot_detection_service
                    
                    # Track failed login attempt
                    ip_address = request.client.host
                    await bot_detection_service.track_failed_login(username, ip_address)
                    
                    # Log the failed attempt
                    logger.warning(f"Failed login attempt for user {username} from IP {ip_address}")
                except Exception as e:
                    logger.error(f"Error tracking failed login attempt: {str(e)}")
                    
                # Log authentication failure
                await self._log_auth_failure(request, username, "invalid_credentials")
                
                logger.warning(f"Authentication failed: Invalid credentials for user {username}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid username or password",
                    headers={"WWW-Authenticate": "Bearer"},
                )
                
            # Check if user is active
            if not user.is_active:
                # Track failed login attempt
                try:
                    # Get bot detection service
                    bot_detection_service = request.app.state.bot_detection_service
                    
                    # Track failed login attempt
                    ip_address = request.client.host
                    await bot_detection_service.track_failed_login(username, ip_address)
                    
                    # Log the failed attempt
                    logger.warning(f"Failed login attempt for inactive user {username} from IP {ip_address}")
                except Exception as e:
                    logger.error(f"Error tracking failed login attempt: {str(e)}")
                    
                # Log authentication failure
                await self._log_auth_failure(request, username, "inactive_user", user.id)
                
                logger.warning(f"Authentication failed: User {username} is inactive")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Inactive user",
                    headers={"WWW-Authenticate": "Bearer"},
                )
                
            # Check if user has admin role
            if is_admin and user.role != Role.admin:
                # Track failed login attempt
                try:
                    # Get bot detection service
                    bot_detection_service = request.app.state.bot_detection_service
                    
                    # Track failed login attempt
                    ip_address = request.client.host
                    await bot_detection_service.track_failed_login(username, ip_address)
                    
                    # Log the failed attempt
                    logger.warning(f"Failed admin login attempt for non-admin user {username} from IP {ip_address}")
                except Exception as e:
                    logger.error(f"Error tracking failed login attempt: {str(e)}")
                    
                # Log authentication failure
                await self._log_auth_failure(request, username, "non_admin_access", user.id)
                
                logger.warning(f"Authentication failed: User {username} does not have admin privileges")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="User does not have admin privileges"
                )

            # Reset failed login attempts
            try:
                # Get bot detection service
                bot_detection_service = request.app.state.bot_detection_service
                
                # Reset failed login attempts
                ip_address = request.client.host
                await bot_detection_service.reset_failed_login(username, ip_address)
            except Exception as e:
                logger.error(f"Error resetting failed login attempts: {str(e)}")

            # Create tokens
            access_token, refresh_token = self.create_auth_tokens(user)
            
            return user, access_token, refresh_token
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication error"
            )
            
    async def _log_auth_failure(self, request, username, reason, user_id=None):
        """Helper method to log authentication failures"""
        if self.security_service:
            try:
                await self.security_service.log_security_event(
                    action_type="login",
                    status="failure",
                    ip_address=request.client.host,
                    risk_level="medium",
                    user_id=user_id,
                    user_agent=request.headers.get("user-agent"),
                    details={"username": username, "reason": reason}
                )
            except Exception as e:
                logger.error(f"Error logging security event: {str(e)}")
    
    async def get_current_user(self, request: Request) -> User:
        """Get current user from request"""
        token = await self.get_token_from_request(request)
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated"
            )
        
        # Pass security_service to validate_token
        user = await self.validate_token(token, self.db, self.security_service)
        if not user:
            # Raise specific exception if validation (including blacklist) fails
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid, expired, or blacklisted token",
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        return user
    
    async def get_current_admin(self, request: Request) -> User:
        """Get current admin user"""
        user = await self.get_current_user(request)
        if user.role != Role.admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        return user
    
    async def initialize(self):
        """Initialize required services"""
        try:
            # Initialize Redis client if available
            if self.redis_client:
                try:
                    await self.redis_client.initialize()
                except Exception as e:
                    logger.warning(f"Redis initialization failed, some features may be limited: {str(e)}")
            
            # Initialize security service if not already set
            if not self.security_service and self.db:
                from services.security_service import SecurityService
                self.security_service = SecurityService(self.db, self.redis_client)
                await self.security_service.initialize()
            
            logger.info("Auth services initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize Auth: {str(e)}")
            # Don't fail completely, just warn
            return False

# Helper functions
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash password"""
    return pwd_context.hash(password)

# Dependency for getting Auth instance
async def get_auth(db: Session = Depends(get_db)) -> Auth:
    """Get Auth instance and initialize authentication services."""
    auth_instance = Auth(db)
    from config.redis_config import RedisClient
    from services.rate_limit_service import RateLimitService
    from services.security_service import SecurityService
    redis_instance = RedisClient()
    await redis_instance.initialize()
    auth_instance.security_service = SecurityService(db, redis_instance)
    await auth_instance.initialize()
    return auth_instance

# Dependencies for route protection
async def get_current_user(
    request: Request,
    auth: Auth = Depends(get_auth)
) -> User:
    """Dependency for getting current user"""
    # Use the instance method which now includes the blacklist check implicitly
    return await auth.get_current_user(request)

async def get_current_admin(
    request: Request,
    auth: Auth = Depends(get_auth)
) -> User:
    """Dependency for getting current admin user"""
    return await auth.get_current_admin(request)

# Role verification
def verify_admin(user: User) -> bool:
    """Verify if user is admin"""
    return user.role == Role.admin

def verify_admin_or_self(current_user: User, target_user_id: int) -> bool:
    """Verify if current user is admin or accessing their own data"""
    return current_user.role == Role.admin or current_user.id == target_user_id

# Custom exceptions
class PermissionDenied(HTTPException):
    """Custom exception for permission denied cases"""
    def __init__(self, detail: str = "Not enough permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail
        )

class InvalidCredentials(HTTPException):
    """Custom exception for invalid credentials"""
    def __init__(self, detail: str = "Could not validate credentials"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"}
        )

class TelegramAuthError(HTTPException):
    def __init__(self, detail: str, error_code: str = None):
        super().__init__(
            status_code=401,
            detail={
                "message": detail,
                "error_code": error_code or "TELEGRAM_AUTH_ERROR"
            }
        )

# Add custom exceptions
class InvalidTelegramData(TelegramAuthError):
    def __init__(self):
        super().__init__(
            detail="Invalid Telegram data",
            error_code="INVALID_TG_DATA"
        )

# Add request origin validation
def verify_request_origin(request: Request) -> bool:
    """Verify request origin and headers"""
    allowed_origins = [
        "https://web.telegram.org",
        None,  # Allow null origin for Telegram WebApp
        "https://dev.atlasvip.cloud"  # Your frontend domain
    ]
    
    origin = request.headers.get("origin")
    referer = request.headers.get("referer")
    
    # Check if request is from Telegram WebApp
    if referer and "https://web.telegram.org/" in referer:
        return True
        
    if origin in allowed_origins:
        return True
        
    # Check user agent for Telegram WebApp
    user_agent = request.headers.get("user-agent", "")
    if "TelegramWebApp" in user_agent:
        return True
        
    return False

# Add input validation
def validate_telegram_data(init_data: str) -> dict:
    """
    Verify Telegram WebApp init data and extract user information.
    Returns the verified user data if valid, raises an exception if not.
    """
    try:
        # Parse the init_data string
        data_dict = dict(parse_qsl(init_data))
        
        # Get the hash to verify
        received_hash = data_dict.pop('hash', None)
        if not received_hash:
            raise ValueError("No hash found in init data")
        
        # Sort the data alphabetically
        data_check_string = '\n'.join(f"{k}={v}" for k, v in sorted(data_dict.items()))
        
        # Calculate secret key using HMAC-SHA256
        secret_key = hmac.new(
            "WebAppData".encode(),
            BOT_TOKEN.encode(),
            hashlib.sha256
        ).digest()
        
        # Calculate data hash
        data_hash = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Verify hash using constant-time comparison
        if not hmac.compare_digest(data_hash, received_hash):
            raise ValueError("Invalid hash")
        
        # Extract and verify user data
        try:
            user_data = json.loads(data_dict.get('user', '{}'))
            if not user_data.get('id'):
                raise ValueError("No user ID in data")
            
            # Verify auth_date is not too old (within 24 hours)
            auth_date = int(data_dict.get('auth_date', 0))
            current_time = int(datetime.utcnow().timestamp())
            if current_time - auth_date > 86400:  # 24 hours
                raise ValueError("Auth date too old")
                
            return user_data
        except json.JSONDecodeError:
            raise ValueError("Invalid user data format")
            
    except Exception as e:
        raise ValueError(f"Telegram data verification failed: {str(e)}")

# Enhanced token creation
def create_secure_token(user_data: dict, telegram_data: dict) -> str:
    """Create a more secure token with additional claims"""
    now = datetime.utcnow()
    token_data = {
        "sub": user_data["username"],
        "type": "access",
        "role": user_data["role"],
        "telegram_id": str(telegram_data["id"]),
        "auth_time": int(now.timestamp()),
        "iat": now,  # issued at
        "nbf": now,  # not valid before
        "jti": secrets.token_hex(16),  # unique token ID
        "auth_method": "telegram"
    }
    return create_access_token(data=token_data)

async def verify_telegram_webapp_data(init_data: str, request: Request = None) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """
    Verify Telegram Web App data according to official documentation with enhanced security
    https://core.telegram.org/bots/webapps#validating-data-received-via-the-web-app
    """
    try:
        # Reduced logging for performance - only log in development
        is_development = os.getenv("DEBUG", "false").lower() == "true"
        if is_development:
            logger.info("Starting Telegram WebApp verification...")
        
        # 1. Validate request origin if provided - simplified check
        if request and not verify_request_origin(request):
            if is_development:
                logger.error("Invalid request origin")
            return False, None

        if not init_data:
            if is_development:
                logger.error("Empty init_data received")
            return False, None

        # 2. Parse the init_data string
        data_dict = dict(parse_qsl(init_data))
        
        # 3. Validate required fields
        required_fields = ['auth_date', 'user', 'hash']
        if not all(field in data_dict for field in required_fields):
            if is_development:
                logger.error("Missing required fields in init_data")
            return False, None

        # 4. Get and validate hash
        received_hash = data_dict.pop('hash', None)
        if not received_hash:
            if is_development:
                logger.error("No hash found in init_data")
            return False, None

        # 5. Sort data alphabetically and create check string
        data_check_string = '\n'.join(
            f"{k}={v}" for k, v in sorted(data_dict.items())
        )

        # 6. Calculate secret key using HMAC-SHA256
        if not BOT_TOKEN:
            if is_development:
                logger.error("BOT_TOKEN not found in environment variables")
            return False, None

        secret_key = hmac.new(
            "WebAppData".encode(),
            BOT_TOKEN.encode(),
            hashlib.sha256
        ).digest()

        # 7. Calculate data hash
        data_hash = hmac.new(
            secret_key,
            data_check_string.encode(),
            hashlib.sha256
        ).hexdigest()

        # 8. Verify hash using constant-time comparison
        if not hmac.compare_digest(data_hash, received_hash):
            if is_development:
                logger.error("Invalid hash in init_data")
            return False, None

        # 9. Verify auth_date is not too old (within 24 hours)
        try:
            auth_date = int(data_dict.get('auth_date', 0))
            current_time = int(datetime.utcnow().timestamp())
            if current_time - auth_date > 86400:  # 24 hours
                if is_development:
                    logger.error("Auth date too old")
                return False, None
        except ValueError:
            if is_development:
                logger.error("Invalid auth_date format")
            return False, None

        # 10. Parse and validate user data
        try:
            user_data = json.loads(data_dict.get('user', '{}'))
            if not isinstance(user_data.get('id'), (int, str)):
                if is_development:
                    logger.error("Invalid user ID format")
                return False, None
                
            # Ensure all values are JSON serializable
            user_data = {
                k: str(v) if isinstance(v, bytes) else v 
                for k, v in user_data.items()
            }
            
            # Log successful verification only in development
            if is_development:
                logger.info(
                    f"Telegram auth verified for user - ID: {user_data.get('id')}, "
                    f"Username: {user_data.get('username', 'N/A')}"
                )
            return True, user_data

        except json.JSONDecodeError as e:
            if is_development:
                logger.error(f"Failed to parse user data: {e}")
            return False, None

    except Exception as e:
        # Always log exceptions
        logger.error(f"Error in verify_telegram_webapp_data: {str(e)}", exc_info=True)
        return False, None

async def process_telegram_auth(init_data: str) -> Optional[Dict[str, Any]]:
    """
    Process Telegram authentication data.
    Returns verified user data if successful, None otherwise.
    """
    try:
        logger.info("Starting Telegram WebApp verification...")
        
        # Verify the data
        is_valid = verify_telegram_webapp_data(init_data)
        if not is_valid:
            logger.error("Telegram data verification failed")
            return None
            
        # Parse the data
        parsed_data = parse_telegram_webapp_data(init_data)
        if not parsed_data:
            logger.error("Failed to parse Telegram data")
            return None
            
        # Extract user info
        user_id = parsed_data.get("id")
        username = parsed_data.get("username")
        
        if not user_id:
            logger.error("No user ID in Telegram data")
            return None
            
        logger.info(f"Telegram auth verified for user - ID: {user_id}, Username: {username}")
        return parsed_data
            
    except Exception as e:
        logger.error(f"Error in process_telegram_auth: {str(e)}")
        return None

def generate_secure_password(length: int = 16) -> str:
    """Generate a secure random password"""
    chars = string.ascii_letters + string.digits + "!@#$%^&*"
    secure_random = secrets.SystemRandom()
    password = ''.join(secure_random.choice(chars) for _ in range(length))
    
    # Ensure password contains at least one of each required type
    if not any(c.isupper() for c in password):
        password = secure_random.choice(string.ascii_uppercase) + password[1:]
    if not any(c.islower() for c in password):
        password = password[:-1] + secure_random.choice(string.ascii_lowercase)
    if not any(c.isdigit() for c in password):
        password = password[:-2] + secure_random.choice(string.digits) + password[-1:]
    if not any(c in "!@#$%^&*" for c in password):
        password = password[:-3] + secure_random.choice("!@#$%^&*") + password[-2:]
    
    return password

async def verify_telegram_setup_request(init_data: str, db: Session) -> Dict[str, Any]:
    """Verify telegram data and check if user can be created"""
    is_valid, telegram_data = await verify_telegram_webapp_data(init_data)
    
    if not is_valid or not telegram_data:
        raise HTTPException(
            status_code=401,
            detail="Invalid Telegram verification"
        )

    telegram_id = telegram_data.get('id')
    if not telegram_id:
        raise HTTPException(
            status_code=400,
            detail="Telegram ID is required"
        )

    # Check if user already exists
    existing_user = db.query(User).filter(User.telegram_id == telegram_id).first()
    if existing_user:
        raise HTTPException(
            status_code=400,
            detail="User already exists"
        )

    return telegram_data

async def logout(response: Response):
    """Simple logout - just clear cookies"""
    response.delete_cookie("access_token", path="/")
    response.delete_cookie("refresh_token", path="/")
    return {"message": "Successfully logged out"}

def set_auth_cookie(response: Response, token: str):
    """Set HttpOnly cookie with the auth token"""
    response.set_cookie(
        key=COOKIE_NAME,
        value=token,
        httponly=True,
        secure=True,  # Only send over HTTPS
        samesite='lax',  # Protect against CSRF
        max_age=ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        path="/"
    )

class AuthRateLimitMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        if request.url.path.startswith("/auth/"):
            try:
                db = next(get_db())
                rate_limiter = RateLimiter(db)
                client_ip = request.client.host
                
                # Use IP-based rate limiting for auth endpoints
                if not await rate_limiter.check_rate_limit(
                    user_id=None,  # No user yet for auth endpoints
                    action_type="auth_request",
                    max_attempts=20,
                    window_seconds=3600,  # 1 hour
                    ip_address=client_ip
                ):
                    return Response(
                        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                        content="Rate limit exceeded. Please try again later."
                    )
                
                # Log the attempt
                await rate_limiter.log_attempt(
                    user_id=None,
                    action_type="auth_request",
                    ip_address=client_ip
                )
            except Exception as e:
                logger.error(f"Rate limit error: {str(e)}")
                # Continue even if rate limiting fails
                pass
                
        return await call_next(request)

# Add these functions after the get_current_user dependency
async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user with additional checks"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Inactive user"
        )
    return current_user

async def get_current_reseller(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get current reseller or admin user"""
    if current_user.role not in [Role.admin, Role.reseller]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

# Add these module-level functions at the end of the file

async def authenticate_user(username: str, password: str, request: Request = None) -> Tuple[User, str, str]:
    """
    Authenticate user with username/password and return user, access_token, and refresh_token
    """
    from database import get_db
    
    # Get database session
    db_gen = get_db()
    db = next(db_gen)
    
    try:
        # Get user from database
        user = db.query(User).filter(User.username == username).first()
        
        if not user:
            logger.warning(f"Authentication failed: User {username} not found")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"message": "Invalid username or password", "code": "INVALID_CREDENTIALS"},
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        if not user.is_active:
            logger.warning(f"Authentication failed: User {username} is not active")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail={"message": "Account is disabled", "code": "ACCOUNT_DISABLED"},
            )
        
        # Verify password
        if not verify_password(password, user.hashed_password):
            logger.warning(f"Authentication failed: Invalid password for user {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"message": "Invalid username or password", "code": "INVALID_CREDENTIALS"},
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Create tokens
        token_data = {
            "sub": user.username,
            "role": user.role.value,
            "user_id": user.id
        }
        
        access_token = create_access_token(token_data)
        refresh_token = create_refresh_token(token_data)
        
        logger.info(f"User {username} authenticated successfully")
        return user, access_token, refresh_token
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Authentication error for user {username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "Authentication failed", "code": "AUTH_ERROR"}
        )
    finally:
        db.close()

def set_auth_cookies(response: Response, access_token: str, refresh_token: str):
    """Module-level function to set authentication cookies"""
    return Auth.set_auth_cookies(response, access_token, refresh_token)

def clear_auth_cookies(response: Response):
    """Module-level function to clear authentication cookies"""
    return Auth.clear_auth_cookies(response)

async def verify_refresh_token(token: str, db: Session = None) -> Optional[User]:
    """Module-level function to verify refresh token"""
    if db is None:
        from database import get_db
        db_gen = get_db()
        db = next(db_gen)
    
    return await Auth.validate_refresh_token(token, db)

def create_access_token(
    data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    """Module-level function to create JWT access token"""
    to_encode = data.copy()
    expire = datetime.utcnow() + (expires_delta or timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))
    to_encode.update({
        "exp": expire,
        "iat": datetime.utcnow(),
        "type": "access",
        "user_id": data.get("user_id"),
        "role": data.get("role"),
        "sub": data.get("sub")
    })
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def create_refresh_token(
    data: Dict[str, Any],
    expires_delta: Optional[timedelta] = None
) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire, "token_type": "refresh"})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def get_password_hash(password: str) -> str:
    """Module-level function to hash password with additional validation"""
    if not password:
        raise ValueError("Password cannot be empty")
    
    if len(password) > 72:  # bcrypt has a 72-byte limit
        raise ValueError("Password must be 72 bytes or fewer")
        
    if isinstance(password, str):
        password = password.encode('utf-8')
    
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error(f"Password hashing failed: {str(e)}")
        raise ValueError("Password hashing failed")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Module-level function to verify password with additional validation"""
    if not plain_password or not hashed_password:
        return False
        
    if len(plain_password) > 72:  # bcrypt has a 72-byte limit
        return False
        
    if isinstance(plain_password, str):
        plain_password = plain_password.encode('utf-8')
    
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Password verification failed: {str(e)}")
        return False

# Add verify_token function for WebSocket authentication
async def verify_token(token: str, db: Session = None) -> Optional[Dict[str, Any]]:
    """
    Verify a JWT token for WebSocket connections and return user information
    
    Args:
        token: JWT token to verify
        db: Database session (optional)
        
    Returns:
        Dictionary with user information if token is valid, None otherwise
    """
    try:
        # If no db provided, get from dependency
        if db is None:
            from database import get_db
            db_gen = get_db()
            db = next(db_gen)
            
        # Decode token
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        token_type: str = payload.get("type")
        user_id: int = payload.get("user_id")
        
        if not username or token_type != "access" or not user_id:
            logging.warning(f"Invalid token format in WebSocket verify_token: {token[:10]}...")
            return None
        
        # Check for token in blacklist if db is available
        if db:
            from models import TokenBlacklist
            blacklisted = db.query(TokenBlacklist).filter(
                TokenBlacklist.token == token,
                TokenBlacklist.expires_at > datetime.utcnow()
            ).first()
            
            if blacklisted:
                logging.warning(f"Blacklisted token used in WebSocket: {token[:10]}...")
                return None
            
            # Get and validate user
            user = db.query(User).filter(User.id == user_id, User.is_active == True).first()
            if not user:
                logging.warning(f"User {user_id} not found or inactive")
                return None
                
            # Return user information
            return {
                "user_id": user_id,
                "username": username,
                "role": payload.get("role"),
                "exp": payload.get("exp")
            }
        else:
            # If no db available, just return the payload
            return {
                "user_id": user_id,
                "username": username,
                "role": payload.get("role")
            }
            
    except JWTError as e:
        logging.error(f"JWT verification error in WebSocket: {str(e)}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error in verify_token: {str(e)}")
        return None
