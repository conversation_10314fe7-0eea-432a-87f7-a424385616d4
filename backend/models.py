# models.py
from datetime import datetime, timedelta
from typing import Optional, List, ClassVar
from sqlalchemy import String, Enum as SQLEnum, Float, DateTime, Text, BigInteger, Integer, Table, Column, ForeignKey, Boolean, Index, JSON, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship, validates, selectinload
from database import Base
import enum
from sqlalchemy.sql import func
from enum import Enum
import json
import os
import math

# Association table for many-to-many relationship between VPNPackage and MarzbanPanel
package_panel_association = Table(
    "package_panel_association",
    Base.metadata,
    Column("package_id", Integer, ForeignKey("vpn_packages.id", ondelete="CASCADE"), primary_key=True),
    Column("panel_id", Integer, ForeignKey("marzban_panels.id", ondelete="CASCADE"), primary_key=True)
)

class Role(str, enum.Enum):
    admin = "admin"
    reseller = "reseller"
    user = "user"

    @classmethod
    def from_str(cls, value: str) -> 'Role':
        try:
            return cls(value.lower())
        except ValueError:
            raise ValueError(f"Invalid role value: {value}")

class VPNPackage(Base):
    __tablename__ = "vpn_packages"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    data_limit: Mapped[int] = mapped_column(BigInteger, nullable=False)
    expire_days: Mapped[int] = mapped_column(Integer, nullable=False)
    price: Mapped[float] = mapped_column(Float, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        nullable=False,
        server_default=func.now()
    )
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationship with MarzbanPanel through association table
    allowed_panels: Mapped[List["MarzbanPanel"]] = relationship(
        "MarzbanPanel",
        secondary=package_panel_association,
        back_populates="available_packages"
    )
 
class User(Base):
    __tablename__ = "users"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    telegram_id: Mapped[Optional[int]] = mapped_column(unique=True, index=True, nullable=True)
    username: Mapped[str] = mapped_column(String(50), unique=True, index=True, nullable=False)
    hashed_password: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    role: Mapped[Role] = mapped_column(SQLEnum(Role), default=Role.user, nullable=False)
    wallet_balance: Mapped[float] = mapped_column(Float, default=0.0, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, nullable=False)
    first_name: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    last_name: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    email: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    telegram_photo_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    discount_percent: Mapped[float] = mapped_column(Float, default=0.0)
    marzban_username: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    marzban_subscription_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    referred_by: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), nullable=True)
    referral_code: Mapped[str] = mapped_column(String(8), unique=True, nullable=False)
    device_platform: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    device_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    last_login: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    last_passive_claim_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Simpler relationship definition
    referrals: Mapped[List["User"]] = relationship(
        "User",
        primaryjoin="User.referred_by==User.id",
        remote_side=[id]
    )
    
    vpn_subscriptions: Mapped[List["VPNSubscription"]] = relationship(
        "VPNSubscription",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    transactions: Mapped[List["Transaction"]] = relationship(
        "Transaction",
        foreign_keys="Transaction.user_id",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    task_completions: Mapped[List["TaskCompletion"]] = relationship(
        "TaskCompletion",
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    daily_streak: Mapped["DailyTaskStreak"] = relationship("DailyTaskStreak", back_populates="user", uselist=False)
    
    security_logs: Mapped[List["SecurityLog"]] = relationship("SecurityLog", back_populates="user", cascade="all, delete")
    revoked_tokens: Mapped[List["TokenBlacklist"]] = relationship("TokenBlacklist", back_populates="user", cascade="all, delete")
    
    daily_cycles: Mapped[List["DailyCycle"]] = relationship("DailyCycle", back_populates="user")
    
    # NEW: Relationship to UserCard
    user_cards: Mapped[List["UserCard"]] = relationship(
        "UserCard",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy='selectin' # Optional performance hint
    )

    # Add these relationships to the existing User model
    sent_messages: Mapped[List["ChatMessage"]] = relationship(
        "ChatMessage", 
        foreign_keys="ChatMessage.sender_id",
        back_populates="sender",
        cascade="all, delete-orphan",
        lazy='selectin'  # Optional performance hint
    )
    
    received_messages: Mapped[List["ChatMessage"]] = relationship(
        "ChatMessage",
        foreign_keys="ChatMessage.receiver_id",
        back_populates="receiver",
        cascade="all, delete-orphan",
        lazy='selectin'  # Optional performance hint
    )

    @property
    def total_passive_hourly_income(self) -> float:
        """Calculates the sum of hourly profit from all owned cards."""
        if not hasattr(self, 'user_cards') or not self.user_cards:
            return 0.0
        total_profit = sum(
            card.current_hourly_profit 
            for card in self.user_cards
            if hasattr(card, 'current_hourly_profit') and card.card_catalog is not None 
        )
        return round(total_profit, 4)

    def __repr__(self) -> str:
        return f"User(id={self.id}, username={self.username}, role={self.role})"

    def to_dict(self) -> dict:
        """Convert user object to dictionary"""
        return {
            "id": self.id,
            "username": self.username,
            "role": self.role.value,
            "telegram_id": self.telegram_id,
            "email": self.email,
            "wallet_balance": float(self.wallet_balance) if self.wallet_balance else 0.0,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "telegram_username": self.telegram_username,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "telegram_photo_url": self.telegram_photo_url,
            "is_active": self.is_active
        }

class MarzbanPanel(Base):
    __tablename__ = "marzban_panels"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    api_url: Mapped[str] = mapped_column(String(255), nullable=False)
    admin_username: Mapped[str] = mapped_column(String(100), nullable=False)
    admin_password: Mapped[str] = mapped_column(String(255), nullable=False)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime, 
        nullable=False,
        server_default=func.now()
    )
    
    # Relationship with VPNPackage through association table
    available_packages: Mapped[List["VPNPackage"]] = relationship(
        "VPNPackage",
        secondary=package_panel_association,
        back_populates="allowed_panels",
        overlaps="packages"
    )
    
    # Remove or update this relationship if you're using it elsewhere
    packages: Mapped[List["VPNPackage"]] = relationship(
        "VPNPackage",
        secondary=package_panel_association,
        overlaps="available_packages,allowed_panels",
        viewonly=True  # Make this relationship read-only
    )

class VPNSubscription(Base):
    __tablename__ = "vpn_subscriptions"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"))
    package_id: Mapped[int] = mapped_column(ForeignKey("vpn_packages.id", ondelete="CASCADE"))
    marzban_username: Mapped[str] = mapped_column(String(100))
    subscription_url: Mapped[str] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    expires_at: Mapped[datetime] = mapped_column(DateTime)
    data_limit: Mapped[int] = mapped_column(BigInteger)  # in bytes
    data_used: Mapped[int] = mapped_column(BigInteger, default=0)  # in bytes
    is_active: Mapped[bool] = mapped_column(default=True)
    
    # Keep only essential relationships
    user: Mapped["User"] = relationship(
        "User", 
        back_populates="vpn_subscriptions"
    )
    package: Mapped["VPNPackage"] = relationship("VPNPackage")

class TransactionType(str, enum.Enum):
    subscription_purchase = "subscription_purchase"
    subscription_renewal = "subscription_renewal"
    wallet_topup = "wallet_topup"
    referral_commission = "referral_commission"
    admin_adjustment = "admin_adjustment"
    task_reward = "task_reward"
    # Card System Transaction Types
    card_profit = "card_profit" # Profit claimed from a single card
    card_profit_all = "card_profit_all" # Profit claimed from all cards at once
    card_upgrade = "card_upgrade" # Cost for upgrading a card
    card_purchase = "card_purchase" # Cost for buying a new card

class Transaction(Base):
    __tablename__ = "transactions"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    type: Mapped[TransactionType] = mapped_column(SQLEnum(TransactionType), nullable=False)
    amount: Mapped[float] = mapped_column(Float, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # User who owns the transaction
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"))
    user: Mapped["User"] = relationship("User", foreign_keys=[user_id], back_populates="transactions")
    
    # Reseller who created the transaction (if applicable)
    reseller_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    reseller: Mapped[Optional["User"]] = relationship("User", foreign_keys=[reseller_id])
    
    # Related subscription (if applicable)
    subscription_id: Mapped[Optional[int]] = mapped_column(ForeignKey("vpn_subscriptions.id", ondelete="SET NULL"), nullable=True)
    subscription: Mapped[Optional["VPNSubscription"]] = relationship("VPNSubscription")
    
    # Package details at the time of transaction
    package_name: Mapped[str] = mapped_column(String(100), nullable=True)
    package_price: Mapped[float] = mapped_column(Float, nullable=True)
    package_data_limit: Mapped[Optional[int]] = mapped_column(BigInteger, nullable=True)
    package_expire_days: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Additional details
    description: Mapped[str] = mapped_column(Text, nullable=True)
    balance_after: Mapped[float] = mapped_column(Float, nullable=False)
    status: Mapped[str] = mapped_column(String(20), default="completed")

    def __repr__(self) -> str:
        return f"Transaction(id={self.id}, type={self.type}, amount={self.amount})"

class SystemConfig(Base):
    __tablename__ = "system_configs"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    key: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    value: Mapped[str] = mapped_column(Text, nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=True)
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self) -> str:
        return f"SystemConfig(key={self.key}, value={self.value})"

class RewardType(str, Enum):
    DATA_BONUS = 'DATA_BONUS'
    DAYS_BONUS = 'DAYS_BONUS'
    WALLET_BONUS = 'WALLET_BONUS'

class TaskType(str, Enum):
    DAILY_CHECKIN = 'DAILY_CHECKIN'
    YOUTUBE_VIEW = 'YOUTUBE_VIEW'
    INSTAGRAM_FOLLOW = 'INSTAGRAM_FOLLOW'
    INSTAGRAM_VIEW = 'INSTAGRAM_VIEW'
    TELEGRAM_CHANNEL = 'TELEGRAM_CHANNEL'
    TWITTER_FOLLOW = 'TWITTER_FOLLOW'
    WEBSITE_VISIT = 'WEBSITE_VISIT'
    REFERRAL = 'REFERRAL'

class TaskStatus(str, Enum):
    ACTIVE = 'ACTIVE'     # When task is available but not started
    PENDING = 'PENDING'   # When task is started but not completed
    COMPLETED = 'COMPLETED' # When task is completed
    FAILED = 'FAILED'     # When task verification fails
    EXPIRED = 'EXPIRED'   # When task time limit expires
    REVOKED = 'REVOKED'   # When task is revoked by admin

class SocialPlatform(str, enum.Enum):
    telegram = "telegram"
    youtube = "youtube"
    instagram = "instagram"
    website = "website"
    twitter = "twitter"

class Task(Base):
    __tablename__ = "tasks"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), nullable=False)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    type: Mapped[TaskType] = mapped_column(SQLEnum(TaskType), nullable=False, index=True)
    reward_type: Mapped[RewardType] = mapped_column(SQLEnum(RewardType), nullable=False, index=True)
    reward_value: Mapped[float] = mapped_column(Float, nullable=False)
    target_value: Mapped[int] = mapped_column(Integer, default=1)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Daily task specific fields
    cycle_length: Mapped[Optional[int]] = mapped_column(Integer, default=7)  # Days in one cycle
    daily_rewards: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON array of daily rewards [10,20,30,40,50,60,100]
    cycle_bonus_reward: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # Additional bonus for completing full cycle
    reset_streak_after_hours: Mapped[Optional[int]] = mapped_column(Integer, default=48)  # Hours until streak resets
    total_cycle_reward: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    
    # Platform fields
    platform_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    platform_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, index=True)
    verify_key: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    max_verification_attempts: Mapped[Optional[int]] = mapped_column(Integer, default=3, nullable=True)
    verification_cooldown: Mapped[Optional[int]] = mapped_column(Integer, default=60, nullable=True)  # In seconds
    
    # Relationships
    completions: Mapped[List["TaskCompletion"]] = relationship(
        "TaskCompletion",
        back_populates="task",
        cascade="all, delete-orphan"
    )

    cycles: Mapped[List["DailyCycle"]] = relationship("DailyCycle", back_populates="task")

    # Indexes
    __table_args__ = (
        Index('idx_task_type_active', 'type', 'is_active'),
        Index('idx_task_reward', 'reward_type', 'reward_value'),
        Index('idx_task_platform', 'platform_id', 'type')
    )

    @property
    def get_daily_rewards(self) -> List[float]:
        """Get the daily rewards array"""
        if not self.daily_rewards:
            return [self.reward_value] * (self.cycle_length or 7)  # Default to base reward if not set
        try:
            return json.loads(self.daily_rewards)
        except:
            return [self.reward_value] * (self.cycle_length or 7)
    
    @property
    def total_cycle_reward(self) -> float:
        """Calculate total reward for completing a full cycle"""
        daily_rewards = self.get_daily_rewards
        base_total = sum(daily_rewards)
        bonus = self.cycle_bonus_reward or 0
        return base_total + bonus

class TaskCompletion(Base):
    __tablename__ = "task_completions"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    task_id: Mapped[int] = mapped_column(ForeignKey("tasks.id", ondelete="CASCADE"), index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), index=True)
    status: Mapped[TaskStatus] = mapped_column(SQLEnum(TaskStatus), default=TaskStatus.ACTIVE, nullable=False, index=True)
    started_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    current_progress: Mapped[int] = mapped_column(Integer, default=0)
    is_claimed: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    last_verified_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    claimed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    verification_data: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    claim_available_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)  # When reward can be claimed
    verification_attempts: Mapped[int] = mapped_column(Integer, default=0)  # Number of verification attempts
    max_verification_attempts: Mapped[int] = mapped_column(Integer, default=3)  # Maximum allowed verification attempts
    verification_cooldown: Mapped[int] = mapped_column(Integer, default=60)  # Cooldown between verifications in seconds
    
    # Daily task specific fields
    streak_day: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    cycle_day: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    reward_multiplier: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Added security fields
    reward_amount: Mapped[float] = mapped_column(Float, nullable=False, default=0.0)
    verification_ip: Mapped[Optional[str]] = mapped_column(String(45), nullable=True)
    verification_user_agent: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    is_verified: Mapped[bool] = mapped_column(Boolean, default=False)
    verification_method: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    verification_token: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Relationships
    task: Mapped["Task"] = relationship("Task", back_populates="completions")
    user: Mapped["User"] = relationship("User", back_populates="task_completions")
    verifications: Mapped[List["TaskVerification"]] = relationship(
        "TaskVerification",
        back_populates="task_completion",
        cascade="all, delete-orphan"
    )

    cycle_id: Mapped[Optional[int]] = mapped_column(ForeignKey("daily_cycles.id"), nullable=True)
    cycle: Mapped[Optional["DailyCycle"]] = relationship("DailyCycle", back_populates="completions")

    @validates('status')
    def validate_status_transition(self, key: str, value: TaskStatus) -> TaskStatus:
        """Validate task status transitions"""
        valid_transitions = {
            None: [TaskStatus.ACTIVE, TaskStatus.PENDING],  # Allow initial status
            TaskStatus.ACTIVE: [TaskStatus.PENDING, TaskStatus.FAILED],
            TaskStatus.PENDING: [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.EXPIRED],
            TaskStatus.COMPLETED: [TaskStatus.REVOKED, TaskStatus.COMPLETED],  # Allow COMPLETED -> COMPLETED for daily tasks
            TaskStatus.FAILED: [],
            TaskStatus.EXPIRED: [],
            TaskStatus.REVOKED: []
        }
        
        current_status = getattr(self, 'status', None)
        if current_status not in valid_transitions or value not in valid_transitions.get(current_status, []):
            if current_status is None and value in [TaskStatus.ACTIVE, TaskStatus.PENDING]:
                return value  # Allow initial status setting
            raise ValueError(f"Invalid status transition from {current_status} to {value}")
            
        # Additional state validation
        if value == TaskStatus.COMPLETED:
            # Skip verification check in test mode
            if os.getenv("TEST_MODE", "false").lower() != "true":
                if not self.is_verified:
                    raise ValueError("Cannot complete unverified task")
            if not self.verification_data:
                raise ValueError("Missing verification data")
            self.completed_at = datetime.utcnow()
                
        elif value == TaskStatus.FAILED:
            if not self.is_verified:
                raise ValueError("Cannot fail unverified task")
                    
        elif value == TaskStatus.EXPIRED:
            if not self.expires_at or datetime.utcnow() <= self.expires_at:
                raise ValueError("Cannot expire valid task")
        
        return value

    @validates('is_claimed')
    def validate_claim(self, key: str, value: bool) -> bool:
        """Validate reward claim"""
        if value and not hasattr(self, 'is_claimed'):
            if self.status != TaskStatus.COMPLETED:
                raise ValueError("Cannot claim uncompleted task")
            if not self.is_verified:
                raise ValueError("Cannot claim unverified task")
            if self.claimed_at:
                raise ValueError("Task already claimed")
                
            # Set claim timestamp and calculate reward
            self.claimed_at = datetime.utcnow()
            self.reward_amount = self.calculate_reward()
            
        return value

    def calculate_reward(self) -> float:
        """Calculate final reward amount"""
        base_reward = self.task.reward_value
        multiplier = self.reward_multiplier or 1.0
        
        # Apply any additional bonuses based on completion time, streak, etc.
        if self.streak_day and self.streak_day > 1:
            multiplier *= 1.0 + (self.streak_day * 0.1)  # 10% bonus per streak day
            
        if self.cycle_day and self.cycle_day == 7:  # Full cycle completion
            multiplier *= 1.5  # 50% bonus for completing full cycle
            
        return base_reward * multiplier

    def verify_task(self, verification_data: Optional[dict] = None) -> tuple[bool, Optional[str]]:
        """Verify task completion"""
        try:
            # Check if can verify
            can_verify, error = self.can_verify()
            if not can_verify:
                return False, error
                
            # Store verification data
            if verification_data:
                self.verification_data = json.dumps(verification_data)
                
            # Mark as verified if successful
            self.is_verified = True
            self.last_verified_at = datetime.utcnow()
            
            return True, None
            
        except Exception as e:
            return False, str(e)

    def can_verify(self) -> tuple[bool, Optional[str]]:
        """Check if task can be verified"""
        # Check task status
        if self.status not in [TaskStatus.PENDING]:
            return False, f"Task cannot be verified in {self.status} status"
            
        # Check expiration
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return False, "Task has expired"
            
        return True, None

    # Keep completion indexes
    __table_args__ = (
        Index('idx_completion_user_task', 'user_id', 'task_id', unique=True),
        Index('idx_completion_status', 'status', 'is_claimed'),
        Index('idx_completion_expiry', 'expires_at', 'status')
    )

class TaskPack(Base):
    __tablename__ = "task_packs"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    name: Mapped[str] = mapped_column(String(100))
    description: Mapped[str] = mapped_column(Text)
    reward_package_id: Mapped[int] = mapped_column(ForeignKey("vpn_packages.id"))
    is_lifetime: Mapped[bool] = mapped_column(default=False)
    auto_renewal: Mapped[bool] = mapped_column(default=False)
    is_active: Mapped[bool] = mapped_column(default=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    tasks: Mapped[List["Task"]] = relationship("Task", secondary="task_pack_tasks")
    reward_package: Mapped["VPNPackage"] = relationship("VPNPackage")

class TaskPackTask(Base):
    __tablename__ = "task_pack_tasks"
    
    pack_id: Mapped[int] = mapped_column(ForeignKey("task_packs.id"), primary_key=True)
    task_id: Mapped[int] = mapped_column(ForeignKey("tasks.id"), primary_key=True)
    order: Mapped[int] = mapped_column(Integer)

    # Composite index for ordered tasks
    __table_args__ = (
        Index('idx_pack_task_order', 'pack_id', 'order'),  # For sequential task access
    )

class DailyTaskStreak(Base):
    __tablename__ = "daily_task_streaks"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), index=True)
    current_streak: Mapped[int] = mapped_column(Integer, default=0)
    longest_streak: Mapped[int] = mapped_column(Integer, default=0)
    last_check_in: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True, index=True)
    total_check_ins: Mapped[int] = mapped_column(Integer, default=0)
    current_cycle_day: Mapped[int] = mapped_column(Integer, default=1)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    first_check_time: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    last_streak_break: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="daily_streak")

    # Composite indexes for streak checks
    __table_args__ = (
        Index('idx_user_streak', 'user_id', 'last_check_in'),  # For streak validation
        Index('idx_user_cycle', 'user_id', 'current_cycle_day', 'last_check_in'),  # For cycle tracking
    )

class TaskVerification(Base):
    __tablename__ = "task_verifications"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    task_completion_id: Mapped[int] = mapped_column(ForeignKey("task_completions.id", ondelete="CASCADE"))
    status: Mapped[str] = mapped_column(String, nullable=False)
    verification_data: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    verified_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    next_verification: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    ip_address: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    user_agent: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    verification_method: Mapped[str] = mapped_column(String)  # "api", "code", "manual", "auto"
    verification_result: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    # Relationship
    task_completion: Mapped["TaskCompletion"] = relationship("TaskCompletion", back_populates="verifications")

    def __repr__(self):
        return f"<TaskVerification(id={self.id}, completion_id={self.task_completion_id}, status={self.status})>"

class SecurityLog(Base):
    """Model for logging security-related events and rate limiting"""
    __tablename__ = "security_logs"

    id = Column(Integer, primary_key=True, index=True)
    action_type = Column(String(50), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(String(255), nullable=True)
    status = Column(String(20), nullable=False)  # success, failure, blocked, etc.
    details = Column(JSON, nullable=True)
    location_info = Column(JSON, nullable=True)  # new field to store location information for security events
    risk_level = Column(String(10), nullable=False)  # low, medium, high, critical
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Rate limiting specific fields
    attempt_count = Column(Integer, default=1)  # Number of attempts in the window
    window_start = Column(DateTime, nullable=True)  # Start of the rate limit window
    blocked_until = Column(DateTime, nullable=True)  # When the block expires
    is_blocked = Column(Boolean, default=False)  # Whether the action is currently blocked

    # Relationships
    user = relationship("User", back_populates="security_logs")

    __table_args__ = (
        Index('idx_security_logs_user_id', 'user_id'),
        Index('idx_security_logs_action_type', 'action_type'),
        Index('idx_security_logs_created_at', 'created_at'),
        Index('idx_security_logs_risk_level', 'risk_level'),
        Index('idx_security_logs_ip_action', 'ip_address', 'action_type'),
        Index('idx_security_logs_blocked', 'is_blocked', 'blocked_until'),
        # Composite index for rate limiting checks
        Index('idx_security_logs_rate_limit', 'action_type', 'ip_address', 'user_id', 'window_start')
    )

    def __repr__(self) -> str:
        return f"SecurityLog(id={self.id}, action={self.action_type}, status={self.status})"

    @property
    def is_rate_limited(self) -> bool:
        """Check if the action is currently rate limited"""
        if self.is_blocked and self.blocked_until:
            return datetime.utcnow() < self.blocked_until
        return False

    @property
    def reset_after(self) -> Optional[int]:
        """Get seconds until rate limit reset"""
        if self.blocked_until:
            delta = self.blocked_until - datetime.utcnow()
            return max(0, int(delta.total_seconds()))
        return None

class TokenBlacklist(Base):
    """Model for storing revoked tokens"""
    __tablename__ = "token_blacklist"

    id = Column(Integer, primary_key=True, index=True)
    token = Column(String(500), nullable=False, unique=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    expires_at = Column(DateTime, nullable=False)
    revoked_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    revocation_reason = Column(String(200), nullable=False)
    revoked_by_ip = Column(String(45), nullable=False)

    # Relationships
    user = relationship("User", back_populates="revoked_tokens")

    __table_args__ = (
        Index('idx_token_blacklist_token', 'token'),
        Index('idx_token_blacklist_user_id', 'user_id'),
        Index('idx_token_blacklist_expires_at', 'expires_at'),
    )

class DailyCycle(Base):
    __tablename__ = "daily_cycles"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    task_id: Mapped[int] = mapped_column(ForeignKey("tasks.id", ondelete="CASCADE"), index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), index=True)
    cycle_number: Mapped[int] = mapped_column(Integer, default=1)
    start_date: Mapped[datetime] = mapped_column(DateTime, nullable=False)
    end_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_days: Mapped[str] = mapped_column(Text, default="[]")  # JSON array of completed days
    total_reward: Mapped[float] = mapped_column(Float, default=0.0)
    is_completed: Mapped[bool] = mapped_column(Boolean, default=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="daily_cycles")
    task: Mapped["Task"] = relationship("Task", back_populates="cycles")
    completions: Mapped[List["TaskCompletion"]] = relationship(
        "TaskCompletion",
        back_populates="cycle",
        cascade="all, delete-orphan"
    )
    
    __table_args__ = (
        Index('idx_cycle_user_task', 'user_id', 'task_id'),
        Index('idx_cycle_dates', 'start_date', 'end_date'),
        Index('idx_cycle_completion', 'is_completed', 'cycle_number')
    )
    
    @property
    def get_completed_days(self) -> List[int]:
        """Get array of completed days"""
        try:
            return json.loads(self.completed_days)
        except:
            return []
    
    def add_completed_day(self, day: int) -> None:
        """Add a completed day to the cycle"""
        days = self.get_completed_days
        if day not in days:
            days.append(day)
            self.completed_days = json.dumps(sorted(days))

# --- NEW Card System Models --- 

class CardCatalog(Base):
    __tablename__ = "card_catalog"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    rarity: Mapped[str] = mapped_column(String(20), nullable=False, default="common")
    level_profits_json: Mapped[str] = mapped_column(Text, nullable=False)
    level_costs_json: Mapped[str] = mapped_column(Text, nullable=False)
    max_level: Mapped[int] = mapped_column(Integer, default=10)
    image_url: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    level_images_json: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    user_cards: Mapped[list["UserCard"]] = relationship("UserCard", back_populates="card_catalog")

    # Mark these as ClassVar so SQLAlchemy ignores them for mapping
    _parsed_profits: ClassVar[Optional[List[float]]] = None
    _parsed_costs: ClassVar[Optional[List[float]]] = None
    _parsed_level_images: ClassVar[Optional[List[str]]] = None

    @property
    def level_profits(self) -> List[float]:
        if self._parsed_profits is None:
            try:
                parsed = json.loads(self.level_profits_json)
                if isinstance(parsed, list) and len(parsed) == self.max_level and all(isinstance(x, (int, float)) for x in parsed):
                    self._parsed_profits = parsed
                else:
                    print(f"Warning: Invalid level_profits_json for CardCatalog ID {self.id}. Expected list of {self.max_level} numbers.")
                    self._parsed_profits = [0.0] * self.max_level
            except (json.JSONDecodeError, TypeError):
                 print(f"Warning: Error decoding level_profits_json for CardCatalog ID {self.id}")
                 self._parsed_profits = [0.0] * self.max_level
        return self._parsed_profits

    @property
    def level_costs(self) -> List[float]:
        # Cost array length should now match max_level (cost to buy L1, cost L1->L2, ..., cost L(max-1)->L(max))
        expected_cost_len = self.max_level
        if self._parsed_costs is None:
            try:
                parsed = json.loads(self.level_costs_json)
                if isinstance(parsed, list) and len(parsed) == expected_cost_len and all(isinstance(x, (int, float)) for x in parsed):
                    self._parsed_costs = parsed
                else:
                    print(f"Warning: Invalid level_costs_json for CardCatalog ID {self.id}. Expected list of {expected_cost_len} numbers.")
                    self._parsed_costs = [999999.99] * expected_cost_len
            except (json.JSONDecodeError, TypeError):
                print(f"Warning: Error decoding level_costs_json for CardCatalog ID {self.id}")
                self._parsed_costs = [999999.99] * expected_cost_len
        return self._parsed_costs

    @property
    def level_images(self) -> List[str]:
        """Parses the JSON string containing image URLs for each level."""
        if self._parsed_level_images is None:
            default_image = self.image_url or ""
            if not self.level_images_json:
                print(f"Warning: level_images_json is null or empty for CardCatalog ID {self.id}. Using default.")
                self._parsed_level_images = [default_image] * self.max_level
                return self._parsed_level_images
            try:
                parsed = json.loads(self.level_images_json)
                if isinstance(parsed, list) and len(parsed) == self.max_level and all(isinstance(x, str) for x in parsed):
                    self._parsed_level_images = parsed
                else:
                    print(f"Warning: Invalid level_images_json for CardCatalog ID {self.id}. Expected list of {self.max_level} strings. Using default.")
                    self._parsed_level_images = [default_image] * self.max_level
            except (json.JSONDecodeError, TypeError) as e:
                print(f"Warning: Error decoding level_images_json for CardCatalog ID {self.id}: {e}. Using default.")
                self._parsed_level_images = [default_image] * self.max_level
        return self._parsed_level_images

class UserCard(Base):
    __tablename__ = "user_cards"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), index=True)
    card_catalog_id: Mapped[int] = mapped_column(ForeignKey("card_catalog.id"), index=True)
    level: Mapped[int] = mapped_column(Integer, default=1)
    last_claim_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, index=True)
    acquired_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)

    user: Mapped["User"] = relationship("User", back_populates="user_cards")
    card_catalog: Mapped["CardCatalog"] = relationship("CardCatalog", back_populates="user_cards", lazy='joined')

    __table_args__ = (UniqueConstraint('user_id', 'card_catalog_id', name='_user_card_uc'),)

    @property
    def current_hourly_profit(self) -> float:
        if not hasattr(self, 'card_catalog') or self.card_catalog is None:
             return 0.0
        profits = self.card_catalog.level_profits
        idx = self.level - 1
        if 0 <= idx < len(profits):
            return round(profits[idx], 4)
        else:
            print(f"Warning: Level {self.level} out of bounds for profits (len={len(profits)}) for Card ID {self.card_catalog_id}")
            return 0.0

    @property
    def next_upgrade_cost(self) -> float | None:
        if not hasattr(self, 'card_catalog') or self.card_catalog is None:
            return None
        # Cost to upgrade FROM current level TO next level
        # If current level is L, we need cost L -> L+1, which is at index L in the costs array
        if self.level >= self.card_catalog.max_level:
            return None # Already max level
        costs = self.card_catalog.level_costs
        idx = self.level # Index L corresponds to cost L -> L+1
        if 0 <= idx < len(costs):
             return round(costs[idx], 2)
        else:
            print(f"Warning: Level {self.level} (index {idx}) out of bounds for costs (len={len(costs)}) for Card ID {self.card_catalog_id}")
            return None

    @property
    def hours_since_last_claim(self) -> float:
        now = datetime.utcnow()
        delta = now - self.last_claim_at
        return delta.total_seconds() / 3600.0

# --- End NEW Card System Models --- 

# --- Chat System Models ---

class ChatMessage(Base):
    __tablename__ = "chat_messages"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    sender_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    receiver_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id", ondelete="SET NULL"), nullable=True, index=True)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, index=True)
    # Field to track read status for private messages
    is_read: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, index=True)
    # Field to determine if message is public or private
    is_public: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False, index=True)
    # Sender's username for display purposes
    sender_nickname: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    # Chat conversation ID for referencing
    conversation_id: Mapped[Optional[str]] = mapped_column(String(50), nullable=True, index=True)
    
    # Relationships to User model
    sender: Mapped["User"] = relationship("User", foreign_keys=[sender_id], back_populates="sent_messages")
    receiver: Mapped[Optional["User"]] = relationship("User", foreign_keys=[receiver_id], back_populates="received_messages")
    
    __table_args__ = (
        Index('ix_private_message_pair', 'sender_id', 'receiver_id', 'created_at'),
        Index('ix_public_messages', 'receiver_id', 'created_at', postgresql_where=(receiver_id.is_(None))),
        Index('ix_unread_private_messages', 'receiver_id', 'is_read', postgresql_where=(receiver_id.is_not(None))),
        # Index for conversation_id
        Index('ix_conversation_messages', 'conversation_id', 'created_at'),
    )

# Table to store online users
class OnlineUser(Base):
    __tablename__ = "online_users"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True, index=True)
    last_active_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, index=True)
    connection_id: Mapped[Optional[str]] = mapped_column(String(100), nullable=True, unique=True)
    
    # Relationship to User model
    user: Mapped["User"] = relationship("User")
    
    __table_args__ = (
        Index('ix_online_users_active', 'last_active_at'),
    )

# NEW: Table to store anonymous conversation mappings
class ChatConversation(Base):
    __tablename__ = "chat_conversations"
    
    id: Mapped[str] = mapped_column(String(50), primary_key=True, index=True)
    user1_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    user2_id: Mapped[int] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    last_message_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow, index=True)
    
    # Relationships to User model
    user1: Mapped["User"] = relationship("User", foreign_keys=[user1_id])
    user2: Mapped["User"] = relationship("User", foreign_keys=[user2_id])
    
    __table_args__ = (
        UniqueConstraint('user1_id', 'user2_id', name='_conversation_users_uc'),
        Index('ix_active_conversations', 'last_message_at'),
    )

# --- End Chat System Models ---
