import asyncio
import logging
import os
import sys
import aiohttp
import json
from urllib.parse import urljoin, urlencode
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Base URL of your API
API_BASE_URL = os.getenv("API_BASE_URL", "https://dev.atlasvip.cloud:2083")

async def login(username, password):
    """Login and get authentication token"""
    url = urljoin(API_BASE_URL, "/auth/token")
    logger.info(f"Logging in with username: {username}")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Prepare form data (following OAuth2 standard)
            form_data = {
                "username": username,
                "password": password,
            }
            
            start_time = time.time()
            async with session.post(url, data=form_data) as response:
                elapsed = (time.time() - start_time) * 1000  # ms
                
                # Get response details
                status = response.status
                try:
                    data = await response.json()
                except:
                    data = await response.text()
                
                # Log response details (hide token for security)
                logger.info(f"Login response: {status} in {elapsed:.2f}ms")
                
                # Check for success
                if response.status == 200:
                    token = data.get('access_token')
                    if token:
                        logger.info(f"Successfully logged in as {username}")
                        
                        # Get cookies for future requests
                        cookies = session.cookie_jar.filter_cookies(url)
                        cookie_header = '; '.join([f"{name}={cookie.value}" for name, cookie in cookies.items()])
                        
                        return {
                            "token": token,
                            "cookie_header": cookie_header
                        }
                    else:
                        logger.error("Login successful but no token in response")
                else:
                    logger.error(f"Login failed. Status: {status}, Response: {data}")
                    
                return None
    except Exception as e:
        logger.error(f"Error during login: {e}", exc_info=True)
        return None

async def test_ws_token_endpoint(auth_token=None, cookie_header=None):
    """
    Test the WebSocket token endpoint directly
    
    Args:
        auth_token: Optional Bearer token for authorization
        cookie_header: Optional cookie header string for auth via cookies
    """
    url = urljoin(API_BASE_URL, "/chat/ws-token")
    
    # Add query parameters
    url += "?client_type=websocket"
    
    logger.info(f"Testing WS token endpoint: {url}")
    
    headers = {
        'Content-Type': 'application/json',
    }
    
    # Add authorization if provided
    if auth_token:
        headers['Authorization'] = f"Bearer {auth_token}"
        logger.info("Using Authorization Bearer token for authentication")
    
    # Add cookie if provided
    if cookie_header:
        headers['Cookie'] = cookie_header
        logger.info("Using Cookie header for authentication")
    
    # Log attempt
    auth_method = "Bearer token" if auth_token else "Cookie" if cookie_header else "None (unauthenticated)"
    logger.info(f"Attempting to get WebSocket token using {auth_method} auth method")
    
    try:
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            async with session.post(url, headers=headers, json={}) as response:
                elapsed = (time.time() - start_time) * 1000  # ms
                
                # Get response details
                status = response.status
                try:
                    data = await response.json()
                except:
                    data = await response.text()
                
                # Log response details
                logger.info(f"Response: {status} in {elapsed:.2f}ms")
                logger.info(f"Response headers: {dict(response.headers)}")
                logger.info(f"Response data: {data}")
                
                # Check for success
                if response.status == 200:
                    logger.info(f"SUCCESS: Retrieved WebSocket token: {data.get('token')}")
                    return data.get('token')
                else:
                    logger.error(f"FAILED: Could not get WebSocket token. Status: {status}")
                    return None
    except Exception as e:
        logger.error(f"Error testing WS token endpoint: {e}", exc_info=True)
        return None

async def test_full_flow(auth_token=None, cookie_header=None):
    """Test both getting a token and connecting to the WebSocket"""
    
    # First get a token
    token = await test_ws_token_endpoint(auth_token, cookie_header)
    if not token:
        logger.error("Failed to get WebSocket token, cannot test WebSocket connection")
        return False
    
    # Now test the WebSocket connection
    ws_url = urljoin(API_BASE_URL.replace('http', 'ws'), f"/ws/chat/{token}")
    logger.info(f"Testing WebSocket connection to: {ws_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.ws_connect(ws_url) as ws:
                logger.info("WebSocket connection established successfully!")
                
                # Send a ping message
                await ws.send_json({'type': 'ping'})
                logger.info("Sent ping message to WebSocket")
                
                # Wait for response with timeout
                try:
                    msg = await asyncio.wait_for(ws.receive(), timeout=5.0)
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        logger.info(f"Received response: {data}")
                        if data.get('type') == 'pong':
                            logger.info("SUCCESS: Received pong response from WebSocket")
                        else:
                            logger.info(f"Received non-pong response: {data}")
                    else:
                        logger.warning(f"Received non-text message: {msg.type}")
                except asyncio.TimeoutError:
                    logger.error("Timeout waiting for WebSocket response")
                
                # Close connection gracefully
                await ws.close()
                logger.info("WebSocket connection closed")
                return True
    except aiohttp.WSServerHandshakeError as e:
        logger.error(f"WebSocket handshake failed: {e}")
        return False
    except Exception as e:
        logger.error(f"Error in WebSocket connection test: {e}", exc_info=True)
        return False

async def run_test_with_auth():
    """Run tests with login authentication first"""
    try:
        # Test credentials
        username = os.getenv("TEST_USERNAME", "admin_test")
        password = os.getenv("TEST_PASSWORD", "admin123")
        
        # Login first
        auth_data = await login(username, password)
        if not auth_data:
            logger.error("Login failed, cannot proceed with tests")
            return
        
        token = auth_data.get("token")
        cookie_header = auth_data.get("cookie_header")
        
        logger.info("Testing full WebSocket flow with authentication...")
        success = await test_full_flow(token, cookie_header)
        
        if success:
            logger.info("🟢 FULL TEST PASSED: WebSocket connection successful!")
        else:
            logger.error("🔴 FULL TEST FAILED: WebSocket connection failed")
    except Exception as e:
        logger.error(f"Error in auth test: {e}", exc_info=True)

if __name__ == "__main__":
    # Get auth info from command line or environment
    import argparse
    parser = argparse.ArgumentParser(description="Test WebSocket token endpoint and connection")
    parser.add_argument("--token", help="Bearer token for authorization")
    parser.add_argument("--cookie", help="Cookie header for authentication (auth_token=value)")
    parser.add_argument("--ws-only", action="store_true", help="Only test the full WebSocket flow")
    parser.add_argument("--username", help="Username for login")
    parser.add_argument("--password", help="Password for login")
    parser.add_argument("--auto-auth", action="store_true", help="Automatically login and use obtained token")
    args = parser.parse_args()
    
    # Get from environment if not provided as args
    auth_token = args.token or os.getenv("AUTH_TOKEN")
    cookie_header = args.cookie or os.getenv("COOKIE_HEADER")
    
    # Run the test
    if args.auto_auth:
        # Override environment vars if provided as args
        if args.username:
            os.environ["TEST_USERNAME"] = args.username
        if args.password:
            os.environ["TEST_PASSWORD"] = args.password
        
        logger.info("Running tests with automatic authentication...")
        asyncio.run(run_test_with_auth())
    elif args.ws_only:
        logger.info("Testing full WebSocket flow (token + connection)...")
        asyncio.run(test_full_flow(auth_token, cookie_header))
    else:
        logger.info("Testing WebSocket token endpoint only...")
        asyncio.run(test_ws_token_endpoint(auth_token, cookie_header)) 