import asyncio
import redis.asyncio as redis
import os
import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
TEST_KEY = "my_test_key"
TEST_VALUE = "hello_redis"

async def test_redis_connection():
    logger.info(f"Attempting to connect to Redis at: {REDIS_URL}")
    redis_client = None # Initialize to None
    try:
        # Create a Redis client instance
        redis_client = redis.from_url(REDIS_URL, encoding="utf-8", decode_responses=True)

        # 1. Test connection with PING
        logger.info("Sending PING to Redis...")
        pong = await redis_client.ping()
        if pong:
            logger.info(f"Received PONG from Redis. Connection successful.")
        else:
            logger.error("Did not receive PONG from Redis. PING command failed.")
            return False

        # 2. Test SET operation
        logger.info(f"Attempting to SET key '{TEST_KEY}' with value '{TEST_VALUE}'...")
        set_result = await redis_client.set(TEST_KEY, TEST_VALUE, ex=60) # Set with 60s expiry
        if set_result:
            logger.info(f"Successfully SET key '{TEST_KEY}'.")
        else:
            logger.error(f"Failed to SET key '{TEST_KEY}'. SET command returned {set_result}.")
            return False

        # 3. Test GET operation
        logger.info(f"Attempting to GET key '{TEST_KEY}'...")
        retrieved_value = await redis_client.get(TEST_KEY)
        if retrieved_value == TEST_VALUE:
            logger.info(f"Successfully GET key '{TEST_KEY}'. Value: '{retrieved_value}'. Matches expected value.")
        elif retrieved_value is None:
            logger.error(f"Failed to GET key '{TEST_KEY}'. Key does not exist (it may have expired or was not set).")
            return False
        else:
            logger.error(f"Successfully GET key '{TEST_KEY}', but value '{retrieved_value}' does not match expected '{TEST_VALUE}'.")
            return False

        # 4. Test DEL operation
        logger.info(f"Attempting to DEL key '{TEST_KEY}'...")
        del_result = await redis_client.delete(TEST_KEY)
        if del_result == 1:
            logger.info(f"Successfully DEL key '{TEST_KEY}'.")
        else:
            logger.warning(f"DEL key '{TEST_KEY}' returned {del_result}. This might mean the key didn't exist before DEL (e.g., expired).")
            # Check if key still exists to be sure
            still_exists = await redis_client.exists(TEST_KEY)
            if still_exists:
                logger.error(f"Key '{TEST_KEY}' still exists after DEL operation failed to return 1.")
                return False
            else:
                logger.info(f"Key '{TEST_KEY}' confirmed not to exist after DEL, so operation effectively succeeded.")

        logger.info("All Redis tests passed successfully!")
        return True

    except redis.exceptions.ConnectionError as e:
        logger.error(f"Redis Connection Error: {e}. Please ensure Redis server is running and accessible at {REDIS_URL}.")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred during Redis test: {e}", exc_info=True)
        return False
    finally:
        if redis_client:
            logger.info("Closing Redis connection used for testing.")
            await redis_client.close()

if __name__ == "__main__":
    logger.info("Starting Redis connection test script...")
    # asyncio.run(test_redis_connection())
    # More robust way to run, especially in different environments
    loop = asyncio.get_event_loop()
    try:
        success = loop.run_until_complete(test_redis_connection())
        if success:
            logger.info("Redis test script finished successfully.")
        else:
            logger.error("Redis test script finished with errors.")
    except KeyboardInterrupt:
        logger.info("Redis test script interrupted by user.")
    except Exception as e:
        logger.error(f"Critical error in test script execution: {e}", exc_info=True)
    finally:
        # Optional: close loop if it was created by this script and not managed by a framework
        # if not loop.is_running():
        #     loop.close()
        #     logger.info("Asyncio event loop closed.")
        pass 