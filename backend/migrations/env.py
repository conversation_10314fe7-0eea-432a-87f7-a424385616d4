from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool, text
from alembic import context
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the parent directory to sys.path
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

# Import your models and database configuration
from models import Base
from database import SQLALCHEMY_DATABASE_URL, engine

# this is the Alembic Config object, which provides
# access to the values within the .ini file in use.
config = context.config

# Interpret the config file for Python logging.
# This line sets up loggers basically.
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Set the database URL in the alembic.ini file
config.set_main_option("sqlalchemy.url", str(SQLALCHEMY_DATABASE_URL))

# add your model's MetaData object here
# for 'autogenerate' support
target_metadata = Base.metadata

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode.

    This configures the context with just a URL
    and not an Engine, though an Engine is acceptable
    here as well.  By skipping the Engine creation
    we don't even need a DBAPI to be available.

    Calls to context.execute() here emit the given string to the
    script output.
    """
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode.

    In this scenario we need to create an Engine
    and associate a connection with the context.
    """
    # Use the existing engine
    with engine.connect() as connection:
        # Ensure the alembic_version table exists before proceeding
        # This helps if it was manually dropped or never created
        try:
            connection.execute(text("SELECT 1 FROM alembic_version LIMIT 1"))
        except Exception: # Catch specific DB exceptions if possible (e.g., ProgrammingError)
            # Table doesn't exist, try creating it based on standard Alembic structure
            # Note: This assumes a simple structure, adjust if using custom version table config
            try:
                connection.execute(text("CREATE TABLE alembic_version (version_num VARCHAR(32) NOT NULL, CONSTRAINT alembic_version_pkc PRIMARY KEY (version_num))"))
                # Since we just created it, stamp it to base (no migrations applied yet)
                # This might not be strictly necessary if autogenerate handles the initial creation,
                # but doesn't hurt.
                # context.impl.stamp_revision(connection, "base") # Requires context setup first
                print("Created alembic_version table.")
            except Exception as create_exc:
                print(f"Failed to automatically create alembic_version table: {create_exc}")
                # Proceeding anyway, Alembic might handle it or fail again

        # Disable foreign key checks for SQLite during migration
        if str(engine.url).startswith('sqlite'):
            connection.execute(text('PRAGMA foreign_keys=OFF'))
            
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            # Add these options for better migrations
            compare_type=True,  # Compare column types
            compare_server_default=True,  # Compare default values
            include_schemas=True,  # Include schema-level objects
            render_as_batch=True,  # Enable batch mode for SQLite
        )

        with context.begin_transaction():
            context.run_migrations()
            
        # Re-enable foreign key checks for SQLite after migration
        if str(engine.url).startswith('sqlite'):
            connection.execute(text('PRAGMA foreign_keys=ON'))

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online() 