"""
Migration script to upgrade the chat system with anonymous nicknames and conversation handling
"""
import os
import sys
import asyncio
import random
import string
from datetime import datetime
from sqlalchemy import create_engine, MetaData, Table, Column, String, Integer, ForeignKey, Boolean, DateTime, Text, text
from sqlalchemy.schema import CreateTable
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.future import select

# Add parent directory to path to import necessary modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models
from models import ChatMessage, ChatConversation, OnlineUser, User

# Import database connection
from database import Base, get_async_db, async_engine

def generate_random_nickname():
    """Generate a random anonymous nickname"""
    adjectives = ["Happy", "Brave", "Lucky", "Clever", "Swift", "Mighty", "Noble", "Gentle", "Wild", "Jolly"]
    nouns = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>lphin", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Owl"]
    
    adjective = random.choice(adjectives)
    noun = random.choice(nouns)
    suffix = ''.join(random.choices(string.digits, k=3))
    
    return f"{adjective}{noun}{suffix}"

async def run_migration():
    """Run the migration"""
    print("Starting chat system migration...")
    
    # Create async engine - use the imported engine
    engine = async_engine
    
    # Create tables if they don't exist
    metadata = MetaData()
    async with engine.begin() as conn:
        # Print the SQL for new tables
        print("\nCreating new tables if they don't exist:")
        for table in [ChatMessage.__table__, OnlineUser.__table__, ChatConversation.__table__]:
            print(CreateTable(table))
        
        # Create tables
        await conn.run_sync(lambda sync_conn: Base.metadata.create_all(sync_conn, tables=[
            ChatMessage.__table__, 
            OnlineUser.__table__, 
            ChatConversation.__table__
        ]))
    
    # Check if necessary columns exist in ChatMessage table and add them if needed
    async with AsyncSession(engine) as session:
        print("\nChecking for existing columns in chat_messages table...")
        # SQLite specific way to get column info
        query = text("PRAGMA table_info(chat_messages)")
        result = await session.execute(query)
        columns = result.fetchall()
        existing_columns = [column[1] for column in columns]  # Column name is at index 1
        
        print(f"Existing columns: {existing_columns}")
        
        # Add sender_nickname column if it doesn't exist
        if 'sender_nickname' not in existing_columns:
            print("Adding sender_nickname column...")
            await session.execute(
                text("ALTER TABLE chat_messages ADD COLUMN sender_nickname VARCHAR(50)")
            )
        
        # Add conversation_id column if it doesn't exist
        if 'conversation_id' not in existing_columns:
            print("Adding conversation_id column...")
            await session.execute(
                text("ALTER TABLE chat_messages ADD COLUMN conversation_id VARCHAR(50)")
            )
            
            # Create index (may fail silently if already exists)
            try:
                await session.execute(
                    text("CREATE INDEX ix_conversation_messages ON chat_messages (conversation_id, created_at)")
                )
            except Exception as e:
                print(f"Note: Couldn't create index - may already exist: {e}")
        
        await session.commit()
    
    # Migrate existing private messages to use conversation_id
    async with AsyncSession(engine) as session:
        print("\nMigrating existing private messages to use conversation_id...")
        
        # Get all unique sender-receiver pairs
        query = text("""
        SELECT DISTINCT 
            CASE WHEN sender_id < receiver_id THEN sender_id ELSE receiver_id END as user1_id,
            CASE WHEN sender_id < receiver_id THEN receiver_id ELSE sender_id END as user2_id
        FROM chat_messages 
        WHERE receiver_id IS NOT NULL
        """)
        result = await session.execute(query)
        pairs = result.all()
        
        if not pairs:
            print("No private messages found to migrate.")
        else:
            print(f"Found {len(pairs)} conversation pairs to migrate.")
            
            for user1_id, user2_id in pairs:
                # Create conversation
                conversation = ChatConversation(
                    id=f"conv_{user1_id}_{user2_id}",
                    user1_id=user1_id,
                    user2_id=user2_id,
                    created_at=datetime.utcnow(),
                    last_message_at=datetime.utcnow()
                )
                session.add(conversation)
                
                # Update messages with this conversation_id
                update_query = text(f"""
                UPDATE chat_messages 
                SET conversation_id = 'conv_{user1_id}_{user2_id}'
                WHERE (sender_id = {user1_id} AND receiver_id = {user2_id}) 
                OR (sender_id = {user2_id} AND receiver_id = {user1_id})
                """)
                await session.execute(update_query)
        
        # Add anonymous nicknames to all messages
        query = text("SELECT DISTINCT sender_id FROM chat_messages")
        result = await session.execute(query)
        sender_ids = [row[0] for row in result]
        
        if not sender_ids:
            print("No chat messages found to add nicknames to.")
        else:
            print(f"Adding nicknames to messages from {len(sender_ids)} users.")
            
            for sender_id in sender_ids:
                nickname = generate_random_nickname()
                update_query = text(f"""
                UPDATE chat_messages 
                SET sender_nickname = '{nickname}'
                WHERE sender_id = {sender_id}
                """)
                await session.execute(update_query)
        
        # Commit all changes
        await session.commit()
    
    print("\nMigration completed successfully!")

if __name__ == "__main__":
    asyncio.run(run_migration()) 