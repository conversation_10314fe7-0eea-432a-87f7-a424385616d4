from typing import Dict, <PERSON><PERSON>, Callable
from . import fix_task_types, remove_task_category, add_high_usage_indexes

# Dictionary of migrations with their names
# Format: "migration_name": (upgrade_func, downgrade_func)
migrations: Dict[str, Tuple[Callable, Callable]] = {
    "fix_task_types": (fix_task_types.upgrade, fix_task_types.downgrade),
    "remove_task_category": (remove_task_category.upgrade, remove_task_category.downgrade),
    "add_high_usage_indexes": (add_high_usage_indexes.upgrade, add_high_usage_indexes.downgrade),
} 