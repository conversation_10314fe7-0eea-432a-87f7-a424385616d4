"""remove anonymous_nickname from online_users

Revision ID: f2c218c32047
Revises: 20250202_161239
Create Date: 2025-05-16 00:39:36.123456

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f2c218c32047'
down_revision: Union[str, None] = '20250202_161239' # Corrected previous revision ID
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('online_users', 'anonymous_nickname')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('online_users', sa.Column('anonymous_nickname', sa.VARCHAR(length=50), nullable=True)) # Or False if it was NOT NULL
    # ### end Alembic commands ### 