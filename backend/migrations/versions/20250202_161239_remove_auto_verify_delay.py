"""Remove auto_verify_delay column

Revision ID: 20250202_161239
Revises: f254e04cf586
Create Date: 2025-02-02 16:12:39.831702

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = '20250202_161239'
down_revision: Union[str, None] = 'f254e04cf586'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tasks', schema=None) as batch_op:
        batch_op.drop_column('auto_verify_delay')


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tasks', schema=None) as batch_op:
        batch_op.add_column(sa.Column('auto_verify_delay', sa.Integer(), nullable=True)) 