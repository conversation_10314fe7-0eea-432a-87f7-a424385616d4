"""Add security fields to TaskCompletion

Revision ID: f254e04cf586
Revises: 
Create Date: 2025-02-02 00:37:40.831702

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision: str = 'f254e04cf586'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def column_exists(table_name, column_name):
    """Helper to check if a column exists in a table."""
    bind = op.get_bind()
    insp = sa.inspect(bind)
    columns = insp.get_columns(table_name)
    return any(c['name'] == column_name for c in columns)

def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('task_completions', schema=None) as batch_op:
        # Add new security fields, checking if they exist first
        if not column_exists('task_completions', 'reward_amount'):
            batch_op.add_column(sa.Column('reward_amount', sa.Float(), server_default='0.0', nullable=False))
        if not column_exists('task_completions', 'verification_ip'):
            batch_op.add_column(sa.Column('verification_ip', sa.String(length=45), nullable=True))
        if not column_exists('task_completions', 'verification_user_agent'):
            batch_op.add_column(sa.Column('verification_user_agent', sa.String(length=255), nullable=True))
        if not column_exists('task_completions', 'last_verification_attempt'):
            batch_op.add_column(sa.Column('last_verification_attempt', sa.DateTime(), nullable=True))
        if not column_exists('task_completions', 'verification_cooldown'):
            batch_op.add_column(sa.Column('verification_cooldown', sa.Integer(), server_default='300', nullable=False))
        if not column_exists('task_completions', 'max_verification_attempts'):
            batch_op.add_column(sa.Column('max_verification_attempts', sa.Integer(), server_default='3', nullable=False))
        if not column_exists('task_completions', 'is_verified'):
            batch_op.add_column(sa.Column('is_verified', sa.Boolean(), server_default='0', nullable=False))
        if not column_exists('task_completions', 'verification_method'):
            batch_op.add_column(sa.Column('verification_method', sa.String(length=50), nullable=True))
        if not column_exists('task_completions', 'verification_token'):
            batch_op.add_column(sa.Column('verification_token', sa.String(length=100), nullable=True))
        
        # Add new index for verification tracking (idempotent check not strictly needed for index creation, but good practice)
        # op.create_index handles this if the index already exists with reflect=True in env.py context. 
        # However, for explicit safety, one might add a check like:
        # insp = sa.inspect(op.get_bind())
        # if not any(idx['name'] == 'idx_task_completion_verification' for idx in insp.get_indexes('task_completions')):
        batch_op.create_index('idx_task_completion_verification', ['verification_attempts', 'last_verification_attempt'], unique=False)


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('task_completions', schema=None) as batch_op:
        # Drop the verification index
        # Check if index exists before dropping (safer for downgrades)
        insp = sa.inspect(op.get_bind())
        if any(idx['name'] == 'idx_task_completion_verification' for idx in insp.get_indexes('task_completions')):
            batch_op.drop_index('idx_task_completion_verification')
        
        # Drop all new columns in reverse order, checking if they exist
        if column_exists('task_completions', 'verification_token'):
            batch_op.drop_column('verification_token')
        if column_exists('task_completions', 'verification_method'):
            batch_op.drop_column('verification_method')
        if column_exists('task_completions', 'is_verified'):
            batch_op.drop_column('is_verified')
        if column_exists('task_completions', 'max_verification_attempts'):
            batch_op.drop_column('max_verification_attempts')
        if column_exists('task_completions', 'verification_cooldown'):
            batch_op.drop_column('verification_cooldown')
        if column_exists('task_completions', 'last_verification_attempt'):
            batch_op.drop_column('last_verification_attempt')
        if column_exists('task_completions', 'verification_user_agent'):
            batch_op.drop_column('verification_user_agent')
        if column_exists('task_completions', 'verification_ip'):
            batch_op.drop_column('verification_ip')
        if column_exists('task_completions', 'reward_amount'):
            batch_op.drop_column('reward_amount') 