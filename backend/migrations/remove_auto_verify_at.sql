-- Create a new table without the columns we want to remove
CREATE TABLE task_completions_new (
    id INTEGER PRIMARY KEY,
    task_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    status VARCHAR NOT NULL,
    started_at DATETIME NOT NULL,
    current_progress INTEGER NOT NULL DEFAULT 0,
    verification_attempts INTEGER NOT NULL DEFAULT 0,
    is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
    expires_at DATETIME,
    last_verified_at DATETIME,
    completed_at DATETIME,
    claimed_at DATETIME,
    verification_data TEXT,
    FOREIGN KEY(task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Copy data from old table to new table
INSERT INTO task_completions_new 
SELECT id, task_id, user_id, status, started_at, current_progress, 
       verification_attempts, is_claimed, expires_at, last_verified_at, 
       completed_at, claimed_at, verification_data
FROM task_completions;

-- Drop old table
DROP TABLE task_completions;

-- Rename new table to original name
ALTER TABLE task_completions_new RENAME TO task_completions;

-- Recreate indexes
CREATE INDEX idx_task_completion_user_status ON task_completions(user_id, status);
CREATE INDEX idx_task_completion_task_status ON task_completions(task_id, status); 