"""
Security Middleware
-----------------
Security middleware for FastAPI applications
"""
from fastapi import Fast<PERSON><PERSON>, Request, Response, status
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import Callable, Dict, Any, List
import logging
import json
import re
import time
import secrets
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)

def add_security_headers_middleware(app: FastAPI) -> None:
    """Add middleware to inject security headers into all responses"""

    @app.middleware("http")
    async def add_security_headers(request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time

        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        # response.headers["X-Frame-Options"] = "SAMEORIGIN"  # Removed for Telegram Mini App
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Permissions-Policy"] = (
            "accelerometer=(), camera=(), geolocation=(), "
            "gyroscope=(), magnetometer=(), microphone=(), "
            "payment=(), usb=()"
        )
        response.headers["Cache-Control"] = "no-store, max-age=0"
        
        # Enhanced Content-Security-Policy to prevent XSS attacks
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org https://challenges.cloudflare.com; "
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
            "font-src 'self' https://fonts.gstatic.com; "
            "img-src 'self' data: https:; "
            "connect-src 'self'; "
            "frame-ancestors 'none'; "
            "form-action 'self'; "
            "base-uri 'self'"
        )
        
        # Add HSTS header for HTTPS security (even if not using HTTPS in development)
        # This will be enforced when deployed with HTTPS
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # Skip X-Bot-Detection header on webhook endpoints
        if "webhook" not in request.url.path:
            response.headers["X-Bot-Detection"] = "false"
            response.headers["X-Bot-Confidence"] = "0.00"
        
        # Add timing header (for debugging only - should be removed in production)
        response.headers["X-Process-Time"] = str(process_time)
        
        return response

def is_telegram_webapp_request(request: Request) -> bool:
    """
    Detect if the request is coming from Telegram WebApp
    
    Checks multiple indicators:
    1. User-Agent containing 'Telegram'
    2. Referer from telegram.org
    3. Telegram-specific headers
    4. WebApp parameters in request body
    """
    # Check User-Agent
    user_agent = request.headers.get("user-agent", "").lower()
    if "telegram" in user_agent:
        return True
    
    # Check Referer
    referer = request.headers.get("referer", "")
    if "telegram.org" in referer:
        return True
    
    # Check for Telegram WebApp specific headers
    telegram_headers = [
        "x-telegram-bot-api-secret-token",
        "x-telegram-webapp",
        "telegram-init-data"
    ]
    
    for header in telegram_headers:
        if request.headers.get(header):
            return True
    
    return False

def is_telegram_auth_endpoint(path: str) -> bool:
    """Check if the path is a Telegram authentication endpoint"""
    telegram_auth_paths = [
        "/auth/telegram/login",
        "/auth/telegram/register", 
        "/auth/telegram/verify"
    ]
    return any(telegram_path in path for telegram_path in telegram_auth_paths)

class CSRFMiddleware(BaseHTTPMiddleware):
    """Middleware to implement CSRF protection for FastAPI with Telegram WebApp support"""
    
    def __init__(self, app: FastAPI, csrf_cookie_name: str = "csrf_token"):
        super().__init__(app)
        self.csrf_cookie_name = csrf_cookie_name
        self.safe_methods = {"GET", "HEAD", "OPTIONS"}
        self.protected_paths = {"/payment/", "/user/profile/", "/auth/"}
        # Paths that are excluded from CSRF protection
        self.excluded_paths = {
            "/auth/admin/login", 
            "/auth/login",
            "/auth/token",
            "/auth/telegram/login",
            "/auth/telegram/register",
            "/auth/verify-session"
        }
        
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip CSRF protection for safe methods
        if request.method in self.safe_methods:
            # Generate and set CSRF token for GET requests that might lead to forms
            response = await call_next(request)
            
            # Check if this is a page that might need CSRF protection later
            path = request.url.path
            if any(protected_path in path for protected_path in self.protected_paths):
                # Check if CSRF cookie already exists
                existing_token = request.cookies.get(self.csrf_cookie_name)
                csrf_token_to_set = existing_token or secrets.token_hex(32)
                
                # Set CSRF token in response cookie
                # Use secure=True and samesite='none' for production/cross-site
                # Use secure=False and samesite='lax' for local HTTP dev
                is_production = True # Assume production, adjust as needed
                
                response.set_cookie(
                    key=self.csrf_cookie_name,
                    value=csrf_token_to_set,
                    httponly=True,
                    secure=is_production, 
                    samesite="none" if is_production else "lax"
                )
            
            return response
        
        # For non-safe methods like POST, PUT, DELETE, check CSRF token
        # Skip CSRF check for webhooks that come from external services
        if "webhook" in request.url.path:
            return await call_next(request)
            
        path = request.url.path
        
        # Skip CSRF protection for localhost development
        if request.client and request.client.host in ["127.0.0.1", "::1"]:
            logger.info(f"CSRF validation skipped for localhost request: {path}")
            return await call_next(request)

        # Skip CSRF protection for requests from localhost origins
        origin = request.headers.get("origin", "")
        if "localhost" in origin:
            logger.info(f"CSRF validation skipped for localhost origin: {origin} -> {path}")
            return await call_next(request)

        # Skip CSRF protection for same-origin requests from production domain
        if origin == "https://dev.atlasvip.cloud":
            logger.info(f"CSRF validation skipped for same-origin production request: {origin} -> {path}")
            return await call_next(request)

        # Check if this is a Telegram WebApp request
        is_telegram_request = is_telegram_webapp_request(request)
        is_telegram_auth = is_telegram_auth_endpoint(path)

        # Skip CSRF protection for Telegram WebApp requests on auth endpoints
        if is_telegram_request and is_telegram_auth:
            logger.info(f"CSRF validation skipped for Telegram WebApp request: {path}")
            return await call_next(request)
            
        # Check if path is explicitly excluded from CSRF protection
        if path in self.excluded_paths:
            logger.info(f"CSRF validation skipped for excluded path: {path}")
            return await call_next(request)
            
        # Check if path requires CSRF protection
        requires_csrf = any(protected_path in path for protected_path in self.protected_paths)
        
        if requires_csrf:
            # Add debug logging for troubleshooting
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"CSRF Debug for {path}:")
                logger.debug(f"  Is Telegram Request: {is_telegram_request}")
                logger.debug(f"  Is Telegram Auth: {is_telegram_auth}")
                logger.debug(f"  User-Agent: {request.headers.get('user-agent', 'None')}")
                logger.debug(f"  Referer: {request.headers.get('referer', 'None')}")
                logger.debug(f"  Cookies Received: {list(request.cookies.keys())}")
                logger.debug(f"  X-CSRF-Token Header: {request.headers.get('X-CSRF-Token', 'None')}")

            # Get CSRF token from cookie
            csrf_cookie = request.cookies.get(self.csrf_cookie_name)
            
            # Get CSRF token from header or form data
            csrf_header = request.headers.get("X-CSRF-Token")
            
            # Try to get from form data if not in header
            csrf_form = None
            if not csrf_header and request.headers.get("content-type") == "application/x-www-form-urlencoded":
                try:
                    form_data = await request.form()
                    csrf_form = form_data.get("csrf_token")
                except Exception as e:
                    logger.warning(f"Failed to parse form data for CSRF token: {e}")
            
            # Special handling for Telegram requests - more lenient validation
            if is_telegram_request:
                if not csrf_cookie and not csrf_header:
                    # For Telegram requests, if no CSRF token is available at all,
                    # we can be more lenient but should still log it
                    logger.warning(f"Telegram request to {path} without CSRF token - allowing due to Telegram context")
                    return await call_next(request)
            
            # Standard CSRF validation for non-Telegram requests
            if not csrf_cookie or (csrf_cookie != csrf_header and csrf_cookie != csrf_form):
                logger.warning(f"CSRF validation failed for {path}")
                logger.warning(f"  CSRF Cookie ({self.csrf_cookie_name}): {csrf_cookie}")
                logger.warning(f"  CSRF Header (X-CSRF-Token): {csrf_header}")
                logger.warning(f"  CSRF Form (csrf_token): {csrf_form}")
                logger.warning(f"  Is Telegram Request: {is_telegram_request}")
                
                return JSONResponse(
                    status_code=status.HTTP_403_FORBIDDEN,
                    content={
                        "detail": "CSRF token missing or invalid",
                        "error_code": "CSRF_VALIDATION_FAILED"
                    }
                )
        
        # Continue with the request
        return await call_next(request)

def add_csrf_middleware(app: FastAPI) -> None:
    """Add CSRF protection middleware to the application"""
    app.add_middleware(CSRFMiddleware)

def add_ip_protection_middleware(app: FastAPI) -> None:
    """Add IP rate limiting and security middleware"""
    
    # In a real production environment, this would be backed by Redis
    ip_counters: Dict[str, Dict[str, Any]] = {}
    ip_whitelist = ["127.0.0.1", "::1"]
    
    @app.middleware("http")
    async def ip_security(request: Request, call_next: Callable) -> Response:
        client_ip = request.client.host if request.client else "unknown"
        path = request.url.path
        
        # Skip for whitelisted IPs
        if client_ip in ip_whitelist:
            return await call_next(request)
        
        current_time = int(time.time())
        
        # Initialize or update IP counter
        if client_ip not in ip_counters:
            ip_counters[client_ip] = {
                "count": 0,
                "first_seen": current_time,
                "last_seen": current_time,
                "blocked_until": 0,
                "paths": {},
                "user_agent": request.headers.get("user-agent", "")
            }
        
        ip_data = ip_counters[client_ip]
        ip_data["count"] += 1
        ip_data["last_seen"] = current_time
        
        # Track path usage
        if path not in ip_data["paths"]:
            ip_data["paths"][path] = 0
        ip_data["paths"][path] += 1
        
        # Check if IP is blocked
        if ip_data.get("blocked_until", 0) > current_time:
            block_remaining = ip_data["blocked_until"] - current_time
            logger.warning(f"Blocked request from {client_ip} - blocked for {block_remaining} more seconds")
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={"detail": f"Too many requests. Try again in {block_remaining} seconds."}
            )
        
        # Check rate limits for critical paths
        if any(critical in path for critical in ["/auth/", "/admin/", "/payment/"]):
            # Rate limit for auth/admin/payment endpoints: 30 per minute
            minute_limit = 30
            window_size = 60
            
            # Calculate requests in the last minute
            if current_time - ip_data.get("first_seen", 0) <= window_size:
                recent_requests = ip_data["count"]
            else:
                # Simplified approach - in production use a proper time-window tracking
                recent_requests = ip_data["paths"].get(path, 0)
            
            if recent_requests > minute_limit:
                # Block for 2 minutes
                block_time = 120
                ip_data["blocked_until"] = current_time + block_time
                
                logger.warning(f"Rate limit exceeded for {client_ip} on {path}: {recent_requests} requests. Blocked for {block_time} seconds.")
                return JSONResponse(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content={"detail": f"Rate limit exceeded. Try again in {block_time} seconds."}
                )
        
        # Cleanup old entries every 100 requests
        if sum(counter["count"] for counter in ip_counters.values()) % 100 == 0:
            cleanup_old_ip_entries(ip_counters, current_time)
        
        # Add rate limit headers
        response = await call_next(request)
        response.headers["X-RateLimit-Limit"] = "60"
        response.headers["X-RateLimit-Remaining"] = str(max(0, 60 - ip_data["paths"].get(path, 0)))
        response.headers["X-RateLimit-Reset"] = "60"
        
        return response

def cleanup_old_ip_entries(ip_counters: Dict[str, Dict[str, Any]], current_time: int) -> None:
    """Clean up old IP entries to prevent memory leaks"""
    expiration_time = current_time - 3600  # 1 hour
    
    # Identify expired entries
    expired_ips = [
        ip for ip, data in ip_counters.items()
        if data["last_seen"] < expiration_time and data.get("blocked_until", 0) < current_time
    ]
    
    # Remove expired entries
    for ip in expired_ips:
        del ip_counters[ip]
    
    if expired_ips:
        logger.info(f"Cleaned up {len(expired_ips)} expired IP entries") 