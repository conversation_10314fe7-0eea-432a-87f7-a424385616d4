from fastapi import Request, Response, status, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from services.rate_limit_service import RateLimitService, RateLimitExceeded
from config.redis_config import RedisClient
from config.rate_limit_config import get_limit_type
from database import get_db
import logging
import json
import re
from typing import Optional, Dict, Any, Tuple
import os
import time

logger = logging.getLogger(__name__)

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware using centralized configuration"""
    
    def __init__(self, app):
        super().__init__(app)
        self.redis_client = RedisClient()
        self.is_test = os.getenv("TEST_MODE", "false").lower() == "true"
        self._initialized = False
        self.rate_limit_service = None
        self.test_mode = os.getenv("TEST_MODE", "false").lower() == "true"
        self.disable_rate_limit = os.getenv("DISABLE_RATE_LIMIT", "false").lower() == "true"
        
        # Configure endpoint-specific rate limits with stricter limits
        self.endpoint_limits = {
            # Default is 60 requests per minute
            "default": {"limit": 60, "window": 60, "block_time": 300},
            
            # Login endpoints - 5 requests per minute
            "login": {"limit": 5, "window": 60, "block_time": 300},
            
            # Registration endpoints - 3 requests per 5 minutes
            "register": {"limit": 3, "window": 300, "block_time": 600},
            
            # API endpoints - 30 requests per minute
            "api": {"limit": 30, "window": 60, "block_time": 300}
        }
        
        # Path patterns for different rate limit types
        self.path_patterns = {
            "login": [r"^/auth/token$", r"^/auth/login$", r"^/auth/admin/login$"],
            "register": [r"^/auth/register$"],
            "telegram_login": [r"^/auth/telegram/login$"],  # Explicitly identify Telegram login
            "api": [r"^/api/.*$"]
        }
        
        # Configure distributed attack detection with stricter thresholds
        self.distributed_attack = {
            "enabled": True,
            "window": 60,  # 1 minute window
            "threshold": 30,  # 30 requests from different IPs to same endpoint
            "block_time": 1800  # 30 minutes block
        }
        
        logger.info(f"Rate limit middleware initialized with test_mode={self.test_mode}, disable_rate_limit={self.disable_rate_limit}")

    async def initialize(self):
        """Initialize Redis client and rate limit service"""
        if not self._initialized:
            try:
                await self.redis_client.initialize()
                self.rate_limit_service = RateLimitService(self.redis_client)
                self._initialized = True
                logger.info("Rate limit middleware Redis client initialized")
            except Exception as e:
                logger.error(f"Failed to initialize rate limit middleware: {str(e)}")
                raise

    def _get_limit_config(self, path: str) -> Dict[str, Any]:
        """Get rate limit configuration for a path"""
        # Determine the limit type based on path
        limit_type = "default"
        
        for type_name, patterns in self.path_patterns.items():
            for pattern in patterns:
                if re.match(pattern, path):
                    limit_type = type_name
                    break
        
        return self.endpoint_limits.get(limit_type, self.endpoint_limits["default"])

    async def _check_distributed_attack(self, path: str, client_ip: str) -> Tuple[bool, Optional[Response]]:
        """Check for distributed attack patterns"""
        if not self.distributed_attack["enabled"]:
            return False, None
            
        # Skip for test mode
        if self.test_mode:
            return False, None
            
        # Create a key for this endpoint
        endpoint_key = f"distributed:{path}"
        
        # Add this IP to the set of IPs accessing this endpoint
        await self.redis_client.client.sadd(endpoint_key, client_ip)
        
        # Set expiry on the key if it's new
        await self.redis_client.client.expire(endpoint_key, self.distributed_attack["window"])
        
        # Get count of unique IPs
        unique_ips = await self.redis_client.client.scard(endpoint_key)
        
        # Check if threshold exceeded
        if unique_ips > self.distributed_attack["threshold"]:
            logger.warning(f"Distributed attack detected on {path}: {unique_ips} unique IPs")
            
            # Block the endpoint temporarily
            block_key = f"distributed:block:{path}"
            await self.redis_client.client.set(block_key, "1", ex=self.distributed_attack["block_time"])
            
            # Return a response indicating distributed attack detected
            return True, Response(
                content=json.dumps({
                    "detail": "Too many requests from different IPs to this endpoint",
                    "type": "distributed_attack"
                }),
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                headers={
                    "Retry-After": str(self.distributed_attack["block_time"]),
                    "X-RateLimit-Reset": str(self.distributed_attack["block_time"])
                },
                media_type="application/json"
            )
            
        # Check if endpoint is currently blocked due to distributed attack
        is_blocked = await self.redis_client.client.exists(f"distributed:block:{path}")
        if is_blocked:
            ttl = await self.redis_client.client.ttl(f"distributed:block:{path}")
            
            return True, Response(
                content=json.dumps({
                    "detail": "This endpoint is temporarily unavailable due to suspicious activity",
                    "type": "distributed_block"
                }),
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                headers={
                    "Retry-After": str(ttl),
                    "X-RateLimit-Reset": str(ttl)
                },
                media_type="application/json"
            )
            
        return False, None

    async def dispatch(self, request: Request, call_next):
        """Handle request with rate limiting"""
        try:
            # Skip rate limiting if disabled or in test mode with test header
            user_agent = request.headers.get("user-agent", "")
            is_security_test = request.headers.get("X-Security-Test") == "true" or "SecurityTestAgent" in user_agent
            
            # Special handling for Telegram WebApp requests
            is_telegram_webapp = "Telegram" in user_agent or "TelegramWebApp" in user_agent
            
            if self.disable_rate_limit or (self.test_mode and not is_security_test):
                return await call_next(request)

            # Get rate limit service from app state
            rate_limit_service = getattr(request.app.state, "rate_limit_service", None)
            if not rate_limit_service:
                logger.warning("Rate limit service not initialized")
                return await call_next(request)

            # Get client IP and user agent
            client_ip = request.client.host
            
            # Get path for rate limit configuration
            path = request.url.path
            
            # For Telegram WebApp, apply more lenient rate limiting
            is_telegram_login = path == "/auth/telegram/login"
            
            # For security tests, include rate limit headers but don't actually block
            if is_security_test:
                # Process the request
                response = await call_next(request)
                
                # Find appropriate config based on path
                endpoint_type = "default"
                if any(path_pattern in path for path_pattern in ["/auth/token", "/auth/login", "/auth/register", "/auth/admin/login"]):
                    endpoint_type = "auth"
                elif path == "/auth/telegram/login":
                    endpoint_type = "telegram_login"
                elif any(path_pattern in path for path_pattern in ["/payment", "/admin"]):
                    endpoint_type = "critical"
                
                config = self.endpoint_limits.get(endpoint_type, self.endpoint_limits["default"])
                
                # Add simulated rate limit headers
                if hasattr(response, "headers"):
                    response.headers["X-RateLimit-Limit"] = str(config["limit"])
                    response.headers["X-RateLimit-Remaining"] = str(config["limit"] - 1)
                    response.headers["X-RateLimit-Reset"] = str(config["window"])
                
                return response
            
            # For Telegram login specifically, use the correct action name
            action = "telegram_login" if is_telegram_login else path
            
            # Check rate limit
            is_allowed, limit_info = await rate_limit_service.check_rate_limit(
                action=action,
                ip=client_ip,
                user_agent=user_agent,
                raise_error=False
            )
            
            if not is_allowed:
                # Add rate limit headers
                headers = {
                    "X-RateLimit-Limit": str(limit_info.get("limit", "")),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(limit_info.get("reset_after", "")),
                    "Retry-After": str(limit_info.get("reset_after", ""))
                }
                
                return Response(
                    content=json.dumps({"detail": "Too many requests"}),
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    headers=headers,
                    media_type="application/json"
                )
            
            # Process the request
            response = await call_next(request)
            
            # Add rate limit headers to response if it's a Response object
            if hasattr(response, "headers"):
                response.headers["X-RateLimit-Limit"] = str(limit_info.get("limit", ""))
                response.headers["X-RateLimit-Remaining"] = str(limit_info.get("remaining", ""))
                response.headers["X-RateLimit-Reset"] = str(limit_info.get("reset_after", ""))
            
            return response
            
        except Exception as e:
            logger.error(f"Error in rate limit middleware: {str(e)}")
            # If an error occurs, just pass the request through without rate limiting
            try:
                return await call_next(request)
            except Exception as inner_e:
                logger.error(f"Inner error in rate limit middleware: {str(inner_e)}")
                return Response(
                    content=json.dumps({"detail": "Internal server error"}),
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    media_type="application/json"
                ) 