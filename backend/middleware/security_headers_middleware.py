"""
Security Headers Middleware
-------------------------
Middleware to add security headers to all responses.
"""
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
import logging

logger = logging.getLogger(__name__)

class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware to add security headers to all responses"""
    
    async def dispatch(self, request: Request, call_next):
        """Add security headers to the response"""
        try:
            response = await call_next(request)
            
            # Add security headers
            response.headers["Content-Security-Policy"] = (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self'; "
                "frame-ancestors 'none'; "
                "form-action 'self'; "
                "base-uri 'self'"
            )
            
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["X-Content-Type-Options"] = "nosniff"
            # response.headers["X-Frame-Options"] = "SAMEORIGIN"  # Removed for Telegram Mini App
            response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
            response.headers["Permissions-Policy"] = (
                "accelerometer=(), camera=(), geolocation=(), "
                "gyroscope=(), magnetometer=(), microphone=(), "
                "payment=(), usb=()"
            )
            response.headers["Cache-Control"] = "no-store, max-age=0"
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
            
            return response
            
        except Exception as e:
            logger.error(f"Error in security headers middleware: {str(e)}")
            # Return a 500 error with security headers
            response = Response(
                content="Internal Server Error",
                status_code=500,
                media_type="text/plain"
            )
            
            # Add security headers even for error responses
            response.headers["Content-Security-Policy"] = "default-src 'none'"
            response.headers["X-XSS-Protection"] = "1; mode=block"
            response.headers["X-Content-Type-Options"] = "nosniff"
            response.headers["X-Frame-Options"] = "DENY"
            response.headers["Referrer-Policy"] = "no-referrer"
            response.headers["Permissions-Policy"] = "accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()"
            response.headers["Cache-Control"] = "no-store, max-age=0"
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
            
            return response 