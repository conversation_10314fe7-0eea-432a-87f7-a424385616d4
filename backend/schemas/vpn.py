"""
VPN-related Pydantic schemas for validation and serialization.
Contains schemas for Marzban panels, VPN packages, and VPN subscriptions.
"""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, ConfigDict

from .base import BaseModelConfig


# Marzban Panel schemas
class MarzbanPanelBase(BaseModelConfig):
    """Base Marzban panel schema"""
    name: str = Field(..., min_length=1, max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., min_length=1, max_length=100)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')
        return v.rstrip('/')  # Remove trailing slashes

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Name cannot be empty')
        if len(v) > 100:
            raise ValueError('Name cannot be longer than 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Username cannot be empty')
        if len(v) > 100:
            raise ValueError('Username cannot be longer than 100 characters')
        return v


class MarzbanPanelCreate(BaseModelConfig):
    """Schema for creating Marzban panel"""
    name: str = Field(..., min_length=1, max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., min_length=1, max_length=100)
    admin_password: str = Field(..., min_length=4)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')
        return v.rstrip('/')  # Remove trailing slashes

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Name cannot be empty')
        if len(v) > 100:
            raise ValueError('Name cannot be longer than 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_username(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Username cannot be empty')
        if len(v) > 100:
            raise ValueError('Username cannot be longer than 100 characters')
        return v

    @field_validator('admin_password')
    @classmethod
    def validate_password(cls, v: str) -> str:
        if len(v) < 4:
            raise ValueError('Password must be at least 4 characters long')
        return v


class MarzbanPanelUpdate(BaseModelConfig):
    """Schema for updating Marzban panel"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    api_url: Optional[str] = Field(None, pattern=r'^https?://.+')
    admin_username: Optional[str] = Field(None, min_length=1, max_length=100)
    admin_password: Optional[str] = Field(None, min_length=4)
    is_active: Optional[bool] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            if not v.startswith(('http://', 'https://')):
                raise ValueError('API URL must start with http:// or https://')
            return v.rstrip('/')  # Remove trailing slashes
        return v

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            v = v.strip()
            if not v:
                raise ValueError('Name cannot be empty')
            if len(v) > 100:
                raise ValueError('Name cannot be longer than 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_username(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            v = v.strip()
            if not v:
                raise ValueError('Username cannot be empty')
            if len(v) > 100:
                raise ValueError('Username cannot be longer than 100 characters')
        return v

    @field_validator('admin_password')
    @classmethod
    def validate_password(cls, v: Optional[str]) -> Optional[str]:
        if v is not None and len(v) < 4:
            raise ValueError('Password must be at least 4 characters long')
        return v

    def dict_with_encrypted_password(self, service) -> dict:
        """Get dictionary with encrypted password for secure storage"""
        data = self.model_dump(exclude_unset=True)
        if 'admin_password' in data and data['admin_password']:
            data['admin_password'] = service.encrypt_password(data['admin_password'])
        return data


class MarzbanPanelOut(BaseModelConfig):
    """Marzban panel output schema"""
    id: int
    name: str = Field(..., max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., max_length=100)
    is_active: bool
    created_at: datetime
    
    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Main Panel",
                "api_url": "https://api.example.com",
                "admin_username": "admin",
                "is_active": True,
                "created_at": "2024-02-05T12:00:00"
            }
        }
    )


# VPN Package schemas
class VPNPackageBase(BaseModelConfig):
    """Base VPN package schema"""
    name: str = Field(..., min_length=3, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    data_limit: int = Field(..., gt=0)
    expire_days: int = Field(..., gt=0)
    price: float = Field(..., ge=0)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)


class VPNPackageCreate(VPNPackageBase):
    """Schema for creating VPN package"""
    allowed_panel_ids: List[int]


class VPNPackageUpdate(VPNPackageBase):
    """Schema for updating VPN package"""
    name: Optional[str] = None
    description: Optional[str] = None
    data_limit: Optional[int] = None
    expire_days: Optional[int] = None
    price: Optional[float] = None
    is_active: Optional[bool] = None
    allowed_panel_ids: Optional[List[int]] = None


class VPNPackageOut(VPNPackageBase):
    """VPN package output schema"""
    id: int
    name: str
    data_limit: int
    expire_days: int
    price: float
    description: Optional[str]
    is_active: bool
    created_at: datetime
    allowed_panels: List[MarzbanPanelOut]
    
    model_config = ConfigDict(from_attributes=True)


# VPN Subscription schemas
class VPNSubscriptionBase(BaseModelConfig):
    """Base VPN subscription schema"""
    package_id: int
    marzban_username: str
    subscription_url: str
    data_limit: int
    data_used: int = 0
    expires_at: datetime
    is_active: bool = True
    package: Optional[VPNPackageOut] = None


class VPNSubscriptionOut(VPNSubscriptionBase):
    """VPN subscription output schema"""
    id: int
    user_id: int
    created_at: datetime
    package: VPNPackageOut
    
    model_config = ConfigDict(from_attributes=True)


class VPNSubscriptionCreate(BaseModelConfig):
    """Schema for creating VPN subscription"""
    package_id: int
    panel_id: int
    custom_name: str
    
    model_config = ConfigDict(from_attributes=True)


# Configuration schemas
class ConfigCreate(BaseModelConfig):
    """Schema for creating configuration"""
    name: str = Field(..., min_length=3, max_length=100)
    api_url: str = Field(..., pattern=r'^https?://.+')
    admin_username: str = Field(..., min_length=4, max_length=50)
    admin_password: str = Field(..., min_length=8, max_length=100)
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('api_url')
    @classmethod
    def validate_api_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('API URL must start with http:// or https://')
        return v.rstrip('/')

    @field_validator('name')
    @classmethod
    def validate_name(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Name cannot be empty')
        if len(v) > 100:
            raise ValueError('Name cannot be longer than 100 characters')
        return v

    @field_validator('admin_username')
    @classmethod
    def validate_admin_username(cls, v: str) -> str:
        v = v.strip()
        if not v:
            raise ValueError('Admin username cannot be empty')
        if len(v) < 4:
            raise ValueError('Admin username must be at least 4 characters long')
        if len(v) > 50:
            raise ValueError('Admin username cannot be longer than 50 characters')
        # Check for valid characters
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Admin username can only contain letters, numbers, hyphens, and underscores')
        return v

    @field_validator('admin_password')
    @classmethod
    def validate_admin_password(cls, v: str) -> str:
        if len(v) < 8:
            raise ValueError('Admin password must be at least 8 characters long')
        if len(v) > 100:
            raise ValueError('Admin password cannot be longer than 100 characters')
        
        # Check for at least one uppercase, one lowercase, one digit
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)
        
        if not (has_upper and has_lower and has_digit):
            raise ValueError('Admin password must contain at least one uppercase letter, one lowercase letter, and one digit')
        
        return v


# User VPN Update schema
class UserVPNUpdate(BaseModelConfig):
    """Schema for updating user VPN information"""
    marzban_username: Optional[str] = None
    marzban_subscription_url: Optional[str] = None 