"""Authentication-related schemas"""
from pydantic import Field, ConfigDict
from typing import Optional
from .base import BaseModelConfig

class UserLogin(BaseModelConfig):
    """User login schema"""
    username: str
    password: str
    csrf_token: Optional[str] = None
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True
    )

class Token(BaseModelConfig):
    """JWT token response schema"""
    access_token: str
    token_type: str
    role: str
    username: str
    id: int
    telegram_id: Optional[str] = None  # Keep as string for API responses

    model_config = ConfigDict(from_attributes=True)

class TelegramAuthData(BaseModelConfig):
    """Telegram authentication data schema"""
    id: int
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    photo_url: Optional[str] = None
    auth_date: int
    hash: str

class AdminLoginRequest(BaseModelConfig):
    """Admin login request schema"""
    username: str
    password: str
    csrf_token: Optional[str] = None  # Allow csrf_token from frontend
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True
    ) 