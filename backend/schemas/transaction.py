"""Transaction-related schemas"""
from pydantic import Field, ConfigDict
from typing import Optional
from datetime import datetime
from .base import BaseModelConfig

class TransactionBase(BaseModelConfig):
    """Base transaction schema with common fields"""
    type: str
    amount: float
    description: Optional[str] = None
    package_name: Optional[str] = None
    package_price: Optional[float] = None
    package_data_limit: Optional[int] = None
    package_expire_days: Optional[int] = None
    status: str = "completed"

class TransactionOut(BaseModelConfig):
    """Transaction output schema"""
    id: int
    type: str
    amount: float
    created_at: datetime
    user_id: int
    reseller_id: Optional[int] = None
    subscription_id: Optional[int] = None
    package_name: Optional[str] = None
    package_price: Optional[float] = None
    package_data_limit: Optional[int] = None
    package_expire_days: Optional[int] = None
    description: Optional[str] = None
    balance_after: float
    status: str
    
    model_config = ConfigDict(from_attributes=True)

class TransactionCreate(TransactionBase):
    """Schema for creating a new transaction"""
    user_id: int
    balance_after: float
    reseller_id: Optional[int] = None
    subscription_id: Optional[int] = None

class TransactionUpdate(BaseModelConfig):
    """Schema for updating transaction"""
    status: Optional[str] = None
    description: Optional[str] = None 