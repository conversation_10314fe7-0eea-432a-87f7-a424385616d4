"""
Modular Schemas Package

This package organizes all Pydantic schemas by domain:
- admin: Admin-specific schemas and operations
- auth: Authentication and authorization schemas  
- base: Base schemas and shared components
- card: Card system and catalog schemas
- chat: Chat and messaging schemas
- task: Task management and completion schemas
- transaction: Transaction and payment schemas
- user: User management and profile schemas
- vpn: VPN packages, panels, and subscription schemas

All schemas are re-exported here for backwards compatibility.
"""

# Base schemas (must be imported first to avoid circular imports)
from .base import BaseModelConfig, RoleEnum

# Import modules in dependency order to minimize circular imports
from . import auth
from . import vpn  # VPN before user (UserOut references VPNSubscriptionOut)
from . import task  # Task before user (UserOut references TaskCompletionOut)
from . import card  # Card before user (UserOut references UserCardOut)
from . import transaction
from . import chat
from . import user  # User after its dependencies
from . import admin  # Admin last (references many other schemas)

# Re-export commonly used schemas for backwards compatibility
from .auth import Token, UserLogin, TelegramAuthData, AdminLoginRequest
from .vpn import (
    MarzbanPanelOut, MarzbanPanelCreate, MarzbanPanelUpdate,
    VPNPackageOut, VPNPackageCreate, VPNPackageUpdate,
    VPNSubscriptionOut, VPNSubscriptionCreate, VPNSubscriptionBase,
    ConfigCreate
)
from .task import (
    TaskOut, TaskCreate, TaskUpdate, TaskCompletionOut, TaskCompletionCreate, TaskCompletionUpdate,
    DailyTaskStreakOut, DailyCycleOut, DailyCheckInResponse, NextCheckInResponse,
    TaskPackOut, TaskPackCreate, SocialTaskOut, SocialTaskCreate, SocialTaskUpdate,
    TaskVerificationOut, TaskVerificationCreate, TaskVerificationResult,
    RewardRevocationOut, RewardRevocationCreate, TaskType, RewardType, TaskStatus, SocialPlatform
)
from .card import (
    UserCardOut, UserCardCreate, ClaimAllResponse, BuyCardResult, 
    CardCatalogOut, CardCatalogCreate, UnifiedCardResponse
)
from .transaction import TransactionOut, TransactionCreate, TransactionUpdate
from .chat import ChatMessageOut, ChatMessageCreate, ChatConversationPartner, UserSearchResult, UserDisplayInfo
from .user import (
    UserOut, UserCreate, UserUpdate, AdminUserUpdate, UserProfile, 
    UserProfileUpdate, ResellerProfileUpdate, UserVPNUpdate, ReferralInfo, ReferredUser
)
from .admin import (
    AdminTaskOut, TaskStatistics, SecurityLogCreate, TokenBlacklistSchema, 
    TaskStatusUpdate, UpdateTaskData, TaskRewardOut, TaskStatusOut,
    YouTubeVerificationData, SocialFollowVerificationData
)

# Rebuild all models to resolve forward references
def _rebuild_models():
    """Rebuild all models to resolve forward references after all schemas are loaded"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        # Import all schema classes
        from .user import UserOut
        from .task import TaskOut, TaskCompletionOut, TaskPackOut
        from .admin import AdminTaskOut
        from .vpn import VPNPackageOut, VPNSubscriptionOut
        from .card import UserCardOut
        
        # List of models to rebuild
        models_to_rebuild = [
            ('UserOut', UserOut),
            ('TaskOut', TaskOut),
            ('TaskCompletionOut', TaskCompletionOut), 
            ('TaskPackOut', TaskPackOut),
            ('AdminTaskOut', AdminTaskOut),
            ('VPNPackageOut', VPNPackageOut),
            ('VPNSubscriptionOut', VPNSubscriptionOut),
            ('UserCardOut', UserCardOut),
        ]
        
        # Rebuild each model individually
        for name, model in models_to_rebuild:
            try:
                model.model_rebuild()
                logger.debug(f"Successfully rebuilt {name}")
            except Exception as e:
                logger.warning(f"Failed to rebuild {name}: {e}")
                
    except Exception as e:
        logger.warning(f"Failed to rebuild models: {e}")

# Call rebuild function
_rebuild_models()

# Export all available schemas
__all__ = [
    # Base
    "BaseModelConfig", "RoleEnum",
    
    # Auth
    "Token", "UserLogin", "TelegramAuthData", "AdminLoginRequest",
    
    # User
    "UserOut", "UserCreate", "UserUpdate", "AdminUserUpdate", 
    "UserProfile", "UserProfileUpdate", "ResellerProfileUpdate", "UserVPNUpdate",
    "ReferralInfo", "ReferredUser",
    
    # Admin
    "AdminTaskOut", "TaskStatistics", "SecurityLogCreate", "TokenBlacklistSchema",
    "TaskStatusUpdate", "UpdateTaskData", "TaskRewardOut", "TaskStatusOut",
    "YouTubeVerificationData", "SocialFollowVerificationData",
    
    # Task
    "TaskOut", "TaskCreate", "TaskUpdate", "TaskCompletionOut", "TaskCompletionCreate", "TaskCompletionUpdate",
    "DailyTaskStreakOut", "DailyCycleOut", "DailyCheckInResponse", "NextCheckInResponse",
    "TaskPackOut", "TaskPackCreate", "SocialTaskOut", "SocialTaskCreate", "SocialTaskUpdate",
    "TaskVerificationOut", "TaskVerificationCreate", "TaskVerificationResult",
    "RewardRevocationOut", "RewardRevocationCreate", "TaskType", "RewardType", "TaskStatus", "SocialPlatform",
    
    # Card
    "UserCardOut", "UserCardCreate", "ClaimAllResponse", "BuyCardResult",
    "CardCatalogOut", "CardCatalogCreate", "UnifiedCardResponse",
    
    # Chat
    "ChatMessageOut", "ChatMessageCreate", "ChatConversationPartner", 
    "UserSearchResult", "UserDisplayInfo",
    
    # Transaction
    "TransactionOut", "TransactionCreate", "TransactionUpdate",
    
    # VPN
    "MarzbanPanelOut", "MarzbanPanelCreate", "MarzbanPanelUpdate",
    "VPNPackageOut", "VPNPackageCreate", "VPNPackageUpdate",
    "VPNSubscriptionOut", "VPNSubscriptionCreate", "VPNSubscriptionBase", "ConfigCreate",
] 