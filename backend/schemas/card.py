"""
Card-related Pydantic schemas for validation and serialization.
Contains schemas for card catalog, user cards, and card transactions.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, ConfigDict
import json

from .base import BaseModelConfig


# User Card schemas
class UserCardBase(BaseModelConfig):
    """Base user card schema"""
    pass


class UserCardCreate(BaseModel):
    """Schema for creating user card"""
    card_catalog_id: int = Field(..., gt=0)

    model_config = ConfigDict(from_attributes=True)


class NestedCardInfo(BaseModel):
    """Nested card information schema"""
    id: int
    name: str
    rarity: str
    image_url: Optional[str] = None
    max_level: int
    description: Optional[str] = "No description available."

    model_config = ConfigDict(from_attributes=True)


class UserCardOut(BaseModel):
    """User card output schema"""
    id: int
    user_id: int
    card_catalog_id: int
    level: int
    purchase_date: str  # ISO format string
    last_profit_claim: str  # ISO format string
    total_claimed_profit: float
    accumulated_profit: float
    current_hourly_profit: float
    next_upgrade_cost: Optional[float]
    card: NestedCardInfo  # Reference the schema defined above

    model_config = ConfigDict(from_attributes=True)


class ClaimAllResponse(BaseModel):
    """Response schema for claiming all profits"""
    total_claimed: float
    new_wallet_balance: float
    cards_count: int
    next_claim_available: datetime

    model_config = ConfigDict(from_attributes=True)


class BuyCardResult(BaseModel):
    """Result schema for buying a card"""
    new_card: UserCardOut
    new_wallet_balance: float

    model_config = ConfigDict(from_attributes=True)


# Card Catalog schemas
class CardCatalogBase(BaseModelConfig):
    """Base card catalog schema"""
    name: str = Field(..., min_length=1, max_length=100)
    rarity: str = Field("common", pattern=r"^(common|rare|epic|legendary|mythic)$")
    level_profits_json: str  # Expecting a JSON string representing a list of floats
    level_costs_json: str    # Expecting a JSON string representing a list of floats
    max_level: int = Field(10, gt=1)
    image_url: Optional[str] = Field(None, max_length=255)

    @field_validator("level_profits_json", "level_costs_json")
    @classmethod
    def validate_level_json(cls, v: str, info) -> str:
        """Validate JSON strings for level profits and costs"""
        try:
            data = json.loads(v)
            if not isinstance(data, list):
                raise ValueError("JSON must represent a list")
            # Basic check: ensure all items are numbers (int or float)
            if not all(isinstance(item, (int, float)) for item in data):
                raise ValueError("All items in the list must be numbers")

            # More specific checks based on field name and max_level (if available)
            # Note: max_level might not be available during base validation depending on order
            # We perform more robust checks in the model properties
            field_name = info.field_name
            # max_level_val = info.data.get("max_level", 10) # Might not work reliably here

            # if field_name == "level_profits_json" and len(data) != max_level_val:
            #     raise ValueError(f"Profits list must have {max_level_val} elements")
            # if field_name == "level_costs_json" and len(data) != max_level_val - 1:
            #     raise ValueError(f"Costs list must have {max_level_val - 1} elements")

            return v  # Return the original JSON string if valid
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON string")
        except Exception as e:
            raise ValueError(f"Validation error for JSON list: {e}")


class CardCatalogCreate(CardCatalogBase):
    """Schema for creating card catalog"""
    pass


class CardCatalogOut(CardCatalogBase):
    """Card catalog output schema"""
    id: int
    created_at: datetime
    # initial_purchase_cost is already inherited from CardCatalogBase
    # Optionally parse the JSON for the response if needed by frontend directly
    # level_profits: List[float] = Field(default_factory=list)
    # level_costs: List[float] = Field(default_factory=list)
    # @field_validator("level_profits", "level_costs", mode="before") ... add parsing logic

    model_config = ConfigDict(from_attributes=True)


# Unified card response for combining catalog and ownership data
class UnifiedCardResponse(BaseModelConfig):
    """Unified card response combining catalog and ownership data"""
    id: int
    name: str = "Unknown Card"
    description: str = "No description available."
    image_url: Optional[str] = ""
    rarity: str = "common"
    max_level: int = 10
    profit_rate: float = 0.0
    price: float = 0.0
    level_profits: Optional[List[float]] = []
    next_level_profit_increase: Optional[float] = None
    is_owned: bool = False
    user_card: Optional[Dict[str, Any]] = None
    
    model_config = ConfigDict(from_attributes=True) 