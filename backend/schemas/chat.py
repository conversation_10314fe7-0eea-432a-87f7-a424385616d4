"""
Chat-related Pydantic schemas for validation and serialization.
Contains schemas for chat messages, conversations, and user interactions.
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict

from .base import BaseModelConfig


# Chat Message schemas
class ChatMessageBase(BaseModel):
    """Base schema for chat messages with privacy focus (no sender_username)"""
    content: str = Field(..., min_length=1, max_length=1000)
    
    model_config = ConfigDict(from_attributes=True)
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v: str) -> str:
        """Sanitize and validate message content"""
        v = v.strip()
        if not v:
            raise ValueError("Message content cannot be empty")
        if len(v) > 1000:
            raise ValueError("Message content too long (max 1000 chars)")
        return v


class ChatMessageCreate(ChatMessageBase):
    """Schema for creating a new chat message"""
    receiver_id: Optional[int] = Field(None, description="User ID of the receiver, null for public messages")
    
    @field_validator('receiver_id')
    @classmethod
    def validate_receiver_id(cls, v: Optional[int]) -> Optional[int]:
        """Validate receiver ID if present"""
        if v is not None and v <= 0:
            raise ValueError("Invalid receiver ID")
        return v


class ChatMessageOut(ChatMessageBase):
    """Schema for returning chat messages (without usernames for privacy)"""
    id: int
    sender_id: int
    receiver_id: Optional[int] = None
    created_at: datetime
    is_public: bool
    sender_nickname: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

    @model_validator(mode='after')
    def compute_is_public(self) -> 'ChatMessageOut':
        """Compute if message is public based on receiver_id"""
        self.is_public = self.receiver_id is None
        return self


# Conversation and User schemas
class ChatConversationPartner(BaseModel):
    """Schema for returning conversation partners (with anonymous nicknames for the list view)"""
    id: int  # Anonymous chat ID, not the actual user ID
    username: str  # Anonymous nickname, not the actual username
    avatar_url: Optional[str] = None
    last_message_at: Optional[datetime] = None
    unread_count: Optional[int] = 0  # Default to 0, will be calculated
    is_online: bool = False  # Flag to indicate if user is online

    model_config = ConfigDict(from_attributes=True)


class UserSearchResult(BaseModel):
    """Schema for user search results (with usernames for selection)"""
    id: int
    username: str
    avatar_url: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class UserDisplayInfo(BaseModel):
    """Schema for minimal user display info (no username for privacy)"""
    avatar_url: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True) 