"""
Task-related Pydantic schemas for validation and serialization.
Contains schemas for tasks, task completions, daily tasks, verifications, and social tasks.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from enum import Enum
import json
import ipaddress

from .base import BaseModelConfig


class TaskType(str, Enum):
    """Task type enumeration"""
    DAILY_CHECKIN = "daily_checkin"
    YOUTUBE_VIEW = "youtube_view"
    TELEGRAM_CHANNEL = "telegram_channel"
    INSTAGRAM_FOLLOW = "instagram_follow"
    TWITTER_FOLLOW = "twitter_follow"
    REFERRAL = "referral"
    DEPOSIT = "deposit"
    TRADE = "trade"


class RewardType(str, Enum):
    """Reward type enumeration"""
    COINS = "coins"
    VPN_PACKAGE = "vpn_package"
    CARD_PACK = "card_pack"
    DISCOUNT = "discount"


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CLAIMED = "claimed"
    FAILED = "failed"
    EXPIRED = "expired"
    ACTIVE = "active"
    INACTIVE = "inactive"


class SocialPlatform(str, Enum):
    """Social platform enumeration"""
    YOUTUBE = "youtube"
    TELEGRAM = "telegram"
    INSTAGRAM = "instagram"
    TWITTER = "twitter"
    DISCORD = "discord"


# Task schemas
class TaskBase(BaseModelConfig):
    """Base task schema with common fields"""
    name: str = Field(..., min_length=3, max_length=100)
    description: str = Field(..., min_length=10, max_length=500)
    type: TaskType
    reward_type: RewardType
    reward_value: float = Field(..., ge=0, le=1000)
    target_value: int = Field(1, ge=1, le=1000)
    is_active: bool = True
    # Daily task fields
    cycle_length: Optional[int] = Field(7, ge=1, le=30)
    daily_rewards: Optional[List[float]] = Field(None, max_length=30)
    cycle_bonus_reward: Optional[float] = Field(None, ge=0, le=1000)
    reset_streak_after_hours: Optional[int] = Field(48, ge=1, le=168)
    # Platform fields
    platform_url: Optional[str] = Field(None, max_length=500, pattern=r'^https?://.+')
    platform_id: Optional[str] = Field(None, max_length=100)
    verify_key: Optional[str] = Field(None, max_length=100)
    # Verification fields
    max_verification_attempts: Optional[int] = Field(3, ge=1, le=10)
    verification_cooldown: Optional[int] = Field(60, ge=30, le=3600)  # 30 seconds to 1 hour
    required_duration: Optional[int] = Field(None, ge=0)

    @field_validator('daily_rewards')
    @classmethod
    def validate_daily_rewards(cls, v: Optional[List[float]], info) -> Optional[List[float]]:
        if v is not None:
            if not isinstance(v, list):
                raise ValueError('Daily rewards must be a list of numbers')
            if len(v) != info.data.get('cycle_length', 7):
                raise ValueError(f'Daily rewards list length must match cycle_length ({info.data.get("cycle_length", 7)})')
            for reward in v:
                if not isinstance(reward, (int, float)) or reward < 0 or reward > 1000:
                    raise ValueError('Daily rewards must be non-negative numbers between 0 and 1000')
        return v


class TaskCreate(TaskBase):
    """Schema for creating a new task"""
    model_config = ConfigDict(extra='ignore')

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: Optional[str], info) -> Optional[str]:
        task_type = info.data.get('type')
        if task_type in [TaskType.YOUTUBE_VIEW, TaskType.TELEGRAM_CHANNEL, TaskType.INSTAGRAM_FOLLOW] and not v:
            raise ValueError(f'Platform URL is required for {task_type} tasks')
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Platform URL must start with http:// or https://')
        return v

    @field_validator('platform_id')
    @classmethod
    def validate_platform_id(cls, v: Optional[str], info) -> Optional[str]:
        task_type = info.data.get('type')
        if task_type in [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW] and not v:
            raise ValueError(f'Platform ID is required for {task_type} tasks')
        return v

    @field_validator('verify_key')
    @classmethod
    def validate_verify_key(cls, v: Optional[str], info) -> Optional[str]:
        task_type = info.data.get('type')
        if task_type in [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW, TaskType.YOUTUBE_VIEW] and not v:
            raise ValueError(f'Verification key is required for {task_type} tasks')
        return v

    @field_validator('max_verification_attempts')
    @classmethod
    def validate_max_attempts(cls, v: Optional[int], info) -> Optional[int]:
        if v is not None and (v < 1 or v > 10):
            raise ValueError("Max verification attempts must be between 1 and 10")
        return v

    @field_validator('verification_cooldown')
    @classmethod
    def validate_cooldown(cls, v: Optional[int], info) -> Optional[int]:
        if v is not None and (v < 30 or v > 3600):
            raise ValueError("Verification cooldown must be between 30 seconds and 1 hour")
        return v


class TaskUpdate(BaseModelConfig):
    """Schema for updating task information"""
    name: Optional[str] = Field(None, min_length=3, max_length=100)
    description: Optional[str] = Field(None, min_length=10)
    reward_type: Optional[RewardType] = None
    reward_value: Optional[float] = Field(None, ge=0, le=1000)
    target_value: Optional[int] = Field(None, ge=1)
    is_active: Optional[bool] = None
    platform_url: Optional[str] = None
    platform_id: Optional[str] = None
    verify_key: Optional[str] = None
    max_verification_attempts: Optional[int] = Field(None, ge=1, le=10)
    verification_cooldown: Optional[int] = Field(None, ge=30, le=3600)

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: Optional[str]) -> Optional[str]:
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Platform URL must start with http:// or https://')
        return v

    @field_validator('max_verification_attempts')
    @classmethod
    def validate_max_attempts(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and (v < 1 or v > 10):
            raise ValueError("Max verification attempts must be between 1 and 10")
        return v

    @field_validator('verification_cooldown')
    @classmethod
    def validate_verification_cooldown(cls, v: Optional[int]) -> Optional[int]:
        if v is not None and (v < 30 or v > 3600):
            raise ValueError("Verification cooldown must be between 30 seconds and 1 hour")
        return v


class TaskOut(TaskBase):
    """Schema for task output with status information"""
    id: int
    created_at: datetime
    # Task status fields
    status: TaskStatus = TaskStatus.ACTIVE
    is_claimed: bool = False
    completion_id: Optional[int] = None
    started_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    last_verified_at: Optional[datetime] = None
    current_progress: Optional[int] = None
    verification_attempts: Optional[int] = None
    next_verification_time: Optional[datetime] = None
    verification_method: Optional[str] = None
    
    model_config = ConfigDict(
        from_attributes=True,
        extra='allow'  # Allow extra fields
    )

    @field_validator('daily_rewards', mode='before')
    def parse_daily_rewards(cls, v: Optional[str]) -> Optional[List[float]]:
        if v is None:
            return None
        try:
            return json.loads(v)
        except:
            return None

    @property
    def total_cycle_reward(self) -> float:
        """Calculate total cycle reward dynamically"""
        if self.type != TaskType.DAILY_CHECKIN:
            return self.reward_value
        
        daily_rewards = self.daily_rewards or [10, 15, 20, 25, 30, 35, 50]
        cycle_bonus = self.cycle_bonus_reward or 100
        return sum(daily_rewards) + cycle_bonus


class TaskStatistics(BaseModelConfig):
    """Task statistics schema"""
    total_attempts: int = Field(..., ge=0)
    successful_completions: int = Field(..., ge=0)
    claimed_rewards: int = Field(..., ge=0)
    success_rate: float = Field(..., ge=0, le=100)
    active_users: int = Field(..., ge=0)
    
    model_config = ConfigDict(from_attributes=True)


class AdminTaskOut(TaskOut):
    """Enhanced task output schema for admin with statistics"""
    statistics: Optional[TaskStatistics] = None
    total_verifications: Optional[int] = Field(None, ge=0)
    successful_verifications: Optional[int] = Field(None, ge=0)
    verification_success_rate: Optional[float] = Field(None, ge=0, le=100)
    average_completion_time: Optional[int] = Field(None, ge=0)  # In minutes
    
    model_config = ConfigDict(from_attributes=True)


# Task Completion schemas
class TaskCompletionBase(BaseModelConfig):
    """Base task completion schema"""
    task_id: int = Field(..., gt=0)
    user_id: int = Field(..., gt=0)
    status: str  # Changed from TaskStatus enum to str to fix serialization warnings
    started_at: datetime
    current_progress: int = Field(0, ge=0, le=1000)
    verification_attempts: int = Field(0, ge=0, le=10)
    is_claimed: bool = False
    expires_at: Optional[datetime] = None
    last_verified_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    claimed_at: Optional[datetime] = None
    verification_data: Optional[Dict] = None
    # Daily task fields
    streak_day: Optional[int] = Field(None, ge=0, le=365)
    cycle_day: Optional[int] = Field(None, ge=0, le=30)
    reward_multiplier: Optional[float] = Field(None, ge=0, le=10)
    # Security fields
    reward_amount: float = Field(0.0, ge=0, le=1000)
    verification_ip: Optional[str] = Field(None, max_length=45)
    verification_user_agent: Optional[str] = Field(None, max_length=255)
    last_verification_attempt: Optional[datetime] = None
    verification_cooldown: int = Field(60, ge=30, le=3600)
    max_verification_attempts: int = Field(3, ge=1, le=10)
    is_verified: bool = False
    verification_method: Optional[str] = Field(None, pattern=r'^(api|code|manual|auto)$')
    verification_token: Optional[str] = Field(None, max_length=100)

    @field_validator('verification_data')
    @classmethod
    def validate_verification_data(cls, v: Optional[Dict]) -> Optional[Dict]:
        if v is not None:
            if isinstance(v, str):
                try:
                    v = json.loads(v)
                except json.JSONDecodeError:
                    raise ValueError('Verification data must be valid JSON')
            if not isinstance(v, dict):
                raise ValueError('Verification data must be a dictionary')
            # Validate max size
            if len(json.dumps(v)) > 1000:
                raise ValueError('Verification data too large')
        return v

    @field_validator('expires_at')
    @classmethod
    def validate_expiry(cls, v: Optional[datetime]) -> Optional[datetime]:
        if v is not None and v < datetime.utcnow():
            raise ValueError('Expiry time cannot be in the past')
        return v

    @field_validator('verification_ip')
    @classmethod
    def validate_ip(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            try:
                ipaddress.ip_address(v)
            except ValueError:
                raise ValueError('Invalid IP address format')
        return v

    @field_validator('verification_user_agent')
    @classmethod
    def sanitize_user_agent(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            # Remove potentially dangerous characters
            v = ''.join(c for c in v if ord(c) < 127)  # Remove non-ASCII
            v = v.replace('<', '').replace('>', '').replace('"', '').replace("'", '')
        return v


class TaskCompletionOut(TaskCompletionBase):
    """Task completion output schema"""
    id: int
    task: Any  # Simplified to avoid forward reference issues
    
    model_config = ConfigDict(
        from_attributes=True,
        extra='allow'
    )

    @field_validator('verification_data', mode='before')
    def parse_verification_data(cls, v: Optional[str]) -> Optional[Dict]:
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except:
                return None
        return v


class TaskCompletionCreate(TaskCompletionBase):
    """Schema for creating task completion"""
    model_config = ConfigDict(from_attributes=True)


class TaskCompletionUpdate(BaseModelConfig):
    """Schema for updating task completion"""
    status: Optional[str] = None  # Changed from TaskStatus enum to str
    current_progress: Optional[int] = None
    verification_attempts: Optional[int] = None
    is_claimed: Optional[bool] = None
    completed_at: Optional[datetime] = None
    claimed_at: Optional[datetime] = None
    verification_data: Optional[str] = None
    streak_day: Optional[int] = None
    cycle_day: Optional[int] = None
    reward_multiplier: Optional[float] = None


class TaskStatusUpdate(BaseModelConfig):
    """Schema for updating task status"""
    status: TaskStatus
    current_progress: Optional[int] = None
    verification_data: Optional[dict] = None
    
    model_config = ConfigDict(from_attributes=True)


class TaskStatusOut(BaseModelConfig):
    """Task status output schema"""
    status: TaskStatus
    verification_attempts: int = Field(..., ge=0, le=10)
    verification_time_left: int = Field(..., ge=0, le=3600)  # Seconds until next verification
    current_progress: int = Field(..., ge=0, le=1000)
    is_claimed: bool
    last_verified_at: Optional[datetime] = None
    next_verification_time: Optional[datetime] = None
    verification_method: Optional[str] = Field(None, pattern=r'^(api|code|manual|auto)$')
    
    model_config = ConfigDict(from_attributes=True)


class TaskRewardOut(BaseModelConfig):
    """Task reward output schema"""
    task_id: int
    task_name: str
    reward_type: RewardType
    reward_value: float
    claimed_at: datetime
    transaction_id: Optional[int]
    
    model_config = ConfigDict(from_attributes=True)


# Daily Task schemas
class DailyTaskStreakBase(BaseModelConfig):
    """Base daily task streak schema"""
    current_streak: int = 0
    longest_streak: int = 0
    total_check_ins: int = 0
    current_cycle_day: int = 1
    last_check_in: Optional[datetime] = None
    first_check_time: Optional[datetime] = None
    last_streak_break: Optional[datetime] = None


class DailyTaskStreakOut(DailyTaskStreakBase):
    """Daily task streak output schema"""
    id: int
    user_id: int
    
    model_config = ConfigDict(from_attributes=True)


class DailyCycleBase(BaseModelConfig):
    """Base daily cycle schema"""
    cycle_number: int = Field(1, ge=1)
    start_date: datetime
    end_date: Optional[datetime] = None
    completed_days: List[int] = Field(default_factory=list)
    total_reward: float = Field(0.0, ge=0)
    is_completed: bool = False


class DailyCycleCreate(DailyCycleBase):
    """Schema for creating daily cycle"""
    task_id: int
    user_id: int


class DailyCycleOut(DailyCycleBase):
    """Daily cycle output schema"""
    id: int
    task_id: int
    user_id: int
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('completed_days', mode='before')
    def parse_completed_days(cls, v: Optional[str]) -> List[int]:
        if v is None:
            return []
        if isinstance(v, str):
            try:
                return json.loads(v)
            except:
                return []
        return v or []


class DailyCheckInResponse(BaseModelConfig):
    """Response schema for daily check-in"""
    streak: DailyTaskStreakOut
    completion: TaskCompletionOut
    reward: float
    multiplier: float
    next_check_in: datetime
    cycle_completion: Optional[bool] = False
    cycle_reward: Optional[float] = None
    current_cycle: Optional[DailyCycleOut] = None
    
    model_config = ConfigDict(from_attributes=True)


class NextCheckInResponse(BaseModelConfig):
    """Response schema for next check-in information"""
    can_check_in: bool
    next_check_in: Optional[datetime]
    time_left: int  # seconds until next check-in
    current_streak: Optional[int]
    current_cycle_day: Optional[int]
    
    model_config = ConfigDict(from_attributes=True)


# Task Pack schemas
class TaskPackBase(BaseModelConfig):
    """Base task pack schema"""
    name: str = Field(..., min_length=3, max_length=100)
    description: str = Field(..., min_length=10, max_length=500)
    reward_package_id: int = Field(..., gt=0)
    is_lifetime: bool = False
    auto_renewal: bool = False
    required_duration: int = Field(..., gt=0)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)


class TaskPackCreate(TaskPackBase):
    """Schema for creating task pack"""
    task_ids: List[int]


class TaskPackOut(TaskPackBase):
    """Task pack output schema"""
    id: int
    tasks: List[Any]  # Simplified to avoid forward reference issues
    reward_package: Any  # Simplified to avoid forward reference issues
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


# Social Task schemas
class SocialTaskBase(BaseModelConfig):
    """Base social task schema"""
    platform: str = Field(..., max_length=50)
    platform_id: str = Field(..., max_length=100)
    platform_url: str = Field(..., pattern=r'^https?://.+')
    verify_key: Optional[str] = Field(None, max_length=100)
    max_verification_attempts: int = Field(3, ge=1, le=10)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)


class SocialTaskCreate(BaseModelConfig):
    """Schema for creating social task"""
    platform: str = Field(..., max_length=50)
    platform_id: str = Field(..., max_length=100)
    platform_url: str = Field(..., pattern=r'^https?://.+')
    verify_key: str = Field(..., max_length=100)
    max_verification_attempts: int = Field(3, ge=1, le=10)
    is_active: bool = True

    model_config = ConfigDict(from_attributes=True)

    @field_validator('platform')
    @classmethod
    def validate_platform(cls, v: str) -> str:
        allowed_platforms = ['youtube', 'telegram', 'instagram', 'twitter', 'discord']
        if v.lower() not in allowed_platforms:
            raise ValueError(f'Platform must be one of: {", ".join(allowed_platforms)}')
        return v.lower()

    @field_validator('max_verification_attempts')
    @classmethod
    def validate_max_attempts(cls, v: int) -> int:
        if v < 1 or v > 10:
            raise ValueError("Max verification attempts must be between 1 and 10")
        return v

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: str) -> str:
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Platform URL must start with http:// or https://')
        return v


class SocialTaskOut(SocialTaskBase):
    """Social task output schema"""
    id: int
    task_id: int
    last_verification_time: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)


class SocialTaskUpdate(BaseModelConfig):
    """Schema for updating social task"""
    platform_id: Optional[str] = Field(None, max_length=100)
    platform_url: Optional[str] = Field(None, pattern=r'^https?://.+')
    is_active: Optional[bool] = None
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: Optional[str]) -> Optional[str]:
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError('Platform URL must start with http:// or https://')
        return v


# Task Verification schemas
class TaskVerificationBase(BaseModelConfig):
    """Base task verification schema"""
    status: TaskStatus
    verification_data: Optional[Dict] = None
    verification_method: str  # "api", "code", "manual", "auto"
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    next_verification: Optional[datetime] = None

    @field_validator('verification_data', mode='before')
    def validate_verification_data(cls, v):
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except:
                return None
        return v


class TaskVerificationCreate(TaskVerificationBase):
    """Schema for creating task verification"""
    task_completion_id: int


class TaskVerificationOut(TaskVerificationBase):
    """Task verification output schema"""
    id: int
    task_completion_id: int
    verified_at: datetime
    verification_result: Optional[Dict] = None
    created_at: datetime

    @field_validator('verification_result', mode='before')
    def validate_verification_result(cls, v):
        if v is None:
            return None
        if isinstance(v, str):
            try:
                return json.loads(v)
            except:
                return None
        return v


class TaskVerificationResult(BaseModelConfig):
    """Task verification result schema"""
    success: bool
    status: TaskStatus
    message: Optional[str] = Field(None, max_length=500)
    error_code: Optional[str] = Field(None, max_length=50)
    remaining_attempts: Optional[int] = Field(None, ge=0, le=10)
    verification_time_left: Optional[int] = Field(None, ge=0, le=3600)  # Seconds until next verification
    next_verification_time: Optional[datetime] = None
    verification_data: Optional[Dict] = None
    claim_available_at: Optional[datetime] = None
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('verification_data')
    @classmethod
    def validate_verification_data(cls, v: Optional[Dict]) -> Optional[Dict]:
        if v is not None:
            if isinstance(v, str):
                try:
                    v = json.loads(v)
                except json.JSONDecodeError:
                    raise ValueError('Verification data must be valid JSON')
            if not isinstance(v, dict):
                raise ValueError('Verification data must be a dictionary')
            # Validate max size
            if len(json.dumps(v)) > 1000:
                raise ValueError('Verification data too large')
        return v


# Reward Revocation schemas
class RewardRevocationBase(BaseModelConfig):
    """Base reward revocation schema"""
    task_completion_id: int
    reason: str
    revoked_by_id: int
    revoked_at: datetime = Field(default_factory=datetime.utcnow)


class RewardRevocationCreate(RewardRevocationBase):
    """Schema for creating reward revocation"""
    model_config = ConfigDict(from_attributes=True)


class RewardRevocationOut(RewardRevocationBase):
    """Reward revocation output schema"""
    id: int
    task_completion_id: int
    revoked_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


# Verification Data schemas
class YouTubeVerificationData(BaseModelConfig):
    """YouTube verification data schema"""
    verification_code: str


class SocialFollowVerificationData(BaseModelConfig):
    """Social follow verification data schema"""
    platform: SocialPlatform
    platform_id: str
    platform_url: str 