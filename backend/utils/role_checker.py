from fastapi import HTTPException
from models import User, Role

def check_user_role(user: User, required_role: str) -> None:
    """
    Check if a user has the required role.
    Raises HTTPException if the user doesn't have the required role.
    
    Args:
        user: The user to check
        required_role: The role required ('admin', 'reseller', or 'user')
    """
    if not user:
        raise HTTPException(
            status_code=401,
            detail="Authentication required"
        )
    
    # Admin can access everything
    if user.role == Role.admin:
        return
        
    # Check specific roles
    if required_role == 'user' and user.role != Role.user:
        raise HTTPException(
            status_code=403,
            detail="User role required"
        )
    elif required_role == 'reseller' and user.role != Role.reseller:
        raise HTTPException(
            status_code=403,
            detail="Reseller role required"
        )
    elif required_role == 'admin' and user.role != Role.admin:
        raise HTTPException(
            status_code=403,
            detail="Admin role required"
        ) 