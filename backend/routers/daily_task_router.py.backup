from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, func
from datetime import datetime, timedelta, timezone
from database import get_async_db
from models import (
    User, Task, TaskCompletion, TaskStatus, TaskType,
    DailyTaskStreak, RewardType, Transaction, TransactionType,
    DailyCycle
)
from schemas import DailyTaskStreakOut, TaskCompletionOut, DailyCheckInResponse
from auth import get_current_user, get_current_admin
import json
import logging
from sqlalchemy.sql import select

# Configure logger
logger = logging.getLogger(__name__)

router = APIRouter(tags=["daily_tasks"])

@router.post("/check-in/{task_id}", response_model=DailyCheckInResponse)
async def daily_check_in(
    task_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Handle daily check-in and update streak, REQUIRES task to be started first."""
    # TODO: Implement rate limiting for check-in
    try:
        # Get simulated time from header if present, otherwise use current time (sync)
        simulated_time = request.headers.get("X-Simulated-Time")
        now = datetime.fromisoformat(simulated_time) if simulated_time else datetime.now(timezone.utc) # Use aware datetime
        
        logger.info(f"Daily check-in attempt for user {current_user.id}, task {task_id} at time {now}")
        
        # Get daily task (async)
        stmt_task = select(Task).where(
            Task.id == task_id,
            Task.type == TaskType.DAILY_CHECKIN,
            Task.is_active == True
        )
        result_task = await db.execute(stmt_task)
        daily_task = result_task.scalar_one_or_none()
        
        if not daily_task:
            logger.warning(f"Daily check-in failed: Task ID {task_id} not found or inactive.")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail={"message": "Daily check-in task not found or is inactive.", "code": "TASK_NOT_FOUND"}
            )

        # --- Check if Task is Started (PENDING) --- (async)
        stmt_completion = select(TaskCompletion).where(
            TaskCompletion.task_id == daily_task.id,
            TaskCompletion.user_id == current_user.id,
            TaskCompletion.status == TaskStatus.PENDING # Must be PENDING to check in
        )
        result_completion = await db.execute(stmt_completion)
        existing_completion = result_completion.scalar_one_or_none()

        if not existing_completion:
            logger.warning(f"Daily check-in blocked for user {current_user.id}, task {task_id}: Task not started (no PENDING completion found).")
            # Check if already completed today (async)
            stmt_already_done = select(TaskCompletion).where(
                TaskCompletion.task_id == daily_task.id,
                TaskCompletion.user_id == current_user.id,
                TaskCompletion.status == TaskStatus.COMPLETED,
                func.date(TaskCompletion.completed_at) == now.date() # Check if completed today (UTC)
            )
            result_already_done = await db.execute(stmt_already_done)
            already_completed = result_already_done.scalar_one_or_none()
            
            if already_completed:
                 raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={
                        "message": "Already checked in today.",
                        "code": "ALREADY_CHECKED_IN",
                        # V Use aware datetime V
                        "next_check_in": (datetime.combine(now.date() + timedelta(days=1), datetime.min.time(), tzinfo=timezone.utc)).isoformat(),
                    }
                 )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"message": "Daily task not started. Please start the task first.", "code": "TASK_NOT_STARTED"}
                )
        # --- End Task Started Check ---

        # Get or create streak record (async)
        stmt_streak = select(DailyTaskStreak).where(DailyTaskStreak.user_id == current_user.id)
        result_streak = await db.execute(stmt_streak)
        streak = result_streak.scalar_one_or_none()
        
        if not streak:
            logger.info(f"Creating new daily streak record for user {current_user.id}")
            streak = DailyTaskStreak(user_id=current_user.id, current_cycle_day=1, first_check_time=now)
            # V db.add is sync V
            db.add(streak) 
            # V await db.flush V
            await db.flush() # Ensure streak exists before potentially using it
        
        # Check if already checked in today (using UTC date) - Double check for safety (sync)
        today_utc = now.date()
        if streak.last_check_in and streak.last_check_in.date() == today_utc:
             logger.warning(f"Daily check-in blocked for user {current_user.id} (redundant check): Already checked in today.")
             raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "message": "Already checked in today.",
                    "code": "ALREADY_CHECKED_IN",
                    # V Use aware datetime V
                    "next_check_in": (datetime.combine(today_utc + timedelta(days=1), datetime.min.time(), tzinfo=timezone.utc)).isoformat()
                }
             )

        # Check if streak should be reset (sync)
        if streak.last_check_in:
            reset_hours = daily_task.reset_streak_after_hours if daily_task.reset_streak_after_hours else 48
            time_since_last = now - streak.last_check_in
            if time_since_last > timedelta(hours=reset_hours):
                logger.info(f"Resetting streak for user {current_user.id}. Time since last: {time_since_last} (>{reset_hours}h). Old streak: {streak.current_streak}")
                streak.current_streak = 0
                streak.current_cycle_day = 0 # Reset cycle day
                streak.last_streak_break = now
        
        # --- Perform Check-in Updates --- (object updates are sync)
        logger.info(f"Processing check-in for user {current_user.id}. Current streak: {streak.current_streak}, Cycle day: {streak.current_cycle_day}")
        
        streak.current_streak += 1
        streak.longest_streak = max(streak.longest_streak, streak.current_streak)
        streak.total_check_ins += 1
        streak.last_check_in = now
        
        cycle_length = daily_task.cycle_length or 7
        streak.current_cycle_day = (streak.current_cycle_day % cycle_length) + 1
        
        logger.info(f"Updated streak for user {current_user.id}: New streak: {streak.current_streak}, New cycle day: {streak.current_cycle_day}")
        
        # Get or create current cycle (async)
        stmt_cycle = select(DailyCycle).where(
            DailyCycle.task_id == task_id,
            DailyCycle.user_id == current_user.id,
            DailyCycle.is_completed == False
        ).order_by(DailyCycle.cycle_number.desc())
        result_cycle = await db.execute(stmt_cycle)
        current_cycle = result_cycle.scalar_one_or_none()
        
        if not current_cycle or (streak.current_cycle_day == 1 and streak.current_streak > 1): # Start new cycle if needed
            cycle_number = (streak.total_check_ins + cycle_length - 1) // cycle_length 
            logger.info(f"Starting new daily cycle {cycle_number} for user {current_user.id}, task {task_id}")
            current_cycle = DailyCycle(
                task_id=task_id, user_id=current_user.id, cycle_number=cycle_number,
                start_date=now, completed_days="[]", is_completed=False
            )
            # V db.add is sync V
            db.add(current_cycle)
            # V await db.flush V
            await db.flush() 
            
        # Get reward for current day (sync parsing)
        try:
            daily_rewards = json.loads(daily_task.daily_rewards or '[]')
            if not isinstance(daily_rewards, list) or not daily_rewards:
                daily_rewards = [10, 15, 20, 25, 30, 35, 50] # Default rewards
                if cycle_length != len(daily_rewards): cycle_length = len(daily_rewards)
            
            reward_index = streak.current_cycle_day - 1
            current_day_reward = daily_rewards[reward_index] if 0 <= reward_index < len(daily_rewards) else 0
        except Exception as e:
            logger.error(f"Error calculating daily reward for task {task_id}, user {current_user.id}: {e}. Defaulting to 0.")
            current_day_reward = 0
            
        logger.info(f"Calculated reward for user {current_user.id}, task {task_id}, cycle day {streak.current_cycle_day}: {current_day_reward}")
        
        # Add day to cycle's completed days (sync object update)
        current_cycle.add_completed_day(streak.current_cycle_day)
        current_cycle.total_reward += current_day_reward 
        
        # Check cycle completion (sync)
        is_cycle_complete = streak.current_cycle_day == cycle_length
        cycle_bonus_reward = daily_task.cycle_bonus_reward if is_cycle_complete else 0.0
        total_cycle_reward_amount = 0.0

        transaction = None # Initialize transaction variable
        if is_cycle_complete:
            logger.info(f"Daily cycle {current_cycle.cycle_number} completed for user {current_user.id}, task {task_id}. Bonus: {cycle_bonus_reward}")
            current_cycle.total_reward += cycle_bonus_reward 
            current_cycle.is_completed = True
            current_cycle.end_date = now
            
            total_cycle_reward_amount = current_cycle.total_reward
            if daily_task.reward_type == RewardType.WALLET_BONUS and total_cycle_reward_amount > 0:
                # V Update user balance (sync object update) V
                current_user.wallet_balance += total_cycle_reward_amount
                logger.info(f"Applied cycle reward {total_cycle_reward_amount} to user {current_user.id} wallet. New balance: {current_user.wallet_balance}")
                
                # V Create transaction object (sync) V
                transaction = Transaction(
                    user_id=current_user.id, type=TransactionType.task_reward, amount=total_cycle_reward_amount,
                    description=f"Daily check-in cycle {current_cycle.cycle_number} completed. (Days 1-{cycle_length} + {cycle_bonus_reward or 0} bonus)",
                    balance_after=current_user.wallet_balance, status="completed"
                )
                # V db.add is sync V
                db.add(transaction) 
            else:
                 logger.warning(f"Cycle {current_cycle.cycle_number} completed for user {current_user.id}, but reward type {daily_task.reward_type} or reward is zero. No transaction created.")

        # --- Update TaskCompletion Record (found earlier) --- (sync object update)
        verification_data = {
            "streak_day": streak.current_streak, "cycle_day": streak.current_cycle_day,
            "check_in_time": now.isoformat(), "daily_reward": current_day_reward,
            "cycle_completed_on_this_checkin": is_cycle_complete,
            "cycle_bonus_reward": cycle_bonus_reward if is_cycle_complete else None,
            "total_reward_for_completed_cycle": total_cycle_reward_amount if is_cycle_complete else None
        }
        
        logger.info(f"Updating TaskCompletion {existing_completion.id} to COMPLETED for user {current_user.id}, task {task_id}")
        existing_completion.status = TaskStatus.COMPLETED
        existing_completion.completed_at = now
        existing_completion.is_verified = True
        existing_completion.verification_data = json.dumps(verification_data)
        existing_completion.is_claimed = True # Daily check-ins are auto-claimed
        existing_completion.claimed_at = now
        existing_completion.streak_day = streak.current_streak
        existing_completion.cycle_day = streak.current_cycle_day
        existing_completion.reward_amount = current_day_reward # Reward for *this* check-in
        existing_completion.verification_method = "auto"
        existing_completion.cycle_id = current_cycle.id # Ensure cycle ID is linked
            
        # Commit all changes (async)
        # V await db.commit V
        await db.commit()
        logger.info(f"Successfully committed daily check-in for user {current_user.id}, task {task_id}")
        
        # Refresh objects needed for response (async)
        # V await db.refresh V
        await db.refresh(existing_completion)
        await db.refresh(streak)
        # Refresh user if balance was changed
        if is_cycle_complete and transaction:
            await db.refresh(current_user)
        
        # V Use aware datetime V
        next_check_in_response_time = datetime.combine(now.date() + timedelta(days=1), datetime.min.time(), tzinfo=timezone.utc)

        response = DailyCheckInResponse(
            streak=DailyTaskStreakOut.model_validate(streak), # Use Pydantic model for response consistency
            completion=TaskCompletionOut.model_validate(existing_completion), # Use Pydantic model
            reward=current_day_reward, 
            multiplier=1.0, # Or calculate actual multiplier if applicable
            next_check_in=next_check_in_response_time,
            cycle_completion=is_cycle_complete,
            cycle_reward=cycle_bonus_reward if is_cycle_complete else None
        )
        
        logger.info(f"Daily check-in response for user {current_user.id}: {response.model_dump_json(exclude_none=True)}")
        return response
        
    except HTTPException as http_exc:
        detail = http_exc.detail if isinstance(http_exc.detail, str) else http_exc.detail.get("message", str(http_exc.detail))
        code = http_exc.detail.get("code", "UNKNOWN") if isinstance(http_exc.detail, dict) else "UNKNOWN"
        logger.warning(f"HTTPException during daily check-in for user {current_user.id}: {http_exc.status_code} - Code: {code}, Detail: {detail}")
        # V await db.rollback V
        await db.rollback() 
        raise http_exc 
    except Exception as e:
        logger.error(f"Unexpected error during daily check-in for user {current_user.id}, task {task_id}: {str(e)}", exc_info=True)
        # V await db.rollback V
        await db.rollback() 
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": f"An unexpected error occurred during check-in.", "code": "INTERNAL_ERROR"}
        )

@router.get("/next-check-in/{task_id}")
async def get_next_check_in_time(
    task_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get time until next check-in is available"""
    # Get simulated time from header if present, otherwise use current time
    simulated_time = request.headers.get("X-Simulated-Time")
    if simulated_time:
        now = datetime.fromisoformat(simulated_time)
    else:
        now = datetime.now(timezone.utc)
    
    today_utc = now.date()
    
    # Get the daily task
    daily_task = db.query(Task).filter(
        Task.id == task_id,
        Task.type == TaskType.DAILY_CHECKIN,
        Task.is_active == True
    ).first()
    
    if not daily_task:
        raise HTTPException(status_code=404, detail="Daily task not found")
    
    streak = db.query(DailyTaskStreak).filter(
        DailyTaskStreak.user_id == current_user.id
    ).first()
    
    if not streak or not streak.last_check_in:
        return {
            "can_check_in": True,
            "next_check_in": None,
            "time_left": 0,
            "current_streak": 0,
            "current_cycle_day": 0,
            "base_time": "00:00 UTC"
        }
    
    last_check_date = streak.last_check_in.date()
    
    # Can check in if it's a new UTC day
    can_check_in = today_utc > last_check_date
    
    # Next check-in time is next UTC midnight
    next_check_time = datetime.combine(today_utc + timedelta(days=1), datetime.min.time(), tzinfo=timezone.utc)
    
    return {
        "can_check_in": can_check_in,
        "base_time": "00:00 UTC",
        "next_check_in": next_check_time.isoformat(),
        "time_left": int((next_check_time - now).total_seconds() / 60) if not can_check_in else 0,
        "current_streak": streak.current_streak,
        "current_cycle_day": streak.current_cycle_day,
        "last_check_in": streak.last_check_in.isoformat() if streak.last_check_in else None
    }

@router.get("/streak/{task_id}")
async def get_streak_info(
    task_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user's current streak information with daily rewards"""
    try:
        # Get the daily task using async query
        stmt_task = select(Task).where(
            Task.id == task_id,
            Task.type == TaskType.DAILY_CHECKIN,
            Task.is_active == True
        )
        result_task = await db.execute(stmt_task)
        daily_task = result_task.scalar_one_or_none()

        if not daily_task:
            logger.warning(f"get_streak_info: Daily task {task_id} not found or inactive.")
            raise HTTPException(status_code=404, detail={"message": "Daily task not found", "code": "TASK_NOT_FOUND"})

        # Get daily rewards from task; default if not present
        try:
            daily_rewards = json.loads(daily_task.daily_rewards or '[]')
            if not isinstance(daily_rewards, list) or not daily_rewards:
                 daily_rewards = [10, 15, 20, 25, 30, 35, 50] # Default
        except json.JSONDecodeError:
            logger.warning(f"Failed to decode daily_rewards for task {task_id}, using default.")
            daily_rewards = [10, 15, 20, 25, 30, 35, 50]

        # Get streak record using async query
        stmt_streak = select(DailyTaskStreak).where(
            DailyTaskStreak.user_id == current_user.id
        )
        result_streak = await db.execute(stmt_streak)
        streak = result_streak.scalar_one_or_none()

        if not streak:
            # Return a default streak object with daily_rewards
            logger.info(f"get_streak_info: No streak found for user {current_user.id}, returning default.")
            return {
                "id": 0,
                "user_id": current_user.id,
                "current_streak": 0,
                "longest_streak": 0,
                "total_check_ins": 0,
                "current_cycle_day": 1, # Typically starts at 1
                "last_check_in": None,
                "first_check_time": None,
                "last_streak_break": None,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "daily_rewards": daily_rewards
            }
        else:
            # Convert streak SQLAlchemy object to dict and attach daily_rewards
            # Use Pydantic model for cleaner conversion if available, otherwise manual dict
            streak_data = {
                "id": streak.id,
                "user_id": streak.user_id,
                "current_streak": streak.current_streak,
                "longest_streak": streak.longest_streak,
                "total_check_ins": streak.total_check_ins,
                "current_cycle_day": streak.current_cycle_day,
                "last_check_in": streak.last_check_in.isoformat() if streak.last_check_in else None,
                "first_check_time": streak.first_check_time.isoformat() if streak.first_check_time else None,
                "last_streak_break": streak.last_streak_break.isoformat() if streak.last_streak_break else None,
                "created_at": streak.created_at.isoformat() if streak.created_at else None
            }
            streak_data['daily_rewards'] = daily_rewards
            return streak_data

    except HTTPException as http_exc:
        # Re-raise HTTP exceptions directly
        raise http_exc
    except Exception as e:
        logger.error(f"Error in get_streak_info for user {current_user.id}, task {task_id}: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "Failed to retrieve streak information.", "code": "STREAK_FETCH_ERROR"}
        )

async def create_cycle_completion_reward(
    user_id: int,
    current_streak: int,
    db: AsyncSession
):
    """Create special reward for completing 7-day cycle"""
    try:
        # Find the 7-day cycle task
        cycle_task = db.query(Task).filter(
            Task.streak_requirement == 7,
            Task.is_active == True
        ).first()
        
        if cycle_task:
            # Calculate bonus multiplier based on total streak
            bonus_multiplier = min(1 + (current_streak * 0.05), 1.5)  # Max 1.5x bonus
            
            completion = TaskCompletion(
                task_id=cycle_task.id,
                user_id=user_id,
                status=TaskStatus.completed,
                current_progress=7,
                completed_at=datetime.now(timezone.utc),
                streak_day=current_streak
            )
            db.add(completion)
            
            # Auto-claim cycle reward
            if cycle_task.reward_type == RewardType.wallet_bonus:
                reward_amount = cycle_task.reward_value * bonus_multiplier
                user = db.query(User).filter(User.id == user_id).first()
                user.wallet_balance += reward_amount
                
                # Create transaction record
                transaction = Transaction(
                    user_id=user_id,
                    type="cycle_reward",
                    amount=reward_amount,
                    description=f"7-day cycle completion bonus (x{bonus_multiplier} multiplier)",
                    balance_after=user.wallet_balance,
                    status="completed"
                )
                db.add(transaction)
            
            completion.is_claimed = True
            completion.claimed_at = datetime.now(timezone.utc)
            
            db.commit()
            
    except Exception as e:
        db.rollback()
        raise e 