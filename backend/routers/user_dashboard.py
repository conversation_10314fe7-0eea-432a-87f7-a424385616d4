from fastapi import APIRouter, Depends, HTTPException, status, Form, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload, selectinload
from typing import List, Optional
import aiohttp
import logging
import asyncio
from datetime import datetime, timedelta
from pydantic import BaseModel, ConfigDict

from database import get_async_db
from models import User, Role, VPNPackage, VPNSubscription, Transaction, MarzbanPanel, TransactionType, UserCard
from schemas import (
    UserOut,
    VPNPackageOut, 
    VPNSubscriptionOut, 
    VPNSubscriptionCreate,
    MarzbanPanelOut,
    TransactionOut
)
from auth import get_current_active_user, get_current_user
from services.marzban_service import get_marzban_service
from utils.role_checker import check_user_role

router = APIRouter(
    prefix="/api/user",
    tags=["user"],
    dependencies=[Depends(get_current_user)]
)
logger = logging.getLogger(__name__)

class UserProfileUpdate(BaseModel):
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    telegram_photo_url: Optional[str] = None

class UserResponse(BaseModel):
    id: int
    username: str
    email: Optional[str]
    first_name: Optional[str]
    last_name: Optional[str]
    telegram_photo_url: Optional[str]
    role: str
    is_active: bool
    wallet_balance: float
    created_at: datetime
    referral_code: str
    discount_percent: float

    model_config = ConfigDict(from_attributes=True)

@router.get("/me", response_model=UserOut)
async def read_users_me(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Fetches the current logged-in user's details, including cards and passive income."""
    try:
        # Fetch the user with eager loading for cards and their catalog
        stmt = (
            select(User)
            .where(User.id == current_user.id)
            .options(
                selectinload(User.user_cards).selectinload(UserCard.card_catalog)
            )
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()

        if user is None:
            # This case should ideally not happen if get_current_active_user works correctly
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )

        # The UserOut schema will automatically handle calculating total_passive_hourly_income
        # based on the eager-loaded user_cards
        return user
    except Exception as e:
        logger.error(f"Error fetching user data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch user data"
        )

@router.get("/stats")
async def get_user_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user stats"""
    return {
        "message": "Not implemented yet"
    }

@router.post("/purchase/{package_id}")
async def purchase_vpn_package(
    package_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user)
):
    """Purchase a VPN package"""
    try:
        # Get package
        stmt = select(VPNPackage).where(VPNPackage.id == package_id)
        package_result = await db.execute(stmt)
        package = package_result.scalar_one_or_none()
        
        if not package:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Package with id {package_id} not found"
            )
        
        if not package.is_active:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="This package is not available for purchase"
            )
        
        # Check user has enough balance
        if current_user.wallet_balance < package.price:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Insufficient wallet balance"
            )
        
        # Apply user's discount if applicable
        final_price = package.price
        if current_user.discount_percent > 0:
            discount = (current_user.discount_percent / 100) * package.price
            final_price = package.price - discount
            final_price = round(final_price, 2)  # Round to 2 decimal places
        
        # Deduct from wallet
        current_user.wallet_balance -= final_price
        
        # Create transaction
        transaction = Transaction(
            user_id=current_user.id,
            type=TransactionType.subscription_purchase,
            amount=-final_price,  # Negative amount for purchase
            description=f"Purchase of {package.name} package",
            package_name=package.name,
            package_price=package.price,
            package_data_limit=package.data_limit,
            package_expire_days=package.expire_days,
            balance_after=current_user.wallet_balance,
            status="completed"
        )
        
        db.add(transaction)
        await db.commit()
        
        # Process referral commission if user was referred
        if current_user.referred_by:
            from services.referral_service import ReferralService
            referral_service = ReferralService(db)
            await referral_service.process_referral_commission(
                current_user.id, 
                final_price, 
                f"Purchase of {package.name} package"
            )
        
        return {
            "status": "success",
            "message": f"Successfully purchased {package.name} package",
            "transaction_id": transaction.id,
            "new_balance": current_user.wallet_balance
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put("/telegram-data/update", response_model=UserOut)
async def update_telegram_data(
    telegram_data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user)
):
    """Update user's Telegram data"""
    try:
        # Safety checks
        telegram_id = telegram_data.get("id")
        
        if not telegram_id:
            raise HTTPException(status_code=400, detail="Telegram ID is required")
        
        # Get fresh user data
        stmt = select(User).where(User.id == current_user.id)
        result = await db.execute(stmt)
        db_user = result.scalar_one_or_none()
        
        if not db_user:
            raise HTTPException(status_code=404, detail="User not found")
            
        # Check if the Telegram ID is already in use by another user
        stmt = select(User).filter(
            User.telegram_id == telegram_id,
            User.id != current_user.id
        )
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise HTTPException(
                status_code=400, 
                detail="This Telegram account is already linked to another user"
            )
            
        # Update fields
        db_user.telegram_id = telegram_id
        db_user.telegram_username = telegram_data.get("username")
        db_user.telegram_first_name = telegram_data.get("first_name")
        db_user.telegram_last_name = telegram_data.get("last_name")
        db_user.telegram_photo_url = telegram_data.get("photo_url")
        
        # Set first and last name from Telegram if they are empty
        if not db_user.first_name and telegram_data.get("first_name"):
            db_user.first_name = telegram_data.get("first_name")
            
        if not db_user.last_name and telegram_data.get("last_name"):
            db_user.last_name = telegram_data.get("last_name")
            
        await db.commit()
        await db.refresh(db_user)
        
        return db_user
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating Telegram data: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update Telegram data: {str(e)}"
        )

@router.get("/subscriptions", response_model=List[VPNSubscriptionOut])
async def get_user_subscriptions(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get all subscriptions for the current user"""
    try:
        check_user_role(current_user, "user")
        
        stmt = select(VPNSubscription).filter(
            VPNSubscription.user_id == current_user.id
        ).options(
            selectinload(VPNSubscription.package).selectinload(VPNPackage.allowed_panels),
            selectinload(VPNSubscription.user)
        ).order_by(VPNSubscription.created_at.desc())
        result = await db.execute(stmt)
        subscriptions = result.scalars().all()
        
        if not subscriptions:
            return []
            
        return subscriptions
    except Exception as e:
        logger.error(f"Error fetching subscriptions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch subscriptions"
        )

@router.post("/subscriptions", response_model=VPNSubscriptionOut)
async def create_subscription(
    request: VPNSubscriptionCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    check_user_role(current_user, "user")
    
    stmt_package = select(VPNPackage).filter(
        VPNPackage.id == request.package_id,
        VPNPackage.is_active == True
    )
    result_package = await db.execute(stmt_package)
    package = result_package.scalar_one_or_none()
    if not package:
        raise HTTPException(status_code=404, detail="Package not found")

    stmt_user = select(User).where(User.id == current_user.id)
    result_user = await db.execute(stmt_user)
    db_user = result_user.scalar_one()
    if db_user.wallet_balance < package.price:
        raise HTTPException(status_code=400, detail="Insufficient balance")

    stmt_panel = select(MarzbanPanel).filter(
        MarzbanPanel.id == request.panel_id,
        MarzbanPanel.is_active == True
    )
    result_panel = await db.execute(stmt_panel)
    panel = result_panel.scalar_one_or_none()
    if not panel:
        raise HTTPException(status_code=404, detail="Panel not found")

    marzban_username = f"{request.username}_{current_user.id}"

    service = get_marzban_service(db)
    try:
        marzban_user = await service.create_user(
            panel=panel,
            username=marzban_username,
            data_limit=package.data_limit,
            expire_days=package.expire_days
        )
    except Exception as e:
        logger.error(f"Error calling Marzban service in create_subscription: {e}")
        raise HTTPException(status_code=500, detail=f"VPN service interaction failed: {e}")

    subscription = VPNSubscription(
        user_id=current_user.id,
        package_id=package.id,
        marzban_username=marzban_username,
        subscription_url=marzban_user["subscription_url"],
        data_limit=package.data_limit,
        data_used=0,
        expires_at=datetime.utcnow() + timedelta(days=package.expire_days),
        is_active=True
    )

    db_user.wallet_balance -= package.price

    transaction = Transaction(
        user_id=current_user.id,
        type=TransactionType.subscription_purchase,
        amount=-package.price,
        description=f"Purchased {package.name} package",
        balance_after=db_user.wallet_balance,
        package_name=package.name,
        package_price=package.price,
        package_data_limit=package.data_limit,
        package_expire_days=package.expire_days,
    )

    db.add(subscription)
    db.add(db_user)
    db.add(transaction)
    await db.flush()

    await db.commit()
    await db.refresh(subscription)
    await db.refresh(db_user)

    return subscription

@router.get("/transactions", response_model=List[TransactionOut])
async def get_user_transactions(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user's transaction history"""
    try:
        check_user_role(current_user, "user")
        
        stmt = select(Transaction).filter(
            Transaction.user_id == current_user.id
        ).order_by(Transaction.created_at.desc())
        result = await db.execute(stmt)
        transactions = result.scalars().all()
        
        return transactions
        
    except Exception as e:
        logger.error(f"Error fetching transactions: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Failed to fetch transactions"
        )

@router.get("/subscriptions/{subscription_id}/usage")
async def get_subscription_usage(
    subscription_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    try:
        check_user_role(current_user, "user")
        
        stmt = select(VPNSubscription).filter(
            VPNSubscription.id == subscription_id,
            VPNSubscription.user_id == current_user.id
        )
        result = await db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")
            
        if not subscription.subscription_url:
            raise HTTPException(status_code=400, detail="No subscription URL found")

        parts = subscription.subscription_url.split('/sub/')
        if len(parts) != 2:
            raise HTTPException(status_code=400, detail="Invalid subscription URL format")
            
        base_url = parts[0]
        token = parts[1]
        info_url = f"{base_url}/sub/{token}/info"

        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(info_url, ssl=False, timeout=10) as response:
                    if response.status != 200:
                        logger.error(f"Failed to get usage data from {info_url}: {response.status} - {await response.text()}")
                        raise HTTPException(
                            status_code=status.HTTP_502_BAD_GATEWAY,
                            detail=f"Failed to get usage data from VPN provider (Status: {response.status})"
                        )

                    info_data = await response.json()
                
                bytes_used = info_data.get("used_traffic", 0)
                bytes_limit = info_data.get("data_limit", 0)
                lifetime_bytes_used = info_data.get("lifetime_used_traffic", 0)
                online_at_str = info_data.get("online_at")
                api_status = info_data.get("status", "inactive")
                
                data_used_gb = round(bytes_used / (1024 ** 3), 2)
                data_limit_gb = round(bytes_limit / (1024 ** 3), 2) if bytes_limit else 0
                lifetime_used_gb = round(lifetime_bytes_used / (1024 ** 3), 2)
                
                last_online = None
                if online_at_str:
                    try:
                        if online_at_str.endswith('Z'):
                            last_online = datetime.fromisoformat(online_at_str.replace('Z', '+00:00'))
                        else:
                            last_online = datetime.fromisoformat(online_at_str)
                    except ValueError:
                        logger.warning(f"Could not parse online_at timestamp: {online_at_str}")
                        last_online = None

                now_ts = datetime.utcnow().timestamp()
                expire_time = info_data.get("expire", 0)
                days_remaining = max(0, int((expire_time - now_ts) / (24 * 60 * 60))) if expire_time else 0
                
                stmt_update = select(VPNSubscription).where(VPNSubscription.id == subscription_id)
                result_update = await db.execute(stmt_update)
                db_subscription = result_update.scalar_one()

                db_subscription.data_used = bytes_used
                db.add(db_subscription)
                await db.commit()
                await db.refresh(db_subscription)

                return {
                    "status": api_status,
                    "data_used": data_used_gb,
                    "data_limit": data_limit_gb,
                    "lifetime_used": lifetime_used_gb,
                    "data_used_bytes": bytes_used,
                    "data_limit_bytes": bytes_limit,
                    "lifetime_used_bytes": lifetime_bytes_used,
                    "expire_date": expire_time,
                    "days_remaining": days_remaining,
                    "last_online": last_online.isoformat() if last_online else None,
                    "is_active": api_status == "active",
                    "is_expired": expire_time < now_ts if expire_time else False,
                    "is_limited": bytes_used >= bytes_limit if bytes_limit else False
                }

            except aiohttp.ClientError as network_error:
                logger.error(f"Network error fetching usage data from {info_url}: {network_error}")
                raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail="Could not connect to VPN provider for usage data.")
            except asyncio.TimeoutError:
                 logger.error(f"Timeout error fetching usage data from {info_url}")
                 raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail="Request to VPN provider timed out.")
            except Exception as inner_ex:
                 logger.exception(f"Unexpected error during VPN usage data fetch/parse from {info_url}: {inner_ex}")
                 raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error processing usage data from VPN provider.")

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Unexpected error getting subscription usage for ID {subscription_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while fetching subscription usage."
        )

@router.get("/dashboard/stats")
async def get_dashboard_stats(
    current_user_dep: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user dashboard statistics including card profit."""
    try:
        check_user_role(current_user_dep, "user")
        
        stmt = (
            select(User)
            .where(User.id == current_user_dep.id)
            .options(
                selectinload(User.vpn_subscriptions),
                selectinload(User.user_cards).selectinload(UserCard.card_catalog)
            )
        )
        result = await db.execute(stmt)
        current_user = result.scalar_one_or_none()

        if not current_user:
             # This shouldn't happen if the dependency worked, but good practice
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        active_subs_count = sum(1 for sub in current_user.vpn_subscriptions if sub.is_active)
        total_data_used = sum(sub.data_used for sub in current_user.vpn_subscriptions)
        total_data_limit = sum(sub.data_limit for sub in current_user.vpn_subscriptions if sub.data_limit is not None)

        total_accumulated_card_profit = 0.0
        now = datetime.utcnow()
        for card in current_user.user_cards:
            if not card.card_catalog:
                logger.warning(f"UserCard ID {card.id} missing card_catalog in stats calculation.")
                continue
            try:
                current_hourly_profit = card.current_hourly_profit if hasattr(card, 'current_hourly_profit') else 0.0
                hours_since_claim = card.hours_since_last_claim if hasattr(card, 'hours_since_last_claim') else 0.0

                claimable_hours = min(hours_since_claim, 24.0)
                accumulated_profit_for_card = round(current_hourly_profit * claimable_hours, 4)
                if accumulated_profit_for_card > 0:
                    total_accumulated_card_profit += accumulated_profit_for_card
            except Exception as e:
                logger.error(f"Error calculating profit for UserCard ID {card.id} in stats: {e}", exc_info=True)
        
        total_accumulated_card_profit = round(total_accumulated_card_profit, 4)
        
        return {
            "active_subscriptions": active_subs_count,
            "total_data_used": total_data_used,
            "total_data_limit": total_data_limit,
            "wallet_balance": current_user.wallet_balance,
            "total_accumulated_card_profit": total_accumulated_card_profit,
            "total_passive_hourly_income": current_user.total_passive_hourly_income,
            "last_passive_claim_at": current_user.last_passive_claim_at
        }
        
    except Exception as e:
        logger.exception(f"Error fetching dashboard stats for user {current_user_dep.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch dashboard statistics"
        )

@router.post("/subscriptions/{user_id}/new", response_model=VPNSubscriptionOut)
async def new_user_subscription(
    user_id: int,
    data: VPNSubscriptionCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new subscription for a user with secure validation and transaction handling"""
    check_user_role(current_user, "user")
    logger.warning(f"User {current_user.id} attempting to create subscription for user {user_id}.")

    async with db.begin():
        stmt_user = select(User).where(User.id == user_id)
        result_user = await db.execute(stmt_user)
        user = result_user.scalar_one_or_none()
        if not user:
            raise HTTPException(status_code=404, detail="Target user not found")

        stmt_package = select(VPNPackage).where(
            VPNPackage.id == data.package_id,
            VPNPackage.is_active == True
        ).options(selectinload(VPNPackage.allowed_panels))
        result_package = await db.execute(stmt_package)
        package = result_package.scalar_one_or_none()
        if not package:
            raise HTTPException(status_code=404, detail="Package not found or inactive")

        panel_is_allowed = any(p.id == data.panel_id for p in package.allowed_panels if p.is_active)
        if not panel_is_allowed:
             raise HTTPException(status_code=404, detail="Panel not found, inactive, or not allowed for this package")

        stmt_panel = select(MarzbanPanel).where(MarzbanPanel.id == data.panel_id)
        result_panel = await db.execute(stmt_panel)
        panel = result_panel.scalar_one()

        final_price = package.price * (1 - user.discount_percent / 100)

        if user.wallet_balance < final_price:
            raise HTTPException(status_code=400, detail="Insufficient wallet balance for target user")

        marzban_username = f"{data.custom_name}_{user.id}"

        service = get_marzban_service(db)
        try:
            enabled_inbounds = await service.get_enabled_inbounds(panel)
            logger.info(f"Enabled inbounds for panel {panel.name}: {enabled_inbounds}")

            subscription_data = await service.create_user(
                panel=panel,
                username=marzban_username,
                proxies=enabled_inbounds,
                data_limit=package.data_limit,
                expire_days=package.expire_days
            )
        except Exception as e:
            error_detail = str(e)
            logger.error(f"Marzban service error during subscription creation for user {user_id}: {error_detail}")
            if "User already exists" in error_detail:
                raise HTTPException(status_code=400, detail="This VPN username is already taken. Please choose a different name.")
            elif "Connection refused" in error_detail:
                raise HTTPException(status_code=503, detail="VPN server is temporarily unavailable. Please try again later.")
            elif "Invalid inbound" in error_detail:
                raise HTTPException(status_code=400, detail="Invalid server configuration. Please select a different server.")
            elif "data limit" in error_detail.lower():
                raise HTTPException(status_code=400, detail="Invalid data limit configuration. Please contact support.")
            elif "expire" in error_detail.lower():
                raise HTTPException(status_code=400, detail="Invalid expiration configuration. Please contact support.")
            elif "authentication" in error_detail.lower():
                raise HTTPException(status_code=401, detail="Server authentication failed. Please contact support.")
            else:
                logger.exception(f"Unexpected Marzban error for user {user_id}: {error_detail}")
                raise HTTPException(status_code=500, detail=f"Failed to create VPN service. Error: {error_detail}")

        subscription = VPNSubscription(
            user_id=user.id,
            package_id=package.id,
            marzban_username=marzban_username,
            subscription_url=subscription_data["subscription_url"],
            data_limit=package.data_limit,
            expires_at=datetime.utcnow() + timedelta(days=package.expire_days),
            is_active=True
        )

        transaction = Transaction(
            user_id=user.id,
            type=TransactionType.subscription_purchase,
            amount=-final_price,
            description=f"VPN subscription purchase: {package.name}",
            created_at=datetime.utcnow(),
            balance_after=user.wallet_balance - final_price,
            package_name=package.name,
            package_price=package.price,
            package_data_limit=package.data_limit,
            package_expire_days=package.expire_days,
            reseller_id=current_user.id if current_user.role == Role.reseller else None,
            status="completed"
        )

        user.wallet_balance -= final_price

        db.add(user)
        db.add(subscription)
        db.add(transaction)

        await db.flush()

        await db.refresh(user)
        await db.refresh(subscription)

    return subscription

@router.get("/available-packages", response_model=List[VPNPackageOut])
async def get_available_packages(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get list of available packages for new service"""
    try:
        check_user_role(current_user, "user")
        
        stmt = select(VPNPackage).filter(
            VPNPackage.is_active == True
        ).options(
            selectinload(VPNPackage.allowed_panels)
        )
        result = await db.execute(stmt)
        packages = result.scalars().unique().all()
        
        package_list = []
        for package in packages:
            active_panels = [
                 {
                     "id": panel.id,
                     "name": panel.name,
                     "api_url": panel.api_url,
                     "is_active": panel.is_active,
                     "created_at": panel.created_at
                 }
                 for panel in package.allowed_panels
                 if panel.is_active
             ]
            if not active_panels:
                 continue

            package_dict = {
                "id": package.id,
                "name": package.name,
                "data_limit": package.data_limit,
                "expire_days": package.expire_days,
                "price": package.price,
                "description": package.description,
                "is_active": package.is_active,
                "created_at": package.created_at,
                "allowed_panels": active_panels
            }
            package_list.append(package_dict)
            
        return package_list
        
    except Exception as e:
        logger.exception(f"Error fetching available packages: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch available packages"
        )

@router.get("/available-panels", response_model=List[MarzbanPanelOut])
async def get_available_panels(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get list of available panels for new service"""
    try:
        check_user_role(current_user, "user")
        
        stmt = select(MarzbanPanel).filter(
            MarzbanPanel.is_active == True
        )
        result = await db.execute(stmt)
        panels = result.scalars().all()
        
        panel_list = []
        for panel in panels:
            panel_dict = {
                "id": panel.id,
                "name": panel.name,
                "api_url": panel.api_url,
                "is_active": panel.is_active,
                "created_at": panel.created_at
            }
            panel_list.append(panel_dict)
            
        return panel_list
        
    except Exception as e:
        logger.exception(f"Error fetching available panels: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch available panels"
        )

@router.post("/me/avatar/")
async def update_user_avatar(
    avatar_url: str = Form(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    try:
        stmt = select(User).where(User.id == current_user.id)
        result = await db.execute(stmt)
        db_user = result.scalar_one_or_none()

        if not db_user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        db_user.telegram_photo_url = avatar_url
        db.add(db_user)
        await db.commit()
        return {"telegram_photo_url": avatar_url}
    except Exception as e:
        logger.exception(f"Error updating avatar for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update avatar"
        )

@router.put("/profile/update", response_model=UserResponse)
async def update_user_profile(
    profile_data: UserProfileUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Update user profile information"""
    try:
        stmt = select(User).where(User.id == current_user.id)
        result = await db.execute(stmt)
        db_user = result.scalar_one_or_none()

        if not db_user:
             raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        if profile_data.telegram_photo_url is not None:
            valid_avatars = [
                "Lovely Angel", "Mysticssl Raven", "Cipherweaver Wolf",
                "Datacenter Gauridan", "Encrypted Spider", "Datacracker Beast",
                "Databrigner Seahorse", "Datashade Rabbit", "Debugger Penguin",
                "Cipherlord Cat", "Uptime Penguin", "Bugaddict Panda",
                "Cryptshade Beast", "Cipherfire Fox", "Devleader Penguin",
                "Shadow Decoder", "Dataforge Seahorse", "Proxyweaver Rabbit",
                "Netshade Angel", "Antiddos Gaurdian", "Cryptkeeper Cat",
                "Cryptweaver Rabbit"
            ]
            if profile_data.telegram_photo_url not in valid_avatars:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid avatar selection"
                )
            db_user.telegram_photo_url = profile_data.telegram_photo_url

        update_made = False
        if profile_data.email is not None and db_user.email != profile_data.email:
            db_user.email = profile_data.email
            update_made = True
        if profile_data.first_name is not None and db_user.first_name != profile_data.first_name:
            db_user.first_name = profile_data.first_name
            update_made = True
        if profile_data.last_name is not None and db_user.last_name != profile_data.last_name:
            db_user.last_name = profile_data.last_name
            update_made = True

        if update_made or profile_data.telegram_photo_url is not None:
            db.add(db_user)
            await db.commit()
            await db.refresh(db_user)

        return db_user

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error updating profile for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile"
        )