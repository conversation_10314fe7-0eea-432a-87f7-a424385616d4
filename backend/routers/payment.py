from fastapi import API<PERSON><PERSON><PERSON>, Depends, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Header
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List, Optional
import httpx
import hmac
import hashlib
import json
import logging
from datetime import datetime
import ipaddress

from database import get_async_db
from models import User, Transaction, TransactionType
from schemas import TransactionOut
from auth import get_current_active_user
from utils.role_checker import check_user_role

router = APIRouter(tags=["payment"])
logger = logging.getLogger(__name__)

# Cryptomus configuration
CRYPTOMUS_MERCHANT_ID = "YOUR_MERCHANT_ID"  # Replace with your Merchant ID
CRYPTOMUS_PAYMENT_KEY = "YOUR_PAYMENT_KEY"  # Replace with your Payment Key

# Cryptomus IP ranges (update these with actual Cryptomus IP ranges)
CRYPTOMUS_IPS = [
    "************/24",  # Example IP range
    "************/24",  # Example IP range
]

def is_cryptomus_ip(ip: str) -> bool:
    """Check if IP is from Cryptomus"""
    try:
        ip_addr = ipaddress.ip_address(ip)
        return any(ip_addr in ipaddress.ip_network(net) for net in CRYPTOMUS_IPS)
    except ValueError:
        return False

def check_cryptomus_webhook(request: Request, merchant: str, sign: str) -> bool:
    """Verify if request is from Cryptomus"""
    # Verify merchant ID
    if merchant != CRYPTOMUS_MERCHANT_ID:
        return False
        
    # Verify IP
    client_ip = request.client.host
    if not is_cryptomus_ip(client_ip):
        return False
        
    return True

def verify_cryptomus_signature(payload: dict, sign: str) -> bool:
    """Verify Cryptomus webhook signature"""
    if not sign:
        return False
        
    sorted_payload = dict(sorted(payload.items()))
    payload_string = json.dumps(sorted_payload, separators=(',', ':'))
    expected_sign = hmac.new(
        CRYPTOMUS_PAYMENT_KEY.encode(),
        payload_string.encode(),
        hashlib.sha512
    ).hexdigest()
    
    return hmac.compare_digest(sign, expected_sign)

def generate_sign(payload: dict, api_key: str) -> str:
    sorted_payload = dict(sorted(payload.items()))
    payload_string = json.dumps(sorted_payload, separators=(',', ':'))
    # Note: Ensure CRYPTOMUS_PAYMENT_KEY is correctly defined elsewhere
    return hmac.new(api_key.encode(), payload_string.encode(), hashlib.sha512).hexdigest()

async def create_cryptomus_payment(amount: float, user_id: int, order_id: str) -> dict:
    """Create payment in Cryptomus"""
    payload = {
        "merchant_id": CRYPTOMUS_MERCHANT_ID,
        "amount": str(amount),
        "currency": "USD",
        "order_id": order_id,
        "url_callback": "https://your-domain.com/api/payment/webhook",  # Replace with your domain
        "url_return": "https://your-domain.com/dashboard",  # Replace with your domain
        "is_payment_multiple": False,
        "lifetime": 3600,  # Payment window: 1 hour
        "to_currency": "USD"
    }

    headers = {
        "merchant": CRYPTOMUS_MERCHANT_ID,
        "sign": generate_sign(payload, CRYPTOMUS_PAYMENT_KEY)
    }

    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                "https://api.cryptomus.com/v1/payment",
                json=payload,
                headers=headers,
                timeout=10.0 # Add timeout
            )
            response.raise_for_status() # Raise exception for bad status codes
            return response.json()
        except httpx.RequestError as exc:
            logger.error(f"HTTP request failed to Cryptomus: {exc}")
            return {"error": True, "message": "Failed to communicate with payment provider"}
        except httpx.HTTPStatusError as exc:
            logger.error(f"Cryptomus returned error status {exc.response.status_code}: {exc.response.text}")
            return {"error": True, "message": f"Payment provider error: {exc.response.status_code}"}
        except json.JSONDecodeError:
            logger.error("Failed to decode JSON response from Cryptomus")
            return {"error": True, "message": "Invalid response from payment provider"}

def verify_cryptomus_request(request: Request, merchant: str, sign: str) -> bool:
    # Verify IP
    client_ip = request.client.host
    if not is_cryptomus_ip(client_ip):
        logger.warning(f"Webhook attempt from non-Cryptomus IP: {client_ip}")
        return False
        
    # Verify merchant ID
    if merchant != CRYPTOMUS_MERCHANT_ID:
        logger.warning(f"Webhook attempt with invalid merchant ID: {merchant}")
        return False
        
    # Basic check for presence of signature (verification happens later)
    if not sign:
        logger.warning("Webhook attempt with missing signature")
        return False
        
    return True

@router.post("/webhook", include_in_schema=False)
async def payment_webhook(
    request: Request,
    merchant: str = Header(None),
    sign: str = Header(None),
    db: AsyncSession = Depends(get_async_db)
):
    """Handle Cryptomus webhook notifications - this endpoint is public but secured with IP and signature checks"""
    try:
        # Verify Cryptomus request (IP, Merchant ID)
        if not verify_cryptomus_request(request, merchant, sign):
            response = JSONResponse(content={"detail": "Forbidden"}, status_code=403)
            _add_security_headers(response)
            return response

        # Get and parse payload
        try:
            payload = await request.json()
            if not isinstance(payload, dict):
                raise ValueError("Payload is not a dictionary")
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Webhook payload error: {e}")
            response = JSONResponse(content={"detail": "Invalid payload"}, status_code=400)
            _add_security_headers(response)
            return response

        # Verify signature
        if not verify_cryptomus_signature(payload, sign):
            logger.warning("Webhook signature verification failed")
            response = JSONResponse(content={"detail": "Invalid signature"}, status_code=400)
            _add_security_headers(response)
            return response

        # Process payment
        payment_id = payload.get("uuid") # Cryptomus uses 'uuid' for payment ID in callbacks
        status = payload.get("status")
        order_id = payload.get("order_id")
        
        if not all([payment_id, status, order_id]):
            logger.warning(f"Webhook missing required fields: payment_id={payment_id}, status={status}, order_id={order_id}")
            response = JSONResponse(content={"detail": "Missing required fields"}, status_code=400)
            _add_security_headers(response)
            return response

        # Find transaction using order_id (assuming order_id is unique and stored)
        stmt = select(Transaction).where(Transaction.order_id == order_id)
        result = await db.execute(stmt)
        transaction = result.scalar_one_or_none()
        
        if not transaction:
            logger.warning(f"Webhook received for unknown order_id: {order_id}")
            # Still return success to Cryptomus to avoid retries for unknown orders
            response = JSONResponse(content={"status": "success - order not found"})
            _add_security_headers(response)
            return response

        # Update transaction status
        if status == "paid" and transaction.status != "completed":
            logger.info(f"Processing successful payment for order_id: {order_id}")
            user = await db.get(User, transaction.user_id)
            if not user:
                response = JSONResponse(status_code=404, detail="User not found")
                _add_security_headers(response)
                return response

            transaction.status = "completed"
            user.wallet_balance += transaction.amount
            
            # Handle referral commission
            if user.referred_by and user.referred_by_username:
                referrer = db.query(User).filter_by(id=user.referred_by).first()
                if referrer:
                    commission_amount = transaction.amount * (referrer.discount_percent / 100)
                    if commission_amount > 0:
                        commission_transaction = Transaction(
                            user_id=referrer.id,
                            type=TransactionType.referral_commission,
                            amount=commission_amount,
                            status="completed",
                            description=f"Referral commission from {user.username}'s wallet top-up",
                            balance_after=referrer.wallet_balance + commission_amount
                        )
                        referrer.wallet_balance += commission_amount
                        db.add(commission_transaction)
            
            db.commit()
            
        elif status in ["failed", "expired"] and transaction.status != "failed":
            transaction.status = "failed"
            db.commit()

        response = JSONResponse(content={"status": "success"})
        _add_security_headers(response)
        return response
        
    except Exception as e:
        logger.error(f"Webhook error: {str(e)}")
        response = JSONResponse(content={"status": "received"})
        _add_security_headers(response)
        return response

def _add_security_headers(response):
    """Add security headers to response"""
    response.headers["Content-Security-Policy"] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data: https:; "
        "connect-src 'self'; "
        "frame-ancestors 'none'; "
        "form-action 'self'; "
        "base-uri 'self'"
    )
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["X-Content-Type-Options"] = "nosniff"
    # response.headers["X-Frame-Options"] = "SAMEORIGIN"  # Removed for Telegram Mini App
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Permissions-Policy"] = (
        "accelerometer=(), camera=(), geolocation=(), "
        "gyroscope=(), magnetometer=(), microphone=(), "
        "payment=(), usb=()"
    )
    response.headers["Cache-Control"] = "no-store, max-age=0"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"

@router.post("/create")
async def create_payment(
    amount: float,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user)
):
    """Create a new payment request - this endpoint requires authentication"""
    # TODO: Implement rate limiting for payment creation

    # Input validation
    if amount < 1:
        raise HTTPException(status_code=400, detail="Minimum amount is 1 USD")

    # Create unique order ID
    order_id = f"order_{current_user.id}_{int(datetime.now().timestamp())}"

    # Create payment in Cryptomus
    payment_data = await create_cryptomus_payment(amount, current_user.id, order_id)

    # Check for error from Cryptomus helper
    if payment_data.get("error"):
        raise HTTPException(status_code=503, detail=payment_data.get("message", "Payment provider unavailable"))

    # Extract payment details (adjust keys based on actual Cryptomus response)
    payment_url = payment_data.get("result", {}).get("url")
    payment_uuid = payment_data.get("result", {}).get("uuid")

    if not payment_url or not payment_uuid:
        logger.error(f"Missing URL or UUID in Cryptomus response for order {order_id}: {payment_data}")
        raise HTTPException(status_code=500, detail="Failed to get payment details from provider")

    # Create transaction record (sync object creation)
    # Calculate expected balance AFTER potential top-up, not before
    new_balance_after_potential_topup = current_user.wallet_balance + amount
    transaction = Transaction(
        user_id=current_user.id,
        type=TransactionType.wallet_topup,
        amount=amount,
        status="pending",
        description=f"Wallet top-up via Cryptomus (Order ID: {order_id})",
        order_id=order_id,
        balance_after=new_balance_after_potential_topup
    )
    db.add(transaction)
    db.commit()

    # Return details needed by frontend
    return {
        "payment_url": payment_url,
        "payment_id": payment_uuid,
        "order_id": order_id
    }

@router.get("/transactions", response_model=List[TransactionOut])
async def get_transactions(
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get user's transaction history - this endpoint requires authentication"""
    query = db.query(Transaction).filter_by(user_id=current_user.id)
    
    if status:
        query = query.filter_by(status=status)
    
    transactions = query.order_by(Transaction.created_at.desc()).all()
    return transactions 