from fastapi import APIRouter, Depends, HTTPException, status, Request, Response
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from database import get_async_db
from services.bot_detection_service import BotDetectionService
from config.redis_config import RedisClient
from typing import Dict, Any, Optional, List
from pydantic import BaseModel
from fastapi.responses import HTMLResponse, JSONResponse
import os
import json
from datetime import datetime, timedelta
import logging

router = APIRouter(tags=["security"], prefix="/api/security")

class CaptchaVerification(BaseModel):
    captcha_id: str
    captcha_solution: str

@router.post("/verify-captcha", response_model=Dict[str, Any])
async def verify_captcha(
    verification: CaptchaVerification,
    request: Request,
    db: AsyncSession = Depends(get_async_db)
):
    """Verify CAPTCHA solution"""
    try:
        # Get Redis client
        redis_client = request.app.state.redis
        
        # Create bot detection service
        bot_detection_service = BotDetectionService(db, redis_client)
        
        # Verify CAPTCHA
        is_valid = await bot_detection_service.verify_captcha(
            verification.captcha_id,
            verification.captcha_solution
        )
        
        if not is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid CAPTCHA solution"
            )
            
        return {
            "success": True,
            "message": "CAPTCHA verification successful"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"CAPTCHA verification failed: {str(e)}"
        )

@router.get("/dashboard", response_class=HTMLResponse)
async def security_dashboard(
    request: Request,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Render the security dashboard HTML
    
    This endpoint serves the security dashboard HTML page that displays
    security metrics and visualizations.
    """
    try:
        # Check if dashboard HTML exists
        dashboard_path = os.path.join("reports", "security_dashboard.html")
        
        if not os.path.exists(dashboard_path):
            # Return a simple message if dashboard doesn't exist
            return HTMLResponse(
                content="""
                <html>
                    <head>
                        <title>Security Dashboard</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
                            .container { max-width: 800px; margin: 0 auto; }
                            .alert { background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; }
                            h1 { color: #333; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>Security Dashboard</h1>
                            <div class="alert">
                                <p>Dashboard has not been generated yet. Please run the security dashboard generator script.</p>
                                <p>Command: <code>python scripts/security_dashboard.py</code></p>
                            </div>
                        </div>
                    </body>
                </html>
                """,
                status_code=200
            )
            
        # Read the dashboard HTML file
        with open(dashboard_path, "r") as f:
            dashboard_html = f.read()
            
        return HTMLResponse(content=dashboard_html)
        
    except Exception as e:
        logging.error(f"Error serving security dashboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error serving security dashboard"
        )

@router.get("/stats", response_model=Dict[str, Any])
async def security_stats(
    request: Request,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get security statistics
    
    Returns statistics about bot detection, CAPTCHA usage, and other security metrics.
    """
    try:
        # Get Redis client
        redis_client = request.app.state.redis
        
        # Create bot detection service
        bot_detection_service = BotDetectionService(db, redis_client)
        
        # Get bot detection stats
        bot_stats = await bot_detection_service.get_detection_stats()
        
        # Get rate limit stats from Redis
        rate_limit_keys = await redis_client.keys("rate_limit:*")
        rate_limit_count = len(rate_limit_keys)
        
        # Get blocked IPs count
        blocked_keys = await redis_client.keys("rate_limit:blocked:*")
        blocked_count = len(blocked_keys)
        
        return {
            "bot_detection": bot_stats,
            "rate_limiting": {
                "active_limits": rate_limit_count,
                "blocked_ips": blocked_count,
                "timestamp": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logging.error(f"Error getting security stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving security statistics"
        )

@router.post("/bot-patterns", response_model=Dict[str, Any])
async def add_bot_pattern(
    pattern: Dict[str, str],
    request: Request,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Add a custom bot detection pattern
    
    Adds a new regex pattern to the bot detection system.
    """
    try:
        if "pattern" not in pattern:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Pattern is required"
            )
            
        # Get Redis client
        redis_client = request.app.state.redis
        
        # Create bot detection service
        bot_detection_service = BotDetectionService(db, redis_client)
        
        # Add pattern
        success = await bot_detection_service.add_custom_bot_pattern(pattern["pattern"])
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid pattern format"
            )
            
        return {
            "success": True,
            "message": "Bot pattern added successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error adding bot pattern: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error adding bot pattern"
        ) 