from fastapi import APIRouter, Depends, HTTPException, status, Request, Response, Body
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text
from typing import List, Dict, Optional, Any
from database import get_db, get_async_db
from models import (
    User, Task, TaskStatus, TaskCompletion, Transaction, 
    TaskType, TransactionType, RewardType, TaskVerification
)
from schemas import (
    TaskCreate, TaskOut, TaskCompletionOut,
    TaskStatusOut, TaskRewardOut, TaskVerificationResult,
    AdminTaskOut, TaskStatistics
)
from auth import get_current_user, get_current_admin
from services.task_service import TaskService, TaskError
from services.verification_service import VerificationService, VerificationError
from services.rate_limit_service import RateLimitService
from config.redis_config import RedisClient
import logging
import traceback
from datetime import datetime, timedelta
import json
import os
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import timezone
from sqlalchemy import func
from sqlalchemy.orm import selectinload

# Configure logger
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)

router = APIRouter(tags=["tasks"])

# User Routes
@router.get("/available", response_model=List[TaskOut])
async def get_available_tasks(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get list of available tasks for the user with proper status handling"""
    try:
        # Get tasks and their completions in a single optimized query
        stmt = (
            select(Task, TaskCompletion) 
            .outerjoin(
                TaskCompletion,
                and_(
                    TaskCompletion.task_id == Task.id,
                    TaskCompletion.user_id == current_user.id,
                    TaskCompletion.status.notin_([TaskStatus.EXPIRED, TaskStatus.REVOKED])
                )
            )
            .where(Task.is_active == True)
            .order_by(Task.created_at.desc())
        )
        result = await db.execute(stmt) 
        tasks_with_completions = result.all()

        task_list = []
        now = datetime.now(timezone.utc)

        for task, completion in tasks_with_completions:
            task_dict = task.__dict__.copy()
            task_dict.pop('_sa_instance_state', None)
            task_dict.pop('completions', None)
            
            if completion:
                # Check if completion has expired
                is_expired = completion.expires_at and completion.expires_at < now
                current_status = TaskStatus.EXPIRED if is_expired and completion.status != TaskStatus.COMPLETED else completion.status
                
                # Task has been interacted with by user
                task_dict.update({
                    'status': current_status,
                    'is_claimed': completion.is_claimed,
                    'started_at': completion.started_at.isoformat() if completion.started_at else None,
                    'current_progress': completion.current_progress,
                    'verification_attempts': completion.verification_attempts,
                    'last_verified_at': completion.last_verified_at.isoformat() if completion.last_verified_at else None,
                    'completion_id': completion.id,
                    'expires_at': completion.expires_at.isoformat() if completion.expires_at else None
                })
            else:
                # Task hasn't been started by user
                task_dict.update({
                    'status': TaskStatus.ACTIVE,
                    'is_claimed': False,
                    'started_at': None,
                    'current_progress': 0,
                    'verification_attempts': 0,
                    'last_verified_at': None,
                    'completion_id': None,
                    'expires_at': None
                })
            
            # Ensure all keys required by TaskOut are present
            task_out_data = {k: v for k, v in task_dict.items() if k in TaskOut.model_fields}
            task_list.append(TaskOut(**task_out_data))

        logger.info(f"Returning {len(task_list)} tasks for user {current_user.id}")
        return task_list

    except Exception as e:
        logger.error(f"Error in get_available_tasks: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "Internal server error", "code": "INTERNAL_ERROR"}
        )

@router.post("/{task_id}/start", response_model=TaskCompletionOut)
async def start_task(
    task_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Start a task for the current user"""
    try:
        task_service = TaskService(db)
        completion = await task_service.start_task(task_id, current_user.id)
        logger.info(f"Task {task_id} started for user {current_user.id}")
        return completion

    except TaskError as e:
        # Provide more user-friendly error messages for common errors
        if e.error_code == "TASK_ALREADY_EXISTS":
            status_code = status.HTTP_400_BAD_REQUEST
            detail = {
                "message": "You've already started this task. Please verify or claim your reward.",
                "code": "TASK_ALREADY_EXISTS",
                "original_message": e.message
            }
        elif e.error_code == "TASK_IN_PROGRESS":
            status_code = status.HTTP_400_BAD_REQUEST
            detail = {
                "message": "This task is already in progress. Please complete it first.",
                "code": "TASK_IN_PROGRESS",
                "original_message": e.message
            }
        else:
            status_code = status.HTTP_400_BAD_REQUEST
            detail = {"message": e.message, "code": e.error_code}
            
        raise HTTPException(status_code=status_code, detail=detail)
    except Exception as e:
        logger.error(f"Error starting task: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "Internal server error", "code": "INTERNAL_ERROR"}
        )

@router.post("/{task_id}/verify", response_model=TaskVerificationResult)
async def verify_task(
    task_id: int,
    request: Request,
    verification_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Verify a task completion with rate limiting"""
    try:
        # Get rate limit service from app state
        rate_limit_service = request.app.state.rate_limit_service
        
        # Check rate limit
        is_allowed, limit_info = await rate_limit_service.check_rate_limit(
            action="task_verification",
            identifier=str(current_user.id),
            ip=request.client.host,
            user_agent=request.headers.get("user-agent")
        )
        
        if not is_allowed:
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=limit_info
            )
        
        # Get verification service
        verification_service = VerificationService(db)
        
        # Perform verification
        result = await verification_service.verify_task(
            task_id=task_id,
            user_id=current_user.id,
            verification_data=verification_data,
            request=request
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying task: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during task verification"
        )

@router.post("/{task_id}/claim", response_model=TaskCompletionOut)
async def claim_task_reward(
    task_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Claim reward for completed task"""
    try:
        # Get task completion
        stmt = select(TaskCompletion).where(
            TaskCompletion.task_id == task_id,
            TaskCompletion.user_id == current_user.id,
            TaskCompletion.status == TaskStatus.COMPLETED,
            TaskCompletion.is_verified == True,
            TaskCompletion.is_claimed == False
        )
        result = await db.execute(stmt)
        completion = result.scalar_one_or_none()

        if not completion:
            # Try finding why claim is invalid for better feedback
            stmt_existing = select(TaskCompletion).where(
                TaskCompletion.task_id == task_id,
                TaskCompletion.user_id == current_user.id
            )
            existing_res = await db.execute(stmt_existing)
            existing_completion = existing_res.scalar_one_or_none()
            
            detail = {"message": "Task not eligible for claim", "code": "INVALID_CLAIM"}
            if existing_completion:
                if existing_completion.is_claimed:
                    detail["message"] = "Reward already claimed."
                    detail["code"] = "ALREADY_CLAIMED"
                elif not existing_completion.is_verified:
                    detail["message"] = "Task verification pending or failed."
                    detail["code"] = "NOT_VERIFIED"
                elif existing_completion.status != TaskStatus.COMPLETED:
                     detail["message"] = f"Task status is {existing_completion.status.value}, not completed."
                     detail["code"] = "NOT_COMPLETED"
            else:
                detail["message"] = "Task completion record not found."
                detail["code"] = "COMPLETION_NOT_FOUND"
                
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

        # Check if enough time has passed since verification
        now = datetime.now(timezone.utc)
        if completion.claim_available_at and now < completion.claim_available_at:
            time_left = max(0, int((completion.claim_available_at - now).total_seconds()))
            detail = {
                "message": f"Please wait {time_left} more seconds before claiming reward",
                "code": "CLAIM_DELAY",
                "retry_after_seconds": time_left
            }
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=detail)

        # Process reward
        task_service = TaskService(db)
        completion_result = await task_service.claim_reward(task_id, current_user.id)
        
        # Convert verification_data from string to dict if needed
        if completion_result.verification_data and isinstance(completion_result.verification_data, str):
            try:
                completion_result.verification_data = json.loads(completion_result.verification_data)
            except json.JSONDecodeError:
                logger.warning(f"Could not decode verification_data for completion {completion_result.id}")
                completion_result.verification_data = None

        return completion_result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error claiming reward: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "Failed to claim reward", "code": "CLAIM_ERROR"}
        )

@router.get("/analytics", response_model=Dict)
async def get_task_analytics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get task analytics for the user"""
    try:
        task_service = TaskService(db)
        return await task_service.get_task_analytics(current_user.id)
    except TaskError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": e.message, "code": e.error_code}
        )

@router.get("/{task_id}/referral-status", response_model=Dict)
async def get_referral_task_status(
    task_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get detailed status of a referral task"""
    try:
        # Use async query
        stmt = select(TaskCompletion).filter(
            TaskCompletion.task_id == task_id,
            TaskCompletion.user_id == current_user.id
        )
        result = await db.execute(stmt)
        completion = result.scalar_one_or_none()
        
        if not completion:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task completion not found"
            )
            
        # Get task
        task_stmt = select(Task).filter(Task.id == task_id)
        task_result = await db.execute(task_stmt)
        task = task_result.scalar_one_or_none()
        
        if not task or task.type != TaskType.REFERRAL:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Not a referral task"
            )
            
        # Get active referrals count
        stmt_active = select(func.count(User.id)).where(
            User.referred_by == current_user.id,
            User.is_active == True
        )
        active_referrals = (await db.execute(stmt_active)).scalar_one_or_none() or 0
        
        # Get total earnings from referrals
        stmt_earnings = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == current_user.id,
            Transaction.type == TransactionType.referral_commission
        )
        total_earnings = (await db.execute(stmt_earnings)).scalar_one_or_none() or 0.0
        
        # Get latest verification
        verif_stmt = select(TaskVerification).filter(
            TaskVerification.task_completion_id == completion.id
        ).order_by(TaskVerification.verified_at.desc())
        verif_result = await db.execute(verif_stmt)
        latest_verification = verif_result.scalar_one_or_none()

        return {
            "status": completion.status,
            "current_progress": active_referrals,
            "target": task.target_value,
            "total_earnings": float(total_earnings),
            "last_verified_at": latest_verification.verified_at if latest_verification else None,
            "is_completed": completion.status == TaskStatus.COMPLETED,
            "is_claimed": completion.is_claimed,
            "claimed_at": completion.claimed_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting referral status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

# Admin Routes