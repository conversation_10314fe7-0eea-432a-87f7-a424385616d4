from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Query, WebSocket, WebSocketDisconnect, Request
from fastapi.responses import JSONResponse
from sqlalchemy import select, or_, and_, desc, func, update
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Optional, Any, Set
from datetime import datetime, timedelta, timezone
import uuid
import random
import string
import secrets
from fastapi import status
from slowapi.errors import RateLimitExceeded
import logging
import redis.asyncio as redis # Import async redis client

from database import get_async_db, get_redis_dependency
from models import User, ChatMessage, OnlineUser, ChatConversation
from schemas import ChatMessageCreate, ChatMessageOut, ChatConversationPartner, UserDisplayInfo, UserSearchResult

# Auth dependency - replace with your actual auth implementation
from auth import get_current_user

router = APIRouter(tags=["chat"], prefix="/chat")
logger = logging.getLogger(__name__)

# Store active WebSocket connections by user ID
active_connections: Dict[int, WebSocket] = {}
# Store connection IDs to user IDs mapping
connection_to_user: Dict[str, int] = {}

# Helper functions
async def get_or_create_conversation_id(db: AsyncSession, user1_id: int, user2_id: int) -> str:
    """Get or create a conversation ID for two users"""
    # Sort user IDs to ensure consistent conversation ID
    if user1_id > user2_id:
        user1_id, user2_id = user2_id, user1_id
    
    # Check if conversation exists
    query = select(ChatConversation).where(
        and_(
            ChatConversation.user1_id == user1_id,
            ChatConversation.user2_id == user2_id
        )
    )
    result = await db.execute(query)
    conversation = result.scalars().first()
    
    if conversation:
        return conversation.id
    
    # Create new conversation
    conversation_id = str(uuid.uuid4())
    new_conversation = ChatConversation(
        id=conversation_id,
        user1_id=user1_id,
        user2_id=user2_id
    )
    db.add(new_conversation)
    await db.commit()
    
    return conversation_id

# --- REVISED: Use Redis for WS Token ---
async def create_ws_token_redis(redis_client: redis.Redis, user_id: int) -> str:
    """Generate, store in Redis (with expiry), and return a WS token."""
    token = secrets.token_urlsafe(32)
    redis_key = f"ws_token:{token}"
    expiry_seconds = 60

    try:
        # Store user_id against the token key, set expiry
        await redis_client.set(redis_key, str(user_id), ex=expiry_seconds)
        logger.info(f"Stored WS token in Redis for user {user_id} (Key: {redis_key[:18]}..., Expires: {expiry_seconds}s)")
        return token
    except Exception as e:
        logger.error(f"Failed to store WS token in Redis for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate WebSocket token")

@router.post("/ws-token", response_model=Dict[str, str])
async def get_websocket_token(
    request: Request,
    client_type: str = Query("websocket", description="Specifies the client type requesting the token"),
    redis_client: redis.Redis = Depends(get_redis_dependency),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """
    Generate a short-lived, one-time token (stored in Redis) for WebSocket auth.
    Requires authentication via HttpOnly cookie.
    """
    if not current_user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Not authenticated")

    token = await create_ws_token_redis(redis_client, current_user.id)
    return {"token": token}
# --- END REVISED ---

async def mark_messages_as_read(db: AsyncSession, user_id: int, conversation_id: str) -> None:
    """Mark all messages in a conversation as read for a specific user"""
    query = select(ChatMessage).where(
        and_(
            ChatMessage.conversation_id == conversation_id,
            ChatMessage.receiver_id == user_id,
            ChatMessage.is_read == False
        )
    )
    result = await db.execute(query)
    messages = result.scalars().all()
    
    for message in messages:
        message.is_read = True
    
    await db.commit()

async def get_online_users(db: AsyncSession) -> List[int]:
    """Get list of currently online users"""
    # Remove inactive users (inactive for more than 2 minutes)
    inactive_threshold = datetime.utcnow() - timedelta(minutes=2)
    query = select(OnlineUser).where(OnlineUser.last_active_at < inactive_threshold)
    result = await db.execute(query)
    inactive_users = result.scalars().all()
    
    for user in inactive_users:
        db.delete(user)
    
    if inactive_users:
        await db.commit()
    
    # Get active users
    query = select(OnlineUser.user_id)
    result = await db.execute(query)
    online_user_ids = result.scalars().all()
    
    return online_user_ids

# REST API Endpoints

@router.get("/public/messages", response_model=List[ChatMessageOut])
async def get_public_messages(
    limit: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """Get recent public chat messages (privacy-focused, no usernames)"""
    result = await db.execute(
        select(ChatMessage)
        .where(ChatMessage.receiver_id == None)  # Public messages only
        .order_by(desc(ChatMessage.created_at))
        .limit(limit)
    )
    
    messages = result.scalars().all()
    
    # Return in chronological order (oldest first)
    return [msg_to_dto(msg) for msg in reversed(messages)]


@router.get("/private/{other_user_id}/messages", response_model=List[ChatMessageOut])
async def get_private_messages(
    other_user_id: int,
    limit: int = Query(50, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """Get private message history with another user (privacy-focused, no usernames)"""
    # Verify the other user exists
    user_exists = await db.scalar(
        select(User.id).where(User.id == other_user_id)
    )
    
    if not user_exists:
        raise HTTPException(status_code=404, detail="User not found")
        
    # Fetch messages where current_user and other_user are either sender or receiver
    result = await db.execute(
        select(ChatMessage)
        .where(
            or_(
                and_(
                    ChatMessage.sender_id == current_user.id,
                    ChatMessage.receiver_id == other_user_id
                ),
                and_(
                    ChatMessage.sender_id == other_user_id,
                    ChatMessage.receiver_id == current_user.id
                )
            )
        )
        .order_by(desc(ChatMessage.created_at))
        .limit(limit)
    )
    
    messages = result.scalars().all()
    
    # Return in chronological order (oldest first)
    return [msg_to_dto(msg) for msg in reversed(messages)]


@router.get("/conversations", response_model=List[ChatConversationPartner])
async def get_conversations(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """Get list of users the current user has chatted with, including unread count."""
    # Find all users the current user has sent messages to (excluding public)
    sent_to_query = select(ChatMessage.receiver_id).where(
        ChatMessage.sender_id == current_user.id,
        ChatMessage.receiver_id != None
    ).distinct()
    
    # Find all users who have sent messages to the current user
    received_from_query = select(ChatMessage.sender_id).where(
        ChatMessage.receiver_id == current_user.id
    ).distinct()
    
    # Combine both queries to get all conversation partners
    result_sent = await db.execute(sent_to_query)
    result_received = await db.execute(received_from_query)
    
    sent_to_ids = result_sent.scalars().all()
    received_from_ids = result_received.scalars().all()
    
    # Combine and deduplicate
    conversation_partner_ids = list(set(sent_to_ids + received_from_ids))
    
    # Get online users
    online_user_ids = await get_online_users(db)
    
    # Build conversation list with last message time and unread count
    conversation_partners = []
    
    for partner_id in conversation_partner_ids:
        # Get partner user object
        user_query = select(User).where(User.id == partner_id)
        result = await db.execute(user_query)
        partner = result.scalars().first()
        
        if not partner:
            continue
        
        # Get conversation ID
        conversation_id = await get_or_create_conversation_id(db, current_user.id, partner_id)
        
        # Count unread messages
        unread_query = select(func.count()).where(
            and_(
                ChatMessage.sender_id == partner_id,
                ChatMessage.receiver_id == current_user.id,
                ChatMessage.is_read == False
            )
        )
        result = await db.execute(unread_query)
        unread_count = result.scalar()
        
        # Get last message time
        last_message_query = select(ChatMessage.created_at).where(
            or_(
                and_(
                    ChatMessage.sender_id == current_user.id,
                    ChatMessage.receiver_id == partner_id
                ),
                and_(
                    ChatMessage.sender_id == partner_id,
                    ChatMessage.receiver_id == current_user.id
                )
            )
        ).order_by(desc(ChatMessage.created_at)).limit(1)
        
        result = await db.execute(last_message_query)
        last_message_time = result.scalar()
        
        # Use real username instead of anonymous nickname
        username = partner.username or f"User_{partner.id}"
        
        # Add to conversation list
        conversation_partners.append({
            "id": partner_id,  # Keep actual ID for internal functionality
            "username": username,  # Use real username instead of anonymous nickname
            "avatar_url": partner.telegram_photo_url,
            "last_message_at": last_message_time,
            "unread_count": unread_count,
            "is_online": partner_id in online_user_ids
        })
    
    # Sort by last message time
    conversation_partners.sort(key=lambda x: x["last_message_at"] if x["last_message_at"] else datetime.min, reverse=True)
    
    return conversation_partners


@router.get("/users/display-info", response_model=Dict[str, UserDisplayInfo])
async def get_user_display_info(
    user_ids: List[int] = Query(..., max_length=100),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """Get minimal display info for users by IDs (avatars only, no usernames)"""
    if not user_ids:
        return {}
        
    # Limit the number of IDs to prevent abuse
    if len(user_ids) > 100:
        raise HTTPException(status_code=400, detail="Too many user IDs requested")
    
    # Map chat partner IDs to real user IDs
    real_user_ids = []
    for partner_id in user_ids:
        # In a real implementation, you'd need a proper mapping table
        # For now, we'll just use the IDs directly but in production
        # you should implement a secure mapping system
        real_user_ids.append(partner_id)
    
    result = await db.execute(
        select(User.id, User.telegram_photo_url, User.username)
        .where(User.id.in_(real_user_ids))
    )
    
    # Return a dictionary mapping chat partner IDs to their display info
    # Include usernames now as we're not exposing actual IDs
    return {
        str(user_id): {
            "avatar_url": photo_url,
            "username": username or f"User_{user_id}"
        }
        for user_id, photo_url, username in result
    }


@router.post("/messages", response_model=ChatMessageOut)
async def create_message(
    request: Request,
    message: ChatMessageCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new chat message (public or private)"""
    # Apply rate limit based on current user ID
    try:
        # Get rate limit service from app state
        rate_limit_service = request.app.state.rate_limit_service
        
        # Get user agent
        user_agent = request.headers.get("user-agent")
        
        # Create a unique identifier for this user
        identifier = f"user_{current_user.id}_chat_message"
        
        # Check rate limit for message creation
        is_limited, limit_info = await rate_limit_service.check_rate_limit(
            action="post_message",
            identifier=identifier,
            ip=request.client.host,
            user_agent=user_agent,
            raise_error=False
        )
        
        if is_limited:
            retry_after = limit_info.get("retry_after", 60) if limit_info else 60
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Rate limit exceeded. Try again in {retry_after} seconds.",
                headers={"Retry-After": str(retry_after)},
            )
    except Exception as e:
        # If rate limiting fails, log and continue
        logger.error(f"Rate limiting error: {str(e)}")

    # Validate receiver if provided (private message)
    receiver_id = message.receiver_id
    conversation_id: Optional[str] = None

    if receiver_id is not None:
        # Check if receiver exists and is not the sender
        if receiver_id == current_user.id:
            raise HTTPException(status_code=400, detail="Cannot send message to yourself")
        
        # For production: Implement secure ID mapping system here
        # Convert chat partner ID to real user ID
        real_receiver_id = receiver_id  # This would involve your mapping logic
            
        receiver_exists = await db.scalar(
            select(User.id).where(User.id == real_receiver_id)
        )
        
        if not receiver_exists:
            raise HTTPException(status_code=404, detail="Receiver not found")
        
        # Use the real receiver ID for database operations
        receiver_id = real_receiver_id

        # Get or create conversation ID for private messages
        conversation_id = await get_or_create_conversation_id(db, current_user.id, receiver_id)

    # Use real username for the sender
    sender_username = current_user.username or f"User_{current_user.id}"

    # Create new message
    new_message = ChatMessage(
        sender_id=current_user.id,
        receiver_id=receiver_id,
        content=message.content,
        conversation_id=conversation_id,
        is_public=(receiver_id is None),
        sender_nickname=sender_username  # Always use the real username
    )
    
    db.add(new_message)
    await db.commit()
    await db.refresh(new_message)
    
    # Return the created message with the chat partner ID
    return msg_to_dto(new_message)


# Search functionality removed - private messages start from UserProfileDialog


@router.post("/mark-as-read")
async def mark_messages_as_read(
    message_ids: List[int],
    receiver_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_user)
):
    """Mark specified messages as read for the current user."""
    try:
        # Only mark messages where current user is the receiver
        query = update(ChatMessage).where(
            and_(
                ChatMessage.id.in_(message_ids),
                ChatMessage.receiver_id == current_user.id,
                ChatMessage.sender_id == receiver_id,
                ChatMessage.is_read == False
            )
        ).values(is_read=True)
        
        result = await db.execute(query)
        await db.commit()
        
        updated_count = result.rowcount
        return {"success": True, "updated_count": updated_count}
    except Exception as e:
        logger.error(f"Error marking messages as read: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to mark messages as read")


# Helper function to convert ChatMessage model to DTO (with real username)
def msg_to_dto(msg: ChatMessage) -> dict:
    """Convert a ChatMessage to a DTO with the real sender information"""
    # Use either stored sender_nickname or a default format
    # Avoid directly accessing msg.sender which causes async operation in sync context
    sender_nickname = msg.sender_nickname if msg.sender_nickname else f"User_{msg.sender_id}"
    
    return {
        "id": msg.id,
        "sender_id": str(msg.sender_id),  # Convert to string to mask actual ID
        "receiver_id": str(msg.receiver_id) if msg.receiver_id else None,  # Convert to string
        "content": msg.content,
        "created_at": msg.created_at.isoformat() + "Z",
        "is_public": msg.is_public,
        "conversation_id": msg.conversation_id,
        "sender_nickname": sender_nickname,
        "sender_avatar": None  # Will need to handle avatars separately
    } 