"""
Admin System Router

Handles all system administration functionality including:
- System configuration management
- System health monitoring
- System statistics and analytics
- Security monitoring and logging
- System maintenance operations
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc, String
from sqlalchemy.orm import selectinload
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging
import psutil
import os

from auth import get_current_admin
from database import get_async_db
from models import (
    User, SystemConfig, SecurityLog, TokenBlacklist, VPNSubscription, 
    VPNPackage, Transaction, TransactionType, OnlineUser, ChatMessage
)
from schemas import SecurityLogCreate

# Configure logging
logger = logging.getLogger(__name__)

# Create router with admin prefix
router = APIRouter(
    prefix="/system",
    tags=["Admin - System"],
    dependencies=[Depends(get_current_admin)]
)

@router.get("/health")
async def get_system_health(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get comprehensive system health status
    
    Features:
    - System resource usage (CPU, memory, disk)
    - Database connectivity and performance
    - Service status monitoring
    - Application health metrics
    """
    try:
        # System resource metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Database health check
        db_start_time = datetime.utcnow()
        test_query = await db.execute(select(func.count(User.id)))
        db_response_time = (datetime.utcnow() - db_start_time).total_seconds() * 1000
        
        # Application uptime (approximate)
        uptime_seconds = psutil.boot_time()
        current_time = datetime.utcnow().timestamp()
        uptime = current_time - uptime_seconds
        
        # Service status checks
        services_status = {
            "database": "healthy" if db_response_time < 1000 else "slow",
            "api": "healthy",
            "redis": "healthy",  # Assuming Redis is working if app is running
            "websocket": "healthy"
        }
        
        # Overall health determination
        is_healthy = (
            cpu_percent < 80 and 
            memory.percent < 85 and 
            disk.percent < 90 and
            db_response_time < 2000
        )
        
        health_status = {
            "status": "healthy" if is_healthy else "warning",
            "timestamp": datetime.utcnow().isoformat(),
            "uptime_seconds": int(uptime),
            "system_resources": {
                "cpu_usage_percent": round(cpu_percent, 2),
                "memory_usage_percent": round(memory.percent, 2),
                "memory_used_mb": round(memory.used / 1024 / 1024, 2),
                "memory_total_mb": round(memory.total / 1024 / 1024, 2),
                "disk_usage_percent": round(disk.percent, 2),
                "disk_used_gb": round(disk.used / 1024 / 1024 / 1024, 2),
                "disk_total_gb": round(disk.total / 1024 / 1024 / 1024, 2)
            },
            "database": {
                "status": services_status["database"],
                "response_time_ms": round(db_response_time, 2)
            },
            "services": services_status
        }
        
        logger.info(f"System health check completed by admin {current_user.username}")
        return health_status
        
    except Exception as e:
        logger.error(f"Error getting system health: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system health status"
        )

@router.get("/stats")
async def get_system_stats(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get comprehensive system statistics and analytics
    
    Features:
    - User growth metrics
    - Revenue analytics
    - System usage statistics
    - Performance metrics
    """
    try:
        today = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Total registered users
        stmt_total_users = select(func.count(User.id))
        result_total_users = await db.execute(stmt_total_users)
        total_users = result_total_users.scalar() or 0
        
        # Active users (last 24 hours)
        yesterday = datetime.utcnow() - timedelta(hours=24)
        stmt_active_users = select(func.count(User.id)).where(
            User.last_login >= yesterday
        )
        result_active_users = await db.execute(stmt_active_users)
        active_users_24h = result_active_users.scalar() or 0
        
        # New users today
        stmt_new_users = select(func.count(User.id)).where(
            User.created_at >= today
        )
        result_new_users = await db.execute(stmt_new_users)
        new_users_today = result_new_users.scalar() or 0
        
        # Total transactions
        stmt_transactions = select(func.count(Transaction.id)).where(
            Transaction.status == "completed"
        )
        result_transactions = await db.execute(stmt_transactions)
        total_transactions = result_transactions.scalar() or 0
        
        # Total revenue
        stmt_revenue = select(func.sum(Transaction.amount)).where(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal,
                TransactionType.card_purchase,
                TransactionType.card_upgrade
            ]),
            Transaction.status == "completed"
        )
        result_revenue = await db.execute(stmt_revenue)
        total_revenue = result_revenue.scalar() or 0
        
        # Revenue today
        stmt_revenue_today = select(func.sum(Transaction.amount)).where(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal,
                TransactionType.card_purchase,
                TransactionType.card_upgrade
            ]),
            Transaction.status == "completed",
            Transaction.created_at >= today
        )
        result_revenue_today = await db.execute(stmt_revenue_today)
        revenue_today = result_revenue_today.scalar() or 0
        
        # Active subscriptions
        stmt_active_subs = select(func.count(VPNSubscription.id)).where(
            VPNSubscription.is_active == True
        )
        result_active_subs = await db.execute(stmt_active_subs)
        active_subscriptions = result_active_subs.scalar() or 0
        
        # API calls in last 24h (approximate using security logs)
        stmt_api_calls = select(func.count(SecurityLog.id)).where(
            SecurityLog.created_at >= yesterday,
            SecurityLog.action_type.in_(["login", "api_request", "token_refresh"])
        )
        result_api_calls = await db.execute(stmt_api_calls)
        api_calls_24h = result_api_calls.scalar() or 0
        
        # Error rate calculation (security logs with risk level high)
        stmt_errors = select(func.count(SecurityLog.id)).where(
            SecurityLog.created_at >= yesterday,
            SecurityLog.risk_level == "high"
        )
        result_errors = await db.execute(stmt_errors)
        errors_24h = result_errors.scalar() or 0
        error_rate_24h = (errors_24h / api_calls_24h * 100) if api_calls_24h > 0 else 0
        
        # Average response time (simulated - would need actual metrics in production)
        avg_response_time = 250  # milliseconds
        
        # Database size (approximate)
        database_size = 0
        try:
            # Use the same database path as configured in database.py
            db_path = 'users.db'  # This matches SQLALCHEMY_DATABASE_URL = "sqlite:///./users.db"
            if os.path.exists(db_path):
                database_size = os.path.getsize(db_path)
        except Exception:
            database_size = 0
        
        system_stats = {
            "overview": {
                "total_users": total_users,
                "active_users_24h": active_users_24h,
                "new_users_today": new_users_today,
                "total_transactions": total_transactions,
                "active_subscriptions": active_subscriptions
            },
            "financial": {
                "total_revenue": float(total_revenue),
                "revenue_today": float(revenue_today)
            },
            "performance": {
                "api_calls_24h": api_calls_24h,
                "error_rate_24h": round(error_rate_24h, 2),
                "avg_response_time": avg_response_time,
                "database_size": database_size
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logger.info(f"System statistics generated by admin {current_user.username}")
        return system_stats
        
    except Exception as e:
        logger.error(f"Error getting system stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system statistics"
        )

@router.get("/config")
async def get_system_config(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get current system configuration"""
    try:
        stmt = select(SystemConfig)
        result = await db.execute(stmt)
        configs = result.scalars().all()
        
        config_dict = {}
        for config in configs:
            config_dict[config.key] = {
                "value": config.value,
                "description": config.description,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None
            }
        
        return config_dict
    except Exception as e:
        logger.error(f"Error getting system config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/config")
async def update_system_config(
    config: Dict[str, Any],
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update system configuration"""
    try:
        for key, value in config.items():
            # Check if config exists
            stmt = select(SystemConfig).filter(SystemConfig.key == key)
            result = await db.execute(stmt)
            existing_config = result.scalar_one_or_none()
            
            if existing_config:
                existing_config.value = str(value)
                existing_config.updated_at = datetime.utcnow()
            else:
                new_config = SystemConfig(
                    key=key,
                    value=str(value),
                    updated_at=datetime.utcnow()
                )
                db.add(new_config)
        
        await db.commit()
        return {"status": "success", "message": "Configuration updated"}
    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating system config: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/configs")
async def get_all_system_configs(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all system configurations with metadata"""
    try:
        stmt = select(SystemConfig).order_by(SystemConfig.key)
        result = await db.execute(stmt)
        configs = result.scalars().all()
        
        configs_data = []
        for config in configs:
            configs_data.append({
                "id": config.id,
                "key": config.key,
                "value": config.value,
                "description": config.description,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None
            })
        
        return configs_data
        
    except Exception as e:
        logger.error(f"Error getting system configs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/configs")
async def create_system_config(
    config_data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Create a new system configuration entry"""
    try:
        # Check if config already exists
        stmt = select(SystemConfig).filter(SystemConfig.key == config_data["key"])
        result = await db.execute(stmt)
        existing_config = result.scalar_one_or_none()
        
        if existing_config:
            raise HTTPException(
                status_code=400,
                detail="Configuration key already exists"
            )
        
        new_config = SystemConfig(
            key=config_data["key"],
            value=config_data["value"],
            description=config_data.get("description")
        )
        
        db.add(new_config)
        await db.commit()
        await db.refresh(new_config)
        
        return {
            "id": new_config.id,
            "key": new_config.key,
            "value": new_config.value,
            "description": new_config.description,
            "updated_at": new_config.updated_at.isoformat() if new_config.updated_at else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating system config: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/configs/{config_id}")
async def update_system_config_by_id(
    config_id: int,
    config_data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update a specific system configuration by ID"""
    try:
        stmt = select(SystemConfig).filter(SystemConfig.id == config_id)
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        # Update fields
        if "value" in config_data:
            config.value = config_data["value"]
        if "description" in config_data:
            config.description = config_data["description"]
        
        config.updated_at = datetime.utcnow()
        
        await db.commit()
        return {"message": "Configuration updated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating system config: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/configs/{config_id}")
async def delete_system_config(
    config_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a system configuration entry"""
    try:
        stmt = select(SystemConfig).filter(SystemConfig.id == config_id)
        result = await db.execute(stmt)
        config = result.scalar_one_or_none()
        
        if not config:
            raise HTTPException(status_code=404, detail="Configuration not found")
        
        await db.delete(config)
        await db.commit()
        
        return {"message": "Configuration deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting system config: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/security/logs")
async def get_security_logs(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100,
    action_type: Optional[str] = None,
    risk_level: Optional[str] = None,
    user_id: Optional[int] = None,
    days: int = 7
):
    """Get security logs with filtering and pagination"""
    try:
        # Build query
        query = select(SecurityLog).options(selectinload(SecurityLog.user))
        
        # Apply filters
        if action_type:
            query = query.filter(SecurityLog.action_type == action_type)
        if risk_level:
            query = query.filter(SecurityLog.risk_level == risk_level)
        if user_id:
            query = query.filter(SecurityLog.user_id == user_id)
            
        # Filter by date range
        start_date = datetime.utcnow() - timedelta(days=days)
        query = query.filter(SecurityLog.created_at >= start_date)
        
        # Order by most recent first
        query = query.order_by(desc(SecurityLog.created_at))
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        logs = result.scalars().all()
        
        # Convert to dict format
        logs_data = []
        for log in logs:
            logs_data.append({
                "id": log.id,
                "action_type": log.action_type,
                "user_id": log.user_id,
                "username": log.user.username if log.user else None,
                "ip_address": log.ip_address,
                "user_agent": log.user_agent,
                "status": log.status,
                "risk_level": log.risk_level,
                "details": log.details,
                "location_info": log.location_info,
                "attempt_count": log.attempt_count,
                "is_blocked": log.is_blocked,
                "blocked_until": log.blocked_until.isoformat() if log.blocked_until else None,
                "created_at": log.created_at.isoformat()
            })
        
        return logs_data
        
    except Exception as e:
        logger.error(f"Error getting security logs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/security/stats")
async def get_security_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 7
):
    """Get security statistics and metrics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Total security events
        total_events_stmt = select(func.count(SecurityLog.id)).filter(
            SecurityLog.created_at >= start_date
        )
        total_events_result = await db.execute(total_events_stmt)
        total_events = total_events_result.scalar() or 0
        
        # High-risk events
        high_risk_stmt = select(func.count(SecurityLog.id)).filter(
            SecurityLog.created_at >= start_date,
            SecurityLog.risk_level == "high"
        )
        high_risk_result = await db.execute(high_risk_stmt)
        high_risk_events = high_risk_result.scalar() or 0
        
        # Blocked IPs
        blocked_ips_stmt = select(func.count(SecurityLog.id.distinct())).filter(
            SecurityLog.created_at >= start_date,
            SecurityLog.is_blocked == True
        )
        blocked_ips_result = await db.execute(blocked_ips_stmt)
        blocked_ips = blocked_ips_result.scalar() or 0
        
        # Failed login attempts
        failed_logins_stmt = select(func.count(SecurityLog.id)).filter(
            SecurityLog.created_at >= start_date,
            SecurityLog.action_type == "login",
            SecurityLog.status == "failed"
        )
        failed_logins_result = await db.execute(failed_logins_stmt)
        failed_logins = failed_logins_result.scalar() or 0
        
        # Risk level distribution
        risk_distribution_stmt = select(
            SecurityLog.risk_level,
            func.count(SecurityLog.id)
        ).filter(
            SecurityLog.created_at >= start_date
        ).group_by(SecurityLog.risk_level)
        
        risk_distribution_result = await db.execute(risk_distribution_stmt)
        risk_distribution = [
            {"risk_level": risk, "count": count}
            for risk, count in risk_distribution_result.all()
        ]
        
        return {
            "total_events": total_events,
            "high_risk_events": high_risk_events,
            "blocked_ips": blocked_ips,
            "failed_logins": failed_logins,
            "risk_distribution": risk_distribution
        }
        
    except Exception as e:
        logger.error(f"Error getting security stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/security/blocked-tokens")
async def get_blocked_tokens(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100
):
    """Get blocked tokens with pagination"""
    try:
        stmt = select(TokenBlacklist).order_by(desc(TokenBlacklist.created_at)).offset(skip).limit(limit)
        result = await db.execute(stmt)
        tokens = result.scalars().all()
        
        tokens_data = []
        for token in tokens:
            tokens_data.append({
                "id": token.id,
                "token_hash": token.token_hash,
                "reason": token.reason,
                "expires_at": token.expires_at.isoformat(),
                "created_at": token.created_at.isoformat()
            })
        
        return tokens_data
        
    except Exception as e:
        logger.error(f"Error getting blocked tokens: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dashboard")
async def admin_dashboard(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get comprehensive admin dashboard statistics"""
    try:
        today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Total registered users
        total_users_stmt = select(func.count(User.id))
        total_users_result = await db.execute(total_users_stmt)
        total_users = total_users_result.scalar() or 0
        
        # New users today
        new_users_today_stmt = select(func.count(User.id)).filter(
            User.created_at >= today
        )
        new_users_today_result = await db.execute(new_users_today_stmt)
        new_users_today = new_users_today_result.scalar() or 0
        
        # Active subscriptions
        active_subs_stmt = select(func.count(VPNSubscription.id)).filter(
            VPNSubscription.is_active == True
        )
        active_subs_result = await db.execute(active_subs_stmt)
        active_subscriptions = active_subs_result.scalar() or 0
        
        # Total revenue
        total_revenue_stmt = select(func.sum(Transaction.amount)).filter(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal,
                TransactionType.card_purchase,
                TransactionType.card_upgrade
            ]),
            Transaction.status == "completed"
        )
        total_revenue_result = await db.execute(total_revenue_stmt)
        total_revenue = total_revenue_result.scalar() or 0
        
        # Revenue today
        revenue_today_stmt = select(func.sum(Transaction.amount)).filter(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal,
                TransactionType.card_purchase,
                TransactionType.card_upgrade
            ]),
            Transaction.status == "completed",
            Transaction.created_at >= today
        )
        revenue_today_result = await db.execute(revenue_today_stmt)
        revenue_today = revenue_today_result.scalar() or 0
        
        # Online users count
        online_users_stmt = select(func.count(OnlineUser.id))
        online_users_result = await db.execute(online_users_stmt)
        online_users = online_users_result.scalar() or 0
        
        # Total chat messages today
        messages_today_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.created_at >= today
        )
        messages_today_result = await db.execute(messages_today_stmt)
        messages_today = messages_today_result.scalar() or 0
        
        # Task completions today
        tasks_completed_today_stmt = select(func.count(TaskCompletion.id)).filter(
            TaskCompletion.completed_at >= today,
            TaskCompletion.status == TaskStatus.COMPLETED
        )
        tasks_completed_today_result = await db.execute(tasks_completed_today_stmt)
        tasks_completed_today = tasks_completed_today_result.scalar() or 0
        
        return {
            "users": {
                "total": total_users,
                "new_today": new_users_today,
                "online": online_users
            },
            "subscriptions": {
                "active": active_subscriptions
            },
            "revenue": {
                "total": float(total_revenue),
                "today": float(revenue_today)
            },
            "activity": {
                "messages_today": messages_today,
                "tasks_completed_today": tasks_completed_today
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats/users")
async def get_user_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get detailed user statistics"""
    try:
        # Total users
        total_users_stmt = select(func.count(User.id))
        total_users_result = await db.execute(total_users_stmt)
        total_users = total_users_result.scalar()
        
        # Active users with subscriptions
        active_users_stmt = select(func.count(User.id)).join(
            VPNSubscription, User.id == VPNSubscription.user_id
        ).filter(VPNSubscription.is_active == True)
        active_users_result = await db.execute(active_users_stmt)
        active_users = active_users_result.scalar()
        
        # Total wallet balance
        total_balance_stmt = select(func.sum(User.wallet_balance))
        total_balance_result = await db.execute(total_balance_stmt)
        total_balance = total_balance_result.scalar() or 0
        
        # Users by role
        role_distribution_stmt = select(
            User.role,
            func.count(User.id)
        ).group_by(User.role)
        role_distribution_result = await db.execute(role_distribution_stmt)
        role_distribution = [
            {"role": role.value, "count": count}
            for role, count in role_distribution_result.all()
        ]
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "total_wallet_balance": float(total_balance),
            "role_distribution": role_distribution
        }
    except Exception as e:
        logger.error(f"Failed to get user stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/transactions")
async def get_transaction_analytics(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get comprehensive transaction analytics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Total transaction volume
        total_volume_stmt = select(func.sum(Transaction.amount)).filter(
            Transaction.created_at >= start_date,
            Transaction.status == "completed"
        )
        total_volume_result = await db.execute(total_volume_stmt)
        total_volume = total_volume_result.scalar() or 0
        
        # Transaction count by type
        type_distribution_stmt = select(
            Transaction.type,
            func.count(Transaction.id),
            func.sum(Transaction.amount)
        ).filter(
            Transaction.created_at >= start_date,
            Transaction.status == "completed"
        ).group_by(Transaction.type)
        
        type_distribution_result = await db.execute(type_distribution_stmt)
        type_distribution = [
            {
                "type": trans_type.value,
                "count": count,
                "volume": float(volume or 0)
            }
            for trans_type, count, volume in type_distribution_result.all()
        ]
        
        # Daily revenue trend
        daily_revenue_stmt = select(
            func.date(Transaction.created_at),
            func.sum(Transaction.amount)
        ).filter(
            Transaction.created_at >= start_date,
            Transaction.status == "completed"
        ).group_by(func.date(Transaction.created_at)).order_by(func.date(Transaction.created_at))
        
        daily_revenue_result = await db.execute(daily_revenue_stmt)
        daily_revenue = [
            {
                "date": date.isoformat(),
                "revenue": float(revenue or 0)
            }
            for date, revenue in daily_revenue_result.all()
        ]
        
        return {
            "total_volume": float(total_volume),
            "type_distribution": type_distribution,
            "daily_revenue": daily_revenue
        }
        
    except Exception as e:
        logger.error(f"Error getting transaction analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 