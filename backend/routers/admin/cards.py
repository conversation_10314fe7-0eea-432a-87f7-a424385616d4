from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc
from sqlalchemy.orm import selectinload
from typing import Dict, List, Optional
from datetime import datetime
import logging

from database import get_async_db
from models import (
    User, CardCatalog, UserCard, Transaction, 
    TransactionType
)
from schemas import UserCardCreate, UserCardOut
from auth import get_current_admin

router = APIRouter(
    prefix="/cards",
    tags=["Admin - Cards"]
)

logger = logging.getLogger(__name__)

# Card Catalog Management
@router.get("/catalog", response_model=List[Dict])
async def get_card_catalog(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all cards in the catalog for admin management"""
    try:
        stmt = select(CardCatalog).order_by(CardCatalog.rarity, CardCatalog.name)
        result = await db.execute(stmt)
        cards = result.scalars().all()
        
        catalog_data = []
        for card in cards:
            catalog_data.append({
                "id": card.id,
                "name": card.name,
                "description": card.description,
                "rarity": card.rarity,
                "max_level": card.max_level,
                "level_profits": card.level_profits,
                "level_costs": card.level_costs,
                "image_url": card.image_url,
                "level_images": card.level_images,
                "created_at": card.created_at.isoformat()
            })
        
        return catalog_data
        
    except Exception as e:
        logger.error(f"Error fetching card catalog: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/catalog")
async def create_card_catalog(
    card_data: dict,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Create a new card in the catalog"""
    try:
        # Validate required fields
        required_fields = ["name", "description", "rarity", "max_level", "level_profits", "level_costs"]
        for field in required_fields:
            if field not in card_data:
                raise HTTPException(
                    status_code=400,
                    detail=f"Missing required field: {field}"
                )
        
        # Check if card name already exists
        stmt = select(CardCatalog).filter(CardCatalog.name == card_data["name"])
        result = await db.execute(stmt)
        existing_card = result.scalar_one_or_none()
        
        if existing_card:
            raise HTTPException(
                status_code=400,
                detail="Card with this name already exists"
            )
        
        new_card = CardCatalog(
            name=card_data["name"],
            description=card_data["description"],
            rarity=card_data["rarity"],
            max_level=card_data["max_level"],
            level_profits=card_data["level_profits"],
            level_costs=card_data["level_costs"],
            image_url=card_data.get("image_url"),
            level_images=card_data.get("level_images", {}),
            created_at=datetime.utcnow()
        )
        
        db.add(new_card)
        await db.commit()
        await db.refresh(new_card)
        
        return {
            "id": new_card.id,
            "name": new_card.name,
            "description": new_card.description,
            "rarity": new_card.rarity,
            "max_level": new_card.max_level,
            "level_profits": new_card.level_profits,
            "level_costs": new_card.level_costs,
            "image_url": new_card.image_url,
            "level_images": new_card.level_images,
            "created_at": new_card.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating card catalog: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/catalog/{card_id}")
async def update_card_catalog(
    card_id: int,
    card_data: dict,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Update a card in the catalog"""
    try:
        stmt = select(CardCatalog).filter(CardCatalog.id == card_id)
        result = await db.execute(stmt)
        card = result.scalar_one_or_none()
        
        if not card:
            raise HTTPException(status_code=404, detail="Card not found")
        
        # Update fields if provided
        if "name" in card_data:
            # Check if new name conflicts with existing cards
            name_check_stmt = select(CardCatalog).filter(
                CardCatalog.name == card_data["name"],
                CardCatalog.id != card_id
            )
            name_check_result = await db.execute(name_check_stmt)
            existing_card = name_check_result.scalar_one_or_none()
            
            if existing_card:
                raise HTTPException(
                    status_code=400,
                    detail="Card with this name already exists"
                )
            
            card.name = card_data["name"]
        
        if "description" in card_data:
            card.description = card_data["description"]
        if "rarity" in card_data:
            card.rarity = card_data["rarity"]
        if "max_level" in card_data:
            card.max_level = card_data["max_level"]
        if "level_profits" in card_data:
            card.level_profits = card_data["level_profits"]
        if "level_costs" in card_data:
            card.level_costs = card_data["level_costs"]
        if "image_url" in card_data:
            card.image_url = card_data["image_url"]
        if "level_images" in card_data:
            card.level_images = card_data["level_images"]
        
        await db.commit()
        
        return {
            "id": card.id,
            "name": card.name,
            "description": card.description,
            "rarity": card.rarity,
            "max_level": card.max_level,
            "level_profits": card.level_profits,
            "level_costs": card.level_costs,
            "image_url": card.image_url,
            "level_images": card.level_images,
            "created_at": card.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating card catalog: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/catalog/{card_id}")
async def delete_card_catalog(
    card_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Delete a card from the catalog"""
    try:
        stmt = select(CardCatalog).filter(CardCatalog.id == card_id)
        result = await db.execute(stmt)
        card = result.scalar_one_or_none()
        
        if not card:
            raise HTTPException(status_code=404, detail="Card not found")
        
        # Check if card is in use by any users
        user_cards_stmt = select(func.count(UserCard.id)).filter(UserCard.card_catalog_id == card_id)
        user_cards_result = await db.execute(user_cards_stmt)
        user_cards_count = user_cards_result.scalar()
        
        if user_cards_count > 0:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot delete card: {user_cards_count} users currently own this card"
            )
        
        await db.delete(card)
        await db.commit()
        
        return {"message": "Card deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting card catalog: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# User Card Administration
@router.get("/users", response_model=List[Dict])
async def get_user_cards_overview(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    user_id: Optional[int] = None,
    card_id: Optional[int] = None,
    min_level: Optional[int] = None,
    limit: int = 100
):
    """Get overview of user cards with filtering"""
    try:
        stmt = select(UserCard).options(
            selectinload(UserCard.user),
            selectinload(UserCard.card_catalog)
        )
        
        # Apply filters
        if user_id:
            stmt = stmt.filter(UserCard.user_id == user_id)
        if card_id:
            stmt = stmt.filter(UserCard.card_catalog_id == card_id)
        if min_level:
            stmt = stmt.filter(UserCard.level >= min_level)
        
        stmt = stmt.order_by(desc(UserCard.created_at)).limit(limit)
        
        result = await db.execute(stmt)
        user_cards = result.scalars().all()
        
        cards_data = []
        for user_card in user_cards:
            cards_data.append({
                "id": user_card.id,
                "user_id": user_card.user_id,
                "username": user_card.user.username if user_card.user else None,
                "card_id": user_card.card_catalog_id,
                "card_name": user_card.card_catalog.name if user_card.card_catalog else None,
                "card_rarity": user_card.card_catalog.rarity if user_card.card_catalog else None,
                "level": user_card.level,
                "quantity": user_card.quantity,
                "total_profit_earned": float(user_card.total_profit_earned),
                "created_at": user_card.created_at.isoformat(),
                "last_profit_collection": user_card.last_profit_collection.isoformat() if user_card.last_profit_collection else None
            })
        
        return cards_data
        
    except Exception as e:
        logger.error(f"Error fetching user cards overview: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/grant", response_model=UserCardOut)
async def grant_card_to_user(
    grant_request: UserCardCreate,
    user_id: int,
    current_admin: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Grant a card to a specific user"""
    try:
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Verify card exists
        card_stmt = select(CardCatalog).filter(CardCatalog.id == grant_request.card_catalog_id)
        card_result = await db.execute(card_stmt)
        card = card_result.scalar_one_or_none()
        
        if not card:
            raise HTTPException(status_code=404, detail="Card not found in catalog")
        
        # Check if user already has this card
        existing_card_stmt = select(UserCard).filter(
            UserCard.user_id == user_id,
            UserCard.card_catalog_id == grant_request.card_catalog_id
        )
        existing_card_result = await db.execute(existing_card_stmt)
        existing_card = existing_card_result.scalar_one_or_none()
        
        if existing_card:
            # Update quantity if card already exists
            existing_card.quantity += grant_request.quantity
            granted_card = existing_card
        else:
            # Create new user card
            granted_card = UserCard(
                user_id=user_id,
                card_catalog_id=grant_request.card_catalog_id,
                level=grant_request.level or 1,
                quantity=grant_request.quantity,
                created_at=datetime.utcnow()
            )
            db.add(granted_card)
        
        await db.commit()
        await db.refresh(granted_card)
        
        # Load relationships for response
        final_stmt = select(UserCard).options(
            selectinload(UserCard.card_catalog),
            selectinload(UserCard.user)
        ).filter(UserCard.id == granted_card.id)
        final_result = await db.execute(final_stmt)
        final_card = final_result.scalar_one()
        
        logger.info(f"Admin {current_admin.username} granted card {card.name} to user {user.username}")
        
        return final_card
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error granting card to user: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/users/{user_card_id}")
async def revoke_user_card(
    user_card_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Revoke a card from a user"""
    try:
        stmt = select(UserCard).options(
            selectinload(UserCard.user),
            selectinload(UserCard.card_catalog)
        ).filter(UserCard.id == user_card_id)
        result = await db.execute(stmt)
        user_card = result.scalar_one_or_none()
        
        if not user_card:
            raise HTTPException(status_code=404, detail="User card not found")
        
        user_name = user_card.user.username if user_card.user else "Unknown"
        card_name = user_card.card_catalog.name if user_card.card_catalog else "Unknown"
        
        await db.delete(user_card)
        await db.commit()
        
        logger.info(f"Admin {current_user.username} revoked card {card_name} from user {user_name}")
        
        return {"message": f"Card {card_name} revoked from user {user_name}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error revoking user card: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/users/{user_card_id}")
async def update_user_card(
    user_card_id: int,
    update_data: dict,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Update a user's card (level, quantity, etc.)"""
    try:
        stmt = select(UserCard).options(
            selectinload(UserCard.user),
            selectinload(UserCard.card_catalog)
        ).filter(UserCard.id == user_card_id)
        result = await db.execute(stmt)
        user_card = result.scalar_one_or_none()
        
        if not user_card:
            raise HTTPException(status_code=404, detail="User card not found")
        
        # Update allowed fields
        if "level" in update_data:
            new_level = update_data["level"]
            if new_level > user_card.card_catalog.max_level:
                raise HTTPException(
                    status_code=400,
                    detail=f"Level {new_level} exceeds max level {user_card.card_catalog.max_level}"
                )
            user_card.level = new_level
        
        if "quantity" in update_data:
            if update_data["quantity"] < 1:
                raise HTTPException(status_code=400, detail="Quantity must be at least 1")
            user_card.quantity = update_data["quantity"]
        
        if "total_profit_earned" in update_data:
            user_card.total_profit_earned = update_data["total_profit_earned"]
        
        await db.commit()
        
        return {
            "id": user_card.id,
            "level": user_card.level,
            "quantity": user_card.quantity,
            "total_profit_earned": float(user_card.total_profit_earned),
            "message": "User card updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user card: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

# Card System Analytics
@router.get("/stats", response_model=Dict)
async def get_card_stats(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get comprehensive card system statistics"""
    try:
        # Total cards in catalog
        total_catalog_stmt = select(func.count(CardCatalog.id))
        total_catalog_result = await db.execute(total_catalog_stmt)
        total_catalog = total_catalog_result.scalar() or 0
        
        # Total user cards (instances)
        total_user_cards_stmt = select(func.sum(UserCard.quantity))
        total_user_cards_result = await db.execute(total_user_cards_stmt)
        total_user_cards = total_user_cards_result.scalar() or 0
        
        # Unique users with cards
        unique_card_owners_stmt = select(func.count(UserCard.user_id.distinct()))
        unique_card_owners_result = await db.execute(unique_card_owners_stmt)
        unique_card_owners = unique_card_owners_result.scalar() or 0
        
        # Total profit generated by all cards
        total_profit_stmt = select(func.sum(UserCard.total_profit_earned))
        total_profit_result = await db.execute(total_profit_stmt)
        total_profit = total_profit_result.scalar() or 0
        
        # Card distribution by rarity
        rarity_distribution_stmt = select(
            CardCatalog.rarity,
            func.count(UserCard.id),
            func.sum(UserCard.quantity)
        ).join(
            UserCard, CardCatalog.id == UserCard.card_catalog_id
        ).group_by(CardCatalog.rarity)
        
        rarity_distribution_result = await db.execute(rarity_distribution_stmt)
        rarity_distribution = [
            {
                "rarity": rarity,
                "owners": owners,
                "total_cards": total_cards
            }
            for rarity, owners, total_cards in rarity_distribution_result.all()
        ]
        
        # Most popular cards
        popular_cards_stmt = select(
            CardCatalog.name,
            CardCatalog.rarity,
            func.count(UserCard.user_id),
            func.sum(UserCard.quantity)
        ).join(
            UserCard, CardCatalog.id == UserCard.card_catalog_id
        ).group_by(
            CardCatalog.id, CardCatalog.name, CardCatalog.rarity
        ).order_by(
            func.count(UserCard.user_id).desc()
        ).limit(10)
        
        popular_cards_result = await db.execute(popular_cards_stmt)
        popular_cards = [
            {
                "name": name,
                "rarity": rarity,
                "owners": owners,
                "total_quantity": total_quantity
            }
            for name, rarity, owners, total_quantity in popular_cards_result.all()
        ]
        
        # Average level by rarity
        avg_level_stmt = select(
            CardCatalog.rarity,
            func.avg(UserCard.level)
        ).join(
            UserCard, CardCatalog.id == UserCard.card_catalog_id
        ).group_by(CardCatalog.rarity)
        
        avg_level_result = await db.execute(avg_level_stmt)
        avg_levels = [
            {
                "rarity": rarity,
                "average_level": round(float(avg_level), 2)
            }
            for rarity, avg_level in avg_level_result.all()
        ]
        
        # Revenue from card purchases
        card_revenue_stmt = select(func.sum(Transaction.amount)).filter(
            Transaction.type.in_([
                TransactionType.card_purchase,
                TransactionType.card_upgrade
            ]),
            Transaction.status == "completed"
        )
        card_revenue_result = await db.execute(card_revenue_stmt)
        card_revenue = card_revenue_result.scalar() or 0
        
        return {
            "overview": {
                "total_catalog_cards": total_catalog,
                "total_user_cards": int(total_user_cards),
                "unique_card_owners": unique_card_owners,
                "total_profit_generated": float(total_profit),
                "total_card_revenue": float(card_revenue)
            },
            "distribution": {
                "by_rarity": rarity_distribution,
                "average_levels": avg_levels
            },
            "popular_cards": popular_cards
        }
        
    except Exception as e:
        logger.error(f"Error getting card stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics/user/{user_id}")
async def get_user_card_analytics(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get detailed card analytics for a specific user"""
    try:
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # User's card collection
        user_cards_stmt = select(UserCard).options(
            selectinload(UserCard.card_catalog)
        ).filter(UserCard.user_id == user_id)
        user_cards_result = await db.execute(user_cards_stmt)
        user_cards = user_cards_result.scalars().all()
        
        # Calculate analytics
        total_cards = sum(card.quantity for card in user_cards)
        total_profit = sum(card.total_profit_earned for card in user_cards)
        
        # Cards by rarity
        rarity_breakdown = {}
        level_breakdown = {}
        
        for card in user_cards:
            rarity = card.card_catalog.rarity
            level = card.level
            
            if rarity not in rarity_breakdown:
                rarity_breakdown[rarity] = {"count": 0, "quantity": 0}
            rarity_breakdown[rarity]["count"] += 1
            rarity_breakdown[rarity]["quantity"] += card.quantity
            
            if level not in level_breakdown:
                level_breakdown[level] = 0
            level_breakdown[level] += card.quantity
        
        # Format card details
        card_details = []
        for card in user_cards:
            card_details.append({
                "card_name": card.card_catalog.name,
                "rarity": card.card_catalog.rarity,
                "level": card.level,
                "quantity": card.quantity,
                "profit_earned": float(card.total_profit_earned),
                "last_collection": card.last_profit_collection.isoformat() if card.last_profit_collection else None
            })
        
        return {
            "user_info": {
                "user_id": user_id,
                "username": user.username
            },
            "summary": {
                "total_cards": total_cards,
                "unique_cards": len(user_cards),
                "total_profit_earned": float(total_profit)
            },
            "breakdown": {
                "by_rarity": rarity_breakdown,
                "by_level": level_breakdown
            },
            "card_details": card_details
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user card analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 