# backend/routers/card_router.py

from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload, joinedload
from sqlalchemy.future import select
from typing import List
from datetime import datetime, timedelta
from sqlalchemy.sql import func

# Revert to direct imports (assuming backend dir is in PYTHONPATH)
import models
import schemas
import auth
from database import get_async_db

router = APIRouter(
    prefix="/api/cards",
    tags=["cards"],
    dependencies=[Depends(auth.get_current_active_user)],
)

@router.get("/all", response_model=List[schemas.UnifiedCardResponse])
async def list_all_cards_with_ownership(
    current_user: models.User = Depends(auth.get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    # Get all catalog cards
    catalog_stmt = select(models.CardCatalog).order_by(models.CardCatalog.rarity, models.CardCatalog.name)
    catalog_result = await db.execute(catalog_stmt)
    all_cards = catalog_result.scalars().all()
    
    # Get user's cards
    user_cards_stmt = (
        select(models.UserCard)
        .where(models.UserCard.user_id == current_user.id)
        .options(joinedload(models.UserCard.card_catalog))
    )
    user_cards_result = await db.execute(user_cards_stmt)
    user_cards = user_cards_result.scalars().all()
    user_card_lookup = {card.card_catalog_id: card for card in user_cards}
    
    response = []
    for catalog_card in all_cards:
        # --- Always set all catalog fields with safe defaults ---
        card_data = {
            "id": getattr(catalog_card, 'id', 0),
            "name": getattr(catalog_card, 'name', 'Unknown Card'),
            "description": getattr(catalog_card, 'description', 'No description available.'),
            "image_url": getattr(catalog_card, 'image_url', '') or '',
            "rarity": getattr(catalog_card, 'rarity', 'common'),
            "max_level": getattr(catalog_card, 'max_level', 10),
            "is_owned": False,
            "user_card": None
        }
        # Level profits and price
        try:
            level_profits_list = getattr(catalog_card, 'level_profits', []) or []
            card_data["level_profits"] = level_profits_list
            card_data["profit_rate"] = float(level_profits_list[0]) if level_profits_list else 0.0
        except Exception:
            card_data["level_profits"] = []
            card_data["profit_rate"] = 0.0
        try:
            level_costs_list = getattr(catalog_card, 'level_costs', []) or []
            card_data["price"] = float(level_costs_list[0]) if level_costs_list else 0.0
        except Exception:
            card_data["price"] = 0.0

        # If user owns this card, add user_card and is_owned
        if catalog_card.id in user_card_lookup:
            user_card = user_card_lookup[catalog_card.id]
            card_data["is_owned"] = True
            card_data["user_card"] = {
                "id": user_card.id,
                "level": user_card.level,
                "purchase_date": user_card.acquired_at.isoformat(),
                "last_profit_claim": user_card.last_claim_at.isoformat(),
                "total_claimed_profit": getattr(user_card, "total_claimed_profit", 0.0),
                "accumulated_profit": getattr(user_card, "accumulated_profit", 0.0),
                "current_hourly_profit": getattr(user_card, "current_hourly_profit", 0.0),
                "next_upgrade_cost": getattr(user_card, "next_upgrade_cost", None)
            }
            # Optionally, add next_level_profit_increase if you want
            if user_card.level < card_data["max_level"] and card_data["level_profits"]:
                try:
                    current_profit = card_data["level_profits"][user_card.level - 1]
                    next_profit = card_data["level_profits"][user_card.level]
                    card_data["next_level_profit_increase"] = next_profit - current_profit
                except Exception:
                    card_data["next_level_profit_increase"] = None
            else:
                card_data["next_level_profit_increase"] = None
        else:
            card_data["next_level_profit_increase"] = None

        response.append(card_data)
    return response

@router.post("/claim-all", response_model=schemas.ClaimAllResponse)
async def claim_all_cards_profit(
    current_user_dep: models.User = Depends(auth.get_current_active_user),
    db: AsyncSession = Depends(get_async_db),
    request: Request = None
):
    """Claims the accumulated passive profit for all user cards with enhanced security."""
    # TODO: Implement robust rate limiting (e.g., using Redis)

    # Record client info for security logging
    client_ip = request.client.host if request else "unknown"
    user_agent = request.headers.get("user-agent", "unknown") if request else "unknown"

    # Rate limiting check (DB based - consider moving to Redis/cache)
    last_minute = datetime.utcnow() - timedelta(minutes=1)
    claim_count_stmt = select(func.count(models.Transaction.id)).where(
        models.Transaction.user_id == current_user_dep.id,
        models.Transaction.type == models.TransactionType.card_profit_all,
        models.Transaction.created_at > last_minute
    )
    claim_count_result = await db.execute(claim_count_stmt)
    recent_claims = claim_count_result.scalar_one()
    
    if recent_claims >= 3:  # Limit to 3 claims per minute
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS, 
            detail="Rate limit exceeded. Please try again later."
        )

    # Reload the user with cards 
    stmt = (
        select(models.User)
        .where(models.User.id == current_user_dep.id)
        .options(
            selectinload(models.User.user_cards).selectinload(models.UserCard.card_catalog)
        )
    )
    result = await db.execute(stmt)
    current_user = result.scalar_one_or_none()

    if not current_user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

    # Calculation based on the reloaded user with cards
    total_income_rate = current_user.total_passive_hourly_income

    if total_income_rate <= 0:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No passive income rate to claim from.")

    now = datetime.utcnow()
    last_claim = current_user.last_passive_claim_at or current_user.created_at
    elapsed_time = now - last_claim
    elapsed_seconds = elapsed_time.total_seconds()

    # Cap claimable time to 3 hours (10800 seconds)
    claimable_seconds = min(elapsed_seconds, 3.0 * 3600.0)

    if claimable_seconds < 10:  # Require at least 10 seconds between claims
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail=f"Not enough time passed since last claim. Please wait a few seconds."
        )

    total_claimed = (total_income_rate / 3600.0) * claimable_seconds
    total_claimed = round(total_claimed, 4)

    if total_claimed <= 0.0001:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No significant profit available to claim.")

    # Update wallet and last claim timestamp for both user and all cards
    current_user.wallet_balance += total_claimed
    current_user.last_passive_claim_at = now
    
    # Update last_claim_at for all cards too
    for card in current_user.user_cards:
        card.last_claim_at = now

    # Log transaction
    transaction = models.Transaction(
        user_id=current_user.id,
        type=models.TransactionType.card_profit_all,
        amount=total_claimed,
        description=f"Claimed total passive income.",
        balance_after=current_user.wallet_balance,
        status="completed"
    )
    db.add(transaction)

    # Security log entry
    security_log = models.SecurityLog(
        action_type="card_profit_claim",
        user_id=current_user.id,
        ip_address=client_ip,
        user_agent=user_agent,
        status="success",
        details={
            "amount": total_claimed,
            "elapsed_seconds": elapsed_seconds,
            "claimable_seconds": claimable_seconds,
            "card_count": len(current_user.user_cards)
        },
        risk_level="low"
    )
    db.add(security_log)

    try:
        await db.commit()
        # Ensure all UserCard objects are refreshed with latest DB values
        for card in current_user.user_cards:
            await db.refresh(card)
        await db.refresh(current_user)
    except Exception as e:
        await db.rollback()
        # Log error
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to process claim-all")

    return schemas.ClaimAllResponse(
        total_claimed=total_claimed,
        new_wallet_balance=current_user.wallet_balance,
        cards_count=len(current_user.user_cards),
        next_claim_available=now + timedelta(seconds=10)  # Allow claiming again after 10 seconds
    )

@router.post("/{user_card_id}/upgrade", response_model=schemas.UserCardOut)
async def upgrade_user_card(
    user_card_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Upgrades a user's card to the next level if requirements are met."""
    # TODO: Implement rate limiting for upgrades

    user_card = await db.get(
        models.UserCard,
        user_card_id,
        options=[selectinload(models.UserCard.card_catalog), selectinload(models.UserCard.user)]
    )

    if not user_card or user_card.user_id != current_user.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User card not found")

    owner = user_card.user
    catalog = user_card.card_catalog

    if user_card.level >= catalog.max_level:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail=f"Card '{catalog.name}' is already at max level ({catalog.max_level})."
        )

    upgrade_cost = user_card.next_upgrade_cost
    if upgrade_cost is None:
         raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Cannot determine upgrade cost. Catalog data might be inconsistent.")

    if owner.wallet_balance < upgrade_cost:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail=f"Insufficient balance to upgrade '{catalog.name}'. Need {upgrade_cost:.2f} coins, have {owner.wallet_balance:.2f}."
        )

    # Apply changes
    owner.wallet_balance -= upgrade_cost
    user_card.level += 1

    # Log transaction
    transaction = models.Transaction(
        user_id=owner.id,
        type=models.TransactionType.card_upgrade,
        amount=-upgrade_cost,
        description=f"Upgraded card {catalog.name} (ID: {user_card.id}) to level {user_card.level}",
        balance_after=owner.wallet_balance,
        status="completed"
    )
    db.add(transaction)

    try:
        await db.commit()
    except Exception as e:
        await db.rollback()
        # Log error e
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to process upgrade")

    # Refresh objects for response
    await db.refresh(owner)
    await db.refresh(user_card)
    await db.refresh(user_card.card_catalog) # Ensure catalog is fresh too

    # --- Construct the full UserCardOut structure for the response --- 
    card_catalog_refreshed = user_card.card_catalog # Access refreshed relation
    if not card_catalog_refreshed:
         # Fallback if relationship didn't load on refresh
         card_catalog_refreshed = await db.get(models.CardCatalog, user_card.card_catalog_id)

    # Recalculate properties after refresh for the response
    try:
        # These are the properties *after* the upgrade (level is already incremented)
        current_hourly_profit = user_card.current_hourly_profit 
        next_upgrade_cost = user_card.next_upgrade_cost 
        hours_since_claim = user_card.hours_since_last_claim
        # Limit claimable time (e.g., max 24 hours) - Accumulated profit restarts after upgrade?
        # For now, let's assume it carries over or resets based on last_claim_at
        claimable_hours = min(hours_since_claim, 24.0)
        accumulated_profit = round(current_hourly_profit * claimable_hours, 4) 
    except Exception as e:
        print(f"Error calculating stats for upgraded card ID {user_card.id}: {e}")
        current_hourly_profit = 0.0
        next_upgrade_cost = None
        accumulated_profit = 0.0

    # --- Get new image url after upgrade --- 
    new_image_url = ""
    if card_catalog_refreshed:
        level_images = card_catalog_refreshed.level_images
        image_idx = user_card.level - 1 # Use the NEW level
        new_image_url = level_images[image_idx] if 0 <= image_idx < len(level_images) else card_catalog_refreshed.image_url or ""
    # --- End Get new image url --- 

    card_obj = {
        "id": card_catalog_refreshed.id if card_catalog_refreshed else user_card.card_catalog_id,
        "name": card_catalog_refreshed.name if card_catalog_refreshed else "Unknown",
        "rarity": card_catalog_refreshed.rarity if card_catalog_refreshed else "common",
        "image_url": new_image_url, # Use the level-specific image
        "max_level": card_catalog_refreshed.max_level if card_catalog_refreshed else 10,
        "description": getattr(card_catalog_refreshed, 'description', 'No description available.') if card_catalog_refreshed else 'No description available.',
    }

    # This structure MUST match schemas.UserCardOut
    user_card_out_data = {
        "id": user_card.id,
        "user_id": user_card.user_id,
        "card_catalog_id": user_card.card_catalog_id,
        "level": user_card.level,
        "purchase_date": user_card.acquired_at.isoformat(),
        "last_profit_claim": user_card.last_claim_at.isoformat(),
        "total_claimed_profit": getattr(user_card, "total_claimed_profit", 0.0),
        "accumulated_profit": accumulated_profit,
        "current_hourly_profit": current_hourly_profit,
        "next_upgrade_cost": next_upgrade_cost,
        "card": card_obj, # Nested catalog info
    }

    return user_card_out_data
    # --- End Construct UserCardOut structure --- 

# --- NEW Buy Endpoint --- 
@router.post("/buy/{card_catalog_id}", response_model=schemas.BuyCardResult, status_code=status.HTTP_201_CREATED)
async def buy_card(
    card_catalog_id: int,
    current_user: models.User = Depends(auth.get_current_active_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Purchases a new card if the user doesn't own it and has enough balance."""
    # TODO: Implement rate limiting for purchases

    # 1. Check if card catalog exists
    card_catalog = await db.get(models.CardCatalog, card_catalog_id)
    if not card_catalog:
        print(f"Buy attempt failed: CardCatalog ID {card_catalog_id} not found.") # Log detail
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Card type not found in catalog.")

    # 2. Check if user already owns this card type
    existing_card_stmt = select(models.UserCard).where(
        models.UserCard.user_id == current_user.id,
        models.UserCard.card_catalog_id == card_catalog_id
    )
    existing_card_result = await db.execute(existing_card_stmt)
    if existing_card_result.scalar_one_or_none():
        print(f"Buy attempt failed: User {current_user.id} already owns CardCatalog ID {card_catalog_id}.") # Log detail
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, 
            detail=f"You already own the card '{card_catalog.name}'."
        )

    # 3. Check user balance and get purchase cost from level_costs[0]
    try:
        level_costs = card_catalog.level_costs # Access the parsed list property
        if not level_costs or len(level_costs) == 0: # Check if list is empty or invalid
            print(f"Buy attempt failed: Purchase cost data missing or invalid for CardCatalog ID {card_catalog_id}.") # Log detail
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Purchase cost data missing for {card_catalog.name}."
            )
        purchase_cost = round(level_costs[0], 2) # Cost to acquire Level 1
    except Exception as e:
        print(f"Error getting purchase cost for CardCatalog ID {card_catalog_id}: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error determining purchase cost.")

    # --- Fetch user within the current session --- 
    db_user_stmt = select(models.User).where(models.User.id == current_user.id)
    db_user_result = await db.execute(db_user_stmt)
    db_user = db_user_result.scalar_one_or_none()

    if not db_user:
        print(f"Buy attempt failed: Could not find user {current_user.id} in the current session.")
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found in current session.")
    # --- End fetch user --- 

    if db_user.wallet_balance < purchase_cost:
        print(f"Buy attempt failed: User {current_user.id} has insufficient balance ({db_user.wallet_balance}) for cost {purchase_cost}.") # Log detail
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Insufficient balance. Need {purchase_cost:.2f} coins to buy {card_catalog.name}."
        )

    # 4. Perform purchase
    now = datetime.utcnow()
    # Deduct cost from the session-bound user object
    db_user.wallet_balance -= purchase_cost 
    
    # Set last claim time if it's null (first card purchase might trigger this)
    if db_user.last_passive_claim_at is None:
        db_user.last_passive_claim_at = now

    # Create new card
    new_user_card = models.UserCard(
        user_id=current_user.id,
        card_catalog_id=card_catalog_id,
        level=1,
        acquired_at=now,
        last_claim_at=now # Initialize last claim for the card itself (though global claim is primary)
    )
    db.add(new_user_card)

    # Log transaction
    transaction = models.Transaction(
        user_id=current_user.id,
        type=models.TransactionType.card_purchase,
        amount=-purchase_cost,
        description=f"Purchased card: {card_catalog.name} (ID: {card_catalog_id})",
        balance_after=db_user.wallet_balance, # Use the updated balance from db_user
        status="completed"
    )
    db.add(transaction)

    try:
        await db.commit()
        print(f"Purchase success: User {current_user.id} bought CardCatalog ID {card_catalog_id}.") # Log success

        # Refresh user first to get updated balance
        await db.refresh(db_user)

        # --- Reload the new card with catalog explicitly loaded ---
        stmt_reload = select(models.UserCard).where(
            models.UserCard.id == new_user_card.id
        ).options(
            selectinload(models.UserCard.card_catalog)
        )
        reloaded_card_result = await db.execute(stmt_reload)
        reloaded_user_card = reloaded_card_result.scalar_one_or_none()
        if not reloaded_user_card:
            # This is unlikely but a safeguard
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to reload purchased card after commit.")
        # --- Use reloaded_user_card for response construction ---

    except Exception as e:
        await db.rollback()
        print(f"ERROR during purchase commit for User {current_user.id}, CardCatalog ID {card_catalog_id}: {e}") # Log commit error
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to process purchase")

    # Manually construct response using reloaded_user_card
    card_catalog_refreshed = reloaded_user_card.card_catalog # Access loaded relation
    if not card_catalog_refreshed:
        # Fallback if relationship didn't load on refresh (should not happen now)
        card_catalog_refreshed = await db.get(models.CardCatalog, card_catalog_id)

    # Recalculate properties for the new card response using reloaded_user_card
    try:
        current_hourly_profit = reloaded_user_card.current_hourly_profit
        next_upgrade_cost = reloaded_user_card.next_upgrade_cost
    except Exception as e:
        print(f"Error calculating stats for newly purchased card ID {new_user_card.id}: {e}")
        current_hourly_profit = 0.0
        next_upgrade_cost = None
        
    # --- Get level 1 image --- 
    level_images = card_catalog_refreshed.level_images
    level_1_image_url = level_images[0] if level_images and len(level_images) > 0 else card_catalog_refreshed.image_url or "" # Check length
    # --- End Get level 1 image --- 
        
    card_obj = {
        "id": card_catalog_refreshed.id,
        "name": card_catalog_refreshed.name,
        "rarity": card_catalog_refreshed.rarity,
        "image_url": level_1_image_url, # Use Level 1 image
        "max_level": card_catalog_refreshed.max_level,
        "description": getattr(card_catalog_refreshed, 'description', 'No description available.'),
        "profit_rate": round(card_catalog_refreshed.level_profits[0], 4) if card_catalog_refreshed.level_profits else 0.0,
        "price": round(card_catalog_refreshed.level_costs[0], 2) if card_catalog_refreshed.level_costs else 0.0,
    }

    user_card_out_data = {
        "id": new_user_card.id,
        "user_id": new_user_card.user_id,
        "card_catalog_id": new_user_card.card_catalog_id,
        "level": new_user_card.level,
        "purchase_date": new_user_card.acquired_at.isoformat(),
        "last_profit_claim": new_user_card.last_claim_at.isoformat(),
        "total_claimed_profit": getattr(new_user_card, "total_claimed_profit", 0.0),
        "accumulated_profit": 0.0, # New card starts with 0 accumulated profit
        "current_hourly_profit": current_hourly_profit,
        "next_upgrade_cost": next_upgrade_cost,
        "card": card_obj,
    }
    
    return schemas.BuyCardResult(
        new_card=schemas.UserCardOut(**user_card_out_data),
        new_wallet_balance=db_user.wallet_balance # Use balance from session-bound user
    )

# --- Helper Endpoint (Admin/Debug) --- 
@router.post("/grant-card", response_model=schemas.UserCardOut, status_code=status.HTTP_201_CREATED)
async def grant_card_to_user(
    grant_request: schemas.UserCardCreate,
    user_id: int, # Specify target user ID
    current_admin: models.User = Depends(auth.get_current_admin), # Use admin dependency
    db: AsyncSession = Depends(get_async_db)
):
    """Grants a specific card from the catalog to a target user. (Admin Only)"""
    # TODO: Implement rate limiting for admin actions if necessary

    user = await db.get(models.User, user_id, options=[selectinload(models.User.user_cards)]) # Load cards to check if first
    card_catalog = await db.get(models.CardCatalog, grant_request.card_catalog_id)

    if not user:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"User with ID {user_id} not found")
    if not card_catalog:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=f"Card Catalog item ID {grant_request.card_catalog_id} not found")

    # Check if user already has this card
    existing_card_stmt = select(models.UserCard).where(
        models.UserCard.user_id == user_id,
        models.UserCard.card_catalog_id == grant_request.card_catalog_id
    )
    existing_card_result = await db.execute(existing_card_stmt)
    if existing_card_result.scalar_one_or_none():
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User already owns this card type")

    # --- Improvement: Set last_passive_claim_at if first card --- 
    is_first_card = not user.user_cards
    now = datetime.utcnow()
    if is_first_card and user.last_passive_claim_at is None:
        user.last_passive_claim_at = now
    # --- End Improvement --- 

    new_user_card = models.UserCard(
        user_id=user_id,
        card_catalog_id=grant_request.card_catalog_id,
        level=1, 
        acquired_at=now,
        last_claim_at=now # Also set card's own last_claim_at
    )
    db.add(new_user_card)

    try:
        await db.commit()
    except Exception as e:
        await db.rollback()
        # Log error e
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Failed to grant card")

    # Refresh to load relationships for the response schema
    await db.refresh(new_user_card)

    return new_user_card 