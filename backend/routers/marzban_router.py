from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import List, Dict, Any
from database import get_async_db
from models import User, MarzbanPanel, VPNPackage
from schemas import MarzbanPanelCreate, MarzbanPanelUpdate, MarzbanPanelOut
from services.marzban_service import get_marzban_service
from auth import get_current_admin
import logging

router = APIRouter(
    prefix="/api/marzban",
    tags=["marzban"]
)

logger = logging.getLogger(__name__)

@router.get("/panels/", response_model=List[MarzbanPanelOut])
async def list_marzban_panels(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    stmt = select(MarzbanPanel)
    result = await db.execute(stmt)
    return result.scalars().all()

@router.post("/panels/", response_model=MarzbanPanelOut)
async def create_marzban_panel(
    panel: MarzbanPanelCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    try:
        service = get_marzban_service(db)
        encrypted_password = service.encrypt_password(panel.admin_password)
        
        db_panel = MarzbanPanel(
            name=panel.name,
            api_url=panel.api_url,
            admin_username=panel.admin_username,
            admin_password=encrypted_password,
            is_active=panel.is_active
        )
            
        db.add(db_panel)
        await db.commit()
        await db.refresh(db_panel)
        return db_panel
        
    except Exception as e:
        logger.error(f"Failed to create panel: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/panels/{panel_id}", response_model=MarzbanPanelOut)
async def update_marzban_panel(
    panel_id: int,
    panel_update: MarzbanPanelUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    try:
        stmt = select(MarzbanPanel).where(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        db_panel = result.scalar_one_or_none()
        
        if not db_panel:
            raise HTTPException(status_code=404, detail="Panel not found")

        update_data = panel_update.model_dump(exclude_unset=True)
        
        if 'admin_password' in update_data and update_data['admin_password']:
            service = get_marzban_service(db)
            update_data['admin_password'] = service.encrypt_password(update_data['admin_password'])

        for key, value in update_data.items():
            setattr(db_panel, key, value)

        await db.commit()
        await db.refresh(db_panel)
        return db_panel

    except Exception as e:
        logger.error(f"Update Error: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/panels/{panel_id}")
async def delete_marzban_panel(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    stmt = select(MarzbanPanel).where(MarzbanPanel.id == panel_id)
    result = await db.execute(stmt)
    panel = result.scalar_one_or_none()
    
    if not panel:
        raise HTTPException(status_code=404, detail="Panel not found")
    
    await db.delete(panel)
    await db.commit()
    return {"message": "Panel deleted successfully"}

@router.post("/panels/{panel_id}/test", response_model=Dict[str, Any])
async def test_panel_connection(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    service = get_marzban_service(db)
    try:
        stmt = select(MarzbanPanel).where(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")
            
        success, message, stats = await service.test_panel_connection(panel)
        
        if not success:
            raise HTTPException(status_code=400, detail=message)
            
        return {
            "success": True,
            "message": message,
            "stats": stats
        }
        
    except Exception as e:
        logger.error(f"Panel connection test failed: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/panels/{panel_id}/stats")
async def get_panel_stats(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    try:
        stmt = select(MarzbanPanel).where(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")

        service = get_marzban_service(db)
        result = await service.get_panel_statistics(panel)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
            
        return result
        
    except Exception as e:
        logger.error(f"Failed to get panel stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/subscriptions/{subscription_url}/usage")
async def get_subscription_usage(
    subscription_url: str,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    try:
        service = get_marzban_service(db)
        result = await service.get_user_usage_from_sub(subscription_url)
        
        if not result["success"]:
            raise HTTPException(status_code=400, detail=result["message"])
            
        return result
        
    except Exception as e:
        logger.error(f"Failed to get subscription usage: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/users/create")
async def create_marzban_user(
    panel_id: int,
    username: str,
    data_limit: int,
    expire_days: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    try:
        stmt = select(MarzbanPanel).where(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")

        service = get_marzban_service(db)
        enabled_inbounds = await service.get_enabled_inbounds(panel)
        
        result = await service.create_user(
            panel=panel,
            username=username,
            proxies=enabled_inbounds,
            data_limit=data_limit,
            expire_days=expire_days
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to create Marzban user: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 