from fastapi import APIRouter, Depends, HTTPException, status, Response, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional
from database import get_async_db
from models import User
from auth import get_current_admin
from services.monitoring_service import MonitoringService
from services.cache_service import CacheService
from config.redis_config import RedisClient
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/monitoring", tags=["monitoring"])

async def get_monitoring_service(
    request: Request,
    db: AsyncSession = Depends(get_async_db)
) -> MonitoringService:
    cache_service = getattr(request.app.state, 'cache_service', None)
    return MonitoringService(db=db, cache_service=cache_service)

@router.get("/metrics")
async def get_prometheus_metrics(
    response: Response,
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
):
    """Get Prometheus metrics"""
    try:
        metrics = await monitoring_service.get_prometheus_metrics()
        response.headers["Content-Type"] = "text/plain; version=0.0.4"
        return Response(content=metrics, media_type="text/plain")
    except Exception as e:
        logger.exception(f"Error getting Prometheus metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get metrics"
        )

@router.get("/system")
async def get_system_metrics(
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
):
    """Get current system metrics"""
    try:
        return await monitoring_service.get_system_metrics()
    except Exception as e:
        logger.exception(f"Error getting system metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system metrics"
        )

@router.get("/redis")
async def get_redis_metrics(
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
):
    """Get Redis metrics"""
    try:
        return await monitoring_service.get_redis_stats()
    except Exception as e:
        logger.exception(f"Error getting Redis metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get Redis metrics"
        )

@router.get("/application")
async def get_application_metrics(
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
):
    """Get application metrics"""
    try:
        return await monitoring_service.get_application_stats()
    except Exception as e:
        logger.exception(f"Error getting application metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get application metrics"
        )

@router.get("/security")
async def get_security_metrics(
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
):
    """Get security metrics"""
    try:
        return await monitoring_service.get_security_metrics()
    except Exception as e:
        logger.exception(f"Error getting security metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get security metrics"
        )

@router.get("/health")
async def health_check():
    """Basic health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat()
    }

@router.get("/historical")
async def get_historical_metrics(
    hours: Optional[int] = 24,
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
):
    """Get historical system metrics"""
    try:
        return await monitoring_service.get_historical_metrics(hours)
    except Exception as e:
        logger.exception(f"Error getting historical metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get historical metrics"
        )

@router.get("/health")
async def get_system_health(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get overall system health status"""
    try:
        monitoring_service = MonitoringService(db, RedisClient.get_client())
        return await monitoring_service.get_system_health()
    except Exception as e:
        logger.error(f"Error getting system health: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get system health"
        )

@router.get("/task-stats")
async def get_task_statistics(
    force_refresh: bool = False,
    request: Request = None,
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
) -> Dict:
    """Get task system statistics"""
    try:
        return await monitoring_service.get_task_statistics(force_refresh)
    except Exception as e:
        logger.exception(f"Error getting task statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get task statistics")

@router.get("/user-stats")
async def get_user_statistics(
    force_refresh: bool = False,
    request: Request = None,
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
) -> Dict:
    """Get user activity statistics"""
    try:
        return await monitoring_service.get_user_statistics(force_refresh)
    except Exception as e:
        logger.exception(f"Error getting user statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get user statistics")

@router.get("/performance")
async def get_performance_metrics(
    request: Request = None,
    monitoring_service: MonitoringService = Depends(get_monitoring_service),
    current_user: User = Depends(get_current_admin)
) -> Dict:
    """Get system performance metrics"""
    try:
        return await monitoring_service.get_performance_metrics()
    except Exception as e:
        logger.exception(f"Error getting performance metrics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get performance metrics")

async def get_cache_service(request: Request) -> CacheService:
    cache_service = getattr(request.app.state, 'cache_service', None)
    if not cache_service:
        logger.error("CacheService not found in application state")
        raise HTTPException(status_code=500, detail="Cache service not configured")
    return cache_service

@router.get("/cache")
async def get_cache_stats(
    cache_service: CacheService = Depends(get_cache_service),
    current_user: User = Depends(get_current_admin)
) -> Dict:
    """Get cache statistics"""
    try:
        return await cache_service.get_cache_stats()
    except Exception as e:
        logger.exception(f"Error getting cache statistics: {e}")
        raise HTTPException(status_code=500, detail="Failed to get cache statistics")

@router.post("/cache/clear")
async def clear_cache(
    request: Request = None,
    cache_service: CacheService = Depends(get_cache_service),
    current_user: User = Depends(get_current_admin)
) -> Dict:
    """Clear all cache entries"""
    try:
        await cache_service.clear_all_cache()
        return {"message": "Cache cleared successfully"}
    except Exception as e:
        logger.exception(f"Error clearing cache: {e}")
        raise HTTPException(status_code=500, detail="Failed to clear cache") 