from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, update
from sqlalchemy.orm import selectinload
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timezone
import logging
from models import (
    User, Transaction, TransactionType,
    Task, TaskCompletion, TaskStatus, TaskType
)
import json

logger = logging.getLogger(__name__)

class ReferralService:
    """Service for handling referral-related functionality"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_referral_info(self, user_id: int) -> Dict:
        """Get detailed referral information for a user"""
        # Get user
        stmt = select(User).filter(User.id == user_id)
        result = await self.db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise ValueError(f"User with ID {user_id} not found")
        
        # Get total referrals
        stmt_total = select(func.count(User.id)).where(User.referred_by == user_id)
        total_referrals = (await self.db.execute(stmt_total)).scalar_one_or_none() or 0

        # Get active referrals
        stmt_active = select(func.count(User.id)).where(
            User.referred_by == user_id,
            User.is_active == True
        )
        active_referrals = (await self.db.execute(stmt_active)).scalar_one_or_none() or 0

        # Get total earnings from referrals
        stmt_earnings = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == user_id,
            Transaction.type == TransactionType.referral_commission
        )
        total_earnings = (await self.db.execute(stmt_earnings)).scalar_one_or_none() or 0.0
        
        return {
            "referral_code": user.referral_code,
            "total_referrals": total_referrals,
            "active_referrals": active_referrals,
            "total_earnings": float(total_earnings)
        }
    
    async def get_referred_users(self, user_id: int) -> List[Dict]:
        """Get list of users referred by a user"""
        # Get referred users
        stmt = select(User).where(User.referred_by == user_id)
        result = await self.db.execute(stmt)
        referred_users = result.scalars().all()
        
        result_list = []
        for user in referred_users:
            # Calculate total spent by user
            stmt_spent = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == user.id,
                Transaction.type.in_([
                    TransactionType.subscription_purchase,
                    TransactionType.subscription_renewal,
                    TransactionType.card_purchase, 
                    TransactionType.card_upgrade,
                    TransactionType.wallet_topup 
                ])
            )
            total_spent = (await self.db.execute(stmt_spent)).scalar_one_or_none() or 0.0

            # Calculate commission earned from this user
            stmt_commission = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == user_id,
                Transaction.type == TransactionType.referral_commission,
                Transaction.description.like(f"%from {user.username}%")
            )
            commission_earned = (await self.db.execute(stmt_commission)).scalar_one_or_none() or 0.0
            
            result_list.append({
                "username": user.username,
                "joined_at": user.created_at,
                "is_active": user.is_active,
                "total_spent": float(total_spent),
                "commission_earned": float(commission_earned),
                "telegram_photo_url": user.telegram_photo_url
            })
        
        return result_list
    
    async def check_referral_code(self, code: str) -> Optional[User]:
        """Check if a referral code is valid and return the user"""
        stmt = select(User).filter(
            User.referral_code == code,
            User.is_active == True
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def process_new_referral(self, referred_user_id: int, referrer_id: int) -> bool:
        """Process a new referral and update referral tasks for the referrer.
        Lazily creates TaskCompletion records if they don't exist."""
        try:
            # 1. Find all active referral tasks in the system
            stmt_tasks = select(Task).where(
                Task.type == TaskType.REFERRAL,
                Task.is_active == True
            )
            result_tasks = await self.db.execute(stmt_tasks)
            active_referral_tasks = result_tasks.scalars().all()

            if not active_referral_tasks:
                logger.info(f"No active referral tasks found. Skipping task processing for referral by user {referrer_id}.")
                return True # No tasks to process, but referral itself is fine

            # 2. Get the referrer's current total referral count (once)
            stmt_count = select(func.count(User.id)).filter(
                User.referred_by == referrer_id,
                # User.is_active == True # Count all referrals, active or not, for progress
            )
            result_count = await self.db.execute(stmt_count)
            referral_count = result_count.scalar_one_or_none() or 0
            logger.info(f"Processing referral for referrer {referrer_id}. Current total referrals: {referral_count}")

            # 3. Process each active referral task for the referrer
            for task in active_referral_tasks:
                # 3a. Find existing TaskCompletion or create a new one
                stmt_completion = select(TaskCompletion).where(
                    TaskCompletion.user_id == referrer_id,
                    TaskCompletion.task_id == task.id
                    # Look for any status initially, we might reactivate a FAILED one potentially?
                    # Or only create if no PENDING/ACTIVE/COMPLETED exists?
                    # Let's stick to finding PENDING/ACTIVE first for simplicity.
                    # TaskCompletion.status.in_([TaskStatus.ACTIVE, TaskStatus.PENDING, TaskStatus.COMPLETED, TaskStatus.FAILED])
                ).order_by(TaskCompletion.id.desc()) # Get the latest if multiple somehow exist
                
                result_completion = await self.db.execute(stmt_completion)
                completion = result_completion.scalar_one_or_none()

                if not completion:
                    # Create a new PENDING completion record - Auto-start!
                    logger.info(f"No existing completion found for user {referrer_id}, task {task.id}. Auto-starting.")
                    # Initialize similar to TaskService.start_task
                    initial_verification_data = json.dumps({"is_verified": False, "last_verified_at": None, "referral_code": None})
                    completion = TaskCompletion(
                        task_id=task.id,
                        user_id=referrer_id,
                        status=TaskStatus.PENDING, # Start as PENDING
                        started_at=datetime.now(timezone.utc),
                        completed_at=None,
                        verification_attempts=0,
                        current_progress=0, # Will be updated below
                        is_claimed=False,
                        claimed_at=None,
                        last_verified_at=None,
                        verification_data=initial_verification_data,
                        verification_cooldown=task.verification_cooldown or 60,
                        max_verification_attempts=task.max_verification_attempts or 3,
                        is_verified=False,
                        verification_method=None,
                        verification_token=None,
                        reward_amount=0.0,
                        streak_day=None, # Not applicable to referral tasks
                        cycle_day=None, # Not applicable
                        reward_multiplier=1.0
                    )
                    self.db.add(completion)
                    await self.db.flush() # Ensure the completion gets an ID before potential status update
                    logger.info(f"Created new TaskCompletion {completion.id} for user {referrer_id}, task {task.id}")
                
                # Ensure we don't update already claimed/completed tasks unless necessary
                if completion.is_claimed:
                    # Optionally update progress even if completed/claimed, but don't change status back
                    completion.current_progress = max(completion.current_progress or 0, referral_count)
                    logger.debug(f"Task {task.id} already claimed for user {referrer_id}. Updating progress only to {completion.current_progress}.")
                    continue # Move to the next task
                
                # 3b. Update progress
                completion.current_progress = referral_count
                logger.debug(f"Updated TaskCompletion {completion.id} progress to {referral_count} for user {referrer_id}, task {task.id}")

                # 3c. Check if target is met and update status if needed
                if referral_count >= task.target_value:
                    # Mark as completed (verification happens on claim? No, referral verification is implicit count check)
                    completion.status = TaskStatus.COMPLETED
                    completion.completed_at = datetime.now(timezone.utc)
                    completion.is_verified = True # Auto-verified based on count
                    logger.info(f"Referral task {task.id} marked as COMPLETED for user {referrer_id}. Progress: {referral_count}/{task.target_value}")
            
            # 4. Commit all changes made in the loop
            await self.db.commit()
            logger.info(f"Successfully processed referral task updates for referrer {referrer_id}.")
            return True
            
        except Exception as e:
            logger.error(f"Error processing referral tasks for referrer {referrer_id}: {str(e)}", exc_info=True)

    async def process_referral_commission(
        self, 
        user_id: int, 
        amount: float, 
        description: str = "Purchase"
    ) -> Dict:
        """
        Process a referral commission when a referred user makes a purchase.
        
        Args:
            user_id: The ID of the user who made the purchase
            amount: The amount of the purchase
            description: Description of the purchase
            
        Returns:
            Dict with status and transaction details if commission was processed
        """
        try:
            # Fetch the user to check if they were referred
            stmt = select(User).filter(User.id == user_id)
            result = await self.db.execute(stmt)
            user = result.scalar_one_or_none()
            
            if not user or not user.referred_by:
                return {"status": "skipped", "reason": "User has no referrer"}
            
            # Get referrer
            referrer_stmt = select(User).filter(User.id == user.referred_by)
            referrer_result = await self.db.execute(referrer_stmt)
            referrer = referrer_result.scalar_one_or_none()
            
            if not referrer or not referrer.is_active:
                return {"status": "skipped", "reason": "Referrer not found or inactive"}
            
            # Calculate commission (default 5% of purchase amount)
            # In a real system, this might be configurable per user or tier-based
            commission_rate = 0.05  # 5%
            commission_amount = amount * commission_rate
            
            # Make sure commission is at least 0.01
            if commission_amount < 0.01:
                return {"status": "skipped", "reason": "Commission amount too small"}
            
            # Round to 2 decimal places
            commission_amount = round(commission_amount, 2)
            
            # Create transaction for the commission
            transaction = Transaction(
                user_id=referrer.id,
                type=TransactionType.referral_commission,
                amount=commission_amount,
                description=f"Referral commission from {user.username}'s {description.lower()}",
                balance_after=referrer.wallet_balance + commission_amount
            )
            
            # Update referrer's wallet balance
            referrer.wallet_balance += commission_amount
            
            # Save changes
            self.db.add(transaction)
            await self.db.commit()
            
            # Update any referral tasks
            await self.process_new_referral(user_id, referrer.id)
            
            return {
                "status": "success",
                "transaction_id": transaction.id,
                "commission_amount": commission_amount,
                "referrer_id": referrer.id
            }
            
        except Exception as e:
            self.logger.error(f"Error processing referral commission: {str(e)}")
            await self.db.rollback()
            return {
                "status": "error",
                "reason": str(e)
            } 