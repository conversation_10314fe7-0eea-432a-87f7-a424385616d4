from typing import Optional, Dict, List, Tuple
import asyncio
import logging
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_
import aiohttp
from models import User, Task, TaskCompletion, TaskStatus, TaskType, RewardType
import os
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
BOT_TOKEN = os.getenv("BOT_TOKEN")

class TelegramError(Exception):
    def __init__(self, message: str, error_code: str):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class TelegramService:
    def __init__(self, db: Session):
        self.db = db
        self.bot_token = BOT_TOKEN
        if not self.bot_token:
            raise ValueError("Telegram BOT_TOKEN not found in environment variables")
        self.api_base = f"https://api.telegram.org/bot{self.bot_token}"
        
    async def verify_channel_membership(self, channel_id: str, user_id: int) -> Dict:
        """Verify if user is a member of the channel"""
        try:
            # Get user's telegram_id
            user = self.db.query(User).filter(User.id == user_id).first()
            logger.info(f"Verifying channel membership for user {user_id} (telegram_id: {user.telegram_id if user else None})")
            
            if not user or not user.telegram_id:
                logger.error(f"User {user_id} has no Telegram ID")
                return {
                    "success": False,
                    "error": "User has no Telegram ID"
                }

            # Clean up channel ID
            channel_username = channel_id.replace("@", "")
            logger.info(f"Checking membership in channel: @{channel_username}")
            
            # Check membership via Telegram API
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_base}/getChatMember"
                params = {
                    "chat_id": f"@{channel_username}",
                    "user_id": int(user.telegram_id)  # Ensure telegram_id is an integer
                }
                
                logger.info(f"Making Telegram API request to {url} with params: {params}")
                
                async with session.get(url, params=params) as response:
                    response_status = response.status
                    logger.info(f"Telegram API response status: {response_status}")
                    
                    response_text = await response.text()
                    logger.info(f"Telegram API response text: {response_text}")
                    
                    try:
                        import json
                        data = json.loads(response_text)
                    except Exception as json_error:
                        logger.error(f"Failed to parse JSON response: {str(json_error)}")
                        data = {"ok": False, "description": "Invalid JSON response"}
                    
                    logger.info(f"Telegram API response: {data}")
                    
                    if not data.get("ok"):
                        error_description = data.get("description", "Unknown error")
                        error_code = data.get("error_code", "unknown")
                        logger.error(f"Telegram API error: Code {error_code} - {error_description}")
                        logger.error(f"Full error response: {data}")
                        
                        # Special handling for "member list is inaccessible" error
                        # This happens when the bot is not an admin of the channel
                        if "member list is inaccessible" in error_description:
                            logger.warning("Cannot verify membership because bot is not an admin of the channel")
                            
                            # Check if we're in test mode
                            test_mode = os.getenv("TEST_MODE", "false").lower() == "true"
                            
                            if test_mode:
                                logger.warning("Running in test mode, assuming verification is successful")
                                return {"success": True, "note": "Test mode: Bypassing verification due to bot permission limitations"}
                        
                        return {
                            "success": False,
                            "error": f"Failed to verify channel membership: {error_description}",
                            "error_code": error_code
                        }
                    
                    result = data.get("result", {})
                    status = result.get("status")
                    logger.info(f"Member status: {status}")
                    logger.info(f"Full member data: {result}")
                    
                    # Valid member statuses: member, administrator, creator
                    if status in ["member", "administrator", "creator"]:
                        logger.info(f"User {user_id} (telegram_id: {user.telegram_id}) is a member of channel @{channel_username}")
                        return {"success": True}
                    
                    logger.error(f"User {user_id} (telegram_id: {user.telegram_id}) is NOT a member of channel @{channel_username}. Status: {status}")
                    return {
                        "success": False,
                        "error": f"You need to join the channel first. Current status: {status}"
                    }
                    
        except Exception as e:
            import traceback
            logger.error(f"Error verifying Telegram membership: {str(e)}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"Failed to verify channel membership: {str(e)}"
            }

    async def verify_task_completion(self, task_completion_id: int) -> dict:
        """Verify if a task is still completed and handle reward revocation if needed"""
        try:
            completion = self.db.query(TaskCompletion).filter(
                TaskCompletion.id == task_completion_id
            ).first()
            
            if not completion or not completion.user:
                return {"success": False, "error": "Task completion not found"}
                
            task = completion.task
            user = completion.user
            
            if not user.telegram_id:
                return {"success": False, "error": "User has no Telegram ID"}
            
            # Skip if task is not a Telegram channel task
            if task.type != TaskType.telegram_channel_join:
                return {"success": True, "message": "Not a Telegram task"}
                
            # Get channel username from social task
            if not task.social_task:
                return {"success": False, "error": "No social task configuration found"}
                
            channel_username = task.social_task.platform_id.replace("@", "")
            
            # Check current membership status
            membership = await self.verify_channel_membership(channel_username, user.telegram_id)
            
            if not membership["success"] and completion.status == TaskStatus.completed:
                # Calculate how long they stayed
                time_stayed = datetime.utcnow() - completion.completed_at
                minimum_duration = timedelta(days=task.minimum_duration or 10)
                
                if time_stayed < minimum_duration and task.allow_reward_revocation:
                    # Revoke reward
                    revocation_result = await self.revoke_reward(completion, time_stayed)
                    if not revocation_result["success"]:
                        return revocation_result
                    
                    # Update task completion status
                    completion.status = TaskStatus.failed
                    completion.last_verified_at = datetime.utcnow()
                    self.db.commit()
                    
                    logger.info(f"Reward revoked for user {user.id} on task {task.id}")
                    return {
                        "success": False,
                        "message": "User left channel before minimum duration",
                        "time_stayed": time_stayed.days
                    }
            
            completion.last_verified_at = datetime.utcnow()
            self.db.commit()
            return {"success": membership["success"], "data": membership.get("data")}
            
        except Exception as e:
            logger.error(f"Error verifying task completion: {str(e)}")
            return {"success": False, "error": str(e)}

    async def revoke_reward(self, completion: TaskCompletion, time_stayed: timedelta) -> dict:
        """Revoke rewards for incomplete task duration"""
        try:
            task = completion.task
            user = completion.user
            
            if task.reward_type == RewardType.wallet_bonus:
                if user.wallet_balance >= task.reward_value:
                    user.wallet_balance -= task.reward_value
                else:
                    return {"success": False, "error": "Insufficient wallet balance"}
                
            # Add revocation record
            revocation = RewardRevocation(
                task_completion_id=completion.id,
                reward_type=task.reward_type,
                revoked_amount=task.reward_value,
                reason=f"Left channel after {time_stayed.days} days",
                wallet_balance_after=user.wallet_balance
            )
            
            self.db.add(revocation)
            self.db.commit()
            
            return {
                "success": True,
                "revoked_amount": task.reward_value,
                "new_balance": user.wallet_balance
            }
            
        except Exception as e:
            logger.error(f"Error revoking reward: {str(e)}")
            self.db.rollback()
            return {"success": False, "error": str(e)}

    async def verify_all_completions(self) -> List[dict]:
        """Verify all telegram channel task completions"""
        results = []
        try:
            completions = self.db.query(TaskCompletion).join(Task).filter(
                and_(
                    Task.type == TaskType.telegram_channel_join,
                    TaskCompletion.status == TaskStatus.completed
                )
            ).all()
            
            for completion in completions:
                result = await self.verify_task_completion(completion.id)
                results.append({
                    "completion_id": completion.id,
                    "result": result
                })
                
            return results
                
        except Exception as e:
            logger.error(f"Error in bulk verification: {str(e)}")
            return [{"error": str(e)}]

    async def start_periodic_verification(self):
        """Start periodic verification of task completions"""
        while True:
            try:
                results = await self.verify_all_completions()
                logger.info(f"Periodic verification completed: {len(results)} tasks checked")
                await asyncio.sleep(3600)  # Check every hour
            except Exception as e:
                logger.error(f"Error in periodic verification: {str(e)}")
                await asyncio.sleep(60)  # Wait a minute before retrying 