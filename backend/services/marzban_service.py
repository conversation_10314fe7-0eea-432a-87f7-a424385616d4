from models import MarzbanPanel
from sqlalchemy.orm import Session
import logging
from datetime import datetime
from cryptography.fernet import Fernet
import base64
import os
from fastapi import HTTPException
import hashlib
import secrets
from dotenv import load_dotenv
import aiohttp
import ssl
import certifi
import json
from typing import Optional, Tuple, Dict, Any

logger = logging.getLogger(__name__)

class MarzbanService:
    def __init__(self, db: Session):
        self.db = db
        self._tokens = {}  # Store tokens per panel
        self._clients = {}  # Initialize _clients dict
        self.ssl_context = ssl.create_default_context(cafile=certifi.where())
        
        # Load secret key from env
        load_dotenv()
        self.secret_key = os.getenv("MARZBAN_SECRET_KEY")
        if not self.secret_key:
            self.secret_key = secrets.token_hex(32)
            with open('.env', 'a') as f:
                f.write(f'\nMARZBAN_SECRET_KEY={self.secret_key}')

    def encrypt_password(self, password: str) -> str:
        """Encrypt password using salt and secret key"""
        try:
            if not password:
                return password
            # Generate salt
            salt = secrets.token_hex(16)
            # Store original password with salt for later recovery
            encoded = base64.b64encode(password.encode()).decode()
            # Create hash for verification
            salted = f"{password}{salt}{self.secret_key}".encode()
            hashed = hashlib.sha256(salted).hexdigest()
            # Return combined string: salt$encoded$hash
            return f"{salt}${encoded}${hashed}"
        except Exception as e:
            logger.error(f"Encryption error: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail="Failed to encrypt password"
            )

    def decrypt_password(self, encrypted_password: str) -> str:
        """Decrypt password and return original value"""
        try:
            if not encrypted_password:
                return encrypted_password
            
            parts = encrypted_password.split('$')
            
            # Handle old format (salt$hash)
            if len(parts) == 2:
                return parts[1]  # Return the original password
            
            # Handle new format (salt$encoded$hash)
            if len(parts) == 3:
                salt, encoded, stored_hash = parts
                # Decode original password
                original_password = base64.b64decode(encoded).decode()
                # Verify hash
                salted = f"{original_password}{salt}{self.secret_key}".encode()
                computed_hash = hashlib.sha256(salted).hexdigest()
                if not secrets.compare_digest(computed_hash, stored_hash):
                    raise ValueError("Password verification failed")
                return original_password
            
            raise ValueError("Invalid password format")
            
        except Exception as e:
            logger.error(f"Decryption error: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail="Failed to decrypt password"
            )

    async def _get_token(self, panel: MarzbanPanel) -> str:
        """Get authentication token from Marzban panel"""
        try:
            async with aiohttp.ClientSession() as session:
                auth_data = {
                    "username": panel.admin_username,
                    "password": self.decrypt_password(panel.admin_password)
                }
                
                async with session.post(
                    f"{panel.api_url.rstrip('/')}/api/admin/token",
                    data=auth_data,
                    ssl=self.ssl_context,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                ) as response:
                    if response.status != 200:
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Authentication failed: {await response.text()}"
                        )
                    
                    data = await response.json()
                    return data.get("access_token")
                    
        except Exception as e:
            logger.error(f"Error getting token: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Failed to get token: {str(e)}")

    async def get_panel_statistics(self, panel: MarzbanPanel) -> Dict[str, Any]:
        """Get panel statistics"""
        try:
            token = await self._get_token(panel)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{panel.api_url.rstrip('/')}/api/system",
                    ssl=self.ssl_context,
                    headers={"Authorization": f"Bearer {token}"}
                ) as response:
                    if response.status != 200:
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Failed to get stats: {await response.text()}"
                        )
                    
                    stats = await response.json()
                    return {
                        "success": True,
                        "message": "Stats retrieved successfully",
                        "stats": {
                            "cpu_usage": stats.get("cpu_percent", 0),
                            "memory_usage": {
                                "used": stats.get("mem_used", 0),
                                "total": stats.get("mem_total", 0),
                                "percent": stats.get("mem_percent", 0)
                            },
                            "network": {
                                "incoming_bandwidth": stats.get("incoming_bandwidth", 0),
                                "outgoing_bandwidth": stats.get("outgoing_bandwidth", 0),
                                "total_bandwidth": stats.get("total_bandwidth", 0)
                            },
                            "users": {
                                "total": stats.get("total_user", 0),
                                "active": stats.get("users_active", 0)
                            },
                            "uptime": stats.get("uptime", 0),
                            "version": stats.get("version", "unknown")
                        }
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get panel statistics: {str(e)}")
            return {
                "success": False,
                "message": str(e),
                "stats": None
            }

    async def get_enabled_inbounds(self, panel: MarzbanPanel) -> Dict[str, Any]:
        """Get enabled inbounds from panel"""
        try:
            token = await self._get_token(panel)
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{panel.api_url.rstrip('/')}/api/inbounds",
                    ssl=self.ssl_context,
                    headers={"Authorization": f"Bearer {token}"}
                ) as response:
                    if response.status != 200:
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Failed to get inbounds: {await response.text()}"
                        )
                    
                    inbounds_data = await response.json()
                    enabled_protocols = {}
                    
                    # Parse the inbounds data structure
                    if isinstance(inbounds_data, dict):
                        # Simply check which protocols are present in inbounds
                        if 'vless' in inbounds_data:
                            enabled_protocols['vless'] = {
                                "tags": [inb.get('tag') for inb in inbounds_data['vless'] if inb.get('tag')]
                            }
                        if 'vmess' in inbounds_data:
                            enabled_protocols['vmess'] = {
                                "tags": [inb.get('tag') for inb in inbounds_data['vmess'] if inb.get('tag')]
                            }
                        if 'trojan' in inbounds_data:
                            enabled_protocols['trojan'] = {
                                "tags": [inb.get('tag') for inb in inbounds_data['trojan'] if inb.get('tag')]
                            }
                        if 'shadowsocks' in inbounds_data:
                            enabled_protocols['shadowsocks'] = {
                                "tags": [inb.get('tag') for inb in inbounds_data['shadowsocks'] if inb.get('tag')]
                            }
                        
                        logger.info(f"Found enabled protocols and tags: {enabled_protocols}")
                        return enabled_protocols
                    
                    raise HTTPException(
                        status_code=500,
                        detail="Invalid inbounds data format from server"
                    )
                    
        except Exception as e:
            logger.error(f"Error getting enabled inbounds: {str(e)}")
            raise HTTPException(
                status_code=500, 
                detail=f"Failed to get enabled inbounds: {str(e)}"
            )

    async def create_user(
        self,
        panel: MarzbanPanel,
        username: str,
        proxies: list,
        data_limit: int,
        expire_days: int
    ) -> Dict[str, Any]:
        """Create a new user in Marzban panel with proper expiry date"""
        try:
            token = await self._get_token(panel)
            
            # Calculate expire date in seconds from now
            current_timestamp = int(datetime.now().timestamp())
            expire_timestamp = current_timestamp + (expire_days * 24 * 60 * 60)
            
            user_data = {
                "username": username,
                "proxies": {proxy: {} for proxy in proxies},
                "data_limit": data_limit * 1024 * 1024 * 1024,  # Convert GB to bytes
                "expire": expire_timestamp
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{panel.api_url.rstrip('/')}/api/user",
                    json=user_data,
                    ssl=self.ssl_context,
                    headers={"Authorization": f"Bearer {token}"}
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"Failed to create user: {error_text}")
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Failed to create user: {error_text}"
                        )
                    
                    response_data = await response.json()
                    
                    return {
                        "username": username,
                        "subscription_url": response_data["subscription_url"],
                        "data_limit": data_limit,
                        "expire_days": expire_days
                    }
                    
        except Exception as e:
            logger.error(f"Error creating user: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating user: {str(e)}"
            )

    async def cleanup(self):
        """Properly close all API clients"""
        if hasattr(self, '_clients'):
            for client in self._clients.values():
                if client and hasattr(client, 'close'):
                    try:
                        await client.close()
                    except Exception as e:
                        logger.error(f"Error closing client: {str(e)}")
            self._clients.clear()
        if hasattr(self, '_tokens'):
            self._tokens.clear()

    def __del__(self):
        """Cleanup handler for garbage collection"""
        try:
            if hasattr(self, '_clients') and self._clients:
                logger.warning("MarzbanService destroyed with active clients")
        except Exception as e:
            # Just log any errors during cleanup
            logger.error(f"Error in MarzbanService.__del__: {str(e)}")

    async def get_system_stats_direct(self, panel: MarzbanPanel) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """Get system stats directly using aiohttp without marzban library"""
        try:
            # Decrypt password for authentication
            decrypted_password = self.decrypt_password(panel.admin_password)
            
            # Configure SSL context
            ssl_context = ssl.create_default_context(cafile=certifi.where())
            
            async with aiohttp.ClientSession() as session:
                # First get the token
                token_url = f"{panel.api_url.rstrip('/')}/api/admin/token"
                
                # Format auth data according to API requirements
                auth_data = {
                    "form_params": {
                        "username": panel.admin_username,
                        "password": decrypted_password
                    }
                }
                
                # Get token using form data
                async with session.post(
                    token_url,
                    data=auth_data["form_params"],  # Use data instead of json for form submission
                    ssl=ssl_context,
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded"
                    }
                ) as token_response:
                    if token_response.status != 200:
                        error_text = await token_response.text()
                        logger.error(f"Auth failed response: {error_text}")
                        return False, f"Authentication failed: {error_text}", None
                        
                    token_data = await token_response.json()
                    access_token = token_data.get("access_token")
                    
                    if not access_token:
                        return False, "No access token in response", None
                
                # Get system stats
                stats_url = f"{panel.api_url.rstrip('/')}/api/system"
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
                
                async with session.get(
                    stats_url,
                    ssl=ssl_context,
                    headers=headers
                ) as stats_response:
                    if stats_response.status != 200:
                        error_text = await stats_response.text()
                        return False, f"Failed to get stats: {error_text}", None
                        
                    stats_data = await stats_response.json()
                    
                    # Format the response based on OpenAPI spec
                    formatted_stats = {
                        "cpu_usage": stats_data.get("cpu_percent", 0),
                        "memory_usage": {
                            "used": stats_data.get("mem_used", 0),
                            "total": stats_data.get("mem_total", 0),
                            "percent": round((stats_data.get("mem_used", 0) / stats_data.get("mem_total", 1)) * 100, 1)
                        },
                        "network": {
                            "incoming_bandwidth": stats_data.get("incoming_bandwidth", 0),
                            "outgoing_bandwidth": stats_data.get("outgoing_bandwidth", 0),
                            "total_bandwidth": stats_data.get("total_bandwidth", 0)
                        },
                        "users": {
                            "total": stats_data.get("total_user", 0),
                            "active": stats_data.get("users_active", 0)
                        },
                        "uptime": stats_data.get("uptime", 0),
                        "version": stats_data.get("version", "unknown")
                    }
                    
                    return True, "Stats retrieved successfully", formatted_stats
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error getting stats: {str(e)}")
            return False, f"Network error: {str(e)}", None
        except Exception as e:
            logger.error(f"Error getting system stats: {str(e)}")
            return False, f"Error: {str(e)}", None

    async def get_user_usage_from_sub(self, subscription_url: str) -> Dict[str, Any]:
        """Get user usage statistics from subscription URL"""
        try:
            # Extract base URL and token from subscription URL
            # Format: https://domain:port/sub/token
            parts = subscription_url.split('/sub/')
            if len(parts) != 2:
                return {
                    "success": False,
                    "message": "Invalid subscription URL format",
                    "data": None
                }
            
            base_url = parts[0]
            token = parts[1]

            # The correct endpoint for subscription usage is /sub/{token}/usage
            usage_url = f"{base_url}/sub/{token}/usage"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    usage_url,
                    ssl=self.ssl_context
                ) as response:
                    if response.status != 200:
                        return {
                            "success": False,
                            "message": f"Failed to get usage data: {await response.text()}",
                            "data": None
                        }
                    
                    usage_data = await response.json()
                    
                    return {
                        "success": True,
                        "message": "Usage data retrieved successfully",
                        "data": {
                            "used_traffic": usage_data.get("used_traffic", 0),
                            "total_traffic": usage_data.get("total", 0),  # Changed from total_traffic to total
                            "expire_date": usage_data.get("expire", None),
                            "status": usage_data.get("status", "unknown")
                        }
                    }
                    
        except Exception as e:
            logger.error(f"Error getting user usage: {str(e)}")
            return {
                "success": False,
                "message": str(e),
                "data": None
            }

def get_marzban_service(db: Session) -> MarzbanService:
    return MarzbanService(db)
