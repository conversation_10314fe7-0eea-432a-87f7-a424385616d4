from fastapi import HTT<PERSON>Ex<PERSON>, status
from redis import Redis
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
import json
import logging
import hashlib
from models import SecurityLog
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_, select, func
import os
from config.rate_limit_config import RATE_LIMIT_CONFIG

logger = logging.getLogger(__name__)

class RateLimitExceeded(Exception):
    def __init__(self, message: str, reset_after: int, limit_info: Dict[str, Any]):
        self.message = message
        self.reset_after = reset_after
        self.limit_info = limit_info
        super().__init__(self.message)

class RateLimitService:
    """Enhanced rate limiting service with security features"""
    
    def __init__(self, redis_client: Redis, db: Session):
        self.db = db
        self.redis = redis_client
        self.enabled = os.getenv("DISABLE_RATE_LIMIT", "false").lower() != "true"
        self.REDIS_RATELIMIT_PREFIX = "ratelimit:"
        self.REDIS_SUSPICIOUS_PREFIX = "suspicious:ip:"
        self.limits = RATE_LIMIT_CONFIG
        logger.info(f"Rate limit service initialized with enabled={self.enabled}")

    async def initialize(self):
        """Initialize rate limit service"""
        try:
            # Initialize Redis connection
            if not self.redis:
                logger.error("Redis client not initialized")
                return False
                
            # Set default configuration
            self.limits = RATE_LIMIT_CONFIG
            
            # Initialize Redis with configuration
            for key, config in self.limits.items():
                redis_key = f"{self.REDIS_RATELIMIT_PREFIX}:config:{key}"
                try:
                    # Store the rate limit config in Redis
                    # Check if we're using async or sync Redis client
                    if hasattr(self.redis, 'setex'):
                        # Sync Redis client
                        self.redis.setex(
                            redis_key,
                            3600,  # 1 hour TTL
                            json.dumps(config)
                        )
                    else:
                        # Async Redis client
                        await self.redis.set(
                            redis_key,
                            json.dumps(config),
                            ex=3600  # 1 hour TTL
                        )
                except Exception as e:
                    logger.error(f"Failed to set Redis config for {key}: {str(e)}")
                    continue
            
            logger.info("Rate limit service initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize rate limit service: {str(e)}")
            return False

    def _get_client_fingerprint(self, ip: str, user_agent: Optional[str] = None) -> str:
        """Generate a unique client fingerprint"""
        components = [ip]
        if user_agent:
            # Extract only browser info from user agent to reduce fingerprint variance
            # This prevents minor changes in user agent from creating new fingerprints
            if "Telegram" in user_agent:
                # For Telegram WebApp requests, use a more lenient fingerprint
                # Just use IP + "TelegramWebApp" to avoid over-limiting
                components = [ip, "TelegramWebApp"]
            else:
                components.append(user_agent)
        fingerprint = hashlib.sha256(''.join(components).encode()).hexdigest()
        return fingerprint

    async def _track_ip_request(self, ip: str) -> bool:
        """Track requests from an IP address to detect distributed attacks"""
        try:
            # Use a separate Redis key for tracking IPs across different user agents
            ip_key = f"{self.REDIS_SUSPICIOUS_PREFIX}requests:{ip}"
            
            # Get current count or initialize
            count = await self.redis.get(ip_key)
            count = int(count) if count else 0
            
            # Increment count
            count += 1
            
            # Store with 5-minute TTL
            await self.redis.setex(ip_key, 300, str(count))
            
            # Check if this IP is making too many requests
            if count > 20:  # Threshold for suspicious activity
                logger.warning(f"Suspicious activity detected from IP {ip}: {count} requests in 5 minutes")
                return True
                
            return False
        except Exception as e:
            logger.error(f"Error tracking IP requests: {str(e)}")
            return False

    async def _is_suspicious_ip(self, ip: str) -> bool:
        """Check if IP shows suspicious behavior using Redis"""
        try:
            # Check Redis first for cached suspicious status
            suspicious_key = f"{self.REDIS_SUSPICIOUS_PREFIX}{ip}"
            cached_data = await self.redis.get(suspicious_key)
            if cached_data:
                return True
                
            # Check if this IP is making too many requests
            if await self._track_ip_request(ip):
                # Cache suspicious IP in Redis for 1 hour
                await self.redis.setex(suspicious_key, 3600, "1")
                return True

            # Check recent failed attempts from security logs
            count_stmt = (
                select(func.count())
                .select_from(SecurityLog)
                .where(
                SecurityLog.ip_address == ip,
                SecurityLog.status == "failure",
                SecurityLog.created_at >= datetime.utcnow() - timedelta(hours=1)
                )
            )
            result = await self.db.scalar(count_stmt)
            
            is_suspicious = result >= 50  # More than 50 failed attempts in an hour
            if is_suspicious:
                # Cache suspicious IP in Redis for 1 hour
                await self.redis.setex(suspicious_key, 3600, "1")
            
            return is_suspicious
            
        except Exception as e:
            logger.error(f"Error checking suspicious IP: {str(e)}")
            return False

    async def check_rate_limit(
        self,
        action: str,
        identifier: Optional[str] = None,
        ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        raise_error: bool = True
    ) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Check rate limit for an action"""
        try:
            if not self.enabled:
                logger.debug("Rate limiting disabled by configuration")
                return True, {"rate_limiting": "disabled"}

            limit_config = self.limits.get(action)
            if not limit_config:
                logger.debug(f"No rate limit configuration for action: {action}")
                return True, None

            # Special handling for Telegram login (more logging for debugging)
            is_telegram_login = "telegram" in action.lower() and "login" in action.lower()
            
            # Check if IP is suspicious
            if ip and await self._is_suspicious_ip(ip):
                logger.warning(f"Suspicious IP detected: {ip} for action {action}")
                await self._log_security_event(action, ip, user_agent, "blocked", "high", identifier)
                if raise_error:
                    raise RateLimitExceeded(
                        "Too many failed attempts",
                        3600,
                        {"suspicious": True, "limit": limit_config.get("attempts"), "window": limit_config.get("window")}
                    )
                return False, {
                    "suspicious": True, 
                    "limit": limit_config.get("attempts"), 
                    "window": limit_config.get("window"),
                    "reset_after": 3600
                }

            # Create Redis key
            key_parts = [self.REDIS_RATELIMIT_PREFIX, action]
            if identifier:
                key_parts.append(f"user:{identifier}")
            if ip:
                fingerprint = self._get_client_fingerprint(ip, user_agent)
                key_parts.append(f"fp:{fingerprint}")
            redis_key = ":".join(key_parts)
            
            if is_telegram_login:
                logger.debug(f"Telegram login rate limit Redis key: {redis_key}, IP: {ip}, User-Agent: {user_agent[:50]}...")

            now = datetime.utcnow().timestamp()
            data = await self.redis.get(redis_key)

            if data:
                try:
                    data = json.loads(data)
                    window_start = data["timestamp"]
                    current_count = data["count"]
                    
                    if is_telegram_login:
                        logger.debug(f"Telegram login rate limit data found: {data}")

                    # Check block status
                    if data.get("blocked_until", 0) > now:
                        logger.warning(f"Access blocked for {action} until {datetime.fromtimestamp(data['blocked_until']).isoformat()}")
                        await self._log_security_event(action, ip, user_agent, "blocked", "medium", identifier)
                        block_time_remaining = int(data["blocked_until"] - now)
                        if raise_error:
                            raise RateLimitExceeded(
                                "Access temporarily blocked",
                                block_time_remaining,
                                {
                                    "blocked": True, 
                                    "blocked_until": data["blocked_until"],
                                    "limit": limit_config.get("attempts"),
                                    "window": limit_config.get("window")
                                }
                            )
                        return False, {
                            "blocked": True, 
                            "blocked_until": data["blocked_until"],
                            "limit": limit_config.get("attempts"),
                            "window": limit_config.get("window"),
                            "reset_after": block_time_remaining
                        }

                    # Check window expiry
                    if now - window_start >= limit_config["window"]:
                        if is_telegram_login:
                            logger.debug(f"Telegram login rate limit window expired, resetting counter for {redis_key}")
                        data = {"count": 1, "timestamp": now}
                        current_count = 1
                    else:
                        current_count += 1
                        data["count"] = current_count
                        if is_telegram_login:
                            logger.debug(f"Incremented Telegram login rate limit counter: {current_count}/{limit_config['attempts']} for {redis_key}")
                except Exception as e:
                    logger.error(f"Error parsing rate limit data: {e}")
                    data = {"count": 1, "timestamp": now}
                    current_count = 1
            else:
                if is_telegram_login:
                    logger.debug(f"No rate limit data found for Telegram login, initializing for {redis_key}")
                data = {"count": 1, "timestamp": now}
                current_count = 1

            # Check limit exceeded
            if current_count >= limit_config["attempts"]:
                logger.warning(f"Rate limit exceeded for {action}: {current_count}/{limit_config['attempts']}")
                
                block_duration = limit_config.get("block_duration", limit_config["window"])
                blocked_until = now + block_duration
                data["blocked_until"] = blocked_until
                
                if limit_config.get("block_duration"):
                    logger.warning(f"Blocking access for {action} until {datetime.fromtimestamp(blocked_until).isoformat()}")
                    await self._log_security_event(action, ip, user_agent, "blocked", "medium", identifier, {"reason": "rate_limit_exceeded"})

                # Store updated data in Redis even when blocked
                await self.redis.setex(
                    redis_key,
                    limit_config["window"] + block_duration,
                    json.dumps(data)
                )

                if raise_error:
                    raise RateLimitExceeded(
                        f"Rate limit exceeded for {action}",
                        block_duration,
                        {
                            "current": current_count, 
                            "limit": limit_config["attempts"],
                            "window": limit_config["window"],
                            "blocked_until": blocked_until
                        }
                    )
                return False, {
                    "current": current_count,
                    "limit": limit_config["attempts"],
                    "window": limit_config["window"],
                    "reset_after": block_duration,
                    "blocked_until": blocked_until
                }

            # Store updated data in Redis
            await self.redis.setex(
                redis_key,
                limit_config["window"],
                json.dumps(data)
            )

            # Log successful attempt
            await self._log_security_event(action, ip, user_agent, "success", "low", identifier)

            # Calculate remaining window time
            remaining_window = int(limit_config["window"] - (now - data["timestamp"]))
            
            return True, {
                "current": current_count,
                "limit": limit_config["attempts"],
                "remaining": limit_config["attempts"] - current_count,
                "reset_after": remaining_window,
                "window": limit_config["window"]
            }

        except RateLimitExceeded:
            raise
        except Exception as e:
            logger.error(f"Rate limit check error: {str(e)}")
            return True, None  # Fail open to prevent blocking legitimate traffic

    async def _log_security_event(
        self,
        action: str,
        ip: Optional[str],
        user_agent: Optional[str],
        status: str,
        risk_level: str,
        identifier: Optional[str] = None,
        details: Optional[Dict] = None
    ) -> None:
        """Log security event to database"""
        try:
            log_entry = SecurityLog(
                action_type=action,
                ip_address=ip or "unknown",
                user_agent=user_agent,
                status=status,
                risk_level=risk_level,
                details={
                    "identifier": identifier,
                    **(details or {})
                },
                created_at=datetime.utcnow()
            )
            self.db.add(log_entry)
            self.db.flush()  # Use flush instead of commit for better performance
        except Exception as e:
            logger.error(f"Error logging security event: {str(e)}")

    async def log_security_event(
        self,
        action: str,
        ip: Optional[str],
        user_agent: Optional[str],
        status: str,
        risk_level: str,
        identifier: Optional[str] = None,
        details: Optional[Dict] = None
    ) -> None:
        return await self._log_security_event(action, ip, user_agent, status, risk_level, identifier, details)

    async def get_rate_limit_status(
        self,
        action: str,
        identifier: Optional[str] = None,
        ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get current rate limit status from Redis"""
        try:
            limit_config = self.limits.get(action)
            if not limit_config:
                return {
                    "enabled": False,
                    "limit": None,
                    "remaining": None,
                    "reset_after": None
                }

            # Create Redis key
            key_parts = [self.REDIS_RATELIMIT_PREFIX, action]
            if identifier:
                key_parts.append(f"user:{identifier}")
            if ip:
                fingerprint = self._get_client_fingerprint(ip, user_agent)
                key_parts.append(f"fp:{fingerprint}")
            redis_key = ":".join(key_parts)
            
            logger.debug(f"Getting rate limit status for key: {redis_key}")

            data = await self.redis.get(redis_key)
            now = datetime.utcnow().timestamp()

            if data:
                try:
                    data = json.loads(data)
                    window_start = data["timestamp"]
                    current_count = data["count"]
                    
                    logger.debug(f"Rate limit status data: {data}")

                    # Check block status
                    if data.get("blocked_until", 0) > now:
                        block_time_remaining = int(data["blocked_until"] - now)
                        return {
                            "enabled": True,
                            "limit": limit_config["attempts"],
                            "remaining": 0,
                            "reset_after": block_time_remaining,
                            "blocked": True,
                            "blocked_until": datetime.fromtimestamp(data["blocked_until"]).isoformat(),
                            "window": limit_config["window"]
                        }

                    # Calculate remaining window time
                    window_end = window_start + limit_config["window"]
                    if now < window_end:
                        remaining_time = int(window_end - now)
                        remaining_attempts = max(0, limit_config["attempts"] - current_count)
                        return {
                            "enabled": True,
                            "limit": limit_config["attempts"],
                            "remaining": remaining_attempts,
                            "reset_after": remaining_time,
                            "window": limit_config["window"]
                        }
                except Exception as e:
                    logger.error(f"Error parsing rate limit status data: {e}")

            # No data or window expired
            return {
                "enabled": True,
                "limit": limit_config["attempts"],
                "remaining": limit_config["attempts"],
                "reset_after": 0,
                "window": limit_config["window"]
            }

        except Exception as e:
            logger.error(f"Error getting rate limit status: {str(e)}")
            return {
                "enabled": True,
                "error": "Failed to get status",
                "limit": None,
                "remaining": None,
                "reset_after": None
            }

    def enable(self):
        """Enable rate limiting"""
        self.enabled = True
        logger.info("Rate limiting enabled")

    def disable(self):
        """Disable rate limiting"""
        self.enabled = False
        logger.info("Rate limiting disabled")

    async def log_attempt(
        self,
        action: str,
        identifier: str,
        success: bool,
        ip: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> None:
        """Log rate limit attempt"""
        try:
            # Store attempt in Redis for rate limiting
            key = f"{self.REDIS_RATELIMIT_PREFIX}attempts:{action}:{identifier}"
            data = {
                "timestamp": datetime.utcnow().isoformat(),
                "success": success,
                "ip": ip,
                "user_agent": user_agent
            }
            await self.redis.lpush(key, json.dumps(data))
            await self.redis.ltrim(key, 0, 99)  # Keep last 100 attempts
            
        except Exception as e:
            logger.error(f"Error logging attempt: {str(e)}") 