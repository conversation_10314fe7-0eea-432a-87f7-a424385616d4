from typing import Optional, Any, Dict, Union
import json
from datetime import datetime, timedelta
import logging
from config.redis_config import RedisClient
from fastapi import HTTPException
import pickle

logger = logging.getLogger(__name__)

class CacheService:
    """Service for handling Redis caching with proper error handling and monitoring"""
    
    def __init__(self, redis_client: RedisClient):
        self.redis = redis_client
        self.logger = logging.getLogger(__name__)
        
        # Default cache times
        self.DEFAULT_CACHE_TIME = 300  # 5 minutes
        self.TASK_CACHE_TIME = 1800    # 30 minutes
        self.USER_CACHE_TIME = 600     # 10 minutes
        self.STATS_CACHE_TIME = 3600   # 1 hour

    async def initialize(self):
        """Initialize cache service"""
        try:
            # Test Redis connection
            client = await self.redis.get_client()
            await client.ping()
            
            self.logger.info("Cache service initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize cache service: {str(e)}")
            return False
        
    async def get_cached_data(self, key: str, default: Any = None) -> Optional[Any]:
        """Get data from cache with error handling"""
        try:
            client = await self.redis.get_client()
            data = await client.get(key)
            
            if not data:
                return default
                
            try:
                # Try JSON first
                return json.loads(data)
            except json.JSONDecodeError:
                try:
                    # Try pickle if JSON fails
                    return pickle.loads(data)
                except:
                    # Return raw data if both fail
                    return data
                    
        except Exception as e:
            self.logger.error(f"Cache get error for key {key}: {str(e)}")
            return default

    async def set_cached_data(
        self,
        key: str,
        data: Any,
        expire_time: int = None,
        use_pickle: bool = False
    ) -> bool:
        """Set data in cache with proper serialization"""
        try:
            client = await self.redis.get_client()
            
            # Determine expiration time
            if expire_time is None:
                expire_time = self.DEFAULT_CACHE_TIME
            
            # Serialize data
            if use_pickle:
                serialized = pickle.dumps(data)
            else:
                try:
                    serialized = json.dumps(data)
                except (TypeError, ValueError):
                    serialized = pickle.dumps(data)
                    use_pickle = True
            
            # Store in Redis
            await client.set(
                key,
                serialized,
                ex=expire_time
            )
            
            self.logger.debug(
                f"Cached data for key {key} (expires in {expire_time}s, pickle={use_pickle})"
            )
            return True
            
        except Exception as e:
            self.logger.error(f"Cache set error for key {key}: {str(e)}")
            return False

    async def invalidate_cache(self, key: str) -> bool:
        """Remove data from cache"""
        try:
            client = await self.redis.get_client()
            await client.delete(key)
            return True
        except Exception as e:
            self.logger.error(f"Cache invalidation error for key {key}: {str(e)}")
            return False

    async def get_or_set_cache(
        self,
        key: str,
        data_func: callable,
        expire_time: int = None,
        use_pickle: bool = False
    ) -> Any:
        """Get data from cache or compute and store it"""
        try:
            # Try to get from cache first
            cached_data = await self.get_cached_data(key)
            if cached_data is not None:
                return cached_data
            
            # If not in cache, compute data
            computed_data = await data_func()
            
            # Store in cache
            await self.set_cached_data(
                key,
                computed_data,
                expire_time,
                use_pickle
            )
            
            return computed_data
            
        except Exception as e:
            self.logger.error(f"Cache get_or_set error for key {key}: {str(e)}")
            raise

    async def clear_user_cache(self, user_id: int) -> bool:
        """Clear all cache entries for a user"""
        try:
            client = await self.redis.get_client()
            pattern = f"user:{user_id}:*"
            
            # Get all keys matching pattern
            keys = await client.keys(pattern)
            
            if keys:
                await client.delete(*keys)
                self.logger.info(f"Cleared {len(keys)} cache entries for user {user_id}")
            
            return True
        except Exception as e:
            self.logger.error(f"Error clearing user cache for user {user_id}: {str(e)}")
            return False

    async def clear_task_cache(self, task_id: int) -> bool:
        """Clear all cache entries for a task"""
        try:
            client = await self.redis.get_client()
            pattern = f"task:{task_id}:*"
            
            # Get all keys matching pattern
            keys = await client.keys(pattern)
            
            if keys:
                await client.delete(*keys)
                self.logger.info(f"Cleared {len(keys)} cache entries for task {task_id}")
            
            return True
        except Exception as e:
            self.logger.error(f"Error clearing task cache for task {task_id}: {str(e)}")
            return False

    async def get_cache_stats(self) -> Dict:
        """Get cache statistics"""
        try:
            client = await self.redis.get_client()
            info = await client.info()
            
            return {
                "used_memory": info.get("used_memory_human"),
                "connected_clients": info.get("connected_clients"),
                "total_keys": await client.dbsize(),
                "uptime_days": info.get("uptime_in_days"),
                "hit_rate": info.get("keyspace_hits", 0) / (info.get("keyspace_hits", 0) + info.get("keyspace_misses", 1)),
                "evicted_keys": info.get("evicted_keys", 0)
            }
        except Exception as e:
            self.logger.error(f"Error getting cache stats: {str(e)}")
            return {} 