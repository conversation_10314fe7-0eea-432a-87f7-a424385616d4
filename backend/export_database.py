import os
import sys
import json
from datetime import datetime

# Add the parent directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

import pandas as pd
import numpy as np
from sqlalchemy import create_engine, text, inspect, MetaData, Table
from sqlalchemy.orm import sessionmaker
from database import engine, DATABASE_URL
import logging

logger = logging.getLogger(__name__)

def get_table_data(table_name, connection):
    """Get all data from a table using pandas"""
    try:
        # Read table data into pandas DataFrame
        df = pd.read_sql_query(f'SELECT * FROM {table_name}', connection)
        
        # Convert datetime columns to string
        for col in df.select_dtypes(include=['datetime64']).columns:
            df[col] = df[col].dt.strftime('%Y-%m-%d %H:%M:%S')
        
        # Convert any JSON columns
        for col in df.select_dtypes(include=['object']).columns:
            df[col] = df[col].apply(lambda x: json.dumps(x, ensure_ascii=False) if isinstance(x, (dict, list)) else x)
        
        # Replace None/NaN with empty string
        df = df.replace({np.nan: '', None: ''})
        
        return df
    except Exception as e:
        logger.error(f"Error getting data from table {table_name}: {str(e)}")
        return pd.DataFrame()

def export_database():
    """Export all database data to Excel using pandas"""
    try:
        logging.basicConfig(level=logging.INFO)
        
        # Create output directory if it doesn't exist
        output_dir = os.path.join(parent_dir, 'exports')
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f'database_backup_{timestamp}.xlsx')
        
        # Create Excel writer
        writer = pd.ExcelWriter(
            output_file,
            engine='xlsxwriter',
            engine_kwargs={'options': {'strings_to_urls': False}}
        )
        
        # Get database connection
        connection = engine.connect()
        inspector = inspect(engine)
        
        # Get all tables
        tables = [
            'users',
            'transactions',
            'vpn_packages',
            'vpn_subscriptions',
            'marzban_panels',
            'package_panel_association',
            'tasks',
            'task_completions',
            'task_verifications',
            'social_tasks',
            'reward_revocations',
            'system_configs'
        ]
        
        # Create summary data
        summary_data = []
        
        # Export each table
        for table_name in tables:
            logger.info(f"Exporting table: {table_name}")
            
            try:
                # Get table data
                df = get_table_data(table_name, connection)
                
                if not df.empty:
                    # Write to Excel
                    df.to_excel(
                        writer,
                        sheet_name=table_name,
                        index=False,
                        freeze_panes=(1, 0)
                    )
                    
                    # Get workbook and worksheet
                    workbook = writer.book
                    worksheet = writer.sheets[table_name]
                    
                    # Add formats
                    header_format = workbook.add_format({
                        'bold': True,
                        'bg_color': '#0066cc',
                        'font_color': 'white',
                        'border': 1,
                        'text_wrap': True
                    })
                    
                    # Format headers
                    for col_num, value in enumerate(df.columns.values):
                        worksheet.write(0, col_num, value, header_format)
                        # Set column width
                        max_length = max(
                            df[value].astype(str).apply(len).max(),
                            len(str(value))
                        )
                        worksheet.set_column(col_num, col_num, min(max_length + 2, 50))
                    
                    # Add to summary
                    summary_data.append({
                        'Table Name': table_name,
                        'Records': len(df),
                        'Columns': len(df.columns),
                        'Size (KB)': df.memory_usage(deep=True).sum() / 1024
                    })
                    
                    logger.info(f"Exported {len(df)} records from {table_name}")
                
            except Exception as e:
                logger.error(f"Error exporting table {table_name}: {str(e)}")
                continue
        
        # Create and write summary sheet
        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(
            writer,
            sheet_name='Summary',
            index=False
        )
        
        # Format summary sheet
        summary_sheet = writer.sheets['Summary']
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#0066cc',
            'font_color': 'white',
            'border': 1
        })
        
        for col_num, value in enumerate(summary_df.columns.values):
            summary_sheet.write(0, col_num, value, header_format)
            summary_sheet.set_column(col_num, col_num, 15)
        
        # Save and close
        writer.close()
        
        # Verify file size
        file_size = os.path.getsize(output_file) / (1024 * 1024)  # Size in MB
        logger.info(f"Export completed. File size: {file_size:.2f} MB")
        logger.info(f"Export saved to: {output_file}")
        
        # Print summary
        print("\nExport Summary:")
        print(f"Total tables: {len(tables)}")
        print(f"Total size: {file_size:.2f} MB")
        print(f"Location: {output_file}")
        
    except Exception as e:
        logger.error(f"Failed to export database: {str(e)}")
        raise
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    export_database() 