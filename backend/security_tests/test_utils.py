#!/usr/bin/env python
"""
Test Utilities
-------------
Utility functions for security tests.
"""
import logging
import sys
import requests
import json
import time
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path
import colorama
from colorama import Fore, Style
from datetime import datetime
import os
import traceback

# Configure logging with more detailed format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/security_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Initialize colorama for colored output
colorama.init()

def print_header(message: str) -> None:
    """Print a header message with enhanced formatting"""
    print(f"\n{Fore.CYAN}{'=' * 80}")
    print(f"{message}")
    print(f"{'=' * 80}{Style.RESET_ALL}")

def print_success(message: str) -> None:
    """Print a success message"""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_error(message: str) -> None:
    """Print an error message"""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def print_warning(message: str) -> None:
    """Print a warning message"""
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

def print_info(message: str) -> None:
    """Print an info message"""
    print(f"{Fore.BLUE}ℹ {message}{Style.RESET_ALL}")

def log_request_details(request, response) -> None:
    """Log detailed request and response information"""
    # Handle dictionary responses from make_request function
    if isinstance(response, dict):
        logger.info(f"Response Status: {response.get('status_code')}")
        logger.info(f"Response Headers: {response.get('headers', {})}")
        try:
            logger.info(f"Response Body: {response.get('data', '')}")
        except:
            logger.info("Response Body: <not JSON>")
        return
        
    # Handle regular requests.Response objects
    if hasattr(request, 'url'):
        logger.info(f"Request URL: {request.url}")
        logger.info(f"Request Method: {request.method}")
        logger.info(f"Request Headers: {dict(request.headers)}")
        if hasattr(request, 'body') and request.body:
            try:
                logger.info(f"Request Body: {request.body.decode()}")
            except:
                logger.info("Request Body: <binary>")
    
    if hasattr(response, 'status_code'):
        logger.info(f"Response Status: {response.status_code}")
        logger.info(f"Response Headers: {dict(response.headers)}")
        try:
            logger.info(f"Response Body: {response.json()}")
        except:
            logger.info("Response Body: <not JSON>")

def log_error_with_traceback(error: Exception, context: str = "") -> None:
    """Log error with full traceback and context"""
    logger.error(f"Error in {context}: {str(error)}")
    logger.error("Traceback:")
    logger.error(traceback.format_exc())

def get_csrf_token(url: str, headers: Dict = None) -> str:
    """Get CSRF token by making a GET request and extracting the token from cookies"""
    try:
        # Handle URLs that don't start with http
        if not url.startswith('http'):
            from test_config import API_BASE_URL
            # Don't add API_BASE_URL if the URL is already a full path
            if not url.startswith(API_BASE_URL):
                url = f"{API_BASE_URL}{url}"
        
        # Add default headers if not provided
        if headers is None:
            headers = {}
        if 'User-Agent' not in headers:
            headers['User-Agent'] = 'SecurityTestAgent/1.0'
        
        # Make a GET request to get the CSRF cookie
        logger.info(f"Getting CSRF token from {url}")
        response = requests.get(url, headers=headers)
        
        # Extract the CSRF token from cookies
        cookies = response.cookies
        csrf_token = cookies.get("csrf_token")
        
        if csrf_token:
            logger.info(f"Retrieved CSRF token: {csrf_token[:5]}...")
            return csrf_token
        else:
            logger.warning(f"No CSRF token found in response cookies from {url}")
            return ""
    except Exception as e:
        logger.error(f"Error getting CSRF token: {str(e)}")
        return ""

def make_request(url: str, method: str = "GET", data: Dict = None, headers: Dict = None, csrf_token: str = None) -> Tuple[bool, Dict[str, Any]]:
    """Make a request to the API"""
    try:
        # Handle URLs that don't start with http
        if not url.startswith('http'):
            from test_config import API_BASE_URL
            # Don't add API_BASE_URL if the URL is already a full path
            if not url.startswith(API_BASE_URL):
                url = f"{API_BASE_URL}{url}"
        
        # Add bot detection test headers if not present
        if headers is None:
            headers = {}
        if 'User-Agent' not in headers:
            headers['User-Agent'] = 'SecurityTestAgent/1.0'
        
        # For non-GET requests, get a CSRF token if not provided and if the URL is protected
        if method.upper() != "GET" and not csrf_token and any(path in url for path in ["/auth/", "/payment/", "/user/profile/"]):
            csrf_token = get_csrf_token(url, headers)
            if csrf_token:
                headers["X-CSRF-Token"] = csrf_token
                if data and isinstance(data, dict):
                    data["csrf_token"] = csrf_token
                elif data and isinstance(data, str):
                    try:
                        data_dict = json.loads(data)
                        data_dict["csrf_token"] = csrf_token
                        data = json.dumps(data_dict)
                    except:
                        # Not JSON, can't add token to body
                        pass
        
        # Log request URL
        logger.info(f"Making {method} request to {url}")
        
        response = requests.request(method, url, json=data, headers=headers)
        return True, {
            "status_code": response.status_code,
            "headers": dict(response.headers),
            "body": response.json() if response.content and response.headers.get('content-type', '').startswith('application/json') else response.text,
            "cookies": {k: v for k, v in response.cookies.items()}
        }
    except requests.RequestException as e:
        logger.error(f"Request error: {str(e)}")
        return False, {
            "error": str(e),
            "status_code": None,
            "headers": {},
            "body": None
        }
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return False, {
            "error": str(e),
            "status_code": None,
            "headers": {},
            "body": None
        }

def check_rate_limit_headers(headers: Dict[str, str]) -> Tuple[bool, Dict[str, Any]]:
    """Check if rate limit headers are present and properly configured"""
    required_headers = [
        "X-RateLimit-Limit",
        "X-RateLimit-Remaining",
        "X-RateLimit-Reset"
    ]
    
    headers_lower = {k.lower(): v for k, v in headers.items()}
    missing_headers = [h for h in required_headers if h.lower() not in headers_lower]
    
    if missing_headers:
        return False, {
            "valid": False,
            "missing_headers": missing_headers,
            "headers": headers
        }
    
    try:
        limit = int(headers_lower.get("x-ratelimit-limit", 0))
        remaining = int(headers_lower.get("x-ratelimit-remaining", 0))
        reset = int(headers_lower.get("x-ratelimit-reset", 0))
        
        return True, {
            "valid": True,
            "limit": limit,
            "remaining": remaining,
            "reset": reset,
            "headers": headers
        }
    except (ValueError, TypeError) as e:
        return False, {
            "valid": False,
            "error": f"Invalid header values: {str(e)}",
            "headers": headers
        }

def simulate_distributed_attack(
    url: str,
    num_requests: int = 30,
    concurrent: int = 5,
    headers: Optional[Dict] = None
) -> Tuple[bool, Dict[str, Any]]:
    """Simulate a distributed attack from multiple IPs"""
    import concurrent.futures
    
    results = {
        "total": num_requests,
        "blocked": 0,
        "succeeded": 0,
        "errors": 0,
        "responses": []
    }
    
    def make_single_request(i: int) -> Dict[str, Any]:
        # Simulate different IP addresses
        request_headers = {
            **(headers or {}),
            "X-Forwarded-For": f"192.168.{i % 256}.{(i * 7) % 256}"
        }
        
        success, result = make_request(url, headers=request_headers)
        
        if result.get("status_code") == 429:  # Too Many Requests
            results["blocked"] += 1
        elif success:
            results["succeeded"] += 1
        else:
            results["errors"] += 1
            
        results["responses"].append(result)
        return result
    
    # Use ThreadPoolExecutor for concurrent requests
    with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent) as executor:
        list(executor.map(make_single_request, range(num_requests)))
    
    # Calculate block rate
    block_rate = (results["blocked"] / num_requests) * 100
    results["block_rate"] = block_rate
    
    return block_rate >= 50, results  # Consider successful if at least 50% of attacks were blocked

def verify_security_headers(headers: Dict[str, str], required_headers: Dict[str, str]) -> Tuple[bool, Dict[str, Any]]:
    """
    Verify that all required security headers are present and have correct values.
    
    Args:
        headers: Response headers to check
        required_headers: Dictionary of required headers and their expected values
        
    Returns:
        Tuple[bool, Dict]: (is_valid, results)
    """
    logger.info("Verifying security headers...")
    logger.info(f"Required headers: {list(required_headers.keys())}")
    logger.info(f"Received headers: {list(headers.keys())}")
    
    # Convert all header keys to lowercase for case-insensitive comparison
    headers_lower = {k.lower(): v for k, v in headers.items()}
    required_headers_lower = {k.lower(): v for k, v in required_headers.items()}
    
    missing_headers = []
    invalid_values = {}
    
    for header, expected_value in required_headers_lower.items():
        # Check if header exists (case insensitive)
        if header not in headers_lower:
            logger.warning(f"Missing required header: {header}")
            missing_headers.append(header)
            continue
            
        # Check if header value matches expected value
        actual_value = headers_lower[header]
        if expected_value and actual_value != expected_value:
            logger.warning(f"Invalid value for header {header}: expected '{expected_value}', got '{actual_value}'")
            invalid_values[header] = {
                "expected": expected_value,
                "actual": actual_value
            }
    
    # Determine if all headers are valid
    is_valid = len(missing_headers) == 0 and len(invalid_values) == 0
    
    result = {
        "missing_headers": missing_headers,
        "invalid_values": invalid_values,
        "details": []
    }
    
    # Add detailed messages
    for header in missing_headers:
        result["details"].append(f"Missing header: {header}")
    
    for header, values in invalid_values.items():
        result["details"].append(f"Invalid value for {header}: expected '{values['expected']}', got '{values['actual']}'")
    
    if is_valid:
        logger.info("All required security headers are present and valid")
    else:
        logger.warning(f"Security headers verification failed: {len(missing_headers)} missing, {len(invalid_values)} invalid")
        
    return is_valid, result

def get_critical_endpoints() -> list:
    """Get all critical endpoints from the configuration"""
    from test_config import CRITICAL_ENDPOINTS
    return [endpoint for endpoints in CRITICAL_ENDPOINTS.values() for endpoint in endpoints]

def log_test_result(test_name: str, success: bool, details: Optional[Dict] = None) -> None:
    """Log test result with detailed information"""
    if success:
        logger.info(f"Test '{test_name}' passed successfully")
    else:
        logger.error(f"Test '{test_name}' failed")
        if details:
            logger.error(f"Failure details: {json.dumps(details, indent=2)}")

def log_environment_info() -> None:
    """Log environment information for debugging"""
    logger.info("Environment Information:")
    logger.info(f"Python Version: {sys.version}")
    logger.info(f"OS: {os.name} {os.uname() if os.name != 'nt' else ''}")
    logger.info(f"Working Directory: {os.getcwd()}")
    logger.info(f"Environment Variables:")
    for key, value in os.environ.items():
        if any(secret in key.lower() for secret in ['password', 'secret', 'key', 'token']):
            logger.info(f"{key}: <redacted>")
        else:
            logger.info(f"{key}: {value}") 