{"timestamp": "2025-04-02T19:14:31.722930", "tests": {"security_headers": {"total_tests": 8, "successful_tests": 8, "failed_tests": 0, "missing_headers": {}, "invalid_values": {}, "errors": []}, "rate_limits": {"total_tests": 8, "successful_tests": 8, "failed_tests": 0, "endpoints_tested": [], "errors": []}, "captcha": {"total_tests": 8, "successful_tests": 4, "failed_tests": 4, "endpoints_tested": [], "errors": []}, "bot_detection": {"total_tests": 8, "successful_tests": 7, "failed_tests": 1, "endpoints_tested": [], "errors": []}, "input_validation": {"total_tests": 176, "successful_tests": 176, "failed_tests": 0, "vulnerabilities_found": [], "endpoints_tested": [], "errors": []}, "auth": {"total_tests": 8, "successful_tests": 8, "failed_tests": 0, "auth_issues": [], "endpoints_tested": ["http://localhost:8000/api/auth/token", "http://localhost:8000/api/auth/register", "http://localhost:8000/api/user/profile", "http://localhost:8000/api/admin/users", "http://localhost:8000/api/user/profile", "http://localhost:8000/api/admin/users", "http://localhost:8000/api/auth/token", "http://localhost:8000/api/auth/token"], "errors": []}, "xss_csrf": {"total_tests": 22, "successful_tests": 22, "failed_tests": 2, "vulnerabilities": [{"endpoint": "Global", "description": "X-XSS-Protection header not properly set: ", "type": "XSS"}, {"endpoint": "Global", "description": "Content-Security-Policy header missing or lacks script-src directive: ", "type": "XSS"}], "errors": []}, "encryption": {"total_tests": 13, "successful_tests": 12, "failed_tests": 3, "vulnerabilities": [{"category": "TLS", "description": "API is not using HTTPS", "severity": "HIGH"}, {"category": "TLS", "description": "HSTS header is missing", "severity": "MEDIUM"}, {"category": "Password Handling", "description": "Possible timing attack vulnerability in password verification", "severity": "MEDIUM"}], "errors": []}}, "summary": {"total_tests": 251, "successful_tests": 245, "failed_tests": 10, "success_rate": 97.60956175298804}, "critical_issues": [{"test": "<PERSON><PERSON>a", "failed_tests": 4, "success_rate": 50.0}, {"test": "bot_detection", "failed_tests": 1, "success_rate": 87.5}, {"test": "xss_csrf", "failed_tests": 2, "success_rate": 100.0}, {"test": "encryption", "failed_tests": 3, "success_rate": 92.3076923076923}], "warnings": []}