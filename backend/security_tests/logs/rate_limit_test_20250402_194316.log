Logs will be saved to ./../security_tests/logs
Reports will be saved to ./../security_tests/reports
Activating virtual environment...
Virtual environment activated successfully
Checking dependencies...
All required packages are installed.
----------------------------------------------
Running rate limit captcha tests: test_rate_limit_captcha.py
----------------------------------------------
API Base URL: http://localhost:8000
Test Mode: true
Cloudflare CAPTCHA: false

================================================================================
                         RATE LIMITING AND CAPTCHA TEST                         
================================================================================


Testing login rate limiting...
2025-04-02 19:44:22,680 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-04-02 19:44:22,765 - INFO - Attempt 1: Status 403
2025-04-02 19:44:23,473 - INFO - Attempt 2: Status 403
2025-04-02 19:44:23,687 - INFO - Attempt 3: Status 403
2025-04-02 19:44:23,901 - INFO - Attempt 4: Status 403
2025-04-02 19:44:24,479 - INFO - Attempt 5: Status 403
2025-04-02 19:44:24,688 - INFO - Attempt 6: Status 403
2025-04-02 19:44:24,899 - INFO - Attempt 7: Status 403
2025-04-02 19:44:25,477 - INFO - Attempt 8: Status 403
2025-04-02 19:44:25,688 - INFO - Attempt 9: Status 403
2025-04-02 19:44:25,910 - INFO - Attempt 10: Status 403
2025-04-02 19:44:26,473 - INFO - Attempt 11: Status 403
2025-04-02 19:44:26,691 - INFO - Attempt 12: Status 403
✗ Rate limiting was not triggered after 12 attempts
✗ Login rate limiting is not working properly

Rate limit headers for login attempts:
  Attempt 1: OK - Headers: {}
  Attempt 2: OK - Headers: {}
  Attempt 3: OK - Headers: {}
  Attempt 4: OK - Headers: {}
  Attempt 5: OK - Headers: {}
  Attempt 6: OK - Headers: {}
  Attempt 7: OK - Headers: {}
  Attempt 8: OK - Headers: {}
  Attempt 9: OK - Headers: {}
  Attempt 10: OK - Headers: {}
  Attempt 11: OK - Headers: {}
  Attempt 12: OK - Headers: {}

Testing rate limiting for API endpoints...

Testing rate limiting for /api/tasks/available...
2025-04-02 19:44:26,895 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-04-02 19:44:26,926 - INFO - Attempt 1: Status 403
2025-04-02 19:44:27,489 - INFO - Attempt 2: Status 403
2025-04-02 19:44:27,698 - INFO - Attempt 3: Status 403
2025-04-02 19:44:27,926 - INFO - Attempt 4: Status 403
2025-04-02 19:44:28,488 - INFO - Attempt 5: Status 403
2025-04-02 19:44:28,717 - INFO - Attempt 6: Status 403
2025-04-02 19:44:28,940 - INFO - Attempt 7: Status 403
2025-04-02 19:44:29,519 - INFO - Attempt 8: Status 403
2025-04-02 19:44:29,746 - INFO - Attempt 9: Status 403
2025-04-02 19:44:30,501 - INFO - Attempt 10: Status 403
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /api/tasks/available

Testing rate limiting for /api/tasks/analytics...
2025-04-02 19:44:30,702 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-04-02 19:44:30,711 - INFO - Attempt 1: Status 403
2025-04-02 19:44:30,934 - INFO - Attempt 2: Status 403
2025-04-02 19:44:31,486 - INFO - Attempt 3: Status 403
2025-04-02 19:44:31,707 - INFO - Attempt 4: Status 403
2025-04-02 19:44:31,927 - INFO - Attempt 5: Status 403
2025-04-02 19:44:32,489 - INFO - Attempt 6: Status 403
2025-04-02 19:44:32,716 - INFO - Attempt 7: Status 403
2025-04-02 19:44:32,942 - INFO - Attempt 8: Status 403
2025-04-02 19:44:33,739 - INFO - Attempt 9: Status 403
2025-04-02 19:44:34,464 - INFO - Attempt 10: Status 403
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /api/tasks/analytics

Testing rate limiting for /auth/register...
2025-04-02 19:44:34,667 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-04-02 19:44:34,679 - INFO - Attempt 1: Status 403
2025-04-02 19:44:34,892 - INFO - Attempt 2: Status 403
2025-04-02 19:44:35,487 - INFO - Attempt 3: Status 403
2025-04-02 19:44:35,703 - INFO - Attempt 4: Status 403
2025-04-02 19:44:35,916 - INFO - Attempt 5: Status 403
2025-04-02 19:44:36,471 - INFO - Attempt 6: Status 403
2025-04-02 19:44:36,685 - INFO - Attempt 7: Status 403
2025-04-02 19:44:37,504 - INFO - Attempt 8: Status 403
2025-04-02 19:44:37,715 - INFO - Attempt 9: Status 403
2025-04-02 19:44:37,930 - INFO - Attempt 10: Status 403
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /auth/register

Testing Cloudflare CAPTCHA integration...
2025-04-02 19:44:38,463 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-04-02 19:44:38,475 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-04-02 19:44:38,493 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-04-02 19:44:38,545 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-04-02 19:44:38,548 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-04-02 19:44:38,609 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
⚠ No CAPTCHA detected for login endpoint
⚠ No CAPTCHA detected for register endpoint
⚠ No CAPTCHA detected for admin_login endpoint

Simulating distributed attack...
2025-04-02 19:44:38,637 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-04-02 19:44:40,825 - INFO - Distributed attack results: 13 blocked, 17 succeeded, 0 errored
✗ Distributed attack was not effectively blocked (block rate: 43.33%)

Sample distributed attack results:
  Request from IP **************: BLOCKED
  Request from IP **************: BLOCKED
  Request from IP ***************: BLOCKED
  Request from IP ***************: BLOCKED
  Request from IP ***************: ALLOWED
✅ rate limit captcha tests completed successfully
Log saved to: ./../security_tests/logs/rate_limit_captcha_test_20250402_194420.log
----------------------------------------------
