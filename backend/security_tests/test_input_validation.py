#!/usr/bin/env python
"""
Input Validation and Injection Protection Test
---------------------------------------------
Test input validation and protection against injection attacks.
"""
import logging
import time
from typing import Dict, Any, List, Tuple
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_request_details,
    log_error_with_traceback,
    make_request,
    get_critical_endpoints,
    log_test_result,
    log_environment_info
)
from test_config import API_BASE_URL

logger = logging.getLogger(__name__)

# Test payloads for different types of injection attacks
SQL_INJECTION_PAYLOADS = [
    "1' OR '1'='1",
    "'; DROP TABLE users; --",
    "1 UNION SELECT username, password FROM users --",
    "1) OR 1=1 --",
    "' OR 1=1 --"
]

XSS_PAYLOADS = [
    "<script>alert(1)</script>",
    "<img src=x onerror=alert(1)>",
    "\"><script>alert(1)</script>",
    "javascript:alert(1)"
]

NOSQL_INJECTION_PAYLOADS = [
    '{"$gt": ""}',
    '{"$ne": null}',
    '{"$where": "this.password == this.username"}',
    '{"$regex": ".*"}'
]

COMMAND_INJECTION_PAYLOADS = [
    "test; ls -la",
    "test && cat /etc/passwd",
    "test | cat /etc/passwd",
    "`cat /etc/passwd`",
    "$(cat /etc/passwd)"
]

PATH_TRAVERSAL_PAYLOADS = [
    "../../../etc/passwd",
    "..%2f..%2f..%2fetc%2fpasswd",
    "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
    "....//....//....//etc/passwd"
]

def test_input_validation() -> Dict[str, Any]:
    """Test input validation and protection against injection attacks"""
    print_header("Testing Input Validation and Injection Protection")
    
    # Log environment info for debugging
    log_environment_info()
    
    # Setup results dictionary
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities_found": [],
        "endpoints_tested": [],
        "errors": []
    }
    
    # Get critical endpoints to test
    endpoints = get_critical_endpoints()
    print_info(f"Testing {len(endpoints)} endpoints for input validation and injection protection")
    
    # For each endpoint, we'll test multiple attack vectors
    test_vectors = [
        {"name": "SQL Injection", "payloads": SQL_INJECTION_PAYLOADS},
        {"name": "XSS", "payloads": XSS_PAYLOADS},
        {"name": "NoSQL Injection", "payloads": NOSQL_INJECTION_PAYLOADS},
        {"name": "Command Injection", "payloads": COMMAND_INJECTION_PAYLOADS},
        {"name": "Path Traversal", "payloads": PATH_TRAVERSAL_PAYLOADS}
    ]
    
    # Calculate total tests
    results["total_tests"] = len(endpoints) * sum(len(vector["payloads"]) for vector in test_vectors)
    
    # Test each endpoint with each attack vector
    for endpoint in endpoints:
        # Create base URL for endpoint (replace placeholders with test values)
        base_url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        print_info(f"Testing endpoint: {endpoint}")
        
        for vector in test_vectors:
            vector_name = vector["name"]
            payloads = vector["payloads"]
            
            print_info(f"  Testing {vector_name} with {len(payloads)} payloads")
            
            # Track if any payload caused a vulnerability for this vector and endpoint
            vector_vulnerability_found = False
            
            for payload in payloads:
                try:
                    # Test in URL parameter
                    url_with_payload = f"{base_url}?id={payload}"
                    success, response = make_request(url_with_payload, headers={"User-Agent": "SecurityTestAgent/1.0"})
                    
                    if not success:
                        # Could not connect - skip this test
                        continue
                    
                    # Check for status code - client errors (4xx) are expected and good
                    status_code = response.get("status_code", 0)
                    
                    # Log the response for debugging
                    log_request_details(None, response)
                    
                    # Potential vulnerability if:
                    # 1. Status code is 200 (OK) - might be accepting malicious input
                    # 2. Status code is 5xx (Server Error) - might be causing exceptions in the backend
                    is_vulnerable = (status_code >= 500 or status_code == 200)
                    
                    # Special case: For XSS, we need to check if the payload is reflected in the response
                    if vector_name == "XSS" and status_code == 200:
                        body = response.get("body", "")
                        if payload in body and "<script>" in body:
                            # Payload reflected without sanitization - definitely vulnerable
                            is_vulnerable = True
                    
                    # Also try in POST body data
                    post_data = {"param": payload, "id": payload, "search": payload}
                    success, response = make_request(base_url, method="POST", data=post_data, headers={"User-Agent": "SecurityTestAgent/1.0"})
                    
                    if not success:
                        # Could not connect - skip this test
                        continue
                    
                    # Check POST response status
                    post_status_code = response.get("status_code", 0)
                    
                    # Log the response for debugging
                    log_request_details(None, response)
                    
                    # Same vulnerability checks for POST
                    post_is_vulnerable = (post_status_code >= 500 or post_status_code == 200)
                    
                    # Special case for XSS in POST responses
                    if vector_name == "XSS" and post_status_code == 200:
                        body = response.get("body", "")
                        if payload in body and "<script>" in body:
                            post_is_vulnerable = True
                    
                    # If either URL or POST test found a vulnerability, record it
                    if is_vulnerable or post_is_vulnerable:
                        vector_vulnerability_found = True
                        vulnerability_info = {
                            "endpoint": endpoint,
                            "vector": vector_name,
                            "payload": payload,
                            "url_status": status_code,
                            "post_status": post_status_code
                        }
                        results["vulnerabilities_found"].append(vulnerability_info)
                        print_warning(f"    Potential {vector_name} vulnerability found with payload: {payload}")
                    else:
                        results["successful_tests"] += 1
                
                except Exception as e:
                    error_msg = f"Error testing {vector_name} on {endpoint} with payload {payload}: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)
                    log_error_with_traceback(e, error_msg)
            
            # Log test result for this vector and endpoint
            test_success = not vector_vulnerability_found
            log_test_result(
                f"Input Validation - {endpoint} - {vector_name}",
                test_success,
                {
                    "endpoint": endpoint,
                    "vector": vector_name,
                    "payloads_tested": len(payloads),
                    "vulnerability_found": vector_vulnerability_found
                }
            )
            
            if test_success:
                print_success(f"  {vector_name} test passed for {endpoint}")
            else:
                print_error(f"  {vector_name} test failed for {endpoint}")
                results["failed_tests"] += 1
    
    # Calculate success rate
    success_rate = (results["successful_tests"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0
    
    # Print summary
    print_header("Input Validation Test Summary")
    print_info(f"Total Tests Run: {results['total_tests']}")
    print_info(f"Successful Tests: {results['successful_tests']}")
    print_info(f"Failed Tests: {results['failed_tests']}")
    print_info(f"Success Rate: {success_rate:.2f}%")
    
    if results["vulnerabilities_found"]:
        print_warning("\nPotential Vulnerabilities Found:")
        for vuln in results["vulnerabilities_found"]:
            print_warning(f"  {vuln['endpoint']} - {vuln['vector']} - Payload: {vuln['payload']}")
    
    if results["errors"]:
        print_warning("\nErrors Encountered:")
        for error in results["errors"]:
            print_warning(f"  {error}")
    
    return results

if __name__ == "__main__":
    test_input_validation() 