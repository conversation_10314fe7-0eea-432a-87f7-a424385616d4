#!/usr/bin/env python
"""
Cloudflare CAPTCHA Test Script
------------------------------
This script tests the Cloudflare CAPTCHA integration in the application.
It verifies that the critical endpoints are properly protected by CAPTCHA.
"""
import requests
import json
import logging
import colorama
from colorama import Fore, Style
import sys
import os
import time
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
env_path = Path(__file__).parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cloudflare_captcha_test.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Initialize colorama
colorama.init()

# Base URL
BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")

# Critical endpoints that should be protected by Cloudflare CAPTCHA
CRITICAL_ENDPOINTS = {
    "login": "/auth/token",
    "register": "/auth/register",
    "admin_login": "/auth/admin/login"
}

# Fake CAPTCHA token for testing
FAKE_CAPTCHA_TOKEN = "token123"

def print_header(message: str) -> None:
    """Print a formatted header message"""
    print(f"\n{Fore.CYAN}{'=' * 80}")
    print(f"{message.center(80)}")
    print(f"{'=' * 80}{Style.RESET_ALL}\n")

def print_success(message: str) -> None:
    """Print a success message"""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_warning(message: str) -> None:
    """Print a warning message"""
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

def print_error(message: str) -> None:
    """Print an error message"""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def test_endpoint_captcha_requirement(endpoint_name: str, endpoint_path: str) -> Tuple[bool, Dict[str, Any]]:
    """Test if an endpoint requires CAPTCHA verification"""
    logger.info(f"Testing CAPTCHA requirement for {endpoint_name} endpoint: {endpoint_path}")
    
    url = f"{BASE_URL}{endpoint_path}"
    result = {
        "endpoint": endpoint_name,
        "path": endpoint_path,
        "captcha_required": False,
        "status_code": None,
        "response": None,
        "error": None
    }
    
    try:
        # For login endpoint, attempt a login without CAPTCHA token
        if endpoint_name == "login":
            data = {"username": "test_user", "password": "wrong_password"}
            response = requests.post(url, json=data, timeout=10)
        elif endpoint_name == "register":
            data = {"username": "new_test_user", "password": "password123", "email": "<EMAIL>"}
            response = requests.post(url, json=data, timeout=10)
        elif endpoint_name == "admin_login":
            data = {"username": "admin", "password": "wrong_password"}
            response = requests.post(url, json=data, timeout=10)
        else:
            # For other endpoints, just try a GET request
            response = requests.get(url, timeout=10)
        
        result["status_code"] = response.status_code
        
        # Check response for CAPTCHA mentions
        response_text = response.text.lower()
        result["response"] = response_text[:200] + "..." if len(response_text) > 200 else response_text
        
        # Check if response indicates CAPTCHA is required
        captcha_indicators = [
            "captcha",
            "cf-turnstile",
            "turnstile",
            "cf_challenge",
            "cloudflare",
            "captcha_required",
            "captcha verification required"
        ]
        
        for indicator in captcha_indicators:
            if indicator in response_text:
                result["captcha_required"] = True
                break
                
        # Check if endpoint rejects requests without CAPTCHA token (status 400 or similar)
        if response.status_code in [400, 401, 403, 429]:
            try:
                response_json = response.json()
                if "code" in response_json and response_json["code"] == "CAPTCHA_REQUIRED":
                    result["captcha_required"] = True
            except:
                # Not JSON or doesn't have code field
                pass
                
    except Exception as e:
        logger.error(f"Error testing {endpoint_name}: {str(e)}")
        result["error"] = str(e)
        
    return result["captcha_required"], result

def test_captcha_verification(endpoint_name: str, endpoint_path: str) -> Tuple[bool, Dict[str, Any]]:
    """Test CAPTCHA verification with an invalid token"""
    logger.info(f"Testing CAPTCHA verification for {endpoint_name} endpoint: {endpoint_path}")
    
    url = f"{BASE_URL}{endpoint_path}"
    result = {
        "endpoint": endpoint_name,
        "path": endpoint_path,
        "verification_working": False,
        "status_code": None,
        "response": None,
        "error": None
    }
    
    try:
        # Attempt a request with an invalid CAPTCHA token
        if endpoint_name == "login":
            data = {
                "username": "test_user",
                "password": "wrong_password",
                "cf-turnstile-response": FAKE_CAPTCHA_TOKEN
            }
            response = requests.post(url, json=data, timeout=10)
        elif endpoint_name == "register":
            data = {
                "username": "new_test_user", 
                "password": "password123",
                "email": "<EMAIL>",
                "cf-turnstile-response": FAKE_CAPTCHA_TOKEN
            }
            response = requests.post(url, json=data, timeout=10)
        elif endpoint_name == "admin_login":
            data = {
                "username": "admin",
                "password": "wrong_password",
                "cf-turnstile-response": FAKE_CAPTCHA_TOKEN
            }
            response = requests.post(url, json=data, timeout=10)
        else:
            # For other endpoints, try a POST request with token
            data = {"cf-turnstile-response": FAKE_CAPTCHA_TOKEN}
            response = requests.post(url, json=data, timeout=10)
        
        result["status_code"] = response.status_code
        
        # Check response 
        response_text = response.text.lower()
        result["response"] = response_text[:200] + "..." if len(response_text) > 200 else response_text
        
        # Verify that the endpoint rejects invalid CAPTCHA tokens
        if response.status_code in [400, 401, 403]:
            try:
                response_json = response.json()
                if "code" in response_json and response_json["code"] == "INVALID_CAPTCHA":
                    result["verification_working"] = True
            except:
                # If we get an error response, it's likely that verification is working
                result["verification_working"] = "captcha" in response_text or "verification failed" in response_text
                
    except Exception as e:
        logger.error(f"Error testing verification for {endpoint_name}: {str(e)}")
        result["error"] = str(e)
        
    return result["verification_working"], result

def check_cloudflare_middleware_enabled() -> bool:
    """Check if the Cloudflare CAPTCHA middleware is enabled"""
    try:
        # Try to access a non-existent endpoint to trigger a 404 response
        # Check if the response headers include any Cloudflare-related headers
        response = requests.get(f"{BASE_URL}/api/non-existent-endpoint", timeout=10)
        
        # Check for Cloudflare headers
        headers = response.headers
        cloudflare_headers = [
            "cf-ray",
            "cf-cache-status",
            "cf-request-id"
        ]
        
        for header in cloudflare_headers:
            if header in headers:
                return True
                
        # Also check if CAPTCHA is enabled in the application
        return os.getenv("ENABLE_CLOUDFLARE_CAPTCHA", "false").lower() == "true"
        
    except Exception as e:
        logger.error(f"Error checking Cloudflare middleware: {str(e)}")
        return False

def validate_captcha_config() -> Tuple[bool, Dict[str, Any]]:
    """Validate Cloudflare CAPTCHA configuration"""
    # Check for required environment variables
    required_env_vars = ["CLOUDFLARE_SECRET_KEY", "CLOUDFLARE_SITE_KEY"]
    missing_vars = []
    env_status = {}
    
    for var in required_env_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
            env_status[var] = "missing"
        else:
            # Check if it's not just the default test key
            if "1x000000000000000000000" in value:
                env_status[var] = "using default test key"
            else:
                env_status[var] = "configured"
    
    # Check if Cloudflare CAPTCHA middleware is enabled
    middleware_enabled = check_cloudflare_middleware_enabled()
    env_status["CLOUDFLARE_MIDDLEWARE"] = "enabled" if middleware_enabled else "disabled"
    
    return len(missing_vars) == 0 and middleware_enabled, env_status

def main() -> None:
    """Main function to run all CAPTCHA tests"""
    print_header("CLOUDFLARE CAPTCHA INTEGRATION TEST")
    
    # Check if Cloudflare CAPTCHA is properly configured
    print(f"\n{Fore.YELLOW}Checking Cloudflare CAPTCHA configuration...{Style.RESET_ALL}")
    config_valid, config_status = validate_captcha_config()
    
    if config_valid:
        print_success("Cloudflare CAPTCHA is properly configured")
    else:
        print_error("Cloudflare CAPTCHA configuration has issues")
        
    for var, status in config_status.items():
        if status == "configured":
            print_success(f"{var}: {status}")
        elif status == "using default test key":
            print_warning(f"{var}: {status}")
        else:
            print_error(f"{var}: {status}")
    
    # Test if critical endpoints require CAPTCHA
    print(f"\n{Fore.YELLOW}Testing CAPTCHA requirements for critical endpoints...{Style.RESET_ALL}")
    requirement_results = {}
    
    for name, endpoint in CRITICAL_ENDPOINTS.items():
        captcha_required, result = test_endpoint_captcha_requirement(name, endpoint)
        requirement_results[name] = result
        
        if captcha_required:
            print_success(f"{name.capitalize()} endpoint requires CAPTCHA")
        else:
            print_error(f"{name.capitalize()} endpoint does not require CAPTCHA")
    
    # Test CAPTCHA verification with invalid tokens
    print(f"\n{Fore.YELLOW}Testing CAPTCHA verification with invalid tokens...{Style.RESET_ALL}")
    verification_results = {}
    
    for name, endpoint in CRITICAL_ENDPOINTS.items():
        if requirement_results[name]["captcha_required"]:
            verification_working, result = test_captcha_verification(name, endpoint)
            verification_results[name] = result
            
            if verification_working:
                print_success(f"{name.capitalize()} endpoint correctly rejects invalid CAPTCHA tokens")
            else:
                print_error(f"{name.capitalize()} endpoint does not properly verify CAPTCHA tokens")
        else:
            print_warning(f"Skipping verification test for {name} endpoint as it doesn't require CAPTCHA")
    
    # Print overall results
    print(f"\n{Fore.YELLOW}CAPTCHA Integration Test Results:{Style.RESET_ALL}")
    
    all_endpoints_protected = all(result["captcha_required"] for result in requirement_results.values())
    all_verifications_working = all(
        verification_results.get(name, {}).get("verification_working", False) 
        for name in requirement_results.keys() 
        if requirement_results[name]["captcha_required"]
    )
    
    if all_endpoints_protected and all_verifications_working:
        print_success("All critical endpoints are properly protected by Cloudflare CAPTCHA")
    else:
        print_error("Some critical endpoints are not properly protected by Cloudflare CAPTCHA")
        
    # Set exit code based on test results
    if not config_valid or not all_endpoints_protected or not all_verifications_working:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main() 