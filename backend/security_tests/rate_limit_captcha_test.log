2025-03-08 15:51:43,825 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-08 15:51:45,820 - INFO - Attempt 1: Status 401
2025-03-08 15:51:47,890 - INFO - Attempt 2: Status 401
2025-03-08 15:51:49,946 - INFO - Attempt 3: Status 401
2025-03-08 15:51:51,715 - INFO - Attempt 4: Status 401
2025-03-08 15:51:54,874 - INFO - Attempt 5: Status 401
2025-03-08 15:51:56,775 - INFO - Attempt 6: Status 401
2025-03-08 15:51:58,607 - INFO - Attempt 7: Status 401
2025-03-08 15:51:59,825 - INFO - Attempt 8: Status 401
2025-03-08 15:52:01,660 - INFO - Attempt 9: Status 401
2025-03-08 15:52:02,811 - INFO - Attempt 10: Status 401
2025-03-08 15:52:04,674 - INFO - Attempt 11: Status 401
2025-03-08 15:52:05,834 - INFO - Attempt 12: Status 401
2025-03-08 15:52:06,462 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-08 15:52:06,606 - INFO - Attempt 1: Status 401
2025-03-08 15:52:06,900 - INFO - Attempt 2: Status 401
2025-03-08 15:52:07,616 - INFO - Attempt 3: Status 401
2025-03-08 15:52:07,875 - INFO - Attempt 4: Status 401
2025-03-08 15:52:08,613 - INFO - Attempt 5: Status 401
2025-03-08 15:52:08,869 - INFO - Attempt 6: Status 401
2025-03-08 15:52:09,632 - INFO - Attempt 7: Status 401
2025-03-08 15:52:09,858 - INFO - Attempt 8: Status 401
2025-03-08 15:52:10,617 - INFO - Attempt 9: Status 401
2025-03-08 15:52:10,884 - INFO - Attempt 10: Status 401
2025-03-08 15:52:11,558 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-08 15:52:11,614 - INFO - Attempt 1: Status 401
2025-03-08 15:52:11,867 - INFO - Attempt 2: Status 401
2025-03-08 15:52:12,628 - INFO - Attempt 3: Status 401
2025-03-08 15:52:12,851 - INFO - Attempt 4: Status 401
2025-03-08 15:52:24,644 - INFO - Attempt 5: Status 401
2025-03-08 15:52:25,017 - INFO - Attempt 6: Status 401
2025-03-08 15:52:25,668 - INFO - Attempt 7: Status 401
2025-03-08 15:52:25,912 - INFO - Attempt 8: Status 401
2025-03-08 15:52:26,583 - INFO - Attempt 9: Status 401
2025-03-08 15:52:26,813 - INFO - Attempt 10: Status 401
2025-03-08 15:52:27,014 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-08 15:52:27,559 - INFO - Attempt 1: Status 405
2025-03-08 15:52:27,769 - INFO - Attempt 2: Status 405
2025-03-08 15:52:28,556 - INFO - Attempt 3: Status 405
2025-03-08 15:52:28,766 - INFO - Attempt 4: Status 405
2025-03-08 15:52:29,556 - INFO - Attempt 5: Status 405
2025-03-08 15:52:29,765 - INFO - Attempt 6: Status 405
2025-03-08 15:52:30,556 - INFO - Attempt 7: Status 405
2025-03-08 15:52:30,766 - INFO - Attempt 8: Status 405
2025-03-08 15:52:31,555 - INFO - Attempt 9: Status 405
2025-03-08 15:52:31,765 - INFO - Attempt 10: Status 405
2025-03-08 15:52:31,966 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-08 15:52:32,556 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-08 15:52:34,560 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-08 15:52:34,570 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-08 15:52:34,571 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-08 15:52:34,603 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-08 15:52:34,606 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-08 15:52:37,795 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-08 15:52:57,818 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-08 15:53:07,901 - INFO - Attempt 1: Status 401
2025-03-08 15:53:09,955 - INFO - Attempt 2: Status 401
2025-03-08 15:53:11,850 - INFO - Attempt 3: Status 401
2025-03-08 15:53:13,602 - INFO - Attempt 4: Status 401
2025-03-08 15:53:15,788 - INFO - Attempt 5: Status 401
2025-03-08 15:53:17,744 - INFO - Attempt 6: Status 401
2025-03-08 15:53:20,775 - INFO - Attempt 7: Status 401
2025-03-08 15:53:22,939 - INFO - Attempt 8: Status 401
2025-03-08 15:53:24,676 - INFO - Attempt 9: Status 401
2025-03-08 15:53:26,684 - INFO - Attempt 10: Status 401
2025-03-08 15:53:29,699 - INFO - Attempt 11: Status 401
2025-03-08 15:53:31,868 - INFO - Attempt 12: Status 401
2025-03-08 15:53:32,458 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-08 15:53:32,580 - INFO - Attempt 1: Status 401
2025-03-08 15:53:32,824 - INFO - Attempt 2: Status 401
2025-03-08 15:53:33,573 - INFO - Attempt 3: Status 401
2025-03-08 15:53:33,817 - INFO - Attempt 4: Status 401
2025-03-08 15:53:34,617 - INFO - Attempt 5: Status 401
2025-03-08 15:53:34,850 - INFO - Attempt 6: Status 401
2025-03-08 15:53:35,624 - INFO - Attempt 7: Status 401
2025-03-08 15:53:35,876 - INFO - Attempt 8: Status 401
2025-03-08 15:53:36,501 - INFO - Attempt 9: Status 401
2025-03-08 15:53:36,760 - INFO - Attempt 10: Status 401
2025-03-08 15:53:37,456 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-08 15:53:37,522 - INFO - Attempt 1: Status 401
2025-03-08 15:53:37,778 - INFO - Attempt 2: Status 401
2025-03-08 15:53:38,502 - INFO - Attempt 3: Status 401
2025-03-08 15:53:38,743 - INFO - Attempt 4: Status 401
2025-03-08 15:53:39,612 - INFO - Attempt 5: Status 401
2025-03-08 15:53:39,834 - INFO - Attempt 6: Status 401
2025-03-08 15:53:40,691 - INFO - Attempt 7: Status 401
2025-03-08 15:53:40,957 - INFO - Attempt 8: Status 401
2025-03-08 15:53:41,492 - INFO - Attempt 9: Status 401
2025-03-08 15:53:41,732 - INFO - Attempt 10: Status 401
2025-03-08 15:53:41,934 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-08 15:53:43,557 - INFO - Attempt 1: Status 405
2025-03-08 15:53:43,784 - INFO - Attempt 2: Status 405
2025-03-08 15:53:43,995 - INFO - Attempt 3: Status 405
2025-03-08 15:53:44,575 - INFO - Attempt 4: Status 405
2025-03-08 15:53:44,799 - INFO - Attempt 5: Status 405
2025-03-08 15:53:45,024 - INFO - Attempt 6: Status 405
2025-03-08 15:53:45,598 - INFO - Attempt 7: Status 405
2025-03-08 15:53:45,820 - INFO - Attempt 8: Status 405
2025-03-08 15:53:46,045 - INFO - Attempt 9: Status 405
2025-03-08 15:53:46,593 - INFO - Attempt 10: Status 405
2025-03-08 15:53:46,795 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-08 15:53:46,806 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-08 15:53:47,835 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-08 15:53:47,852 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-08 15:53:47,852 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-08 15:53:47,874 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-08 15:53:47,876 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-08 15:53:50,861 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-08 16:25:12,583 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-08 16:25:12,653 - INFO - Attempt 1: Status 400
2025-03-08 16:25:12,861 - INFO - Attempt 2: Status 400
2025-03-08 16:25:13,571 - INFO - Attempt 3: Status 400
2025-03-08 16:25:13,779 - INFO - Attempt 4: Status 400
2025-03-08 16:25:14,566 - INFO - Attempt 5: Status 400
2025-03-08 16:25:14,775 - INFO - Attempt 6: Status 400
2025-03-08 16:25:15,576 - INFO - Attempt 7: Status 400
2025-03-08 16:25:15,788 - INFO - Attempt 8: Status 400
2025-03-08 16:25:16,737 - INFO - Attempt 9: Status 400
2025-03-08 16:25:16,944 - INFO - Attempt 10: Status 400
2025-03-08 16:25:17,571 - INFO - Attempt 11: Status 400
2025-03-08 16:25:17,799 - INFO - Attempt 12: Status 400
2025-03-08 16:25:18,001 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-08 16:25:18,589 - INFO - Attempt 1: Status 403
2025-03-08 16:25:18,821 - INFO - Attempt 2: Status 403
2025-03-08 16:25:19,598 - INFO - Attempt 3: Status 403
2025-03-08 16:25:19,838 - INFO - Attempt 4: Status 403
2025-03-08 16:25:20,588 - INFO - Attempt 5: Status 403
2025-03-08 16:25:20,831 - INFO - Attempt 6: Status 403
2025-03-08 16:25:21,578 - INFO - Attempt 7: Status 403
2025-03-08 16:25:21,797 - INFO - Attempt 8: Status 403
2025-03-08 16:25:22,558 - INFO - Attempt 9: Status 403
2025-03-08 16:25:22,802 - INFO - Attempt 10: Status 403
2025-03-08 16:25:23,018 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-08 16:25:23,582 - INFO - Attempt 1: Status 403
2025-03-08 16:25:23,885 - INFO - Attempt 2: Status 403
2025-03-08 16:25:24,598 - INFO - Attempt 3: Status 403
2025-03-08 16:25:24,838 - INFO - Attempt 4: Status 403
2025-03-08 16:25:25,913 - INFO - Attempt 5: Status 403
2025-03-08 16:25:26,608 - INFO - Attempt 6: Status 403
2025-03-08 16:25:26,828 - INFO - Attempt 7: Status 403
2025-03-08 16:25:27,575 - INFO - Attempt 8: Status 403
2025-03-08 16:25:27,795 - INFO - Attempt 9: Status 403
2025-03-08 16:25:28,575 - INFO - Attempt 10: Status 403
2025-03-08 16:25:28,776 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-08 16:25:28,792 - INFO - Attempt 1: Status 403
2025-03-08 16:25:29,611 - INFO - Attempt 2: Status 403
2025-03-08 16:25:29,852 - INFO - Attempt 3: Status 403
2025-03-08 16:25:30,719 - INFO - Attempt 4: Status 403
2025-03-08 16:25:30,937 - INFO - Attempt 5: Status 403
2025-03-08 16:25:31,606 - INFO - Attempt 6: Status 403
2025-03-08 16:25:31,827 - INFO - Attempt 7: Status 403
2025-03-08 16:25:32,593 - INFO - Attempt 8: Status 403
2025-03-08 16:25:32,819 - INFO - Attempt 9: Status 403
2025-03-08 16:25:33,575 - INFO - Attempt 10: Status 403
2025-03-08 16:25:33,777 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-08 16:25:33,798 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-08 16:25:33,804 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-08 16:25:33,822 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-08 16:25:33,822 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-08 16:25:33,852 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-08 16:25:33,858 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-08 16:25:44,758 - INFO - Distributed attack results: 13 blocked, 17 succeeded, 0 errored
2025-03-08 16:37:43,673 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-08 16:37:44,876 - INFO - Attempt 1: Status 401
2025-03-08 16:37:46,626 - INFO - Attempt 2: Status 401
2025-03-08 16:37:47,797 - INFO - Attempt 3: Status 401
2025-03-08 16:37:50,589 - INFO - Attempt 4: Status 401
2025-03-08 16:37:51,950 - INFO - Attempt 5: Status 401
2025-03-08 16:37:53,873 - INFO - Attempt 6: Status 401
2025-03-08 16:37:55,857 - INFO - Attempt 7: Status 401
2025-03-08 16:37:57,900 - INFO - Attempt 8: Status 401
2025-03-08 16:37:59,892 - INFO - Attempt 9: Status 401
2025-03-08 16:38:01,602 - INFO - Attempt 10: Status 401
2025-03-08 16:38:03,559 - INFO - Attempt 11: Status 401
2025-03-08 16:38:05,845 - INFO - Attempt 12: Status 401
2025-03-08 16:38:06,459 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-08 16:38:06,606 - INFO - Attempt 1: Status 401
2025-03-08 16:38:06,833 - INFO - Attempt 2: Status 401
2025-03-08 16:38:07,610 - INFO - Attempt 3: Status 401
2025-03-08 16:38:07,831 - INFO - Attempt 4: Status 401
2025-03-08 16:38:08,591 - INFO - Attempt 5: Status 401
2025-03-08 16:38:08,824 - INFO - Attempt 6: Status 401
2025-03-08 16:38:09,602 - INFO - Attempt 7: Status 401
2025-03-08 16:38:09,840 - INFO - Attempt 8: Status 401
2025-03-08 16:38:10,630 - INFO - Attempt 9: Status 401
2025-03-08 16:38:10,845 - INFO - Attempt 10: Status 401
2025-03-08 16:38:11,457 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-08 16:38:11,589 - INFO - Attempt 1: Status 401
2025-03-08 16:38:11,818 - INFO - Attempt 2: Status 401
2025-03-08 16:38:12,638 - INFO - Attempt 3: Status 401
2025-03-08 16:38:12,942 - INFO - Attempt 4: Status 401
2025-03-08 16:38:13,693 - INFO - Attempt 5: Status 401
2025-03-08 16:38:13,941 - INFO - Attempt 6: Status 401
2025-03-08 16:38:14,702 - INFO - Attempt 7: Status 401
2025-03-08 16:38:15,594 - INFO - Attempt 8: Status 401
2025-03-08 16:38:15,870 - INFO - Attempt 9: Status 401
2025-03-08 16:38:16,596 - INFO - Attempt 10: Status 401
2025-03-08 16:38:16,797 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-08 16:38:16,808 - INFO - Attempt 1: Status 405
2025-03-08 16:38:17,574 - INFO - Attempt 2: Status 405
2025-03-08 16:38:17,792 - INFO - Attempt 3: Status 405
2025-03-08 16:38:18,585 - INFO - Attempt 4: Status 405
2025-03-08 16:38:18,795 - INFO - Attempt 5: Status 405
2025-03-08 16:38:19,586 - INFO - Attempt 6: Status 405
2025-03-08 16:38:19,808 - INFO - Attempt 7: Status 405
2025-03-08 16:38:20,576 - INFO - Attempt 8: Status 405
2025-03-08 16:38:20,786 - INFO - Attempt 9: Status 405
2025-03-08 16:38:21,574 - INFO - Attempt 10: Status 405
2025-03-08 16:38:21,775 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-08 16:38:21,793 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-08 16:38:23,586 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-08 16:38:23,600 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-08 16:38:23,600 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-08 16:38:23,609 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-08 16:38:23,610 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-08 16:38:25,833 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-08 16:55:27,880 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-08 16:55:27,912 - ERROR - Request error on attempt 1: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2617e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:27,940 - ERROR - Request error on attempt 2: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260970>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,014 - ERROR - Request error on attempt 3: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2621a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,570 - ERROR - Request error on attempt 4: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262aa0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,573 - ERROR - Request error on attempt 5: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2633a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,625 - ERROR - Request error on attempt 6: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263ca0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,664 - ERROR - Request error on attempt 7: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262d10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,680 - ERROR - Request error on attempt 8: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262410>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,777 - ERROR - Request error on attempt 9: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2617e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,780 - ERROR - Request error on attempt 10: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260610>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,820 - ERROR - Request error on attempt 11: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b05e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,844 - ERROR - Request error on attempt 12: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f261bd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,900 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-08 16:55:28,902 - ERROR - Request error on attempt 1: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f261960>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,959 - ERROR - Request error on attempt 2: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2624d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:28,961 - ERROR - Request error on attempt 3: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262dd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,012 - ERROR - Request error on attempt 4: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263d60>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,029 - ERROR - Request error on attempt 5: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263b50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,615 - ERROR - Request error on attempt 6: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262fe0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,653 - ERROR - Request error on attempt 7: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2626e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,655 - ERROR - Request error on attempt 8: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260d30>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,696 - ERROR - Request error on attempt 9: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260c40>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,707 - ERROR - Request error on attempt 10: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b0f70>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,742 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-08 16:55:29,747 - ERROR - Request error on attempt 1: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f261780>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,799 - ERROR - Request error on attempt 2: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262740>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,801 - ERROR - Request error on attempt 3: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263040>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,811 - ERROR - Request error on attempt 4: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263940>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,864 - ERROR - Request error on attempt 5: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b08e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,866 - ERROR - Request error on attempt 6: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263af0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,923 - ERROR - Request error on attempt 7: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262fe0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,925 - ERROR - Request error on attempt 8: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2626e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:29,988 - ERROR - Request error on attempt 9: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260d30>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,032 - ERROR - Request error on attempt 10: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/analytics (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260bb0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,042 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-08 16:55:30,052 - ERROR - Request error on attempt 1: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b0c40>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,640 - ERROR - Request error on attempt 2: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f261b40>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,696 - ERROR - Request error on attempt 3: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2615a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,703 - ERROR - Request error on attempt 4: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262890>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,771 - ERROR - Request error on attempt 5: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263190>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,773 - ERROR - Request error on attempt 6: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b1180>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,811 - ERROR - Request error on attempt 7: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2639a0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,835 - ERROR - Request error on attempt 8: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263820>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,872 - ERROR - Request error on attempt 9: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263400>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,943 - ERROR - Request error on attempt 10: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262b00>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,944 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-08 16:55:30,951 - ERROR - Error testing CAPTCHA for login endpoint: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/token (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f263e20>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:30,999 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-08 16:55:31,004 - ERROR - Error testing CAPTCHA for register endpoint: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/register (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b1870>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,563 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-08 16:55:31,566 - ERROR - Error testing CAPTCHA for admin_login endpoint: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /auth/admin/login (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260df0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,618 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-08 16:55:31,641 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f262d10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,655 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b1900>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,731 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b3130>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,702 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2638e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,704 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b16c0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,719 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b2860>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,684 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f260df0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,779 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b3e50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,786 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b2d10>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,786 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b3e80>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,795 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bcd60>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,797 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bd570>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,804 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b01f0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,843 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bdf00>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,846 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0be740>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,875 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bdd20>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,847 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b3a30>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,908 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bf880>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,906 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bcb50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,912 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bf850>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,914 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bccd0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,905 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b2c50>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,910 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0f4130>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,943 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b3550>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,943 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0befe0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:31,948 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0f4820>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:39,562 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bc6d0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:39,565 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f2b05e0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:39,565 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0f5030>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:39,568 - ERROR - Request error in distributed attack simulation: HTTPConnectionPool(host='localhost', port=8000): Max retries exceeded with url: /api/tasks/available (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fbc5f0bcee0>: Failed to establish a new connection: [Errno 111] Connection refused'))
2025-03-08 16:55:39,570 - INFO - Distributed attack results: 0 blocked, 0 succeeded, 30 errored
2025-03-10 23:22:51,579 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-10 23:22:52,749 - INFO - Attempt 1: Status 401
2025-03-10 23:22:55,571 - INFO - Attempt 2: Status 401
2025-03-10 23:22:56,784 - INFO - Attempt 3: Status 401
2025-03-10 23:22:58,565 - INFO - Attempt 4: Status 401
2025-03-10 23:22:59,796 - INFO - Attempt 5: Status 401
2025-03-10 23:23:01,790 - INFO - Attempt 6: Status 401
2025-03-10 23:23:03,583 - INFO - Attempt 7: Status 401
2025-03-10 23:23:05,567 - INFO - Attempt 8: Status 401
2025-03-10 23:23:06,860 - INFO - Attempt 9: Status 401
2025-03-10 23:23:08,575 - INFO - Attempt 10: Status 401
2025-03-10 23:23:09,871 - INFO - Attempt 11: Status 401
2025-03-10 23:23:12,589 - INFO - Attempt 12: Status 401
2025-03-10 23:23:12,791 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-10 23:23:12,848 - INFO - Attempt 1: Status 401
2025-03-10 23:23:13,590 - INFO - Attempt 2: Status 401
2025-03-10 23:23:13,824 - INFO - Attempt 3: Status 401
2025-03-10 23:23:14,604 - INFO - Attempt 4: Status 401
2025-03-10 23:23:14,858 - INFO - Attempt 5: Status 401
2025-03-10 23:23:15,602 - INFO - Attempt 6: Status 401
2025-03-10 23:23:15,843 - INFO - Attempt 7: Status 401
2025-03-10 23:23:16,608 - INFO - Attempt 8: Status 401
2025-03-10 23:23:16,875 - INFO - Attempt 9: Status 401
2025-03-10 23:23:17,596 - INFO - Attempt 10: Status 401
2025-03-10 23:23:17,798 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-10 23:23:17,832 - INFO - Attempt 1: Status 401
2025-03-10 23:23:18,611 - INFO - Attempt 2: Status 401
2025-03-10 23:23:18,858 - INFO - Attempt 3: Status 401
2025-03-10 23:23:19,605 - INFO - Attempt 4: Status 401
2025-03-10 23:23:19,840 - INFO - Attempt 5: Status 401
2025-03-10 23:23:20,556 - INFO - Attempt 6: Status 401
2025-03-10 23:23:20,801 - INFO - Attempt 7: Status 401
2025-03-10 23:23:21,587 - INFO - Attempt 8: Status 401
2025-03-10 23:23:21,818 - INFO - Attempt 9: Status 401
2025-03-10 23:23:22,555 - INFO - Attempt 10: Status 401
2025-03-10 23:23:22,758 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-10 23:23:22,798 - INFO - Attempt 1: Status 405
2025-03-10 23:23:23,575 - INFO - Attempt 2: Status 405
2025-03-10 23:23:23,799 - INFO - Attempt 3: Status 405
2025-03-10 23:23:24,602 - INFO - Attempt 4: Status 405
2025-03-10 23:23:24,830 - INFO - Attempt 5: Status 405
2025-03-10 23:23:25,578 - INFO - Attempt 6: Status 405
2025-03-10 23:23:25,819 - INFO - Attempt 7: Status 405
2025-03-10 23:23:26,599 - INFO - Attempt 8: Status 405
2025-03-10 23:23:26,821 - INFO - Attempt 9: Status 405
2025-03-10 23:23:27,595 - INFO - Attempt 10: Status 405
2025-03-10 23:23:27,802 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-10 23:23:27,826 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-10 23:23:28,939 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-10 23:23:29,569 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-10 23:23:29,569 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-10 23:23:29,597 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-10 23:23:29,597 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-10 23:23:31,758 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-10 23:58:35,468 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-10 23:58:35,737 - INFO - Attempt 1: Status 403
2025-03-10 23:58:36,574 - INFO - Attempt 2: Status 403
2025-03-10 23:58:36,806 - INFO - Attempt 3: Status 403
2025-03-10 23:58:37,604 - INFO - Attempt 4: Status 403
2025-03-10 23:58:37,844 - INFO - Attempt 5: Status 403
2025-03-10 23:58:38,602 - INFO - Attempt 6: Status 403
2025-03-10 23:58:38,825 - INFO - Attempt 7: Status 403
2025-03-10 23:58:39,595 - INFO - Attempt 8: Status 403
2025-03-10 23:58:39,843 - INFO - Attempt 9: Status 403
2025-03-10 23:58:40,569 - INFO - Attempt 10: Status 429
2025-03-10 23:58:40,783 - INFO - Attempt 11: Status 429
2025-03-10 23:58:41,575 - INFO - Attempt 12: Status 429
2025-03-10 23:58:41,776 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-10 23:58:41,804 - INFO - Attempt 1: Status 403
2025-03-10 23:58:42,596 - INFO - Attempt 2: Status 403
2025-03-10 23:58:42,825 - INFO - Attempt 3: Status 403
2025-03-10 23:58:43,602 - INFO - Attempt 4: Status 403
2025-03-10 23:58:43,863 - INFO - Attempt 5: Status 403
2025-03-10 23:58:44,603 - INFO - Attempt 6: Status 403
2025-03-10 23:58:44,867 - INFO - Attempt 7: Status 403
2025-03-10 23:58:45,586 - INFO - Attempt 8: Status 403
2025-03-10 23:58:45,807 - INFO - Attempt 9: Status 403
2025-03-10 23:58:46,619 - INFO - Attempt 10: Status 403
2025-03-10 23:58:46,821 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-10 23:58:46,894 - INFO - Attempt 1: Status 403
2025-03-10 23:58:47,591 - INFO - Attempt 2: Status 403
2025-03-10 23:58:47,822 - INFO - Attempt 3: Status 403
2025-03-10 23:58:48,583 - INFO - Attempt 4: Status 403
2025-03-10 23:58:48,847 - INFO - Attempt 5: Status 403
2025-03-10 23:58:49,599 - INFO - Attempt 6: Status 403
2025-03-10 23:58:49,871 - INFO - Attempt 7: Status 403
2025-03-10 23:58:50,627 - INFO - Attempt 8: Status 403
2025-03-10 23:58:50,895 - INFO - Attempt 9: Status 403
2025-03-10 23:58:51,635 - INFO - Attempt 10: Status 403
2025-03-10 23:58:51,836 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-10 23:58:51,856 - INFO - Attempt 1: Status 429
2025-03-10 23:58:52,585 - INFO - Attempt 2: Status 429
2025-03-10 23:58:52,805 - INFO - Attempt 3: Status 429
2025-03-10 23:58:53,575 - INFO - Attempt 4: Status 429
2025-03-10 23:58:53,785 - INFO - Attempt 5: Status 429
2025-03-10 23:58:54,582 - INFO - Attempt 6: Status 429
2025-03-10 23:58:54,797 - INFO - Attempt 7: Status 429
2025-03-10 23:58:55,569 - INFO - Attempt 8: Status 429
2025-03-10 23:58:55,826 - INFO - Attempt 9: Status 429
2025-03-10 23:58:56,576 - INFO - Attempt 10: Status 429
2025-03-10 23:58:56,804 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-10 23:58:56,817 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-10 23:58:56,833 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-10 23:58:56,849 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-10 23:58:56,850 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-10 23:58:56,891 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-10 23:58:56,892 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-10 23:59:02,629 - INFO - Distributed attack results: 11 blocked, 19 succeeded, 0 errored
2025-03-11 00:11:14,616 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-11 00:11:14,856 - INFO - Attempt 1: Status 429
2025-03-11 00:11:15,563 - INFO - Attempt 2: Status 429
2025-03-11 00:11:15,775 - INFO - Attempt 3: Status 429
2025-03-11 00:11:16,554 - INFO - Attempt 4: Status 429
2025-03-11 00:11:16,777 - INFO - Attempt 5: Status 429
2025-03-11 00:11:17,554 - INFO - Attempt 6: Status 429
2025-03-11 00:11:17,777 - INFO - Attempt 7: Status 429
2025-03-11 00:11:18,555 - INFO - Attempt 8: Status 429
2025-03-11 00:11:18,766 - INFO - Attempt 9: Status 429
2025-03-11 00:11:19,554 - INFO - Attempt 10: Status 429
2025-03-11 00:11:19,769 - INFO - Attempt 11: Status 429
2025-03-11 00:11:20,557 - INFO - Attempt 12: Status 429
2025-03-11 00:11:20,761 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-11 00:11:20,827 - INFO - Attempt 1: Status 403
2025-03-11 00:11:21,568 - INFO - Attempt 2: Status 403
2025-03-11 00:11:21,798 - INFO - Attempt 3: Status 403
2025-03-11 00:11:22,573 - INFO - Attempt 4: Status 403
2025-03-11 00:11:22,818 - INFO - Attempt 5: Status 403
2025-03-11 00:11:23,573 - INFO - Attempt 6: Status 403
2025-03-11 00:11:23,827 - INFO - Attempt 7: Status 403
2025-03-11 00:11:24,611 - INFO - Attempt 8: Status 403
2025-03-11 00:11:24,876 - INFO - Attempt 9: Status 403
2025-03-11 00:11:25,591 - INFO - Attempt 10: Status 429
2025-03-11 00:11:25,798 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-11 00:11:25,814 - INFO - Attempt 1: Status 429
2025-03-11 00:11:26,570 - INFO - Attempt 2: Status 429
2025-03-11 00:11:26,791 - INFO - Attempt 3: Status 429
2025-03-11 00:11:27,027 - INFO - Attempt 4: Status 429
2025-03-11 00:11:27,594 - INFO - Attempt 5: Status 429
2025-03-11 00:11:27,805 - INFO - Attempt 6: Status 429
2025-03-11 00:11:28,594 - INFO - Attempt 7: Status 429
2025-03-11 00:11:28,823 - INFO - Attempt 8: Status 429
2025-03-11 00:11:29,570 - INFO - Attempt 9: Status 429
2025-03-11 00:11:29,782 - INFO - Attempt 10: Status 429
2025-03-11 00:11:30,456 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-11 00:11:30,570 - INFO - Attempt 1: Status 429
2025-03-11 00:11:30,820 - INFO - Attempt 2: Status 429
2025-03-11 00:11:31,560 - INFO - Attempt 3: Status 429
2025-03-11 00:11:31,791 - INFO - Attempt 4: Status 429
2025-03-11 00:11:32,573 - INFO - Attempt 5: Status 429
2025-03-11 00:11:32,785 - INFO - Attempt 6: Status 429
2025-03-11 00:11:33,591 - INFO - Attempt 7: Status 429
2025-03-11 00:11:33,818 - INFO - Attempt 8: Status 429
2025-03-11 00:11:34,038 - INFO - Attempt 9: Status 429
2025-03-11 00:11:34,596 - INFO - Attempt 10: Status 429
2025-03-11 00:11:34,798 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-11 00:11:34,835 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-11 00:11:34,858 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-11 00:11:34,885 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-11 00:11:34,885 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-11 00:11:34,937 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-11 00:11:34,938 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-11 00:11:38,845 - INFO - Distributed attack results: 7 blocked, 23 succeeded, 0 errored
2025-03-11 00:20:39,752 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-11 00:20:41,810 - INFO - Attempt 1: Status 429
2025-03-11 00:20:42,633 - INFO - Attempt 2: Status 429
2025-03-11 00:20:42,875 - INFO - Attempt 3: Status 429
2025-03-11 00:20:43,582 - INFO - Attempt 4: Status 429
2025-03-11 00:20:43,792 - INFO - Attempt 5: Status 429
2025-03-11 00:20:44,567 - INFO - Attempt 6: Status 429
2025-03-11 00:20:44,779 - INFO - Attempt 7: Status 429
2025-03-11 00:20:45,567 - INFO - Attempt 8: Status 429
2025-03-11 00:20:45,815 - INFO - Attempt 9: Status 429
2025-03-11 00:20:46,599 - INFO - Attempt 10: Status 429
2025-03-11 00:20:46,835 - INFO - Attempt 11: Status 429
2025-03-11 00:20:47,584 - INFO - Attempt 12: Status 429
2025-03-11 00:20:47,785 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-11 00:20:47,824 - INFO - Attempt 1: Status 403
2025-03-11 00:20:48,635 - INFO - Attempt 2: Status 403
2025-03-11 00:20:48,899 - INFO - Attempt 3: Status 403
2025-03-11 00:20:49,579 - INFO - Attempt 4: Status 403
2025-03-11 00:20:49,852 - INFO - Attempt 5: Status 403
2025-03-11 00:20:50,598 - INFO - Attempt 6: Status 403
2025-03-11 00:20:50,861 - INFO - Attempt 7: Status 403
2025-03-11 00:20:51,603 - INFO - Attempt 8: Status 403
2025-03-11 00:20:51,856 - INFO - Attempt 9: Status 403
2025-03-11 00:20:52,506 - INFO - Attempt 10: Status 429
2025-03-11 00:20:52,707 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-11 00:20:52,730 - INFO - Attempt 1: Status 429
2025-03-11 00:20:53,563 - INFO - Attempt 2: Status 429
2025-03-11 00:20:53,780 - INFO - Attempt 3: Status 429
2025-03-11 00:20:54,573 - INFO - Attempt 4: Status 429
2025-03-11 00:20:54,785 - INFO - Attempt 5: Status 429
2025-03-11 00:20:55,570 - INFO - Attempt 6: Status 429
2025-03-11 00:20:55,784 - INFO - Attempt 7: Status 429
2025-03-11 00:20:56,569 - INFO - Attempt 8: Status 429
2025-03-11 00:20:56,779 - INFO - Attempt 9: Status 429
2025-03-11 00:20:57,573 - INFO - Attempt 10: Status 429
2025-03-11 00:20:57,774 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-11 00:20:57,788 - INFO - Attempt 1: Status 429
2025-03-11 00:20:58,570 - INFO - Attempt 2: Status 429
2025-03-11 00:20:58,782 - INFO - Attempt 3: Status 429
2025-03-11 00:20:59,565 - INFO - Attempt 4: Status 429
2025-03-11 00:20:59,800 - INFO - Attempt 5: Status 429
2025-03-11 00:21:00,573 - INFO - Attempt 6: Status 429
2025-03-11 00:21:00,791 - INFO - Attempt 7: Status 429
2025-03-11 00:21:01,593 - INFO - Attempt 8: Status 429
2025-03-11 00:21:01,805 - INFO - Attempt 9: Status 429
2025-03-11 00:21:02,581 - INFO - Attempt 10: Status 429
2025-03-11 00:21:02,782 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-11 00:21:02,801 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-11 00:21:02,818 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-11 00:21:02,847 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-11 00:21:02,847 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-11 00:21:02,892 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-11 00:21:02,892 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-11 00:21:08,951 - INFO - Distributed attack results: 5 blocked, 25 succeeded, 0 errored
2025-03-11 00:30:11,495 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-11 00:30:11,886 - INFO - Attempt 1: Status 403
2025-03-11 00:30:12,577 - INFO - Attempt 2: Status 403
2025-03-11 00:30:12,832 - INFO - Attempt 3: Status 403
2025-03-11 00:30:13,622 - INFO - Attempt 4: Status 403
2025-03-11 00:30:13,873 - INFO - Attempt 5: Status 403
2025-03-11 00:30:14,581 - INFO - Attempt 6: Status 403
2025-03-11 00:30:14,842 - INFO - Attempt 7: Status 403
2025-03-11 00:30:15,580 - INFO - Attempt 8: Status 403
2025-03-11 00:30:15,797 - INFO - Attempt 9: Status 403
2025-03-11 00:30:16,595 - INFO - Attempt 10: Status 429
2025-03-11 00:30:16,843 - INFO - Attempt 11: Status 429
2025-03-11 00:30:17,584 - INFO - Attempt 12: Status 429
2025-03-11 00:30:17,788 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-11 00:30:17,888 - INFO - Attempt 1: Status 401
2025-03-11 00:30:18,635 - INFO - Attempt 2: Status 401
2025-03-11 00:30:18,914 - INFO - Attempt 3: Status 401
2025-03-11 00:30:19,625 - INFO - Attempt 4: Status 401
2025-03-11 00:30:19,875 - INFO - Attempt 5: Status 401
2025-03-11 00:30:20,599 - INFO - Attempt 6: Status 401
2025-03-11 00:30:20,842 - INFO - Attempt 7: Status 401
2025-03-11 00:30:21,578 - INFO - Attempt 8: Status 403
2025-03-11 00:30:21,794 - INFO - Attempt 9: Status 403
2025-03-11 00:30:22,590 - INFO - Attempt 10: Status 429
2025-03-11 00:30:22,791 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-11 00:30:22,800 - INFO - Attempt 1: Status 429
2025-03-11 00:30:23,579 - INFO - Attempt 2: Status 429
2025-03-11 00:30:23,795 - INFO - Attempt 3: Status 429
2025-03-11 00:30:24,574 - INFO - Attempt 4: Status 429
2025-03-11 00:30:24,790 - INFO - Attempt 5: Status 429
2025-03-11 00:30:25,569 - INFO - Attempt 6: Status 429
2025-03-11 00:30:25,781 - INFO - Attempt 7: Status 429
2025-03-11 00:30:26,589 - INFO - Attempt 8: Status 429
2025-03-11 00:30:26,803 - INFO - Attempt 9: Status 429
2025-03-11 00:30:27,572 - INFO - Attempt 10: Status 429
2025-03-11 00:30:27,773 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-11 00:30:27,794 - INFO - Attempt 1: Status 403
2025-03-11 00:30:28,574 - INFO - Attempt 2: Status 403
2025-03-11 00:30:28,788 - INFO - Attempt 3: Status 429
2025-03-11 00:30:29,562 - INFO - Attempt 4: Status 429
2025-03-11 00:30:29,802 - INFO - Attempt 5: Status 429
2025-03-11 00:30:30,486 - INFO - Attempt 6: Status 429
2025-03-11 00:30:30,701 - INFO - Attempt 7: Status 429
2025-03-11 00:30:30,921 - INFO - Attempt 8: Status 429
2025-03-11 00:30:31,484 - INFO - Attempt 9: Status 429
2025-03-11 00:30:31,714 - INFO - Attempt 10: Status 429
2025-03-11 00:30:31,916 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-11 00:30:32,456 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-11 00:30:32,491 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-11 00:30:32,512 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-11 00:30:32,512 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-11 00:30:32,541 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-11 00:30:32,542 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-11 00:30:36,706 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-11 00:48:38,567 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-11 00:48:39,731 - INFO - Attempt 1: Status 400
2025-03-11 00:48:39,975 - INFO - Attempt 2: Status 400
2025-03-11 00:48:40,624 - INFO - Attempt 3: Status 400
2025-03-11 00:48:40,879 - INFO - Attempt 4: Status 400
2025-03-11 00:48:41,626 - INFO - Attempt 5: Status 400
2025-03-11 00:48:41,877 - INFO - Attempt 6: Status 400
2025-03-11 00:48:42,587 - INFO - Attempt 7: Status 400
2025-03-11 00:48:42,841 - INFO - Attempt 8: Status 400
2025-03-11 00:48:43,608 - INFO - Attempt 9: Status 400
2025-03-11 00:48:43,821 - INFO - Attempt 10: Status 429
2025-03-11 00:48:44,507 - INFO - Attempt 11: Status 429
2025-03-11 00:48:44,751 - INFO - Attempt 12: Status 429
2025-03-11 00:48:44,955 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-11 00:48:45,589 - INFO - Attempt 1: Status 401
2025-03-11 00:48:45,844 - INFO - Attempt 2: Status 401
2025-03-11 00:48:46,579 - INFO - Attempt 3: Status 401
2025-03-11 00:48:46,853 - INFO - Attempt 4: Status 401
2025-03-11 00:48:47,610 - INFO - Attempt 5: Status 401
2025-03-11 00:48:47,843 - INFO - Attempt 6: Status 401
2025-03-11 00:48:48,611 - INFO - Attempt 7: Status 401
2025-03-11 00:48:48,869 - INFO - Attempt 8: Status 401
2025-03-11 00:48:49,588 - INFO - Attempt 9: Status 401
2025-03-11 00:48:49,820 - INFO - Attempt 10: Status 429
2025-03-11 00:48:50,021 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-11 00:48:50,581 - INFO - Attempt 1: Status 401
2025-03-11 00:48:50,817 - INFO - Attempt 2: Status 401
2025-03-11 00:48:51,587 - INFO - Attempt 3: Status 401
2025-03-11 00:48:51,836 - INFO - Attempt 4: Status 401
2025-03-11 00:48:52,576 - INFO - Attempt 5: Status 401
2025-03-11 00:48:52,838 - INFO - Attempt 6: Status 401
2025-03-11 00:48:53,595 - INFO - Attempt 7: Status 401
2025-03-11 00:48:53,840 - INFO - Attempt 8: Status 401
2025-03-11 00:48:54,594 - INFO - Attempt 9: Status 401
2025-03-11 00:48:54,815 - INFO - Attempt 10: Status 429
2025-03-11 00:48:55,016 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-11 00:48:55,555 - ERROR - Request error on attempt 1: ('Connection broken: IncompleteRead(0 bytes read, 31 more expected)', IncompleteRead(0 bytes read, 31 more expected))
2025-03-11 00:48:55,610 - ERROR - Request error on attempt 2: ('Connection broken: IncompleteRead(0 bytes read, 31 more expected)', IncompleteRead(0 bytes read, 31 more expected))
2025-03-11 00:48:55,628 - INFO - Attempt 3: Status 429
2025-03-11 00:48:55,847 - INFO - Attempt 4: Status 429
2025-03-11 00:48:56,556 - INFO - Attempt 5: Status 429
2025-03-11 00:48:56,769 - INFO - Attempt 6: Status 429
2025-03-11 00:48:57,555 - INFO - Attempt 7: Status 429
2025-03-11 00:48:57,766 - INFO - Attempt 8: Status 429
2025-03-11 00:48:58,556 - INFO - Attempt 9: Status 429
2025-03-11 00:48:58,777 - INFO - Attempt 10: Status 429
2025-03-11 00:48:58,978 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-11 00:48:59,554 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-11 00:48:59,570 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-11 00:48:59,587 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-11 00:48:59,587 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-11 00:48:59,625 - ERROR - Error testing CAPTCHA for admin_login endpoint: ('Connection broken: IncompleteRead(0 bytes read, 31 more expected)', IncompleteRead(0 bytes read, 31 more expected))
2025-03-11 00:48:59,626 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-11 00:49:06,827 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-11 00:57:24,635 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-11 00:57:24,723 - INFO - Attempt 1: Status 500
2025-03-11 00:57:24,955 - INFO - Attempt 2: Status 500
2025-03-11 00:57:25,575 - INFO - Attempt 3: Status 500
2025-03-11 00:57:25,801 - INFO - Attempt 4: Status 500
2025-03-11 00:57:26,020 - INFO - Attempt 5: Status 500
2025-03-11 00:57:26,578 - INFO - Attempt 6: Status 500
2025-03-11 00:57:26,795 - INFO - Attempt 7: Status 500
2025-03-11 00:57:27,018 - INFO - Attempt 8: Status 500
2025-03-11 00:57:27,587 - INFO - Attempt 9: Status 500
2025-03-11 00:57:27,811 - INFO - Attempt 10: Status 500
2025-03-11 00:57:28,569 - INFO - Attempt 11: Status 500
2025-03-11 00:57:28,787 - INFO - Attempt 12: Status 500
2025-03-11 00:57:29,456 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-11 00:57:29,634 - INFO - Attempt 1: Status 401
2025-03-11 00:57:29,855 - INFO - Attempt 2: Status 401
2025-03-11 00:57:30,604 - INFO - Attempt 3: Status 401
2025-03-11 00:57:30,847 - INFO - Attempt 4: Status 401
2025-03-11 00:57:31,599 - INFO - Attempt 5: Status 401
2025-03-11 00:57:31,847 - INFO - Attempt 6: Status 401
2025-03-11 00:57:32,593 - INFO - Attempt 7: Status 401
2025-03-11 00:57:32,811 - INFO - Attempt 8: Status 401
2025-03-11 00:57:33,625 - INFO - Attempt 9: Status 401
2025-03-11 00:57:33,865 - INFO - Attempt 10: Status 401
2025-03-11 00:57:34,454 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-11 00:57:34,603 - INFO - Attempt 1: Status 401
2025-03-11 00:57:34,832 - INFO - Attempt 2: Status 401
2025-03-11 00:57:35,907 - INFO - Attempt 3: Status 401
2025-03-11 00:57:36,647 - INFO - Attempt 4: Status 401
2025-03-11 00:57:36,869 - INFO - Attempt 5: Status 401
2025-03-11 00:57:37,762 - INFO - Attempt 6: Status 401
2025-03-11 00:57:38,599 - INFO - Attempt 7: Status 401
2025-03-11 00:57:38,843 - INFO - Attempt 8: Status 401
2025-03-11 00:57:39,575 - INFO - Attempt 9: Status 401
2025-03-11 00:57:39,828 - INFO - Attempt 10: Status 401
2025-03-11 00:57:40,457 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-11 00:57:40,484 - INFO - Attempt 1: Status 405
2025-03-11 00:57:40,695 - INFO - Attempt 2: Status 405
2025-03-11 00:57:40,903 - INFO - Attempt 3: Status 405
2025-03-11 00:57:41,471 - INFO - Attempt 4: Status 405
2025-03-11 00:57:41,683 - INFO - Attempt 5: Status 405
2025-03-11 00:57:41,903 - INFO - Attempt 6: Status 405
2025-03-11 00:57:42,467 - INFO - Attempt 7: Status 405
2025-03-11 00:57:42,687 - INFO - Attempt 8: Status 405
2025-03-11 00:57:42,912 - INFO - Attempt 9: Status 405
2025-03-11 00:57:43,479 - INFO - Attempt 10: Status 405
2025-03-11 00:57:43,681 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-11 00:57:43,688 - INFO - CAPTCHA detected for login endpoint
2025-03-11 00:57:43,689 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-11 00:57:43,699 - INFO - CAPTCHA detected for register endpoint
2025-03-11 00:57:43,702 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-11 00:57:43,710 - INFO - CAPTCHA detected for admin_login endpoint
2025-03-11 00:57:43,713 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-11 00:57:45,907 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-11 01:07:08,786 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-11 01:07:09,560 - INFO - Attempt 1: Status 429
2025-03-11 01:07:09,769 - INFO - Attempt 2: Status 429
2025-03-11 01:07:10,496 - INFO - Attempt 3: Status 429
2025-03-11 01:07:10,763 - INFO - Attempt 4: Status 429
2025-03-11 01:07:11,483 - INFO - Attempt 5: Status 429
2025-03-11 01:07:11,699 - INFO - Attempt 6: Status 429
2025-03-11 01:07:11,946 - INFO - Attempt 7: Status 429
2025-03-11 01:07:12,577 - INFO - Attempt 8: Status 429
2025-03-11 01:07:12,793 - INFO - Attempt 9: Status 429
2025-03-11 01:07:13,555 - INFO - Attempt 10: Status 429
2025-03-11 01:07:13,765 - INFO - Attempt 11: Status 429
2025-03-11 01:07:14,555 - INFO - Attempt 12: Status 429
2025-03-11 01:07:14,757 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-11 01:07:14,816 - INFO - Attempt 1: Status 401
2025-03-11 01:07:15,595 - INFO - Attempt 2: Status 401
2025-03-11 01:07:15,824 - INFO - Attempt 3: Status 401
2025-03-11 01:07:16,586 - INFO - Attempt 4: Status 401
2025-03-11 01:07:16,843 - INFO - Attempt 5: Status 401
2025-03-11 01:07:17,595 - INFO - Attempt 6: Status 401
2025-03-11 01:07:17,855 - INFO - Attempt 7: Status 401
2025-03-11 01:07:18,587 - INFO - Attempt 8: Status 401
2025-03-11 01:07:18,845 - INFO - Attempt 9: Status 401
2025-03-11 01:07:19,568 - INFO - Attempt 10: Status 429
2025-03-11 01:07:19,769 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-11 01:07:19,801 - INFO - Attempt 1: Status 401
2025-03-11 01:07:20,587 - INFO - Attempt 2: Status 401
2025-03-11 01:07:20,842 - INFO - Attempt 3: Status 401
2025-03-11 01:07:21,595 - INFO - Attempt 4: Status 401
2025-03-11 01:07:21,856 - INFO - Attempt 5: Status 401
2025-03-11 01:07:22,620 - INFO - Attempt 6: Status 401
2025-03-11 01:07:22,876 - INFO - Attempt 7: Status 401
2025-03-11 01:07:23,644 - INFO - Attempt 8: Status 401
2025-03-11 01:07:23,913 - INFO - Attempt 9: Status 401
2025-03-11 01:07:24,586 - INFO - Attempt 10: Status 429
2025-03-11 01:07:24,788 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-11 01:07:24,811 - INFO - Attempt 1: Status 429
2025-03-11 01:07:25,036 - INFO - Attempt 2: Status 429
2025-03-11 01:07:25,575 - INFO - Attempt 3: Status 429
2025-03-11 01:07:25,795 - INFO - Attempt 4: Status 429
2025-03-11 01:07:26,021 - INFO - Attempt 5: Status 429
2025-03-11 01:07:26,596 - INFO - Attempt 6: Status 429
2025-03-11 01:07:26,815 - INFO - Attempt 7: Status 429
2025-03-11 01:07:27,024 - INFO - Attempt 8: Status 429
2025-03-11 01:07:27,587 - INFO - Attempt 9: Status 429
2025-03-11 01:07:27,823 - INFO - Attempt 10: Status 429
2025-03-11 01:07:28,030 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-11 01:07:28,470 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-11 01:07:28,569 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-11 01:07:28,586 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-11 01:07:28,587 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-11 01:07:28,625 - INFO - CAPTCHA detected for admin_login endpoint
2025-03-11 01:07:28,626 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-11 01:07:31,933 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-13 05:02:20,844 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-13 05:02:20,892 - INFO - Attempt 1: Status 500
2025-03-13 05:02:21,566 - INFO - Attempt 2: Status 500
2025-03-13 05:02:21,792 - INFO - Attempt 3: Status 500
2025-03-13 05:02:22,555 - INFO - Attempt 4: Status 500
2025-03-13 05:02:22,775 - INFO - Attempt 5: Status 500
2025-03-13 05:02:23,554 - INFO - Attempt 6: Status 500
2025-03-13 05:02:23,772 - INFO - Attempt 7: Status 500
2025-03-13 05:02:24,558 - INFO - Attempt 8: Status 500
2025-03-13 05:02:24,781 - INFO - Attempt 9: Status 500
2025-03-13 05:02:25,554 - INFO - Attempt 10: Status 500
2025-03-13 05:02:25,801 - INFO - Attempt 11: Status 500
2025-03-13 05:02:26,556 - INFO - Attempt 12: Status 500
2025-03-13 05:02:26,759 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-13 05:02:26,793 - INFO - Attempt 1: Status 401
2025-03-13 05:02:27,574 - INFO - Attempt 2: Status 401
2025-03-13 05:02:27,815 - INFO - Attempt 3: Status 401
2025-03-13 05:02:28,572 - INFO - Attempt 4: Status 401
2025-03-13 05:02:28,798 - INFO - Attempt 5: Status 401
2025-03-13 05:02:29,567 - INFO - Attempt 6: Status 401
2025-03-13 05:02:29,796 - INFO - Attempt 7: Status 401
2025-03-13 05:02:30,578 - INFO - Attempt 8: Status 401
2025-03-13 05:02:30,806 - INFO - Attempt 9: Status 401
2025-03-13 05:02:31,614 - INFO - Attempt 10: Status 401
2025-03-13 05:02:31,818 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-13 05:02:31,883 - INFO - Attempt 1: Status 401
2025-03-13 05:02:32,495 - INFO - Attempt 2: Status 401
2025-03-13 05:02:32,719 - INFO - Attempt 3: Status 401
2025-03-13 05:02:34,470 - INFO - Attempt 4: Status 401
2025-03-13 05:02:34,711 - INFO - Attempt 5: Status 401
2025-03-13 05:02:34,945 - INFO - Attempt 6: Status 401
2025-03-13 05:02:35,523 - INFO - Attempt 7: Status 401
2025-03-13 05:02:35,771 - INFO - Attempt 8: Status 401
2025-03-13 05:02:36,503 - INFO - Attempt 9: Status 401
2025-03-13 05:02:36,787 - INFO - Attempt 10: Status 401
2025-03-13 05:02:37,453 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-13 05:02:37,483 - INFO - Attempt 1: Status 405
2025-03-13 05:02:37,695 - INFO - Attempt 2: Status 405
2025-03-13 05:02:38,023 - INFO - Attempt 3: Status 405
2025-03-13 05:02:38,587 - INFO - Attempt 4: Status 405
2025-03-13 05:02:38,814 - INFO - Attempt 5: Status 429
2025-03-13 05:02:39,593 - INFO - Attempt 6: Status 429
2025-03-13 05:02:39,814 - INFO - Attempt 7: Status 429
2025-03-13 05:02:40,556 - INFO - Attempt 8: Status 429
2025-03-13 05:02:40,785 - INFO - Attempt 9: Status 429
2025-03-13 05:02:41,557 - INFO - Attempt 10: Status 429
2025-03-13 05:02:41,758 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-13 05:02:41,799 - INFO - CAPTCHA detected for login endpoint
2025-03-13 05:02:41,799 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-13 05:02:41,821 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-13 05:02:41,821 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-13 05:02:41,853 - INFO - CAPTCHA detected for admin_login endpoint
2025-03-13 05:02:41,855 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-13 05:02:45,833 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-13 05:02:54,734 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-13 05:02:54,787 - INFO - Attempt 1: Status 500
2025-03-13 05:02:55,577 - INFO - Attempt 2: Status 500
2025-03-13 05:02:55,812 - INFO - Attempt 3: Status 500
2025-03-13 05:02:56,585 - INFO - Attempt 4: Status 500
2025-03-13 05:02:56,811 - INFO - Attempt 5: Status 500
2025-03-13 05:02:57,560 - INFO - Attempt 6: Status 500
2025-03-13 05:02:57,787 - INFO - Attempt 7: Status 500
2025-03-13 05:02:58,559 - INFO - Attempt 8: Status 500
2025-03-13 05:02:58,772 - INFO - Attempt 9: Status 500
2025-03-13 05:02:59,557 - INFO - Attempt 10: Status 429
2025-03-13 05:02:59,765 - INFO - Attempt 11: Status 429
2025-03-13 05:03:00,560 - INFO - Attempt 12: Status 429
2025-03-13 05:03:00,765 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-13 05:03:00,809 - INFO - Attempt 1: Status 401
2025-03-13 05:03:01,558 - INFO - Attempt 2: Status 401
2025-03-13 05:03:01,806 - INFO - Attempt 3: Status 401
2025-03-13 05:03:02,555 - INFO - Attempt 4: Status 401
2025-03-13 05:03:02,787 - INFO - Attempt 5: Status 401
2025-03-13 05:03:03,554 - INFO - Attempt 6: Status 401
2025-03-13 05:03:03,801 - INFO - Attempt 7: Status 401
2025-03-13 05:03:04,559 - INFO - Attempt 8: Status 401
2025-03-13 05:03:04,790 - INFO - Attempt 9: Status 401
2025-03-13 05:03:05,558 - INFO - Attempt 10: Status 429
2025-03-13 05:03:05,760 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-13 05:03:05,789 - INFO - Attempt 1: Status 401
2025-03-13 05:03:06,579 - INFO - Attempt 2: Status 401
2025-03-13 05:03:06,817 - INFO - Attempt 3: Status 401
2025-03-13 05:03:07,555 - INFO - Attempt 4: Status 401
2025-03-13 05:03:07,785 - INFO - Attempt 5: Status 401
2025-03-13 05:03:08,561 - INFO - Attempt 6: Status 401
2025-03-13 05:03:08,795 - INFO - Attempt 7: Status 401
2025-03-13 05:03:09,554 - INFO - Attempt 8: Status 401
2025-03-13 05:03:09,789 - INFO - Attempt 9: Status 401
2025-03-13 05:03:10,570 - INFO - Attempt 10: Status 429
2025-03-13 05:03:10,771 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-13 05:03:10,787 - INFO - Attempt 1: Status 405
2025-03-13 05:03:11,554 - INFO - Attempt 2: Status 405
2025-03-13 05:03:11,765 - INFO - Attempt 3: Status 429
2025-03-13 05:03:12,554 - INFO - Attempt 4: Status 429
2025-03-13 05:03:12,767 - INFO - Attempt 5: Status 429
2025-03-13 05:03:13,609 - INFO - Attempt 6: Status 429
2025-03-13 05:03:13,829 - INFO - Attempt 7: Status 429
2025-03-13 05:03:14,473 - INFO - Attempt 8: Status 429
2025-03-13 05:03:14,716 - INFO - Attempt 9: Status 429
2025-03-13 05:03:14,943 - INFO - Attempt 10: Status 429
2025-03-13 05:03:15,455 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-13 05:03:15,466 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-13 05:03:15,481 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-13 05:03:15,497 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-13 05:03:15,506 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-13 05:03:15,515 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-13 05:03:15,527 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-13 05:03:18,938 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-15 23:24:11,612 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-15 23:24:11,702 - INFO - Attempt 1: Status 500
2025-03-15 23:24:11,928 - INFO - Attempt 2: Status 500
2025-03-15 23:24:12,489 - INFO - Attempt 3: Status 500
2025-03-15 23:24:12,712 - INFO - Attempt 4: Status 500
2025-03-15 23:24:12,922 - INFO - Attempt 5: Status 500
2025-03-15 23:24:13,575 - INFO - Attempt 6: Status 500
2025-03-15 23:24:13,812 - INFO - Attempt 7: Status 500
2025-03-15 23:24:14,624 - INFO - Attempt 8: Status 500
2025-03-15 23:24:14,852 - INFO - Attempt 9: Status 500
2025-03-15 23:24:15,495 - INFO - Attempt 10: Status 500
2025-03-15 23:24:15,718 - INFO - Attempt 11: Status 500
2025-03-15 23:24:16,550 - INFO - Attempt 12: Status 500
2025-03-15 23:24:16,759 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-15 23:24:16,807 - INFO - Attempt 1: Status 401
2025-03-15 23:24:17,594 - INFO - Attempt 2: Status 401
2025-03-15 23:24:17,850 - INFO - Attempt 3: Status 401
2025-03-15 23:24:18,587 - INFO - Attempt 4: Status 401
2025-03-15 23:24:18,838 - INFO - Attempt 5: Status 401
2025-03-15 23:24:19,609 - INFO - Attempt 6: Status 401
2025-03-15 23:24:19,862 - INFO - Attempt 7: Status 401
2025-03-15 23:24:20,592 - INFO - Attempt 8: Status 401
2025-03-15 23:24:20,839 - INFO - Attempt 9: Status 401
2025-03-15 23:24:21,616 - INFO - Attempt 10: Status 401
2025-03-15 23:24:21,817 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-15 23:24:21,846 - INFO - Attempt 1: Status 401
2025-03-15 23:24:22,606 - INFO - Attempt 2: Status 401
2025-03-15 23:24:22,835 - INFO - Attempt 3: Status 401
2025-03-15 23:24:23,605 - INFO - Attempt 4: Status 401
2025-03-15 23:24:23,832 - INFO - Attempt 5: Status 401
2025-03-15 23:24:24,609 - INFO - Attempt 6: Status 401
2025-03-15 23:24:24,839 - INFO - Attempt 7: Status 401
2025-03-15 23:24:25,585 - INFO - Attempt 8: Status 401
2025-03-15 23:24:25,825 - INFO - Attempt 9: Status 401
2025-03-15 23:24:26,620 - INFO - Attempt 10: Status 401
2025-03-15 23:24:26,821 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-15 23:24:26,832 - INFO - Attempt 1: Status 405
2025-03-15 23:24:27,575 - INFO - Attempt 2: Status 405
2025-03-15 23:24:27,856 - INFO - Attempt 3: Status 405
2025-03-15 23:24:28,602 - INFO - Attempt 4: Status 405
2025-03-15 23:24:28,858 - INFO - Attempt 5: Status 429
2025-03-15 23:24:29,514 - INFO - Attempt 6: Status 429
2025-03-15 23:24:29,749 - INFO - Attempt 7: Status 429
2025-03-15 23:24:30,567 - INFO - Attempt 8: Status 429
2025-03-15 23:24:30,820 - INFO - Attempt 9: Status 429
2025-03-15 23:24:31,555 - INFO - Attempt 10: Status 429
2025-03-15 23:24:31,767 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-15 23:24:31,800 - INFO - CAPTCHA detected for login endpoint
2025-03-15 23:24:31,801 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-15 23:24:31,816 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-15 23:24:31,816 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-15 23:24:31,840 - INFO - CAPTCHA detected for admin_login endpoint
2025-03-15 23:24:31,840 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-15 23:24:36,567 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-15 23:47:01,571 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-15 23:47:01,661 - INFO - Attempt 1: Status 500
2025-03-15 23:47:01,894 - INFO - Attempt 2: Status 500
2025-03-15 23:47:02,477 - INFO - Attempt 3: Status 500
2025-03-15 23:47:02,702 - INFO - Attempt 4: Status 500
2025-03-15 23:47:02,933 - INFO - Attempt 5: Status 500
2025-03-15 23:47:03,501 - INFO - Attempt 6: Status 500
2025-03-15 23:47:03,727 - INFO - Attempt 7: Status 500
2025-03-15 23:47:04,460 - INFO - Attempt 8: Status 500
2025-03-15 23:47:04,699 - INFO - Attempt 9: Status 500
2025-03-15 23:47:05,595 - INFO - Attempt 10: Status 500
2025-03-15 23:47:05,821 - INFO - Attempt 11: Status 500
2025-03-15 23:47:06,583 - INFO - Attempt 12: Status 500
2025-03-15 23:47:06,785 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-15 23:47:06,825 - INFO - Attempt 1: Status 401
2025-03-15 23:47:07,616 - INFO - Attempt 2: Status 401
2025-03-15 23:47:07,861 - INFO - Attempt 3: Status 401
2025-03-15 23:47:08,597 - INFO - Attempt 4: Status 401
2025-03-15 23:47:08,836 - INFO - Attempt 5: Status 401
2025-03-15 23:47:09,602 - INFO - Attempt 6: Status 401
2025-03-15 23:47:09,824 - INFO - Attempt 7: Status 401
2025-03-15 23:47:10,587 - INFO - Attempt 8: Status 401
2025-03-15 23:47:10,816 - INFO - Attempt 9: Status 401
2025-03-15 23:47:11,621 - INFO - Attempt 10: Status 401
2025-03-15 23:47:11,822 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-15 23:47:11,851 - INFO - Attempt 1: Status 401
2025-03-15 23:47:12,589 - INFO - Attempt 2: Status 401
2025-03-15 23:47:12,818 - INFO - Attempt 3: Status 401
2025-03-15 23:47:13,598 - INFO - Attempt 4: Status 401
2025-03-15 23:47:13,823 - INFO - Attempt 5: Status 401
2025-03-15 23:47:14,591 - INFO - Attempt 6: Status 401
2025-03-15 23:47:14,823 - INFO - Attempt 7: Status 401
2025-03-15 23:47:15,582 - INFO - Attempt 8: Status 401
2025-03-15 23:47:15,815 - INFO - Attempt 9: Status 401
2025-03-15 23:47:16,606 - INFO - Attempt 10: Status 401
2025-03-15 23:47:16,807 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-15 23:47:16,818 - INFO - Attempt 1: Status 405
2025-03-15 23:47:17,567 - INFO - Attempt 2: Status 405
2025-03-15 23:47:17,791 - INFO - Attempt 3: Status 429
2025-03-15 23:47:18,607 - INFO - Attempt 4: Status 429
2025-03-15 23:47:18,839 - INFO - Attempt 5: Status 429
2025-03-15 23:47:19,572 - INFO - Attempt 6: Status 429
2025-03-15 23:47:19,816 - INFO - Attempt 7: Status 429
2025-03-15 23:47:20,575 - INFO - Attempt 8: Status 429
2025-03-15 23:47:20,801 - INFO - Attempt 9: Status 429
2025-03-15 23:47:21,571 - INFO - Attempt 10: Status 429
2025-03-15 23:47:21,773 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-15 23:47:21,824 - INFO - CAPTCHA detected for login endpoint
2025-03-15 23:47:21,824 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-15 23:47:21,850 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-15 23:47:21,851 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-15 23:47:21,901 - INFO - CAPTCHA detected for admin_login endpoint
2025-03-15 23:47:21,906 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-15 23:47:30,874 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-15 23:51:50,651 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-15 23:51:50,690 - INFO - Attempt 1: Status 500
2025-03-15 23:51:50,906 - INFO - Attempt 2: Status 500
2025-03-15 23:51:51,568 - INFO - Attempt 3: Status 500
2025-03-15 23:51:51,793 - INFO - Attempt 4: Status 500
2025-03-15 23:51:52,554 - INFO - Attempt 5: Status 500
2025-03-15 23:51:52,783 - INFO - Attempt 6: Status 500
2025-03-15 23:51:53,554 - INFO - Attempt 7: Status 500
2025-03-15 23:51:53,770 - INFO - Attempt 8: Status 500
2025-03-15 23:51:54,554 - INFO - Attempt 9: Status 500
2025-03-15 23:51:54,763 - INFO - Attempt 10: Status 429
2025-03-15 23:51:55,553 - INFO - Attempt 11: Status 429
2025-03-15 23:51:55,765 - INFO - Attempt 12: Status 429
2025-03-15 23:51:55,967 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-15 23:51:56,565 - INFO - Attempt 1: Status 401
2025-03-15 23:51:56,810 - INFO - Attempt 2: Status 401
2025-03-15 23:51:57,556 - INFO - Attempt 3: Status 401
2025-03-15 23:51:57,786 - INFO - Attempt 4: Status 401
2025-03-15 23:51:58,554 - INFO - Attempt 5: Status 401
2025-03-15 23:51:58,784 - INFO - Attempt 6: Status 401
2025-03-15 23:51:59,554 - INFO - Attempt 7: Status 401
2025-03-15 23:51:59,778 - INFO - Attempt 8: Status 401
2025-03-15 23:52:00,554 - INFO - Attempt 9: Status 401
2025-03-15 23:52:00,776 - INFO - Attempt 10: Status 429
2025-03-15 23:52:00,977 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-15 23:52:01,561 - INFO - Attempt 1: Status 401
2025-03-15 23:52:01,791 - INFO - Attempt 2: Status 401
2025-03-15 23:52:02,510 - INFO - Attempt 3: Status 401
2025-03-15 23:52:02,756 - INFO - Attempt 4: Status 401
2025-03-15 23:52:03,507 - INFO - Attempt 5: Status 401
2025-03-15 23:52:03,763 - INFO - Attempt 6: Status 401
2025-03-15 23:52:04,491 - INFO - Attempt 7: Status 401
2025-03-15 23:52:04,735 - INFO - Attempt 8: Status 401
2025-03-15 23:52:05,541 - INFO - Attempt 9: Status 401
2025-03-15 23:52:05,753 - INFO - Attempt 10: Status 429
2025-03-15 23:52:06,453 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-15 23:52:06,511 - INFO - Attempt 1: Status 405
2025-03-15 23:52:06,743 - INFO - Attempt 2: Status 405
2025-03-15 23:52:07,465 - INFO - Attempt 3: Status 429
2025-03-15 23:52:07,677 - INFO - Attempt 4: Status 429
2025-03-15 23:52:07,886 - INFO - Attempt 5: Status 429
2025-03-15 23:52:08,571 - INFO - Attempt 6: Status 429
2025-03-15 23:52:08,781 - INFO - Attempt 7: Status 429
2025-03-15 23:52:09,587 - INFO - Attempt 8: Status 429
2025-03-15 23:52:09,827 - INFO - Attempt 9: Status 429
2025-03-15 23:52:10,579 - INFO - Attempt 10: Status 429
2025-03-15 23:52:10,780 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-15 23:52:10,798 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-15 23:52:10,819 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-15 23:52:10,829 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-15 23:52:10,829 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-15 23:52:10,841 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-15 23:52:10,842 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-15 23:52:16,456 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-15 23:52:30,656 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-15 23:52:30,668 - INFO - Attempt 1: Status 500
2025-03-15 23:52:30,880 - INFO - Attempt 2: Status 500
2025-03-15 23:52:31,564 - INFO - Attempt 3: Status 500
2025-03-15 23:52:31,780 - INFO - Attempt 4: Status 500
2025-03-15 23:52:32,559 - INFO - Attempt 5: Status 500
2025-03-15 23:52:32,778 - INFO - Attempt 6: Status 500
2025-03-15 23:52:33,559 - INFO - Attempt 7: Status 500
2025-03-15 23:52:33,777 - INFO - Attempt 8: Status 500
2025-03-15 23:52:34,559 - INFO - Attempt 9: Status 500
2025-03-15 23:52:34,770 - INFO - Attempt 10: Status 429
2025-03-15 23:52:35,557 - INFO - Attempt 11: Status 429
2025-03-15 23:52:35,765 - INFO - Attempt 12: Status 429
2025-03-15 23:52:35,967 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-15 23:52:36,583 - INFO - Attempt 1: Status 401
2025-03-15 23:52:36,820 - INFO - Attempt 2: Status 401
2025-03-15 23:52:37,566 - INFO - Attempt 3: Status 401
2025-03-15 23:52:37,786 - INFO - Attempt 4: Status 401
2025-03-15 23:52:38,578 - INFO - Attempt 5: Status 401
2025-03-15 23:52:38,812 - INFO - Attempt 6: Status 401
2025-03-15 23:52:39,580 - INFO - Attempt 7: Status 401
2025-03-15 23:52:39,811 - INFO - Attempt 8: Status 401
2025-03-15 23:52:40,581 - INFO - Attempt 9: Status 401
2025-03-15 23:52:40,795 - INFO - Attempt 10: Status 429
2025-03-15 23:52:40,996 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-15 23:52:41,590 - INFO - Attempt 1: Status 401
2025-03-15 23:52:41,824 - INFO - Attempt 2: Status 401
2025-03-15 23:52:42,788 - INFO - Attempt 3: Status 401
2025-03-15 23:52:43,581 - INFO - Attempt 4: Status 401
2025-03-15 23:52:43,804 - INFO - Attempt 5: Status 401
2025-03-15 23:52:44,590 - INFO - Attempt 6: Status 401
2025-03-15 23:52:44,823 - INFO - Attempt 7: Status 401
2025-03-15 23:52:45,591 - INFO - Attempt 8: Status 401
2025-03-15 23:52:45,838 - INFO - Attempt 9: Status 401
2025-03-15 23:52:46,566 - INFO - Attempt 10: Status 429
2025-03-15 23:52:46,768 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-15 23:52:46,793 - INFO - Attempt 1: Status 405
2025-03-15 23:52:47,560 - INFO - Attempt 2: Status 405
2025-03-15 23:52:47,770 - INFO - Attempt 3: Status 429
2025-03-15 23:52:48,562 - INFO - Attempt 4: Status 429
2025-03-15 23:52:48,770 - INFO - Attempt 5: Status 429
2025-03-15 23:52:49,561 - INFO - Attempt 6: Status 429
2025-03-15 23:52:49,770 - INFO - Attempt 7: Status 429
2025-03-15 23:52:50,563 - INFO - Attempt 8: Status 429
2025-03-15 23:52:50,773 - INFO - Attempt 9: Status 429
2025-03-15 23:52:51,573 - INFO - Attempt 10: Status 429
2025-03-15 23:52:51,774 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-15 23:52:51,796 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-15 23:52:51,810 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-15 23:52:51,826 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-15 23:52:51,827 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-15 23:52:51,849 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-15 23:52:51,850 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-15 23:52:56,780 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-16 21:53:32,519 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-16 21:53:32,539 - INFO - Attempt 1: Status 500
2025-03-16 21:53:32,751 - INFO - Attempt 2: Status 500
2025-03-16 21:53:33,165 - INFO - Attempt 3: Status 500
2025-03-16 21:53:33,380 - INFO - Attempt 4: Status 500
2025-03-16 21:53:33,592 - INFO - Attempt 5: Status 500
2025-03-16 21:53:33,805 - INFO - Attempt 6: Status 500
2025-03-16 21:53:34,015 - INFO - Attempt 7: Status 500
2025-03-16 21:53:34,227 - INFO - Attempt 8: Status 500
2025-03-16 21:53:34,438 - INFO - Attempt 9: Status 500
2025-03-16 21:53:34,650 - INFO - Attempt 10: Status 500
2025-03-16 21:53:34,860 - INFO - Attempt 11: Status 500
2025-03-16 21:53:35,169 - INFO - Attempt 12: Status 500
2025-03-16 21:53:35,373 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-16 21:53:35,402 - INFO - Attempt 1: Status 401
2025-03-16 21:53:35,624 - INFO - Attempt 2: Status 401
2025-03-16 21:53:36,169 - INFO - Attempt 3: Status 401
2025-03-16 21:53:36,391 - INFO - Attempt 4: Status 401
2025-03-16 21:53:36,615 - INFO - Attempt 5: Status 401
2025-03-16 21:53:36,847 - INFO - Attempt 6: Status 401
2025-03-16 21:53:37,082 - INFO - Attempt 7: Status 401
2025-03-16 21:53:37,308 - INFO - Attempt 8: Status 401
2025-03-16 21:53:37,543 - INFO - Attempt 9: Status 401
2025-03-16 21:53:37,771 - INFO - Attempt 10: Status 401
2025-03-16 21:53:37,972 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-16 21:53:37,995 - INFO - Attempt 1: Status 401
2025-03-16 21:53:38,221 - INFO - Attempt 2: Status 401
2025-03-16 21:53:38,447 - INFO - Attempt 3: Status 401
2025-03-16 21:53:38,672 - INFO - Attempt 4: Status 401
2025-03-16 21:53:39,194 - INFO - Attempt 5: Status 401
2025-03-16 21:53:39,416 - INFO - Attempt 6: Status 401
2025-03-16 21:53:39,636 - INFO - Attempt 7: Status 401
2025-03-16 21:53:39,859 - INFO - Attempt 8: Status 401
2025-03-16 21:53:40,085 - INFO - Attempt 9: Status 401
2025-03-16 21:53:40,312 - INFO - Attempt 10: Status 401
2025-03-16 21:53:40,517 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-16 21:53:40,529 - INFO - Attempt 1: Status 405
2025-03-16 21:53:40,739 - INFO - Attempt 2: Status 405
2025-03-16 21:53:41,166 - INFO - Attempt 3: Status 405
2025-03-16 21:53:41,378 - INFO - Attempt 4: Status 405
2025-03-16 21:53:41,592 - INFO - Attempt 5: Status 405
2025-03-16 21:53:41,806 - INFO - Attempt 6: Status 405
2025-03-16 21:53:42,020 - INFO - Attempt 7: Status 405
2025-03-16 21:53:42,235 - INFO - Attempt 8: Status 405
2025-03-16 21:53:42,451 - INFO - Attempt 9: Status 405
2025-03-16 21:53:42,683 - INFO - Attempt 10: Status 405
2025-03-16 21:53:42,884 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-16 21:53:42,901 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-16 21:53:42,917 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-16 21:53:42,957 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-16 21:53:42,958 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-16 21:53:42,975 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-16 21:53:42,979 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-16 21:53:44,342 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-16 21:53:46,257 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-16 21:53:46,272 - INFO - Attempt 1: Status 500
2025-03-16 21:53:46,481 - INFO - Attempt 2: Status 500
2025-03-16 21:53:46,701 - INFO - Attempt 3: Status 500
2025-03-16 21:53:47,171 - INFO - Attempt 4: Status 500
2025-03-16 21:53:47,389 - INFO - Attempt 5: Status 500
2025-03-16 21:53:47,601 - INFO - Attempt 6: Status 500
2025-03-16 21:53:47,812 - INFO - Attempt 7: Status 500
2025-03-16 21:53:48,026 - INFO - Attempt 8: Status 500
2025-03-16 21:53:48,241 - INFO - Attempt 9: Status 500
2025-03-16 21:53:48,454 - INFO - Attempt 10: Status 500
2025-03-16 21:53:48,666 - INFO - Attempt 11: Status 500
2025-03-16 21:53:48,877 - INFO - Attempt 12: Status 500
2025-03-16 21:53:49,082 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-16 21:53:49,103 - INFO - Attempt 1: Status 401
2025-03-16 21:53:49,352 - INFO - Attempt 2: Status 401
2025-03-16 21:53:49,577 - INFO - Attempt 3: Status 401
2025-03-16 21:53:49,802 - INFO - Attempt 4: Status 401
2025-03-16 21:53:50,029 - INFO - Attempt 5: Status 401
2025-03-16 21:53:50,276 - INFO - Attempt 6: Status 401
2025-03-16 21:53:50,505 - INFO - Attempt 7: Status 401
2025-03-16 21:53:50,743 - INFO - Attempt 8: Status 401
2025-03-16 21:53:50,966 - INFO - Attempt 9: Status 401
2025-03-16 21:53:51,190 - INFO - Attempt 10: Status 401
2025-03-16 21:53:51,392 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-16 21:53:51,417 - INFO - Attempt 1: Status 401
2025-03-16 21:53:51,641 - INFO - Attempt 2: Status 401
2025-03-16 21:53:51,864 - INFO - Attempt 3: Status 401
2025-03-16 21:53:52,090 - INFO - Attempt 4: Status 401
2025-03-16 21:53:52,313 - INFO - Attempt 5: Status 401
2025-03-16 21:53:52,543 - INFO - Attempt 6: Status 401
2025-03-16 21:53:52,770 - INFO - Attempt 7: Status 401
2025-03-16 21:53:52,995 - INFO - Attempt 8: Status 401
2025-03-16 21:53:53,223 - INFO - Attempt 9: Status 401
2025-03-16 21:53:53,445 - INFO - Attempt 10: Status 401
2025-03-16 21:53:53,648 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-16 21:53:53,662 - INFO - Attempt 1: Status 405
2025-03-16 21:53:53,875 - INFO - Attempt 2: Status 405
2025-03-16 21:53:54,162 - INFO - Attempt 3: Status 405
2025-03-16 21:53:54,375 - INFO - Attempt 4: Status 405
2025-03-16 21:53:54,584 - INFO - Attempt 5: Status 405
2025-03-16 21:53:55,161 - INFO - Attempt 6: Status 405
2025-03-16 21:53:55,375 - INFO - Attempt 7: Status 405
2025-03-16 21:53:55,587 - INFO - Attempt 8: Status 405
2025-03-16 21:53:55,799 - INFO - Attempt 9: Status 405
2025-03-16 21:53:56,163 - INFO - Attempt 10: Status 405
2025-03-16 21:53:56,364 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-16 21:53:56,377 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-16 21:53:56,385 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-16 21:53:56,416 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-16 21:53:56,416 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-16 21:53:56,428 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-16 21:53:56,429 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-16 21:53:57,568 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-25 22:37:27,248 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-25 22:37:27,269 - INFO - Attempt 1: Status 500
2025-03-25 22:37:27,480 - INFO - Attempt 2: Status 500
2025-03-25 22:37:27,689 - INFO - Attempt 3: Status 500
2025-03-25 22:37:27,902 - INFO - Attempt 4: Status 500
2025-03-25 22:37:28,113 - INFO - Attempt 5: Status 500
2025-03-25 22:37:28,323 - INFO - Attempt 6: Status 500
2025-03-25 22:37:28,535 - INFO - Attempt 7: Status 500
2025-03-25 22:37:28,746 - INFO - Attempt 8: Status 500
2025-03-25 22:37:28,955 - INFO - Attempt 9: Status 500
2025-03-25 22:37:29,168 - INFO - Attempt 10: Status 500
2025-03-25 22:37:29,384 - INFO - Attempt 11: Status 500
2025-03-25 22:37:29,622 - INFO - Attempt 12: Status 500
2025-03-25 22:37:29,841 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-25 22:37:29,866 - INFO - Attempt 1: Status 401
2025-03-25 22:37:30,173 - INFO - Attempt 2: Status 401
2025-03-25 22:37:30,400 - INFO - Attempt 3: Status 401
2025-03-25 22:37:31,170 - INFO - Attempt 4: Status 401
2025-03-25 22:37:31,399 - INFO - Attempt 5: Status 401
2025-03-25 22:37:31,623 - INFO - Attempt 6: Status 401
2025-03-25 22:37:31,844 - INFO - Attempt 7: Status 401
2025-03-25 22:37:32,064 - INFO - Attempt 8: Status 401
2025-03-25 22:37:32,286 - INFO - Attempt 9: Status 401
2025-03-25 22:37:32,516 - INFO - Attempt 10: Status 401
2025-03-25 22:37:32,719 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-25 22:37:32,741 - INFO - Attempt 1: Status 401
2025-03-25 22:37:32,967 - INFO - Attempt 2: Status 401
2025-03-25 22:37:33,209 - INFO - Attempt 3: Status 401
2025-03-25 22:37:33,437 - INFO - Attempt 4: Status 401
2025-03-25 22:37:33,656 - INFO - Attempt 5: Status 401
2025-03-25 22:37:33,880 - INFO - Attempt 6: Status 401
2025-03-25 22:37:34,175 - INFO - Attempt 7: Status 401
2025-03-25 22:37:34,405 - INFO - Attempt 8: Status 401
2025-03-25 22:37:34,642 - INFO - Attempt 9: Status 401
2025-03-25 22:37:35,184 - INFO - Attempt 10: Status 401
2025-03-25 22:37:35,385 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-25 22:37:35,400 - INFO - Attempt 1: Status 405
2025-03-25 22:37:35,611 - INFO - Attempt 2: Status 405
2025-03-25 22:37:35,822 - INFO - Attempt 3: Status 405
2025-03-25 22:37:36,171 - INFO - Attempt 4: Status 405
2025-03-25 22:37:36,388 - INFO - Attempt 5: Status 405
2025-03-25 22:37:36,602 - INFO - Attempt 6: Status 405
2025-03-25 22:37:37,167 - INFO - Attempt 7: Status 405
2025-03-25 22:37:37,386 - INFO - Attempt 8: Status 405
2025-03-25 22:37:37,597 - INFO - Attempt 9: Status 405
2025-03-25 22:37:37,807 - INFO - Attempt 10: Status 405
2025-03-25 22:37:38,009 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-25 22:37:38,019 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-25 22:37:38,027 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-25 22:37:38,048 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-25 22:37:38,049 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-25 22:37:38,059 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-25 22:37:38,060 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-25 22:37:39,153 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-25 22:37:40,476 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-25 22:37:40,487 - INFO - Attempt 1: Status 500
2025-03-25 22:37:41,177 - INFO - Attempt 2: Status 500
2025-03-25 22:37:41,385 - INFO - Attempt 3: Status 500
2025-03-25 22:37:41,596 - INFO - Attempt 4: Status 500
2025-03-25 22:37:41,807 - INFO - Attempt 5: Status 500
2025-03-25 22:37:42,164 - INFO - Attempt 6: Status 500
2025-03-25 22:37:42,378 - INFO - Attempt 7: Status 500
2025-03-25 22:37:42,589 - INFO - Attempt 8: Status 500
2025-03-25 22:37:42,800 - INFO - Attempt 9: Status 500
2025-03-25 22:37:43,015 - INFO - Attempt 10: Status 500
2025-03-25 22:37:43,226 - INFO - Attempt 11: Status 500
2025-03-25 22:37:43,438 - INFO - Attempt 12: Status 500
2025-03-25 22:37:43,641 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-25 22:37:43,668 - INFO - Attempt 1: Status 401
2025-03-25 22:37:43,896 - INFO - Attempt 2: Status 401
2025-03-25 22:37:44,141 - INFO - Attempt 3: Status 401
2025-03-25 22:37:44,366 - INFO - Attempt 4: Status 401
2025-03-25 22:37:44,595 - INFO - Attempt 5: Status 401
2025-03-25 22:37:44,819 - INFO - Attempt 6: Status 401
2025-03-25 22:37:45,044 - INFO - Attempt 7: Status 401
2025-03-25 22:37:45,276 - INFO - Attempt 8: Status 401
2025-03-25 22:37:45,510 - INFO - Attempt 9: Status 401
2025-03-25 22:37:45,730 - INFO - Attempt 10: Status 401
2025-03-25 22:37:45,931 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-25 22:37:45,953 - INFO - Attempt 1: Status 401
2025-03-25 22:37:46,188 - INFO - Attempt 2: Status 401
2025-03-25 22:37:46,412 - INFO - Attempt 3: Status 401
2025-03-25 22:37:46,640 - INFO - Attempt 4: Status 401
2025-03-25 22:37:46,867 - INFO - Attempt 5: Status 401
2025-03-25 22:37:47,093 - INFO - Attempt 6: Status 401
2025-03-25 22:37:47,314 - INFO - Attempt 7: Status 401
2025-03-25 22:37:47,544 - INFO - Attempt 8: Status 401
2025-03-25 22:37:47,766 - INFO - Attempt 9: Status 401
2025-03-25 22:37:47,991 - INFO - Attempt 10: Status 401
2025-03-25 22:37:48,193 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-25 22:37:48,206 - INFO - Attempt 1: Status 405
2025-03-25 22:37:48,419 - INFO - Attempt 2: Status 405
2025-03-25 22:37:48,633 - INFO - Attempt 3: Status 405
2025-03-25 22:37:48,846 - INFO - Attempt 4: Status 405
2025-03-25 22:37:49,058 - INFO - Attempt 5: Status 405
2025-03-25 22:37:49,271 - INFO - Attempt 6: Status 405
2025-03-25 22:37:49,485 - INFO - Attempt 7: Status 405
2025-03-25 22:37:49,697 - INFO - Attempt 8: Status 405
2025-03-25 22:37:49,909 - INFO - Attempt 9: Status 405
2025-03-25 22:37:50,122 - INFO - Attempt 10: Status 405
2025-03-25 22:37:50,345 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-25 22:37:50,365 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-25 22:37:50,376 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-25 22:37:50,396 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-25 22:37:50,397 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-25 22:37:50,407 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-25 22:37:50,408 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-25 22:37:51,433 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-03-26 17:06:00,543 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-26 17:06:00,557 - INFO - Attempt 1: Status 500
2025-03-26 17:06:00,778 - INFO - Attempt 2: Status 500
2025-03-26 17:06:00,995 - INFO - Attempt 3: Status 500
2025-03-26 17:06:01,206 - INFO - Attempt 4: Status 500
2025-03-26 17:06:01,418 - INFO - Attempt 5: Status 500
2025-03-26 17:06:01,683 - INFO - Attempt 6: Status 500
2025-03-26 17:06:02,166 - INFO - Attempt 7: Status 500
2025-03-26 17:06:02,381 - INFO - Attempt 8: Status 500
2025-03-26 17:06:02,593 - INFO - Attempt 9: Status 500
2025-03-26 17:06:02,805 - INFO - Attempt 10: Status 500
2025-03-26 17:06:03,057 - INFO - Attempt 11: Status 500
2025-03-26 17:06:03,281 - INFO - Attempt 12: Status 500
2025-03-26 17:06:03,486 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-26 17:06:03,521 - INFO - Attempt 1: Status 401
2025-03-26 17:06:03,764 - INFO - Attempt 2: Status 401
2025-03-26 17:06:04,174 - INFO - Attempt 3: Status 401
2025-03-26 17:06:04,402 - INFO - Attempt 4: Status 401
2025-03-26 17:06:04,626 - INFO - Attempt 5: Status 401
2025-03-26 17:06:04,858 - INFO - Attempt 6: Status 401
2025-03-26 17:06:05,085 - INFO - Attempt 7: Status 401
2025-03-26 17:06:05,313 - INFO - Attempt 8: Status 401
2025-03-26 17:06:05,549 - INFO - Attempt 9: Status 401
2025-03-26 17:06:05,784 - INFO - Attempt 10: Status 401
2025-03-26 17:06:05,985 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-26 17:06:06,011 - INFO - Attempt 1: Status 401
2025-03-26 17:06:06,241 - INFO - Attempt 2: Status 401
2025-03-26 17:06:06,468 - INFO - Attempt 3: Status 401
2025-03-26 17:06:06,717 - INFO - Attempt 4: Status 401
2025-03-26 17:06:06,978 - INFO - Attempt 5: Status 401
2025-03-26 17:06:07,216 - INFO - Attempt 6: Status 401
2025-03-26 17:06:07,454 - INFO - Attempt 7: Status 401
2025-03-26 17:06:07,682 - INFO - Attempt 8: Status 401
2025-03-26 17:06:07,905 - INFO - Attempt 9: Status 401
2025-03-26 17:06:08,156 - INFO - Attempt 10: Status 401
2025-03-26 17:06:08,358 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-26 17:06:08,376 - INFO - Attempt 1: Status 405
2025-03-26 17:06:08,596 - INFO - Attempt 2: Status 405
2025-03-26 17:06:08,810 - INFO - Attempt 3: Status 405
2025-03-26 17:06:09,028 - INFO - Attempt 4: Status 405
2025-03-26 17:06:09,245 - INFO - Attempt 5: Status 405
2025-03-26 17:06:09,460 - INFO - Attempt 6: Status 405
2025-03-26 17:06:09,677 - INFO - Attempt 7: Status 405
2025-03-26 17:06:09,920 - INFO - Attempt 8: Status 405
2025-03-26 17:06:10,138 - INFO - Attempt 9: Status 405
2025-03-26 17:06:10,355 - INFO - Attempt 10: Status 405
2025-03-26 17:06:10,564 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-26 17:06:10,590 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-26 17:06:10,604 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-26 17:06:10,641 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-26 17:06:10,642 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-26 17:06:10,657 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-03-26 17:06:10,658 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-26 17:06:12,511 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
2025-04-02 19:44:22,680 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-04-02 19:44:22,765 - INFO - Attempt 1: Status 403
2025-04-02 19:44:23,473 - INFO - Attempt 2: Status 403
2025-04-02 19:44:23,687 - INFO - Attempt 3: Status 403
2025-04-02 19:44:23,901 - INFO - Attempt 4: Status 403
2025-04-02 19:44:24,479 - INFO - Attempt 5: Status 403
2025-04-02 19:44:24,688 - INFO - Attempt 6: Status 403
2025-04-02 19:44:24,899 - INFO - Attempt 7: Status 403
2025-04-02 19:44:25,477 - INFO - Attempt 8: Status 403
2025-04-02 19:44:25,688 - INFO - Attempt 9: Status 403
2025-04-02 19:44:25,910 - INFO - Attempt 10: Status 403
2025-04-02 19:44:26,473 - INFO - Attempt 11: Status 403
2025-04-02 19:44:26,691 - INFO - Attempt 12: Status 403
2025-04-02 19:44:26,895 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-04-02 19:44:26,926 - INFO - Attempt 1: Status 403
2025-04-02 19:44:27,489 - INFO - Attempt 2: Status 403
2025-04-02 19:44:27,698 - INFO - Attempt 3: Status 403
2025-04-02 19:44:27,926 - INFO - Attempt 4: Status 403
2025-04-02 19:44:28,488 - INFO - Attempt 5: Status 403
2025-04-02 19:44:28,717 - INFO - Attempt 6: Status 403
2025-04-02 19:44:28,940 - INFO - Attempt 7: Status 403
2025-04-02 19:44:29,519 - INFO - Attempt 8: Status 403
2025-04-02 19:44:29,746 - INFO - Attempt 9: Status 403
2025-04-02 19:44:30,501 - INFO - Attempt 10: Status 403
2025-04-02 19:44:30,702 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-04-02 19:44:30,711 - INFO - Attempt 1: Status 403
2025-04-02 19:44:30,934 - INFO - Attempt 2: Status 403
2025-04-02 19:44:31,486 - INFO - Attempt 3: Status 403
2025-04-02 19:44:31,707 - INFO - Attempt 4: Status 403
2025-04-02 19:44:31,927 - INFO - Attempt 5: Status 403
2025-04-02 19:44:32,489 - INFO - Attempt 6: Status 403
2025-04-02 19:44:32,716 - INFO - Attempt 7: Status 403
2025-04-02 19:44:32,942 - INFO - Attempt 8: Status 403
2025-04-02 19:44:33,739 - INFO - Attempt 9: Status 403
2025-04-02 19:44:34,464 - INFO - Attempt 10: Status 403
2025-04-02 19:44:34,667 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-04-02 19:44:34,679 - INFO - Attempt 1: Status 403
2025-04-02 19:44:34,892 - INFO - Attempt 2: Status 403
2025-04-02 19:44:35,487 - INFO - Attempt 3: Status 403
2025-04-02 19:44:35,703 - INFO - Attempt 4: Status 403
2025-04-02 19:44:35,916 - INFO - Attempt 5: Status 403
2025-04-02 19:44:36,471 - INFO - Attempt 6: Status 403
2025-04-02 19:44:36,685 - INFO - Attempt 7: Status 403
2025-04-02 19:44:37,504 - INFO - Attempt 8: Status 403
2025-04-02 19:44:37,715 - INFO - Attempt 9: Status 403
2025-04-02 19:44:37,930 - INFO - Attempt 10: Status 403
2025-04-02 19:44:38,463 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-04-02 19:44:38,475 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-04-02 19:44:38,493 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-04-02 19:44:38,545 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-04-02 19:44:38,548 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-04-02 19:44:38,609 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
2025-04-02 19:44:38,637 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-04-02 19:44:40,825 - INFO - Distributed attack results: 13 blocked, 17 succeeded, 0 errored
