"""
Test Configuration
----------------
Configuration for security tests including endpoints, headers, and test data.
"""

# Base configuration
BASE_URL = "http://localhost:8000"
API_PREFIX = "/api"
API_BASE_URL = BASE_URL + API_PREFIX

# Test data
TEST_USER = {
    "username": "test_user",
    "password": "Test123!@#",
    "email": "<EMAIL>"
}

TEST_ADMIN = {
    "username": "admin",
    "password": "Admin123!@#"
}

# Endpoint groups for testing - Updated to match actual router paths
AUTH_ENDPOINTS = {
    "login": "/auth/token",  # Updated from /api/token
    "register": "/auth/register",  # Updated from /api/register
    "admin_login": "/auth/admin/login",  # Updated from /api/admin/login
    "logout": "/auth/logout",
    "refresh": "/auth/refresh",
    "verify_session": "/auth/verify-session"
}

USER_ENDPOINTS = {
    "profile": "/user/profile/update",
    "dashboard_stats": "/user/dashboard/stats",
    "transactions": "/user/transactions",
    "subscriptions": "/user/subscriptions",
    "available_packages": "/user/available-packages",
    "available_panels": "/user/available-panels"
}

TASK_ENDPOINTS = {
    "available_tasks": "/tasks/available",
    "start_task": "/tasks/{task_id}/start",
    "verify_task": "/tasks/{task_id}/verify",
    "claim_task": "/tasks/{task_id}/claim",
    "task_analytics": "/tasks/analytics"
}

PAYMENT_ENDPOINTS = {
    "create_payment": "/payment/create",
    "payment_webhook": "/payment/webhook",
    "transactions": "/payment/transactions"
}

SECURITY_ENDPOINTS = {
    "verify_captcha": "/security/verify-captcha",
    "security_stats": "/security/stats",
    "bot_patterns": "/security/bot-patterns",
    "security_dashboard": "/security/dashboard",
    "bot_detection_config": "/security/bot-detection/config"
}

MONITORING_ENDPOINTS = {
    "metrics": "/monitoring/metrics",
    "system": "/monitoring/system",
    "redis": "/monitoring/redis",
    "application": "/monitoring/application",
    "security": "/monitoring/security",
    "health": "/monitoring/health",
    "historical": "/monitoring/historical",
    "task_stats": "/monitoring/task-stats",
    "user_stats": "/monitoring/user-stats",
    "performance": "/monitoring/performance",
    "cache": "/monitoring/cache"
}

REFERRAL_ENDPOINTS = {
    "info": "/referrals/info",
    "users": "/referrals/users",
    "check_code": "/referrals/check/{code}",
    "process_commission": "/referrals/process-commission"
}

DAILY_TASK_ENDPOINTS = {
    "check_in": "/tasks/daily/check-in/{task_id}",
    "next_check_in": "/tasks/daily/next-check-in/{task_id}",
    "streak_info": "/tasks/daily/streak/{task_id}",
    "reset_streak": "/tasks/admin/daily/reset-streak/{task_id}"
}

MARZBAN_ENDPOINTS = {
    "panels": "/marzban/panels/",
    "panel_test": "/marzban/panels/{panel_id}/test",
    "panel_stats": "/marzban/panels/{panel_id}/stats",
    "subscription_usage": "/marzban/subscriptions/{subscription_url}/usage",
    "create_user": "/marzban/users/create"
}

# Critical endpoints that need extra security testing
CRITICAL_ENDPOINTS = {
    "authentication": [
        AUTH_ENDPOINTS["login"],
        AUTH_ENDPOINTS["admin_login"],
        AUTH_ENDPOINTS["register"]
    ],
    "payment": [
        PAYMENT_ENDPOINTS["create_payment"],
        PAYMENT_ENDPOINTS["payment_webhook"]
    ],
    "user_data": [
        USER_ENDPOINTS["profile"],
        USER_ENDPOINTS["transactions"]
    ],
    "admin": [
        "/admin/*"  # Updated from /api/admin/*
    ]
}

# Required security headers for all responses
REQUIRED_HEADERS = {
    "Content-Security-Policy": (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
        "style-src 'self' 'unsafe-inline'; "
        "img-src 'self' data: https:; "
        "connect-src 'self'; "
        "frame-ancestors 'none'; "
        "form-action 'self'; "
        "base-uri 'self'"
    ),
    "X-XSS-Protection": "1; mode=block",
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": (
        "accelerometer=(), camera=(), geolocation=(), "
        "gyroscope=(), magnetometer=(), microphone=(), "
        "payment=(), usb=()"
    ),
    "Cache-Control": "no-store, max-age=0",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains"
}

# Rate limiting configuration
RATE_LIMIT_CONFIG = {
    "default": {
        "rate": "100/minute",
        "burst": 20,
        "requests_per_minute": 100
    },
    "auth": {
        "rate": "5/minute",
        "burst": 3,
        "requests_per_minute": 5
    },
    "critical": {
        "rate": "30/minute",
        "burst": 5,
        "requests_per_minute": 30
    }
}

# Test tokens for authentication
TEST_TOKENS = {
    "valid_user": "eyJ0eXAi...",  # Replace with actual test token
    "valid_admin": "eyJ0eXAi...",  # Replace with actual test token
    "expired": "eyJ0eXAi..."  # Replace with expired test token
}

# Authentication configuration for testing
AUTH_CONFIG = {
    "token_expiration": 3600,  # 1 hour in seconds
    "refresh_expiration": 86400,  # 24 hours in seconds
    "password_requirements": {
        "min_length": 8,
        "require_uppercase": True,
        "require_lowercase": True,
        "require_digit": True,
        "require_special": True,
        "max_login_attempts": 5,
        "lockout_duration": 300  # 5 minutes in seconds
    },
    "rbac": {
        "roles": ["user", "moderator", "admin", "superadmin"],
        "permissions": {
            "user": ["read:own", "update:own"],
            "moderator": ["read:own", "read:any", "update:own", "update:any"],
            "admin": ["read:own", "read:any", "update:own", "update:any", "delete:any"],
            "superadmin": ["read:own", "read:any", "update:own", "update:any", "delete:any", "create:any"]
        },
        "protected_endpoints": {
            "/user/profile": ["user", "moderator", "admin", "superadmin"],
            "/admin/users": ["admin", "superadmin"],
            "/admin/system": ["superadmin"]
        }
    }
}

# Bot detection patterns for testing
BOT_DETECTION_PATTERNS = [
    {
        "name": "Generic Bot",
        "user_agent": "Bot/1.0",
        "headers": {}
    },
    {
        "name": "Web Crawler",
        "user_agent": "Crawler/2.0",
        "headers": {}
    },
    {
        "name": "Spider Bot",
        "user_agent": "Spider/1.1",
        "headers": {}
    },
    {
        "name": "Scraper",
        "user_agent": "Scraper/3.0",
        "headers": {}
    }
]

# CAPTCHA configuration for testing
CAPTCHA_CONFIG = {
    "site_key": "1x00000000000000000000AA",  # Cloudflare Turnstile test key
    "secret_key": "1x0000000000000000000000000000000AA",  # Cloudflare Turnstile test secret
    "verify_endpoint": "/security/verify-captcha",
    "test_token": "valid_token_for_testing",
    "test_actions": ["login", "register", "payment"],
    "bypass_token": "bypass_captcha_in_test_mode",
    # Set specific endpoint configurations
    "/auth/token": {
        "enabled": True,
        "required": True
    },
    "/auth/login": {
        "enabled": True,
        "required": True
    },
    "/auth/register": {
        "enabled": True,
        "required": True
    },
    "/auth/admin/login": {
        "enabled": True,
        "required": True
    },
    "/payment/create": {
        "enabled": True,
        "required": True
    },
    "/payment/webhook": {
        "enabled": False,  # Webhooks shouldn't require CAPTCHA
        "required": False
    },
    # Default configuration for endpoints not explicitly configured
    "default": {
        "enabled": True,
        "required": False
    }
}

# Bot detection configuration for testing
BOT_DETECTION_CONFIG = {
    "enabled": True,
    "test_mode": True,
    "suspicious_threshold": 0.7,
    "block_threshold": 0.9,
    "patterns": BOT_DETECTION_PATTERNS,
    "test_endpoints": [
        AUTH_ENDPOINTS["login"],
        AUTH_ENDPOINTS["register"],
        PAYMENT_ENDPOINTS["create_payment"]
    ],
    "bypassed_user_agents": ["SecurityTestAgent/1.0"],
    "detection_endpoint": "/security/bot-detection/check",
    # Default configuration for endpoints not explicitly configured
    "default": {
        "enabled": True,
        "threshold": 0.8,
        "requires_captcha": False
    }
} 