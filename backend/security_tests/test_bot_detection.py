#!/usr/bin/env python
"""
Bot Detection Test
----------------
Test bot detection mechanisms across endpoints.
"""
import logging
from typing import Dict, Any, List, Tuple
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_request_details,
    log_error_with_traceback,
    make_request,
    get_critical_endpoints,
    log_test_result,
    log_environment_info
)
from test_config import API_BASE_URL, BOT_DETECTION_CONFIG

logger = logging.getLogger(__name__)

def test_bot_detection() -> Dict[str, Any]:
    """Test bot detection across critical endpoints"""
    print_header("Testing Bot Detection")
    
    # Log environment info for debugging
    log_environment_info()
    
    # Setup results dictionary
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "endpoints_tested": [],
        "errors": []
    }
    
    # Get all critical endpoints
    endpoints = get_critical_endpoints()
    print_info(f"Testing {len(endpoints)} endpoints for bot detection")
    results["total_tests"] = len(endpoints)
    
    # Test each endpoint
    for endpoint in endpoints:
        # Create URL for endpoint (replace placeholders)
        url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        try:
            # Check if this endpoint should have bot detection
            logger.info(f"Testing bot detection for {endpoint}")
            
            # Make a normal request
            headers_normal = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}
            success, response = make_request(url, headers=headers_normal)
            
            if not success:
                results["failed_tests"] += 1
                results["errors"].append(f"Failed to connect to {url}: {response.get('error')}")
                continue
            
            # Get response status code and bot detection headers
            status_code = response.get("status_code", 0)
            endpoint_exists = status_code != 404  # Endpoint exists if not 404
            requires_auth = status_code == 401  # Requires authentication if 401
            method_allowed = status_code != 405  # Method is allowed if not 405
            
            # Check normal request for bot detection headers
            headers = response.get("headers", {})
            has_bot_header = False
            bot_detected = False
            
            # Check for X-Bot-Detection header
            if "x-bot-detection" in {k.lower(): v for k, v in headers.items()}:
                has_bot_header = True
                bot_detected = str({k.lower(): v for k, v in headers.items()}.get("x-bot-detection", "")).lower() == "true"
                logger.info(f"Normal request - Bot detection header present: {bot_detected}")
            
            # Make a bot-like request
            headers_bot = {"User-Agent": BOT_DETECTION_CONFIG["patterns"][0]["user_agent"]}
            bot_success, bot_response = make_request(url, headers=headers_bot)
            
            if not bot_success:
                results["failed_tests"] += 1
                results["errors"].append(f"Failed to connect to {url} with bot UA: {bot_response.get('error')}")
                continue
            
            # Check bot request for bot detection headers
            bot_headers = bot_response.get("headers", {})
            bot_status_code = bot_response.get("status_code", 0)
            has_bot_header_in_bot_req = False
            bot_detected_in_bot_req = False
            
            # Check for X-Bot-Detection header in bot request
            if "x-bot-detection" in {k.lower(): v for k, v in bot_headers.items()}:
                has_bot_header_in_bot_req = True
                bot_detected_in_bot_req = str({k.lower(): v for k, v in bot_headers.items()}.get("x-bot-detection", "")).lower() == "true"
                logger.info(f"Bot request - Bot detection header present: {bot_detected_in_bot_req}")
            
            # Verify bot detection behavior
            expected_behavior = False
            
            # If endpoint doesn't exist (404) or method not allowed (405), not a critical error
            if not endpoint_exists or not method_allowed:
                expected_behavior = True
                logger.info(f"Endpoint {endpoint} returned {status_code} - skipping bot detection verification")
            # If endpoint requires authentication (401), check if response is consistent
            elif requires_auth:
                # Both requests should return 401
                expected_behavior = True
                logger.info(f"Endpoint {endpoint} requires authentication (401) - skipping bot detection verification")
            # For endpoints that should have bot detection
            elif endpoint in BOT_DETECTION_CONFIG["test_endpoints"]:
                # Should have bot headers and detect the bot UA
                expected_behavior = (
                    has_bot_header and 
                    has_bot_header_in_bot_req and 
                    (not bot_detected) and  # Normal request not detected as bot
                    bot_detected_in_bot_req  # Bot request detected as bot
                )
                logger.info(f"Endpoint {endpoint} should have bot detection - checking headers and detection")
            else:
                # For non-critical endpoints, at least bot headers should be present
                expected_behavior = has_bot_header
                logger.info(f"Endpoint {endpoint} - checking for presence of bot detection headers")
            
            if expected_behavior:
                results["successful_tests"] += 1
                print_success(f"Bot detection working as expected for {endpoint}")
            else:
                results["failed_tests"] += 1
                print_error(f"Bot detection not working as expected for {endpoint}")
                if endpoint in BOT_DETECTION_CONFIG["test_endpoints"]:
                    print_warning(f"  Headers present: normal={has_bot_header}, bot={has_bot_header_in_bot_req}")
                    print_warning(f"  Bot detected: normal={bot_detected}, bot={bot_detected_in_bot_req}")
                else:
                    print_warning(f"  Bot detection headers not present")
            
            # Log test result
            log_test_result(
                f"Bot Detection - {endpoint}",
                expected_behavior,
                {
                    "endpoint_exists": endpoint_exists,
                    "requires_auth": requires_auth,
                    "method_allowed": method_allowed,
                    "has_bot_header": has_bot_header,
                    "has_bot_header_in_bot_req": has_bot_header_in_bot_req,
                    "bot_detected": bot_detected,
                    "bot_detected_in_bot_req": bot_detected_in_bot_req,
                    "is_critical_endpoint": endpoint in BOT_DETECTION_CONFIG["test_endpoints"]
                }
            )
        except Exception as e:
            results["failed_tests"] += 1
            error_msg = f"Error testing bot detection for {endpoint}: {str(e)}"
            results["errors"].append(error_msg)
            print_error(error_msg)
            log_error_with_traceback(e, f"Testing bot detection for endpoint {endpoint}")
    
    # Calculate success rate
    success_rate = (results["successful_tests"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0
    
    # Print summary
    print_header("Bot Detection Test Summary")
    print_info(f"Total Endpoints Tested: {results['total_tests']}")
    print_info(f"Successful Tests: {results['successful_tests']}")
    print_info(f"Failed Tests: {results['failed_tests']}")
    print_info(f"Success Rate: {success_rate:.2f}%")
    
    if results["errors"]:
        print_warning("\nErrors Encountered:")
        for error in results["errors"]:
            print_warning(f"  {error}")
    
    return results

if __name__ == "__main__":
    test_bot_detection() 