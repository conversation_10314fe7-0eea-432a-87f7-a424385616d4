<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test Report - 20250313_050322</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .summary {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            color: #28a745;
        }
        .failure {
            color: #dc3545;
        }
        .test-result {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .test-name {
            font-weight: bold;
            font-size: 1.2em;
        }
        .test-status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-failure {
            background-color: #f8d7da;
            color: #721c24;
        }
        .test-details {
            background-color: #f8f9fa;
            border: 1px solid #eee;
            border-radius: 3px;
            padding: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>Security Test Report</h1>
    <div class="timestamp">Generated on: 2025-03-13 05:03:22</div>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Tests: 2</p>
        <p class="success">Passed: 2</p>
        <p class="failure">Failed: 0</p>
        <p>Success Rate: 100.00%</p>
    </div>
    
    <h2>Test Results</h2>

    <div class="test-result">
        <div class="test-header">
            <div class="test-name">test_security_headers.py</div>
            <div class="test-status status-success">PASSED</div>
        </div>
        <div>Duration: 4.58 seconds</div>
        <div>Started: 2025-03-13T05:02:46.995297</div>
        
        <h3>Output</h3>
        <div class="test-details">
================================================================================
                    SECURITY HEADERS AND BOT DETECTION TEST                     
================================================================================


Testing security headers on main page...
2025-03-13 05:02:50,602 - INFO - Testing security headers for: http://localhost:8000
✓ All required security headers are present

Testing Content-Security-Policy header...
2025-03-13 05:02:50,630 - WARNING - CSP allows unsafe inline scripts
⚠ CSP allows unsafe inline scripts - this can reduce XSS protection
✓ Content-Security-Policy is properly configured

Testing security headers on API endpoints...
2025-03-13 05:02:50,630 - INFO - Testing security headers for: http://localhost:8000/api/tasks/available
✓ Endpoint /api/tasks/available has all required security headers
2025-03-13 05:02:50,680 - INFO - Testing security headers for: http://localhost:8000/api/security/verify-captcha
✗ Endpoint /api/security/verify-captcha is missing headers: Content-Security-Policy, X-XSS-Protection, X-Content-Type-Options, X-Frame-Options, Referrer-Policy, Permissions-Policy, Cache-Control
2025-03-13 05:02:50,695 - INFO - Testing security headers for: http://localhost:8000/auth/token
✗ Endpoint /auth/token is missing headers: Content-Security-Policy, X-XSS-Protection, X-Content-Type-Options, X-Frame-Options, Referrer-Policy, Permissions-Policy, Cache-Control

Testing bot detection...
2025-03-13 05:02:50,719 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)': 404
2025-03-13 05:02:50,731 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)': 404
2025-03-13 05:02:50,745 - INFO - Bot test with User-Agent 'python-requests/2.25.1': 404
2025-03-13 05:02:50,767 - INFO - Bot test with User-Agent 'Wget/1.20.3 (linux-gnu)': 404
2025-03-13 05:02:50,776 - INFO - Bot test with User-Agent 'curl/7.68.0': 404
⚠ Legitimate search engine bots blocked: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)
⚠ Automation tools allowed without CAPTCHA: python-requests/2.25.1, Wget/1.20.3 (linux-gnu), curl/7.68.0
✗ Bot detection has issues

Detailed Bot Detection Results:
  Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html): ✓ OK
  Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm): ✓ OK
  python-requests/2.25.1: ✓ OK
  Wget/1.20.3 (linux-gnu): ✓ OK
  curl/7.68.0: ✓ OK
</div>
        
        
    </div>

    <div class="test-result">
        <div class="test-header">
            <div class="test-name">test_rate_limit_captcha.py</div>
            <div class="test-status status-success">PASSED</div>
        </div>
        <div>Duration: 31.14 seconds</div>
        <div>Started: 2025-03-13T05:02:51.577723</div>
        
        <h3>Output</h3>
        <div class="test-details">
================================================================================
                         RATE LIMITING AND CAPTCHA TEST                         
================================================================================


Testing login rate limiting...
2025-03-13 05:02:54,734 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-13 05:02:54,787 - INFO - Attempt 1: Status 500
2025-03-13 05:02:55,577 - INFO - Attempt 2: Status 500
2025-03-13 05:02:55,812 - INFO - Attempt 3: Status 500
2025-03-13 05:02:56,585 - INFO - Attempt 4: Status 500
2025-03-13 05:02:56,811 - INFO - Attempt 5: Status 500
2025-03-13 05:02:57,560 - INFO - Attempt 6: Status 500
2025-03-13 05:02:57,787 - INFO - Attempt 7: Status 500
2025-03-13 05:02:58,559 - INFO - Attempt 8: Status 500
2025-03-13 05:02:58,772 - INFO - Attempt 9: Status 500
2025-03-13 05:02:59,557 - INFO - Attempt 10: Status 429
2025-03-13 05:02:59,765 - INFO - Attempt 11: Status 429
2025-03-13 05:03:00,560 - INFO - Attempt 12: Status 429
✓ Rate limiting triggered appropriately (after 10 attempts)
✓ Login rate limiting is working

Rate limit headers for login attempts:
  Attempt 1: OK - Headers: {}
  Attempt 2: OK - Headers: {}
  Attempt 3: OK - Headers: {}
  Attempt 4: OK - Headers: {}
  Attempt 5: OK - Headers: {}
  Attempt 6: OK - Headers: {}
  Attempt 7: OK - Headers: {}
  Attempt 8: OK - Headers: {}
  Attempt 9: OK - Headers: {}
  Attempt 10: BLOCKED - Headers: {'retry-after': '3600', 'x-ratelimit-limit': '10', 'x-ratelimit-remaining': '0', 'x-ratelimit-reset': '3600', 'x-ratelimit-window': '300', 'x-ratelimit-type': 'login'}
  Attempt 11: BLOCKED - Headers: {'retry-after': '3600', 'x-ratelimit-limit': '10', 'x-ratelimit-remaining': '0', 'x-ratelimit-reset': '3600', 'x-ratelimit-window': '300', 'x-ratelimit-type': 'login'}
  Attempt 12: BLOCKED - Headers: {'retry-after': '3600', 'x-ratelimit-limit': '10', 'x-ratelimit-remaining': '0', 'x-ratelimit-reset': '3600', 'x-ratelimit-window': '300', 'x-ratelimit-type': 'login'}

Testing rate limiting for API endpoints...

Testing rate limiting for /api/tasks/available...
2025-03-13 05:03:00,765 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-13 05:03:00,809 - INFO - Attempt 1: Status 401
2025-03-13 05:03:01,558 - INFO - Attempt 2: Status 401
2025-03-13 05:03:01,806 - INFO - Attempt 3: Status 401
2025-03-13 05:03:02,555 - INFO - Attempt 4: Status 401
2025-03-13 05:03:02,787 - INFO - Attempt 5: Status 401
2025-03-13 05:03:03,554 - INFO - Attempt 6: Status 401
2025-03-13 05:03:03,801 - INFO - Attempt 7: Status 401
2025-03-13 05:03:04,559 - INFO - Attempt 8: Status 401
2025-03-13 05:03:04,790 - INFO - Attempt 9: Status 401
2025-03-13 05:03:05,558 - INFO - Attempt 10: Status 429
✓ Rate limiting triggered appropriately (after 10 attempts)
✓ Rate limiting is working for /api/tasks/available

Testing rate limiting for /api/tasks/analytics...
2025-03-13 05:03:05,760 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-13 05:03:05,789 - INFO - Attempt 1: Status 401
2025-03-13 05:03:06,579 - INFO - Attempt 2: Status 401
2025-03-13 05:03:06,817 - INFO - Attempt 3: Status 401
2025-03-13 05:03:07,555 - INFO - Attempt 4: Status 401
2025-03-13 05:03:07,785 - INFO - Attempt 5: Status 401
2025-03-13 05:03:08,561 - INFO - Attempt 6: Status 401
2025-03-13 05:03:08,795 - INFO - Attempt 7: Status 401
2025-03-13 05:03:09,554 - INFO - Attempt 8: Status 401
2025-03-13 05:03:09,789 - INFO - Attempt 9: Status 401
2025-03-13 05:03:10,570 - INFO - Attempt 10: Status 429
✓ Rate limiting triggered appropriately (after 10 attempts)
✓ Rate limiting is working for /api/tasks/analytics

Testing rate limiting for /auth/register...
2025-03-13 05:03:10,771 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-13 05:03:10,787 - INFO - Attempt 1: Status 405
2025-03-13 05:03:11,554 - INFO - Attempt 2: Status 405
2025-03-13 05:03:11,765 - INFO - Attempt 3: Status 429
2025-03-13 05:03:12,554 - INFO - Attempt 4: Status 429
2025-03-13 05:03:12,767 - INFO - Attempt 5: Status 429
2025-03-13 05:03:13,609 - INFO - Attempt 6: Status 429
2025-03-13 05:03:13,829 - INFO - Attempt 7: Status 429
2025-03-13 05:03:14,473 - INFO - Attempt 8: Status 429
2025-03-13 05:03:14,716 - INFO - Attempt 9: Status 429
2025-03-13 05:03:14,943 - INFO - Attempt 10: Status 429
⚠ Rate limiting triggered too early (after 3 attempts)
✓ Rate limiting is working for /auth/register

Testing Cloudflare CAPTCHA integration...
2025-03-13 05:03:15,455 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-13 05:03:15,466 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-13 05:03:15,481 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-13 05:03:15,497 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-13 05:03:15,506 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-13 05:03:15,515 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
⚠ No CAPTCHA detected for login endpoint
⚠ No CAPTCHA detected for register endpoint
⚠ No CAPTCHA detected for admin_login endpoint

Simulating distributed attack...
2025-03-13 05:03:15,527 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-13 05:03:18,938 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
✗ Distributed attack was not effectively blocked (block rate: 0.00%)

Sample distributed attack results:
  Request from IP ***************: ALLOWED
  Request from IP ***************: ALLOWED
  Request from IP ***************: ALLOWED
  Request from IP **************: ALLOWED
  Request from IP *************: ALLOWED
</div>
        
        
    </div>

</body>
</html>
