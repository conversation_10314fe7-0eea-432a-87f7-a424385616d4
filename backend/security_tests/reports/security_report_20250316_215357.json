{"timestamp": "2025-03-16T21:53:57.626255", "total_tests": 2, "passed": 2, "failed": 0, "success_rate": 100.0, "results": [{"script": "test_security_headers.py", "start_time": "2025-03-16T21:53:44.455234", "success": true, "output": "\n================================================================================\n                    SECURITY HEADERS AND BOT DETECTION TEST                     \n================================================================================\n\n\nTesting security headers on main page...\n2025-03-16 21:53:45,275 - INFO - Testing security headers for: http://localhost:8000\n2025-03-16 21:53:45,288 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ All required security headers are present\n\nTesting Content-Security-Policy header...\n✓ Content-Security-Policy is properly configured\n\nTesting security headers on API endpoints...\n2025-03-16 21:53:45,289 - INFO - Testing security headers for: http://localhost:8000/api/tasks/available\n2025-03-16 21:53:45,318 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ Endpoint /api/tasks/available has all required security headers\n2025-03-16 21:53:45,319 - INFO - Testing security headers for: http://localhost:8000/api/security/verify-captcha\n2025-03-16 21:53:45,330 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ Endpoint /api/security/verify-captcha has all required security headers\n2025-03-16 21:53:45,331 - INFO - Testing security headers for: http://localhost:8000/auth/token\n2025-03-16 21:53:45,341 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ Endpoint /auth/token has all required security headers\n\nTesting bot detection...\n2025-03-16 21:53:45,353 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)': 404\n2025-03-16 21:53:45,368 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)': 404\n2025-03-16 21:53:45,380 - INFO - Bot test with User-Agent 'python-requests/2.25.1': 404\n2025-03-16 21:53:45,391 - INFO - Bot test with User-Agent 'Wget/1.20.3 (linux-gnu)': 404\n2025-03-16 21:53:45,400 - INFO - Bot test with User-Agent 'curl/7.68.0': 404\n⚠ Legitimate search engine bots blocked: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\n⚠ Automation tools allowed without CAPTCHA: python-requests/2.25.1, Wget/1.20.3 (linux-gnu), curl/7.68.0\n✗ Bot detection has issues\n\nDetailed Bot Detection Results:\n  Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html): ✓ OK\n  Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm): ✓ OK\n  python-requests/2.25.1: ✓ OK\n  Wget/1.20.3 (linux-gnu): ✓ OK\n  curl/7.68.0: ✓ OK\n", "error": "", "duration": 0.9852955341339111}, {"script": "test_rate_limit_captcha.py", "start_time": "2025-03-16T21:53:45.440665", "success": true, "output": "\n================================================================================\n                         RATE LIMITING AND CAPTCHA TEST                         \n================================================================================\n\n\nTesting login rate limiting...\n2025-03-16 21:53:46,257 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts\n2025-03-16 21:53:46,272 - INFO - Attempt 1: Status 500\n2025-03-16 21:53:46,481 - INFO - Attempt 2: Status 500\n2025-03-16 21:53:46,701 - INFO - Attempt 3: Status 500\n2025-03-16 21:53:47,171 - INFO - Attempt 4: Status 500\n2025-03-16 21:53:47,389 - INFO - Attempt 5: Status 500\n2025-03-16 21:53:47,601 - INFO - Attempt 6: Status 500\n2025-03-16 21:53:47,812 - INFO - Attempt 7: Status 500\n2025-03-16 21:53:48,026 - INFO - Attempt 8: Status 500\n2025-03-16 21:53:48,241 - INFO - Attempt 9: Status 500\n2025-03-16 21:53:48,454 - INFO - Attempt 10: Status 500\n2025-03-16 21:53:48,666 - INFO - Attempt 11: Status 500\n2025-03-16 21:53:48,877 - INFO - Attempt 12: Status 500\n✗ Rate limiting was not triggered after 12 attempts\n✗ Login rate limiting is not working properly\n\nRate limit headers for login attempts:\n  Attempt 1: OK - Headers: {}\n  Attempt 2: OK - Headers: {}\n  Attempt 3: OK - Headers: {}\n  Attempt 4: OK - Headers: {}\n  Attempt 5: OK - Headers: {}\n  Attempt 6: OK - Headers: {}\n  Attempt 7: OK - Headers: {}\n  Attempt 8: OK - Headers: {}\n  Attempt 9: OK - Headers: {}\n  Attempt 10: OK - Headers: {}\n  Attempt 11: OK - Headers: {}\n  Attempt 12: OK - Headers: {}\n\nTesting rate limiting for API endpoints...\n\nTesting rate limiting for /api/tasks/available...\n2025-03-16 21:53:49,082 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts\n2025-03-16 21:53:49,103 - INFO - Attempt 1: Status 401\n2025-03-16 21:53:49,352 - INFO - Attempt 2: Status 401\n2025-03-16 21:53:49,577 - INFO - Attempt 3: Status 401\n2025-03-16 21:53:49,802 - INFO - Attempt 4: Status 401\n2025-03-16 21:53:50,029 - INFO - Attempt 5: Status 401\n2025-03-16 21:53:50,276 - INFO - Attempt 6: Status 401\n2025-03-16 21:53:50,505 - INFO - Attempt 7: Status 401\n2025-03-16 21:53:50,743 - INFO - Attempt 8: Status 401\n2025-03-16 21:53:50,966 - INFO - Attempt 9: Status 401\n2025-03-16 21:53:51,190 - INFO - Attempt 10: Status 401\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /api/tasks/available\n\nTesting rate limiting for /api/tasks/analytics...\n2025-03-16 21:53:51,392 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts\n2025-03-16 21:53:51,417 - INFO - Attempt 1: Status 401\n2025-03-16 21:53:51,641 - INFO - Attempt 2: Status 401\n2025-03-16 21:53:51,864 - INFO - Attempt 3: Status 401\n2025-03-16 21:53:52,090 - INFO - Attempt 4: Status 401\n2025-03-16 21:53:52,313 - INFO - Attempt 5: Status 401\n2025-03-16 21:53:52,543 - INFO - Attempt 6: Status 401\n2025-03-16 21:53:52,770 - INFO - Attempt 7: Status 401\n2025-03-16 21:53:52,995 - INFO - Attempt 8: Status 401\n2025-03-16 21:53:53,223 - INFO - Attempt 9: Status 401\n2025-03-16 21:53:53,445 - INFO - Attempt 10: Status 401\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /api/tasks/analytics\n\nTesting rate limiting for /auth/register...\n2025-03-16 21:53:53,648 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts\n2025-03-16 21:53:53,662 - INFO - Attempt 1: Status 405\n2025-03-16 21:53:53,875 - INFO - Attempt 2: Status 405\n2025-03-16 21:53:54,162 - INFO - Attempt 3: Status 405\n2025-03-16 21:53:54,375 - INFO - Attempt 4: Status 405\n2025-03-16 21:53:54,584 - INFO - Attempt 5: Status 405\n2025-03-16 21:53:55,161 - INFO - Attempt 6: Status 405\n2025-03-16 21:53:55,375 - INFO - Attempt 7: Status 405\n2025-03-16 21:53:55,587 - INFO - Attempt 8: Status 405\n2025-03-16 21:53:55,799 - INFO - Attempt 9: Status 405\n2025-03-16 21:53:56,163 - INFO - Attempt 10: Status 405\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /auth/register\n\nTesting Cloudflare CAPTCHA integration...\n2025-03-16 21:53:56,364 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token\n2025-03-16 21:53:56,377 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior\n2025-03-16 21:53:56,385 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register\n2025-03-16 21:53:56,416 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior\n2025-03-16 21:53:56,416 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login\n2025-03-16 21:53:56,428 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior\n⚠ No CAPTCHA detected for login endpoint\n⚠ No CAPTCHA detected for register endpoint\n⚠ No CAPTCHA detected for admin_login endpoint\n\nSimulating distributed attack...\n2025-03-16 21:53:56,429 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent\n2025-03-16 21:53:57,568 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored\n✗ Distributed attack was not effectively blocked (block rate: 0.00%)\n\nSample distributed attack results:\n  Request from IP **************: ALLOWED\n  Request from IP **************: ALLOWED\n  Request from IP ***************: ALLOWED\n  Request from IP **************: ALLOWED\n  Request from IP ***************: ALLOWED\n", "error": "", "duration": 12.18336820602417}]}