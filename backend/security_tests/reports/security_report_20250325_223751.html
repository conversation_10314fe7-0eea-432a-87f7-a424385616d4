<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test Report - 20250325_223751</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .summary {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            color: #28a745;
        }
        .failure {
            color: #dc3545;
        }
        .test-result {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .test-name {
            font-weight: bold;
            font-size: 1.2em;
        }
        .test-status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-failure {
            background-color: #f8d7da;
            color: #721c24;
        }
        .test-details {
            background-color: #f8f9fa;
            border: 1px solid #eee;
            border-radius: 3px;
            padding: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>Security Test Report</h1>
    <div class="timestamp">Generated on: 2025-03-25 22:37:51</div>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Tests: 2</p>
        <p class="success">Passed: 2</p>
        <p class="failure">Failed: 0</p>
        <p>Success Rate: 100.00%</p>
    </div>
    
    <h2>Test Results</h2>

    <div class="test-result">
        <div class="test-header">
            <div class="test-name">test_security_headers.py</div>
            <div class="test-status status-success">PASSED</div>
        </div>
        <div>Duration: 0.43 seconds</div>
        <div>Started: 2025-03-25T22:37:39.285305</div>
        
        <h3>Output</h3>
        <div class="test-details">
================================================================================
                    SECURITY HEADERS AND BOT DETECTION TEST                     
================================================================================


Testing security headers on main page...
2025-03-25 22:37:39,531 - INFO - Testing security headers for: http://localhost:8000
2025-03-25 22:37:39,543 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ All required security headers are present

Testing Content-Security-Policy header...
✓ Content-Security-Policy is properly configured

Testing security headers on API endpoints...
2025-03-25 22:37:39,543 - INFO - Testing security headers for: http://localhost:8000/api/tasks/available
2025-03-25 22:37:39,565 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ Endpoint /api/tasks/available has all required security headers
2025-03-25 22:37:39,566 - INFO - Testing security headers for: http://localhost:8000/api/security/verify-captcha
2025-03-25 22:37:39,578 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ Endpoint /api/security/verify-captcha has all required security headers
2025-03-25 22:37:39,579 - INFO - Testing security headers for: http://localhost:8000/auth/token
2025-03-25 22:37:39,595 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ Endpoint /auth/token has all required security headers

Testing bot detection...
2025-03-25 22:37:39,607 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)': 404
2025-03-25 22:37:39,628 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)': 404
2025-03-25 22:37:39,647 - INFO - Bot test with User-Agent 'python-requests/2.25.1': 404
2025-03-25 22:37:39,664 - INFO - Bot test with User-Agent 'Wget/1.20.3 (linux-gnu)': 404
2025-03-25 22:37:39,682 - INFO - Bot test with User-Agent 'curl/7.68.0': 404
⚠ Legitimate search engine bots blocked: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)
⚠ Automation tools allowed without CAPTCHA: python-requests/2.25.1, Wget/1.20.3 (linux-gnu), curl/7.68.0
✗ Bot detection has issues

Detailed Bot Detection Results:
  Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html): ✓ OK
  Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm): ✓ OK
  python-requests/2.25.1: ✓ OK
  Wget/1.20.3 (linux-gnu): ✓ OK
  curl/7.68.0: ✓ OK
</div>
        
        
    </div>

    <div class="test-result">
        <div class="test-header">
            <div class="test-name">test_rate_limit_captcha.py</div>
            <div class="test-status status-success">PASSED</div>
        </div>
        <div>Duration: 11.78 seconds</div>
        <div>Started: 2025-03-25T22:37:39.720466</div>
        
        <h3>Output</h3>
        <div class="test-details">
================================================================================
                         RATE LIMITING AND CAPTCHA TEST                         
================================================================================


Testing login rate limiting...
2025-03-25 22:37:40,476 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-25 22:37:40,487 - INFO - Attempt 1: Status 500
2025-03-25 22:37:41,177 - INFO - Attempt 2: Status 500
2025-03-25 22:37:41,385 - INFO - Attempt 3: Status 500
2025-03-25 22:37:41,596 - INFO - Attempt 4: Status 500
2025-03-25 22:37:41,807 - INFO - Attempt 5: Status 500
2025-03-25 22:37:42,164 - INFO - Attempt 6: Status 500
2025-03-25 22:37:42,378 - INFO - Attempt 7: Status 500
2025-03-25 22:37:42,589 - INFO - Attempt 8: Status 500
2025-03-25 22:37:42,800 - INFO - Attempt 9: Status 500
2025-03-25 22:37:43,015 - INFO - Attempt 10: Status 500
2025-03-25 22:37:43,226 - INFO - Attempt 11: Status 500
2025-03-25 22:37:43,438 - INFO - Attempt 12: Status 500
✗ Rate limiting was not triggered after 12 attempts
✗ Login rate limiting is not working properly

Rate limit headers for login attempts:
  Attempt 1: OK - Headers: {}
  Attempt 2: OK - Headers: {}
  Attempt 3: OK - Headers: {}
  Attempt 4: OK - Headers: {}
  Attempt 5: OK - Headers: {}
  Attempt 6: OK - Headers: {}
  Attempt 7: OK - Headers: {}
  Attempt 8: OK - Headers: {}
  Attempt 9: OK - Headers: {}
  Attempt 10: OK - Headers: {}
  Attempt 11: OK - Headers: {}
  Attempt 12: OK - Headers: {}

Testing rate limiting for API endpoints...

Testing rate limiting for /api/tasks/available...
2025-03-25 22:37:43,641 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-25 22:37:43,668 - INFO - Attempt 1: Status 401
2025-03-25 22:37:43,896 - INFO - Attempt 2: Status 401
2025-03-25 22:37:44,141 - INFO - Attempt 3: Status 401
2025-03-25 22:37:44,366 - INFO - Attempt 4: Status 401
2025-03-25 22:37:44,595 - INFO - Attempt 5: Status 401
2025-03-25 22:37:44,819 - INFO - Attempt 6: Status 401
2025-03-25 22:37:45,044 - INFO - Attempt 7: Status 401
2025-03-25 22:37:45,276 - INFO - Attempt 8: Status 401
2025-03-25 22:37:45,510 - INFO - Attempt 9: Status 401
2025-03-25 22:37:45,730 - INFO - Attempt 10: Status 401
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /api/tasks/available

Testing rate limiting for /api/tasks/analytics...
2025-03-25 22:37:45,931 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-25 22:37:45,953 - INFO - Attempt 1: Status 401
2025-03-25 22:37:46,188 - INFO - Attempt 2: Status 401
2025-03-25 22:37:46,412 - INFO - Attempt 3: Status 401
2025-03-25 22:37:46,640 - INFO - Attempt 4: Status 401
2025-03-25 22:37:46,867 - INFO - Attempt 5: Status 401
2025-03-25 22:37:47,093 - INFO - Attempt 6: Status 401
2025-03-25 22:37:47,314 - INFO - Attempt 7: Status 401
2025-03-25 22:37:47,544 - INFO - Attempt 8: Status 401
2025-03-25 22:37:47,766 - INFO - Attempt 9: Status 401
2025-03-25 22:37:47,991 - INFO - Attempt 10: Status 401
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /api/tasks/analytics

Testing rate limiting for /auth/register...
2025-03-25 22:37:48,193 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-25 22:37:48,206 - INFO - Attempt 1: Status 405
2025-03-25 22:37:48,419 - INFO - Attempt 2: Status 405
2025-03-25 22:37:48,633 - INFO - Attempt 3: Status 405
2025-03-25 22:37:48,846 - INFO - Attempt 4: Status 405
2025-03-25 22:37:49,058 - INFO - Attempt 5: Status 405
2025-03-25 22:37:49,271 - INFO - Attempt 6: Status 405
2025-03-25 22:37:49,485 - INFO - Attempt 7: Status 405
2025-03-25 22:37:49,697 - INFO - Attempt 8: Status 405
2025-03-25 22:37:49,909 - INFO - Attempt 9: Status 405
2025-03-25 22:37:50,122 - INFO - Attempt 10: Status 405
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /auth/register

Testing Cloudflare CAPTCHA integration...
2025-03-25 22:37:50,345 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-25 22:37:50,365 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-25 22:37:50,376 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-25 22:37:50,396 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-25 22:37:50,397 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-25 22:37:50,407 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
⚠ No CAPTCHA detected for login endpoint
⚠ No CAPTCHA detected for register endpoint
⚠ No CAPTCHA detected for admin_login endpoint

Simulating distributed attack...
2025-03-25 22:37:50,408 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-25 22:37:51,433 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
✗ Distributed attack was not effectively blocked (block rate: 0.00%)

Sample distributed attack results:
  Request from IP *************: ALLOWED
  Request from IP **************: ALLOWED
  Request from IP ***************: ALLOWED
  Request from IP ***************: ALLOWED
  Request from IP ***************: ALLOWED
</div>
        
        
    </div>

</body>
</html>
