#!/usr/bin/env python
"""
Security Tests Runner
-------------------
Run all security tests and generate a comprehensive report.
"""
import logging
import json
import os
from datetime import datetime
from typing import Dict, Any, List
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_environment_info
)
from test_security_headers import test_security_headers
from test_rate_limits import test_rate_limits
from test_captcha import test_captcha
from test_bot_detection import test_bot_detection
from test_input_validation import test_input_validation
from test_auth import test_auth
from test_xss_csrf import test_xss_csrf
from test_encryption import test_encryption

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.FileHandler(f"logs/security_tests_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_security_tests() -> Dict[str, Any]:
    """Run all security tests and generate a comprehensive report"""
    print_header("Starting Security Tests")
    
    # Create logs directory if it doesn't exist
    os.makedirs("logs", exist_ok=True)
    
    # Log environment information
    log_environment_info()
    
    # Initialize results
    results = {
        "timestamp": datetime.now().isoformat(),
        "tests": {},
        "summary": {
            "total_tests": 0,
            "successful_tests": 0,
            "failed_tests": 0,
            "success_rate": 0
        },
        "critical_issues": [],
        "warnings": []
    }
    
    try:
        # Run security headers test
        print_header("Running Security Headers Test")
        results["tests"]["security_headers"] = test_security_headers()
        
        # Run rate limits test
        print_header("Running Rate Limits Test")
        results["tests"]["rate_limits"] = test_rate_limits()
        
        # Run CAPTCHA test
        print_header("Running CAPTCHA Test")
        results["tests"]["captcha"] = test_captcha()
        
        # Run bot detection test
        print_header("Running Bot Detection Test")
        results["tests"]["bot_detection"] = test_bot_detection()
        
        # Run input validation test
        print_header("Running Input Validation Test")
        results["tests"]["input_validation"] = test_input_validation()
        
        # Run authentication test
        print_header("Running Authentication Test")
        results["tests"]["auth"] = test_auth()
        
        # Run XSS and CSRF test
        print_header("Running XSS and CSRF Test")
        results["tests"]["xss_csrf"] = test_xss_csrf()
        
        # Run encryption test
        print_header("Running Encryption Test")
        results["tests"]["encryption"] = test_encryption()
        
        # Calculate overall statistics
        for test_name, test_results in results["tests"].items():
            # Ensure test_results has valid counts
            if "total_tests" not in test_results or test_results["total_tests"] <= 0:
                logger.warning(f"Test '{test_name}' reported invalid total_tests: {test_results.get('total_tests')}")
                test_results["total_tests"] = 1  # Set minimum to avoid division by zero
                
            if "successful_tests" not in test_results:
                test_results["successful_tests"] = 0
                
            if "failed_tests" not in test_results:
                test_results["failed_tests"] = test_results["total_tests"] - test_results["successful_tests"]
            
            # Ensure successful_tests doesn't exceed total_tests
            if test_results["successful_tests"] > test_results["total_tests"]:
                logger.warning(f"Test '{test_name}' reported more successful tests than total tests")
                test_results["successful_tests"] = test_results["total_tests"]
            
            # Update summary counters
            results["summary"]["total_tests"] += test_results["total_tests"]
            results["summary"]["successful_tests"] += test_results["successful_tests"]
            results["summary"]["failed_tests"] += test_results["failed_tests"]
            
            # Check for critical issues
            if test_results["failed_tests"] > 0:
                # Calculate success rate per test, capped at 100%
                success_rate = min(
                    (test_results["successful_tests"] / test_results["total_tests"] * 100), 
                    100.0
                ) if test_results["total_tests"] > 0 else 0
                
                results["critical_issues"].append({
                    "test": test_name,
                    "failed_tests": test_results["failed_tests"],
                    "success_rate": success_rate
                })
            
            # Check for warnings
            if test_results.get("errors"):
                results["warnings"].extend([
                    {
                        "test": test_name,
                        "error": error
                    }
                    for error in test_results["errors"]
                ])
        
        # Ensure successful_tests doesn't exceed total_tests at the summary level
        if results["summary"]["successful_tests"] > results["summary"]["total_tests"]:
            logger.warning("Total successful tests exceeds total tests count, capping to 100%")
            results["summary"]["successful_tests"] = results["summary"]["total_tests"]
        
        # Calculate overall success rate (cap at 100%)
        results["summary"]["success_rate"] = min(
            (results["summary"]["successful_tests"] / results["summary"]["total_tests"] * 100),
            100.0
        ) if results["summary"]["total_tests"] > 0 else 0
        
        # Print comprehensive summary
        print_header("Security Tests Summary")
        print_info(f"Total Tests: {results['summary']['total_tests']}")
        print_info(f"Successful Tests: {results['summary']['successful_tests']}")
        print_info(f"Failed Tests: {results['summary']['failed_tests']}")
        print_info(f"Overall Success Rate: {results['summary']['success_rate']:.2f}%")
        
        if results["critical_issues"]:
            print_warning("\nCritical Issues Found:")
            for issue in results["critical_issues"]:
                print_warning(f"  {issue['test']}:")
                print_warning(f"    Failed Tests: {issue['failed_tests']}")
                print_warning(f"    Success Rate: {issue['success_rate']:.2f}%")
        
        if results["warnings"]:
            print_warning("\nWarnings:")
            for warning in results["warnings"]:
                print_warning(f"  {warning['test']}: {warning['error']}")
        
        # Save detailed results to file
        output_file = f"logs/security_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, "w") as f:
            json.dump(results, f, indent=2)
        
        print_info(f"\nDetailed results saved to: {output_file}")
        
        # Return success/failure based on critical issues
        return len(results["critical_issues"]) == 0
        
    except Exception as e:
        logger.error(f"Error running security tests: {str(e)}")
        print_error(f"Error running security tests: {str(e)}")
        return False

if __name__ == "__main__":
    success = run_security_tests()
    if success:
        print_success("\nAll security tests passed successfully!")
    else:
        print_error("\nSecurity tests failed. Please check the logs for details.") 