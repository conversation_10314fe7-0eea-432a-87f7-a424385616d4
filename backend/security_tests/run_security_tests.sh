#!/bin/bash

# Security Tests Runner
# Runs security tests for the API system with comprehensive reporting

# Set default values
API_BASE_URL=${API_BASE_URL:-"http://localhost:8000"}
TEST_MODE=${TEST_MODE:-"true"}
TEST_SCRIPT=""
SCRIPT_DIR=$(dirname "$0")
LOGS_DIR="$SCRIPT_DIR/logs"
REPORTS_DIR="$SCRIPT_DIR/reports"
RUN_ALL=false
CLOUDFLARE_CAPTCHA=${ENABLE_CLOUDFLARE_CAPTCHA:-"false"}
DETAILED_OUTPUT=false

# Display help message
usage() {
    echo "Usage: $0 [options] [test_script]"
    echo
    echo "Options:"
    echo "  --all                Run all security test scripts"
    echo "  --headers            Run security headers test"
    echo "  --rate-limit         Run rate limiting test"
    echo "  --bot-detection      Run bot detection test"
    echo "  --captcha            Run CAPTCHA integration test"
    echo "  --url <url>          Set API base URL (default: http://localhost:8000)"
    echo "  --cloudflare         Enable Cloudflare CAPTCHA tests"
    echo "  --test-mode <bool>   Set test mode (true/false, default: true)"
    echo "  --detailed           Show detailed output"
    echo "  --help               Display this help message"
    echo
    echo "Examples:"
    echo "  $0 --all             Run all security tests"
    echo "  $0 --headers         Run security headers test"
    echo "  $0 --cloudflare --captcha  Run CAPTCHA tests with Cloudflare enabled"
    echo "  $0 --url https://api.example.com --headers"
    echo "  $0 --all --detailed  Run all tests with detailed output"
    echo
}

# Create logs and reports directories if they don't exist
create_dirs() {
    mkdir -p "$LOGS_DIR"
    mkdir -p "$REPORTS_DIR"
    echo "Logs will be saved to $LOGS_DIR"
    echo "Reports will be saved to $REPORTS_DIR"
}

# Check if virtual environment exists and activate it
activate_venv() {
    if [ -d "$SCRIPT_DIR/../venv" ]; then
        echo "Activating virtual environment..."
        source "$SCRIPT_DIR/../venv/bin/activate"
        echo "Virtual environment activated successfully"
    else
        echo "Virtual environment not found at $SCRIPT_DIR/../venv"
        echo "Using system Python..."
    fi
}

# Run a specific test script
run_test() {
    local script=$1
    local test_type=$2
    
    echo "----------------------------------------------"
    echo "Running $test_type tests: $script"
    echo "----------------------------------------------"
    echo "API Base URL: $API_BASE_URL"
    echo "Test Mode: $TEST_MODE"
    echo "Cloudflare CAPTCHA: $CLOUDFLARE_CAPTCHA"
    
    # Set environment variables
    export API_BASE_URL=$API_BASE_URL
    export TEST_MODE=$TEST_MODE
    export ENABLE_CLOUDFLARE_CAPTCHA=$CLOUDFLARE_CAPTCHA
    
    # Run the test script - ensure we're in the script directory
    timestamp=$(date +"%Y%m%d_%H%M%S")
    
    # Replace spaces with underscores in test_type for the filename
    test_type_safe=$(echo "$test_type" | tr ' ' '_')
    log_file="$LOGS_DIR/${test_type_safe}_test_${timestamp}.log"
    
    # Set PYTHONPATH to include the parent directory
    export PYTHONPATH="$SCRIPT_DIR/..:$PYTHONPATH"
    
    # Run from the script directory
    cd "$SCRIPT_DIR"
    
    # Use python3 instead of python
    if [ "$DETAILED_OUTPUT" = true ]; then
        # Show detailed output and save to log
        python3 "$script" 2>&1 | tee "$log_file"
    else
        # Run with minimal output and save detailed output to log
        echo "Running $test_type tests..."
        python3 "$script" > "$log_file" 2>&1
        
        # Check exit status
        status=$?
        if [ $status -eq 0 ]; then
            echo "✅ $test_type tests passed"
        else
            echo "❌ $test_type tests failed"
            echo "Last 5 lines of log:"
            tail -n 5 "$log_file"
        fi
    fi
    
    # Check exit status
    status=${PIPESTATUS[0]}
    if [ $status -eq 0 ]; then
        echo "✅ $test_type tests completed successfully"
    else
        echo "❌ $test_type tests failed with status $status"
    fi
    
    echo "Log saved to: $log_file"
    echo "----------------------------------------------"
    return $status
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --all)
                RUN_ALL=true
                shift
                ;;
            --headers)
                TEST_SCRIPT="test_security_headers.py"
                shift
                ;;
            --rate-limit)
                TEST_SCRIPT="test_rate_limits.py"
                shift
                ;;
            --bot-detection)
                TEST_SCRIPT="test_bot_detection.py"
                shift
                ;;
            --captcha)
                TEST_SCRIPT="test_cloudflare_captcha.py"
                shift
                ;;
            --url)
                API_BASE_URL="$2"
                shift 2
                ;;
            --cloudflare)
                CLOUDFLARE_CAPTCHA="true"
                shift
                ;;
            --test-mode)
                TEST_MODE="$2"
                shift 2
                ;;
            --detailed)
                DETAILED_OUTPUT=true
                shift
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                # Handle special cases for test scripts
                if [[ "$1" == "rate_limit_captcha" ]]; then
                    TEST_SCRIPT="test_rate_limit_captcha.py"
                elif [[ "$1" == "security_headers" ]]; then
                    TEST_SCRIPT="test_security_headers.py"
                elif [[ "$1" == "cloudflare_captcha" ]]; then
                    TEST_SCRIPT="test_cloudflare_captcha.py"
                elif [[ "$1" == "bot_detection" ]]; then
                    TEST_SCRIPT="test_bot_detection.py"
                else
                    # Assume it's a test script
                    TEST_SCRIPT="$1"
                fi
                shift
                ;;
        esac
    done
}

# Check for required Python packages
check_dependencies() {
    echo "Checking dependencies..."
    
    # List of required packages
    required_packages=("httpx" "rich" "colorama" "requests" "pydantic" "fastapi")
    missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if ! python3 -c "import $package" &>/dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        echo "Missing required packages: ${missing_packages[*]}"
        echo "Installing missing packages..."
        pip3 install "${missing_packages[@]}"
    else
        echo "All required packages are installed."
    fi
}

# Generate HTML report from test results
generate_report() {
    timestamp=$(date +"%Y%m%d_%H%M%S")
    report_file="$REPORTS_DIR/security_report_${timestamp}.html"
    
    echo "Generating comprehensive security report..."
    python3 "$SCRIPT_DIR/run_security_tests.py"
    
    echo "Report generated: $report_file"
}

# Main function
main() {
    # Parse arguments
    parse_args "$@"
    
    # Create logs directory
    create_dirs
    
    # Activate virtual environment
    activate_venv
    
    # Check dependencies
    check_dependencies
    
    # If no test script specified and not running all, show help
    if [ -z "$TEST_SCRIPT" ] && [ "$RUN_ALL" = false ]; then
        echo "Error: No test script specified"
        usage
        exit 1
    fi
    
    # Tracking overall status
    overall_status=0
    
    # Run specified test or all tests
    if [ "$RUN_ALL" = true ]; then
        echo "Running all security tests..."
        
        # Run security headers test
        run_test "test_security_headers.py" "headers"
        headers_status=$?
        [ $headers_status -ne 0 ] && overall_status=1
        
        # Run rate limiting test
        run_test "test_rate_limits.py" "rate_limit"
        rate_limit_status=$?
        [ $rate_limit_status -ne 0 ] && overall_status=1
        
        # Run CAPTCHA test if Cloudflare is enabled
        if [ "$CLOUDFLARE_CAPTCHA" = "true" ]; then
            run_test "test_cloudflare_captcha.py" "cloudflare_captcha"
            captcha_status=$?
            [ $captcha_status -ne 0 ] && overall_status=1
        fi
        
        # Generate comprehensive report
        generate_report
        
        # Display summary
        echo "----------------------------------------------"
        echo "Security Test Summary:"
        echo "----------------------------------------------"
        echo "Headers Tests:     $([ $headers_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        echo "Rate Limit Tests:  $([ $rate_limit_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        if [ "$CLOUDFLARE_CAPTCHA" = "true" ]; then
            echo "CAPTCHA Tests:     $([ $captcha_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        fi
        echo "----------------------------------------------"
        echo "Overall Status: $([ $overall_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        echo "----------------------------------------------"
    else
        # Extract test type from filename
        test_type=$(basename $TEST_SCRIPT .py | sed 's/test_//' | sed 's/_/ /g')
        
        # Run the specified test
        run_test "$TEST_SCRIPT" "$test_type"
        overall_status=$?
    fi
    
    return $overall_status
}

# Run main function
main "$@"
exit $? 