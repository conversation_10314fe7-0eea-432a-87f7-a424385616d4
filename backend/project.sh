#!/bin/bash

# Set variables
PROJECT_DIR="/opt/atlasvpn"  # Use absolute path to project root
BAC<PERSON>UP_DIR="backups"  # Relative to backend directory
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="project_backup_${TIMESTAMP}.zip"

# Create backups directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "Creating backup of project from $PROJECT_DIR..."
echo "Excluding node_modules, venv, and other large/unnecessary directories..."

# Use better exclusion patterns for more efficient backup
zip -r "$BACKUP_DIR/$BACKUP_FILE" "$PROJECT_DIR" \
    -x "*/node_modules/*" \
    "*/venv/*" \
    "*/.venv/*" \
    "*/dist/*" \
    "*/build/*" \
    "*/__pycache__/*" \
    "*/.git/*" \
    "*/.next/*" \
    "*/coverage/*" \
    "*/.pytest_cache/*" \
    "*/logs/*" \
    "*/backups/*" \
    "*/static/uploads/*" \
    "*/tmp/*" \
    "*/cache/*" \
    "*.pyc" \
    "*.pyo" \
    "*.pyd" \
    "*.so" \
    "*.zip" \
    "*.tar" \
    "*.gz" \
    "*.rar" \
    "*.log" \
    "*package-lock.json" \
    "*yarn.lock"

# Check backup size
BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
echo "Backup completed: $BACKUP_FILE (Size: $BACKUP_SIZE)"

# Create a list of all included files (optional, for logging)
echo "Creating file list..."
unzip -l "$BACKUP_DIR/$BACKUP_FILE" > "$BACKUP_DIR/backup_contents_${TIMESTAMP}.txt"
echo "File list saved to: $BACKUP_DIR/backup_contents_${TIMESTAMP}.txt"

echo "Backup process completed successfully."
