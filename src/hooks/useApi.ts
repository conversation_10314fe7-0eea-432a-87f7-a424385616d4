/**
 * @file useApi.ts
 * @description React Query hooks for API data fetching
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiService, handleApiError, shouldUseMockData, mockUser, mockDashboardStats } from '../services/api';
import { QUERY_KEYS, DASHBOARD } from '../utils/constants';
import type { User, DashboardStats } from '../services/api';

// User hooks
export const useCurrentUser = () => {
  return useQuery({
    queryKey: QUERY_KEYS.USER,
    queryFn: async (): Promise<User> => {
      if (shouldUseMockData()) {
        return mockUser;
      }
      return apiService.getCurrentUser();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error) => {
      console.error('Failed to fetch user:', handleApiError(error));
    },
  });
};

// Dashboard stats hooks
export const useDashboardStats = () => {
  return useQuery({
    queryKey: QUERY_KEYS.DASHBOARD_STATS,
    queryFn: async (): Promise<DashboardStats> => {
      if (shouldUseMockData()) {
        return mockDashboardStats;
      }
      return apiService.getDashboardStats();
    },
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: DASHBOARD.STATS_REFRESH_INTERVAL,
    retry: 2,
    onError: (error) => {
      console.error('Failed to fetch dashboard stats:', handleApiError(error));
    },
  });
};

// Health check hook
export const useHealthCheck = () => {
  return useQuery({
    queryKey: QUERY_KEYS.HEALTH,
    queryFn: () => apiService.healthCheck(),
    staleTime: 60 * 1000, // 1 minute
    cacheTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    onError: (error) => {
      console.error('Health check failed:', handleApiError(error));
    },
  });
};

// Tasks hooks
export const useTasks = () => {
  return useQuery({
    queryKey: QUERY_KEYS.TASKS,
    queryFn: () => apiService.getTasks(),
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    onError: (error) => {
      console.error('Failed to fetch tasks:', handleApiError(error));
    },
  });
};

// VPN status hook
export const useVpnStatus = () => {
  return useQuery({
    queryKey: QUERY_KEYS.VPN_STATUS,
    queryFn: () => apiService.getVpnStatus(),
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // 1 minute
    retry: 2,
    onError: (error) => {
      console.error('Failed to fetch VPN status:', handleApiError(error));
    },
  });
};

// Referral stats hook
export const useReferralStats = () => {
  return useQuery({
    queryKey: QUERY_KEYS.REFERRAL_STATS,
    queryFn: () => apiService.getReferralStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    onError: (error) => {
      console.error('Failed to fetch referral stats:', handleApiError(error));
    },
  });
};

// Mutation hooks
export const useClaimAllProfits = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => apiService.claimAllProfits(),
    onSuccess: (data) => {
      // Invalidate and refetch dashboard stats
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_STATS });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USER });
      
      console.log('Profits claimed successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to claim profits:', handleApiError(error));
    },
  });
};

export const useCompleteTask = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (taskId: number) => apiService.completeTask(taskId),
    onSuccess: () => {
      // Invalidate and refetch tasks and stats
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TASKS });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_STATS });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USER });
    },
    onError: (error) => {
      console.error('Failed to complete task:', handleApiError(error));
    },
  });
};

// Custom hook for combined dashboard data
export const useDashboardData = () => {
  const userQuery = useCurrentUser();
  const statsQuery = useDashboardStats();

  return {
    user: userQuery.data,
    stats: statsQuery.data,
    isLoading: userQuery.isLoading || statsQuery.isLoading,
    isError: userQuery.isError || statsQuery.isError,
    error: userQuery.error || statsQuery.error,
    refetch: () => {
      userQuery.refetch();
      statsQuery.refetch();
    },
  };
};

// Utility hook for API status
export const useApiStatus = () => {
  const healthQuery = useHealthCheck();

  return {
    isOnline: !healthQuery.isError,
    isLoading: healthQuery.isLoading,
    lastChecked: healthQuery.dataUpdatedAt,
    error: healthQuery.error,
  };
};
