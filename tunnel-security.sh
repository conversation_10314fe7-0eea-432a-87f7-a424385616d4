#!/bin/bash

# VS Code Tunnel Security Management Script
# This script helps manage secure access to the VS Code tunnel

TUNNEL_NAME="atlasvpn-server"
CONFIG_DIR="/opt/atlasvpn/.vscode"
LOG_FILE="/opt/atlasvpn/logs/tunnel-security.log"

# Create logs directory
mkdir -p /opt/atlasvpn/logs

# Function to log security events
log_event() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Function to check tunnel status
check_tunnel_status() {
    if systemctl is-active --quiet vscode-tunnel.service; then
        echo "✅ VS Code tunnel service is running"
        log_event "Tunnel status check: ACTIVE"
    else
        echo "❌ VS Code tunnel service is not running"
        log_event "Tunnel status check: INACTIVE"
    fi
}

# Function to show tunnel connection info
show_tunnel_info() {
    echo "🔗 VS Code Tunnel Information:"
    echo "   Name: $TUNNEL_NAME"
    echo "   Authentication: GitHub required"
    echo "   Access URL: https://vscode.dev/tunnel/$TUNNEL_NAME"
    echo "   Security: End-to-end encrypted (AES 256 CTR)"
    log_event "Tunnel info requested"
}

# Function to restart tunnel with security check
restart_tunnel() {
    echo "🔄 Restarting VS Code tunnel service..."
    systemctl restart vscode-tunnel.service
    sleep 3
    check_tunnel_status
    log_event "Tunnel restart requested"
}

# Function to show security status
show_security_status() {
    echo "🔒 Security Status:"
    echo "   Authentication: GitHub OAuth required"
    echo "   Encryption: AES 256 CTR (end-to-end)"
    echo "   Access Control: Single user session"
    echo "   Network: Outbound connections only"
    echo "   Firewall: No inbound ports required"
    log_event "Security status check"
}

# Function to show logs
show_logs() {
    echo "📋 Recent tunnel logs:"
    journalctl -u vscode-tunnel.service --no-pager -n 20
}

# Main menu
case "$1" in
    "status")
        check_tunnel_status
        ;;
    "info")
        show_tunnel_info
        ;;
    "restart")
        restart_tunnel
        ;;
    "security")
        show_security_status
        ;;
    "logs")
        show_logs
        ;;
    *)
        echo "🔧 VS Code Tunnel Security Manager"
        echo ""
        echo "Usage: $0 {status|info|restart|security|logs}"
        echo ""
        echo "Commands:"
        echo "  status   - Check if tunnel service is running"
        echo "  info     - Show tunnel connection information"
        echo "  restart  - Restart the tunnel service"
        echo "  security - Show security configuration"
        echo "  logs     - Show recent service logs"
        echo ""
        ;;
esac 