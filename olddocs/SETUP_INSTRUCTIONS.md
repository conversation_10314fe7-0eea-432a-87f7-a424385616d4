# Setup Instructions for New Server

## 1. Backend Setup
```bash
cd /root/project/backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

## 2. Frontend Setup
```bash
cd /root/project/frontend
# Install pnpm if not already installed
npm install -g pnpm@9.15.4
pnpm install
```

## 3. Configure NGINX
- Edit the NGINX configuration based on the files in frontend/nginx.conf.txt
- Update domain names and SSL certificate paths as needed
```bash
sudo cp /root/project/frontend/nginx.conf.txt /etc/nginx/sites-available/your-domain.conf
sudo ln -s /etc/nginx/sites-available/your-domain.conf /etc/nginx/sites-enabled/
sudo nginx -t  # Test configuration
sudo systemctl restart nginx
```

## 4. Set up SSL certificates
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 5. Set up systemd services
Create a systemd service file for the backend:
```bash
sudo nano /etc/systemd/system/vpn-backend.service
```

Add the following content:
```
[Unit]
Description=VPN Dashboard Backend
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/root/project/backend
Environment="PATH=/root/project/backend/venv/bin"
ExecStart=/root/project/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start the service:
```bash
sudo systemctl daemon-reload
sudo systemctl enable vpn-backend
sudo systemctl start vpn-backend
```

## 6. Final steps
- Check logs to verify everything is working:
  - Backend: `sudo journalctl -u vpn-backend -f`
  - NGINX: `sudo tail -f /var/log/nginx/error.log`
- Set proper permissions:
  `sudo chown -R www-data:www-data /root/project`
- Configure firewall:
  ```bash
  sudo ufw allow 22/tcp
  sudo ufw allow 80/tcp
  sudo ufw allow 443/tcp
  sudo ufw allow 8443/tcp
  sudo ufw enable
  ```

## SSL Certificates
The SSL certificates have been migrated to the new server.
- Domain: dev.atlasvip.cloud
- Location: /etc/letsencrypt/live/dev.atlasvip.cloud

No need to run certbot again as certificates are already in place.
Make sure NGINX is configured to use these certificates.
