# Development Guide

## Build & Test Commands
- Server runs as Ubuntu system service (no need to start manually)
- Run all tests: `python -m pytest`
- Run specific test file: `python -m pytest tests/test_file.py`
- Run single test: `python -m pytest tests/test_file.py::test_function`
- Database migrations: `alembic upgrade head`
- Create migration: `alembic revision -m "description"`

## Code Style Guidelines
- **Imports**: Group standard library → third-party → local modules, alphabetically ordered
- **Types**: Use type annotations with `Mapped[Type]` for SQLAlchemy, Pydantic models for validation
- **Naming**: snake_case for variables/functions, PascalCase for classes/types, UPPER_CASE for constants
- **Error Handling**: Use custom exceptions, consistent try/except with detailed error responses
- **Functions**: Descriptive names, single responsibility, docstrings
- **Security**: Validate all inputs, rate limit sensitive operations, log security events
- **Formatting**: 4-space indentation, consistent operator spacing
- **Testing**: Write unit tests for all new functionality, use fixtures for test data

## Developer Notes
- Verify database schema changes against existing data
- Use the security_service for authentication/authorization
- Rate-limit user-facing endpoints to prevent abuse