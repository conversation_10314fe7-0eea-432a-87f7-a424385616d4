# Authentication Flow Documentation

## Overview

This document details the authentication system for the Astro-migrated VIPVerse Telegram mini app, maintaining full compatibility with the existing FastAPI backend while leveraging Astro's server-side capabilities.

## Current Authentication Architecture

### Backend Authentication (FastAPI)
- **Method**: Telegram WebApp authentication with JWT tokens
- **Storage**: HTTP-only cookies for security
- **Session Management**: Server-side session verification
- **Token Refresh**: Automatic token refresh mechanism
- **Security**: CSRF protection and secure cookie configuration

### Frontend Authentication (React)
- **Integration**: @telegram-apps/sdk-react
- **State Management**: TanStack Query for auth state
- **Storage**: Encrypted local storage for non-sensitive data
- **Flow**: Telegram WebApp → Backend verification → Session establishment

## Astro Authentication Strategy

### 1. Server-Side Authentication (Astro Middleware)

**Authentication Middleware:**
```typescript
// src/middleware.ts
import { defineMiddleware } from 'astro:middleware';
import type { APIContext } from 'astro';

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, cookies, redirect, locals } = context;
  
  // Public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/new-user-setup',
    '/api/auth/telegram/login',
    '/api/auth/verify-session'
  ];
  
  const isPublicRoute = publicRoutes.some(route => 
    url.pathname === route || url.pathname.startsWith(route)
  );
  
  if (isPublicRoute) {
    return next();
  }
  
  try {
    // Extract auth cookies
    const authToken = cookies.get('auth_token')?.value;
    const refreshToken = cookies.get('refresh_token')?.value;
    
    if (!authToken) {
      return redirect('/');
    }
    
    // Verify session with backend
    const response = await fetch(`${getApiBaseUrl()}/auth/verify-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': request.headers.get('cookie') || '',
      },
      credentials: 'include',
    });
    
    if (response.ok) {
      const data = await response.json();
      if (data.status === 'authenticated') {
        // Store user data in locals for use in pages
        locals.user = data.data;
        locals.isAuthenticated = true;
        return next();
      }
    }
    
    // If verification fails, try to refresh token
    if (refreshToken) {
      const refreshResponse = await fetch(`${getApiBaseUrl()}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': request.headers.get('cookie') || '',
        },
        credentials: 'include',
      });
      
      if (refreshResponse.ok) {
        // Token refreshed successfully, continue
        const userData = await refreshResponse.json();
        locals.user = userData.data;
        locals.isAuthenticated = true;
        return next();
      }
    }
    
  } catch (error) {
    console.error('Authentication middleware error:', error);
  }
  
  // Authentication failed, redirect to landing
  return redirect('/');
});

function getApiBaseUrl(): string {
  return process.env.API_URL || 'http://localhost:8000';
}
```

### 2. Telegram WebApp Integration

**Telegram Initialization (Astro Layout):**
```astro
---
// src/layouts/BaseLayout.astro
export interface Props {
  title: string;
  description?: string;
  requireAuth?: boolean;
}

const { title, description, requireAuth = false } = Astro.props;
const user = Astro.locals.user;
const isAuthenticated = Astro.locals.isAuthenticated;

// Redirect if auth required but user not authenticated
if (requireAuth && !isAuthenticated) {
  return Astro.redirect('/');
}
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
  <title>{title}</title>
  <meta name="description" content={description} />
  
  <!-- Telegram WebApp Script - Critical -->
  <script src="https://telegram.org/js/telegram-web-app.js" is:inline></script>
  
  <!-- Telegram Initialization -->
  <script is:inline>
    // Initialize Telegram WebApp immediately
    if (window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;
      tg.ready();
      tg.expand();
      tg.disableVerticalSwipes();
      
      // Store Telegram data globally for React components
      window.__TELEGRAM_DATA__ = {
        initData: tg.initData,
        initDataUnsafe: tg.initDataUnsafe,
        user: tg.initDataUnsafe?.user,
        isReady: true
      };
    } else {
      window.__TELEGRAM_DATA__ = { isReady: false };
    }
  </script>
</head>
<body>
  <slot />
</body>
</html>
```

### 3. Client-Side Authentication (React Islands)

**Authentication Hook:**
```typescript
// src/hooks/useAuth.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../utils/apiClient';

interface TelegramData {
  initData: string;
  initDataUnsafe: any;
  user: any;
  isReady: boolean;
}

declare global {
  interface Window {
    __TELEGRAM_DATA__: TelegramData;
  }
}

export function useAuth() {
  const queryClient = useQueryClient();
  
  // Get current user from server state
  const { data: user, isLoading, error } = useQuery({
    queryKey: ['currentUser'],
    queryFn: async () => {
      const response = await api.post('/auth/verify-session');
      return response.data?.status === 'authenticated' ? response.data.data : null;
    },
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
  
  // Telegram login mutation
  const telegramLoginMutation = useMutation({
    mutationFn: async (telegramData: { init_data: string; auth_method: 'telegram' }) => {
      const response = await api.post('/auth/telegram/login', telegramData);
      return response.data;
    },
    onSuccess: (data) => {
      if (data.isNewUser) {
        // Redirect to user setup
        window.location.href = '/new-user-setup';
      } else if (data.user) {
        // Update user cache and redirect to dashboard
        queryClient.setQueryData(['currentUser'], data.user);
        window.location.href = '/dashboard';
      }
    },
    onError: (error) => {
      console.error('Telegram login failed:', error);
    },
  });
  
  // Logout mutation
  const logoutMutation = useMutation({
    mutationFn: () => api.post('/auth/logout'),
    onSuccess: () => {
      queryClient.setQueryData(['currentUser'], null);
      queryClient.clear();
      window.location.href = '/';
    },
  });
  
  // Auto-login with Telegram data
  const autoLogin = () => {
    const telegramData = window.__TELEGRAM_DATA__;
    
    if (!telegramData?.isReady || !telegramData.initData) {
      throw new Error('Telegram WebApp not available');
    }
    
    telegramLoginMutation.mutate({
      init_data: telegramData.initData,
      auth_method: 'telegram'
    });
  };
  
  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    isError: !!error,
    autoLogin,
    logout: logoutMutation.mutate,
    isLoggingIn: telegramLoginMutation.isPending,
    loginError: telegramLoginMutation.error,
  };
}
```

**Landing Page with Authentication:**
```typescript
// src/components/react/LandingPage.tsx
import React, { useEffect, useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { motion } from 'framer-motion';

export default function LandingPage() {
  const { autoLogin, isLoggingIn, loginError, isAuthenticated } = useAuth();
  const [isTelegramReady, setIsTelegramReady] = useState(false);
  
  useEffect(() => {
    // Check Telegram availability
    const checkTelegram = () => {
      const telegramData = window.__TELEGRAM_DATA__;
      setIsTelegramReady(telegramData?.isReady || false);
    };
    
    checkTelegram();
    
    // If already authenticated, redirect to dashboard
    if (isAuthenticated) {
      window.location.href = '/dashboard';
    }
  }, [isAuthenticated]);
  
  const handleGetStarted = () => {
    if (!isTelegramReady) {
      alert('Please open this app in Telegram');
      return;
    }
    
    try {
      autoLogin();
    } catch (error) {
      console.error('Login failed:', error);
      alert('Login failed. Please try again.');
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-purple-900 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center max-w-md w-full"
      >
        <div className="text-6xl mb-8">🚀</div>
        <h1 className="text-4xl font-bold text-white mb-4">VIPVerse</h1>
        <p className="text-lg text-gray-300 mb-8">
          Welcome to the future of secure VIP experiences
        </p>
        
        {/* Status indicator */}
        <div className="mb-8">
          {isTelegramReady ? (
            <div className="flex items-center justify-center text-green-400">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              Telegram Connected
            </div>
          ) : (
            <div className="text-yellow-400">
              ⚠️ Telegram WebApp not detected
            </div>
          )}
        </div>
        
        {/* Error display */}
        {loginError && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-500 rounded-lg text-red-200">
            Login failed: {loginError.message}
          </div>
        )}
        
        {/* Get Started Button */}
        <button
          onClick={handleGetStarted}
          disabled={isLoggingIn || !isTelegramReady}
          className="w-full py-4 px-8 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoggingIn ? 'Connecting...' : 'Get Started'}
        </button>
      </motion.div>
    </div>
  );
}
```

### 4. Protected Routes

**Dashboard Layout with Authentication:**
```astro
---
// src/pages/dashboard/index.astro
import BaseLayout from '../../layouts/BaseLayout.astro';
import DashboardContent from '../../components/react/DashboardContent';

// This page requires authentication
const user = Astro.locals.user;
if (!user) {
  return Astro.redirect('/');
}

// Fetch initial dashboard data
let dashboardStats = null;
try {
  const response = await fetch(`${process.env.API_URL}/api/user/dashboard/stats`, {
    headers: {
      'Cookie': Astro.request.headers.get('cookie') || '',
    },
    credentials: 'include',
  });
  
  if (response.ok) {
    dashboardStats = await response.json();
  }
} catch (error) {
  console.error('Failed to fetch dashboard stats:', error);
}
---

<BaseLayout title="Dashboard" requireAuth={true}>
  <DashboardContent 
    client:load 
    initialUser={user}
    initialStats={dashboardStats}
  />
</BaseLayout>
```

### 5. Session Management

**Automatic Session Refresh:**
```typescript
// src/utils/sessionManager.ts
import { QueryClient } from '@tanstack/react-query';
import { api } from './apiClient';

export class SessionManager {
  private queryClient: QueryClient;
  private refreshTimer: NodeJS.Timeout | null = null;
  
  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }
  
  startSessionRefresh() {
    // Refresh session every 10 minutes
    this.refreshTimer = setInterval(() => {
      this.refreshSession();
    }, 10 * 60 * 1000);
  }
  
  stopSessionRefresh() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }
  
  private async refreshSession() {
    try {
      const response = await api.post('/auth/refresh');
      if (response.data?.data) {
        // Update user cache
        this.queryClient.setQueryData(['currentUser'], response.data.data);
      }
    } catch (error) {
      console.error('Session refresh failed:', error);
      // Redirect to login if refresh fails
      window.location.href = '/';
    }
  }
}
```

### 6. Security Considerations

**CSRF Protection:**
```typescript
// src/utils/apiClient.ts
api.interceptors.request.use((config) => {
  // Add CSRF token if available
  const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
  if (csrfToken) {
    config.headers['X-CSRF-Token'] = csrfToken;
  }
  
  return config;
});
```

**Secure Storage:**
```typescript
// src/utils/secureStorage.ts
import CryptoJS from 'crypto-js';

const SECRET_KEY = 'your-encryption-key';

export const secureStorage = {
  setItem(key: string, value: any) {
    const encrypted = CryptoJS.AES.encrypt(JSON.stringify(value), SECRET_KEY).toString();
    localStorage.setItem(key, encrypted);
  },
  
  getItem(key: string) {
    const encrypted = localStorage.getItem(key);
    if (!encrypted) return null;
    
    try {
      const decrypted = CryptoJS.AES.decrypt(encrypted, SECRET_KEY).toString(CryptoJS.enc.Utf8);
      return JSON.parse(decrypted);
    } catch {
      return null;
    }
  },
  
  removeItem(key: string) {
    localStorage.removeItem(key);
  }
};
```

## Authentication Flow Diagram

```
1. User opens Telegram mini app
2. Astro serves landing page with Telegram SDK
3. User clicks "Get Started"
4. React component extracts Telegram initData
5. POST /auth/telegram/login with initData
6. Backend verifies Telegram data
7. Backend creates/updates user session
8. Backend sets HTTP-only auth cookies
9. Frontend redirects to dashboard
10. Astro middleware verifies session on each request
11. Protected pages receive user data via Astro.locals
```

## Testing Strategy

### 1. Authentication Tests
- Test Telegram WebApp integration
- Verify session creation and verification
- Test token refresh mechanism
- Test logout functionality

### 2. Security Tests
- Verify CSRF protection
- Test cookie security settings
- Validate session timeout
- Test unauthorized access prevention

### 3. Integration Tests
- Test server-side authentication middleware
- Verify client-side auth state management
- Test protected route access
- Test authentication error handling

## Migration Checklist

- [ ] Implement Astro authentication middleware
- [ ] Set up Telegram WebApp integration
- [ ] Create authentication hooks for React components
- [ ] Implement protected route patterns
- [ ] Set up session management
- [ ] Add security measures (CSRF, secure storage)
- [ ] Test authentication flows
- [ ] Test error scenarios
- [ ] Verify session persistence
- [ ] Test logout functionality

## Conclusion

The authentication system maintains full compatibility with the existing backend while leveraging Astro's server-side capabilities for improved security and performance. The hybrid approach ensures secure session management and seamless user experience across the application.
