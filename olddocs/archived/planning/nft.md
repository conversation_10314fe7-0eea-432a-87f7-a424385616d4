# PLAN: Implementing NFT Virtual Lands in VPNVerse Mini App

**Version:** 1.0
**Date:** 2024-07-26

## 1. Goal

To integrate a virtual land system ("Verse") into the existing Telegram Mini App game. Lands will be represented as NFTs on the TON blockchain, initially sold directly by the project, with future potential for player-to-player trading via a marketplace and integration with a future game token. This aims to enhance user engagement, provide new monetization avenues, and add long-term value to the game ecosystem.

## 2. Core Concepts

*   **Virtual Land (NFTs):** Unique, ownable plots within the game's 2D map view, represented as TEP-62 standard NFTs on the TON blockchain. Each land NFT will have associated metadata (size, location, image, base VPN gift).
*   **Smart Contracts (TON):** Blockchain programs defining the Land NFT collection (ownership, minting, transfers) and potentially a future marketplace (listing, buying, bidding). Written in FunC for TON.
*   **Initial Batch Minting:** Pre-minting a small subset of Land NFTs (e.g., 20) to control initial costs and test the market.
*   **Placeholders:** Representing the remaining potential land plots (e.g., 1980) visually on the map but without a corresponding minted NFT initially.
*   **TON Blockchain:** The underlying network for NFT minting, ownership, and transactions.
*   **TON Connect:** Protocol/SDK used to connect user TON wallets (like Telegram Wallet) within the Mini App for purchases and NFT management.
*   **Marketplace (Future):** A system (potentially involving smart contracts) allowing users to trade their Land NFTs with each other.

## 3. Chosen Strategy: Initial Batch + Placeholders

*   **Phase 1:** Deploy the Land NFT smart contract. Mint an initial small batch (e.g., 20) of NFTs representing specific plots. Display all 2000 potential plots on the map UI, marking only the 20 minted ones as "Available for Sale". Sell these directly to users for TON or USDT (on TON network).
*   **Phase 2:** Based on Phase 1 success, periodically mint and release new batches of land. Implement a player-to-player marketplace.
*   **Phase 3:** (Optional/Future) Design and integrate a native game token, potentially airdropping some to early landowners.

## 4. Technology Stack

*   **Frontend:** React (Vite), TypeScript, Zustand (or other state management), TailwindCSS, TON Connect UI SDK.
*   **Backend:** FastAPI (Python), Pydantic, SQLAlchemy (or other ORM/DB driver), TON SDK for Python (e.g., `pytonlib`, `tonsdk`).
*   **Blockchain:** TON (The Open Network).
*   **Smart Contracts:** FunC.
*   **Metadata Storage:** IPFS (recommended) or centralized server.
*   **Database:** PostgreSQL / MySQL (or chosen DB).

## 5. Phase 1: Foundation & Initial Land Sale

### 5.1. Smart Contract Design (NFT Collection - FunC)

*   **Standard:** Adhere to TEP-62 (NFT Standard).
*   **Functionality:**
    *   Define collection metadata.
    *   Define NFT item metadata structure (including off-chain URI).
    *   `mint(item_index, owner_address, metadata_uri)`: Function callable *only by the collection owner/admin* to mint a *single* new NFT. (Essential for adding more land later).
    *   *(Optional but Recommended)* `batch_mint(item_indices[], owner_addresses[], metadata_uris[])`: Function callable by owner to mint multiple NFTs in one transaction (saves gas for future batches).
    *   Standard transfer functions (implicit in TEP-62).
    *   Function to get NFT metadata URI by index/ID.
*   **Security:** Must be audited by a reputable firm before Mainnet deployment.

### 5.2. Metadata Preparation & Storage

*   **Format:** Create JSON files for *each* of the 2000 potential lands.
    *   `name`: "VPNVerse Land #123"
    *   `description`: "A 1x1 plot in the Genesis Sector. Grants [VPN benefit]."
    *   `image`: URI pointing to the land's visual representation (e.g., `ipfs://Qm...` or `https://yourserver.com/images/land123.png`).
    *   `attributes`: Array of traits (e.g., `{"trait_type": "Size", "value": "1x1"}`, `{"trait_type": "Sector", "value": "Genesis"}`, `{"trait_type": "VPN Gift", "value": "1 Month Basic"}`).
*   **Storage:** Upload images and JSON files to IPFS. Record the resulting IPFS URIs for each of the 2000 plots. Store these URIs securely, likely in your backend database associated with plot coordinates/ID.

### 5.3. Backend Development (FastAPI)

*   **Database Models (e.g., using SQLAlchemy):**
    *   `LandPlot`:
        *   `id` (int, PK)
        *   `map_x` (int)
        *   `map_y` (int)
        *   `size_x` (int, default 1)
        *   `size_y` (int, default 1)
        *   `sector` (string)
        *   `metadata_uri` (string) - IPFS URI for this plot
        *   `nft_item_index` (int, nullable, unique) - Index within the TON smart contract
        *   `nft_mint_status` (enum: `UNMINTED`, `MINTED`, `SOLD`)
        *   `current_owner_wallet` (string, nullable) - TON wallet address
        *   `current_sale_price` (decimal, nullable) - Price in TON/USDT if listed by admin
        *   `sale_currency` (enum: `TON`, `USDT`, nullable)
*   **Pydantic Schemas:** For API request/response validation.
*   **TON SDK Integration:**
    *   Add a Python TON SDK (`pytonlib`, `tonsdk`).
    *   Securely configure the admin wallet's private key (use environment variables, secrets manager - **NEVER hardcode**).
    *   Implement functions to:
        *   Interact with the deployed NFT Collection contract.
        *   Call the `transfer` function (requires admin key if transferring from admin wallet).
        *   Check transaction status on the TON network.
        *   Monitor specific wallet addresses for incoming payments.
*   **New API Endpoints:**
    *   `GET /api/lands/map`: Returns data for all 2000 plots (coordinates, size, status `UNMINTED`/`MINTED`/`SOLD`, owner if applicable, sale price if available).
    *   `POST /api/lands/initiate-purchase/{plot_id}`:
        *   Requires authenticated user.
        *   Verifies the plot ID corresponds to a `MINTED` land currently for sale by the admin.
        *   Checks if the user has connected their TON wallet.
        *   Returns the admin's payment address, required amount (TON/USDT), currency, and a unique transaction identifier/memo.
    *   `POST /api/lands/verify-payment/{plot_id}`:
        *   Requires authenticated user and transaction identifier/memo from previous step.
        *   Backend checks the TON blockchain (via SDK or reliable API like Toncenter) if the expected payment arrived at the admin address with the correct memo/identifier.
        *   **Crucial:** Implement robust checks to prevent double-spending/replay attacks. Check transaction is recent and not already processed.
        *   If payment verified:
            *   Update `LandPlot` status to `PENDING_TRANSFER`.
            *   Trigger an internal background task to transfer the NFT.
            *   Return `{"status": "pending_transfer"}`.
        *   If payment not found (yet): Return `{"status": "pending_payment"}`.
        *   If error: Return error status.
    *   *(Internal Task/Webhook)* `process_nft_transfer(plot_id, user_wallet_address)`:
        *   Called after successful payment verification.
        *   Uses admin wallet via TON SDK to call the `transfer` function on the NFT Collection contract, sending the Land NFT from the admin wallet to the `user_wallet_address`.
        *   Handles potential errors during transfer (e.g., insufficient gas).
        *   On successful blockchain confirmation: Update `LandPlot` status to `SOLD`, set `current_owner_wallet` to `user_wallet_address`, clear sale price. Update user's profile/inventory.
        *   On failure: Log error, potentially flag for manual review or retry.
*   **User Authentication:** Ensure existing auth system identifies the user making purchase requests.

### 5.4. Frontend Development (React/Vite)

*   **Wallet Integration:**
    *   Integrate `@tonconnect/ui-react` library.
    *   Add connect/disconnect wallet buttons/flow.
    *   Store connected wallet address and connection status in state (Zustand/Context).
*   **New UI Components:**
    *   `LandMap`: Fetches data from `/api/lands/map`. Renders the 2D grid visually. Differentiates between `UNMINTED` (placeholder), `MINTED` (available for sale), and `SOLD` (showing owner or generic 'owned' state) plots. Handles panning/zooming if needed.
    *   `LandPlotDetail`: Displays info when a plot is clicked/selected. Shows metadata (image, size, sector), status. If available for sale, shows price (TON/USDT) and a "Buy" button. If owned by the current user, shows management options (future). If owned by others, shows owner info (future).
    *   `PurchaseFlowModal`: Guides the user through the purchase:
        1.  Confirms purchase intention.
        2.  Calls `/api/lands/initiate-purchase`.
        3.  Displays QR code / deep link for payment using the user's connected TON wallet (or prompts connection if needed). Shows amount, currency, address, memo.
        4.  Provides a "I have paid" button.
        5.  When clicked, calls `/api/lands/verify-payment`.
        6.  Shows status updates (`pending_payment`, `pending_transfer`, `success`, `error`).
*   **API Integration:** Use `axios` or `fetch` (via the configured `api.ts` interceptor) to call the new backend endpoints.
*   **State Management:** Update Zustand/Context store with map data, selected plot details, wallet connection status, and purchase flow state.

### 5.5. Minting & Deployment

*   **Deploy Contract:** Deploy the audited NFT Collection smart contract to TON Mainnet. Record its address.
*   **Configure Backend:** Update backend config with the contract address and admin wallet credentials.
*   **Run Initial Mint Script:** Execute the automation script (developed in step 5.3/Prerequisites) to batch mint the initial 10-20 Land NFTs, assigning them to the admin wallet and associating them with the correct `LandPlot` entries in the database (updating `nft_item_index` and `nft_mint_status`).
*   **Set Prices:** Update the `current_sale_price` and `sale_currency` for the initially minted plots in the database.
*   **Deploy Backend/Frontend:** Deploy the updated application code.

## 6. Phase 2: Marketplace Implementation

### 6.1. Smart Contract Design (Marketplace/Escrow - FunC)

*   **Functionality:**
    *   `list_for_sale(nft_address, item_index, price, currency)`: Allows NFT owner to list their land. Contract potentially takes temporary custody (escrow) or approves transfer upon sale.
    *   `cancel_listing(nft_address, item_index)`: Allows owner to remove their listing.
    *   `buy_listed(nft_address, item_index)`: Buyer sends payment (TON/USDT) to the contract. Contract verifies payment, transfers NFT to buyer, sends payment to seller (minus optional fee).
    *   *(Optional: Bidding)* `make_bid(...)`, `accept_bid(...)`, `withdraw_bid(...)`.
*   **Fees:** Contract can be programmed to take a small percentage fee on sales, sent to the project wallet.
*   **Security:** **CRITICAL** - Must be rigorously audited. Escrow logic is complex.

### 6.2. Backend Development (FastAPI)

*   Integrate with Marketplace smart contract via TON SDK.
*   Endpoints:
    *   `POST /api/marketplace/list`: User requests to list their owned land. Backend calls the `list_for_sale` contract function.
    *   `DELETE /api/marketplace/list/{plot_id}`: User requests to cancel. Backend calls `cancel_listing`.
    *   `GET /api/marketplace/listings`: Fetch currently listed lands (querying contract state or mirroring in DB).
    *   `POST /api/marketplace/buy/{plot_id}`: User wants to buy listed land. Backend helps construct the transaction for the user to sign/send payment to the marketplace contract.
    *   *(Optional: Bidding endpoints)*
*   Potentially mirror marketplace state in the DB for faster querying.

### 6.3. Frontend Development (React/Vite)

*   Marketplace UI: Browse listings, filter/sort.
*   Display "List for Sale" button on owned lands in `LandPlotDetail`.
*   UI for managing user's own listings and bids.
*   Integration with `PurchaseFlowModal` or a similar component for buying from other users via the marketplace contract.

## 7. Phase 3: Future Token Integration

*   **Tokenomics Design:** Define token supply, allocation (team, marketing, rewards, airdrop), utility, vesting schedules, burning mechanisms.
*   **Smart Contract (Token - FunC):** Deploy a TEP-74 (Fungible Token / Jetton) standard contract.
*   **Integration:**
    *   Modify game tasks to reward the token.
    *   Allow spending tokens on premium features, land upgrades, marketplace fees.
    *   Implement staking mechanisms (if planned).
*   **Airdrop:**
    *   Define snapshot criteria (e.g., own land before X date).
    *   Take blockchain snapshot of landowners' wallets.
    *   Distribute tokens via a script or distribution contract.

## 8. Security Considerations

*   **Smart Contract Audits:** Budget for and conduct professional audits for *all* contracts (NFT Collection, Marketplace, Token) before Mainnet deployment.
*   **Backend Security:** Protect admin private keys. Use secure authentication. Validate all inputs. Prevent common web vulnerabilities (SQLi, XSS, CSRF).
*   **Frontend Security:** Sanitize inputs. Protect against XSS.
*   **Payment Verification:** Implement robust checks against double-spending when verifying blockchain payments. Use unique memos/IDs.
*   **TON Connect Security:** Follow best practices for wallet connection and transaction signing prompts.

## 9. Cost Considerations

*   Smart Contract Development & Audits (Significant cost).
*   TON Gas Fees (Deployment, Minting, Transfers, Marketplace interactions).
*   Backend/Frontend Development time.
*   Infrastructure (Servers, IPFS pinning services if needed).

## 10. Timeline & Resources

*   Phase 1 is a substantial project (weeks to months depending on team size/experience).
*   Phases 2 & 3 build upon Phase 1 and add significant further complexity.
*   Requires developers experienced in: React, FastAPI, TON smart contracts (FunC), blockchain integration, and security best practices.

## 11. TODO List

**Phase 1: Foundation & Initial Land Sale**

*   [ ] Design detailed Land NFT attributes and metadata structure.
*   [ ] Choose Metadata storage solution (IPFS recommended) and prepare storage.
*   [ ] Create JSON metadata and image files for all 2000 potential plots. Upload & record URIs.
*   [ ] Develop & Test NFT Collection Smart Contract (FunC - TEP-62, owner minting).
*   [ ] **SECURITY:** Obtain professional audit for NFT Collection contract.
*   [ ] Define Database schema (`LandPlot` table). Implement DB models (FastAPI).
*   [ ] Integrate Python TON SDK into FastAPI backend.
*   [ ] Securely configure admin wallet credentials in backend.
*   [ ] Implement Backend API endpoints (`/api/lands/map`, `/initiate-purchase`, `/verify-payment`).
*   [ ] Implement internal NFT transfer logic (background task/webhook).
*   [ ] Integrate TON Connect UI into React Frontend.
*   [ ] Develop Frontend components (`LandMap`, `LandPlotDetail`, `PurchaseFlowModal`).
*   [ ] Implement Frontend state management for map, wallet, purchase flow.
*   [ ] Develop & Test automation script for batch minting initial lands.
*   [ ] Deploy audited smart contract to TON Mainnet.
*   [ ] Deploy backend and frontend updates.
*   [ ] Run script to mint initial 10-20 Land NFTs.
*   [ ] Update database for minted lands (status, owner=admin, price).
*   [ ] **TEST:** Conduct thorough end-to-end testing of the purchase flow on Testnet and then Mainnet with small amounts.

**Phase 2: Marketplace (Post-Phase 1)**

*   [ ] Design Marketplace/Escrow Smart Contract (FunC).
*   [ ] **SECURITY:** Obtain professional audit for Marketplace contract.
*   [ ] Develop Backend endpoints for listing, canceling, buying, fetching listings.
*   [ ] Develop Frontend UI for marketplace browsing, listing, managing.
*   [ ] Deploy Marketplace contract and backend/frontend updates.
*   [ ] **TEST:** Thoroughly test marketplace interactions.

**Phase 3: Token Integration (Future)**

*   [ ] Design detailed Tokenomics.
*   [ ] Develop & Audit Token Smart Contract (FunC - TEP-74).
*   [ ] Integrate token rewards/spending into game mechanics/backend.
*   [ ] Plan and execute airdrop (snapshot, distribution).
*   [ ] Update Frontend to display token balance/interactions.
