# Plan: Verse Icon Notification Badge for Unread Private Messages

This plan outlines the steps required to implement a notification badge (displaying a count of unread private messages) on the "Verse" icon within the `BottomNavigation` component.

**Goal:** Inform the user about new private chat messages via a badge on the Verse navigation icon, even when they are not actively on the `VersePage` or using the chat UI.

**Key Decisions & Constraints:**

1.  **WebSocket Scope:** The WebSocket connection for chat will *only* be active when the `VersePage` component is mounted. This minimizes constant server load but means real-time updates only happen on that specific page.
2.  **Backend Dependency:** This feature *requires* the backend API endpoint `/api/chat/conversations` to return an `unread_count: number` field for each conversation object. Without this backend change, accurate unread counts cannot be displayed reliably.

**Implementation Steps:**

1.  **Modify `VersePage.tsx`:**
    *   Add `useEffect` hook:
        *   On mount: Call `useChatStore.getState().connectWebSocket()`.
        *   On unmount: Call `useChatStore.getState().disconnectWebSocket()`.
    *   Remove any existing `setInterval` used for polling public messages (replaced by WebSocket when active).

2.  **Update `types/chat.ts` (or relevant type definition file):**
    *   Modify the `ChatConversation` interface to include an optional `unread_count` property:
      ```typescript
      export interface ChatConversation {
        // ... other properties
        unread_count?: number; // Number of unread messages in this conversation
      }
      ```

3.  **Enhance `chatStore.ts`:**
    *   **Add Selector:** Implement a `getTotalUnreadCount` selector function that iterates through the `state.conversations` array and sums up the `unread_count` for all conversations.
      ```typescript
      export const getTotalUnreadCount = (state: ChatState): number => {
        return state.conversations.reduce((sum, convo) => sum + (convo.unread_count || 0), 0);
      };
      ```
    *   **Add Periodic Conversation Fetch:**
        *   Implement logic within the store (e.g., using `setInterval`) to periodically call `fetchConversations(true)` (silent fetch) at a reasonable interval (e.g., every 60 seconds).
        *   This interval should only run when the user is authenticated (`useAuthStore.getState().user` exists).
        *   Use `initializeChat` (or a similar mechanism triggered by auth changes) to start and stop this interval based on user login/logout status.

4.  **Update `BottomNavigation.tsx`:**
    *   Import `useChatStore` and the `getTotalUnreadCount` selector.
    *   In the component, get the current unread count: `const totalUnreadCount = useChatStore(getTotalUnreadCount);`
    *   Locate the rendering logic for the "Verse" navigation item (`item.id === 'verse'`).
    *   Conditionally render a badge element (e.g., a `<span>`) positioned near the `FaGlobe` icon if `totalUnreadCount > 0`.
    *   Style the badge appropriately (e.g., absolute positioning, red background, white text, small font size, rounded shape, potentially limit displayed count like '9+').

5.  **Update `App.tsx` (or relevant entry point):**
    *   Ensure that `initializeChat` from `chatStore.ts` is called whenever the authentication state changes (user logs in or out). This typically involves using `useEffect` and subscribing to the relevant state in `useAuthStore`. This ensures the periodic conversation fetching starts/stops correctly.

**Deployment Considerations:**

*   **Backend Update:** The backend *must* be updated first to provide the `unread_count` in the `/api/chat/conversations` response. Frontend changes depend on this.
*   **Polling Interval:** The frequency of the periodic `fetchConversations` call (Step 3) is a trade-off between badge update latency and server load. 60 seconds might be a reasonable starting point.
*   **Testing:** Thoroughly test the badge appearance/disappearance based on receiving messages while on different pages and ensure the periodic refresh works as expected. 