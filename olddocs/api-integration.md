# API Integration Guide for Astro Migration

## Overview

This document outlines how to integrate the existing FastAPI backend with the new Astro frontend, maintaining all current functionality while leveraging Astro's capabilities for optimal performance.

## Current API Architecture

### Backend Structure
- **Framework**: FastAPI with Python
- **Database**: SQLite with SQLAlchemy ORM
- **Authentication**: JWT tokens with HTTP-only cookies
- **API Base URL**: `http://localhost:8000` (dev), `https://dev.atlasvip.cloud` (prod)
- **WebSocket**: Real-time updates for chat and notifications

### Key API Endpoints

**Authentication:**
- `POST /auth/telegram/login` - Telegram WebApp authentication
- `POST /auth/verify-session` - Session verification
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Token refresh

**User Management:**
- `GET /api/user/dashboard/stats` - Dashboard statistics
- `GET /api/user/transactions` - User transactions
- `PUT /api/user/profile` - Update user profile

**Game Mechanics:**
- `GET /api/tasks/available` - Available tasks
- `POST /api/tasks/{id}/start` - Start task
- `POST /api/tasks/{id}/verify` - Verify task completion
- `GET /api/cards/unified` - User cards and catalog
- `POST /api/cards/{id}/upgrade` - Upgrade card

**VPN Services:**
- `GET /api/vpn/subscriptions` - User VPN subscriptions
- `GET /api/vpn/packages` - Available VPN packages
- `POST /api/vpn/subscribe` - Subscribe to VPN package

**Referral System:**
- `GET /api/referral/info` - Referral information
- `GET /api/referral/users` - Referred users

## Astro Integration Strategy

### 1. Server-Side Data Fetching

**Astro Pages with Server-Side Rendering:**
```typescript
---
// src/pages/dashboard/index.astro
import { api } from '../utils/apiClient';
import DashboardLayout from '../layouts/DashboardLayout.astro';
import DashboardContent from '../components/react/DashboardContent';

// Server-side data fetching
let dashboardStats = null;
let user = null;

try {
  // Fetch initial data on the server
  const [statsResponse, userResponse] = await Promise.all([
    api.get('/api/user/dashboard/stats'),
    api.get('/auth/verify-session')
  ]);
  
  dashboardStats = statsResponse.data;
  user = userResponse.data;
} catch (error) {
  console.error('Failed to fetch initial data:', error);
}
---

<DashboardLayout title="Dashboard">
  <DashboardContent 
    client:load
    initialStats={dashboardStats}
    initialUser={user}
  />
</DashboardLayout>
```

### 2. Client-Side Data Fetching with React Query

**React Islands with TanStack Query:**
```typescript
// src/components/react/DashboardContent.tsx
import { useQuery } from '@tanstack/react-query';
import { api } from '../../utils/apiClient';

interface Props {
  initialStats?: DashboardStats;
  initialUser?: User;
}

export default function DashboardContent({ initialStats, initialUser }: Props) {
  const { data: stats } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: () => api.get('/api/user/dashboard/stats').then(res => res.data),
    initialData: initialStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: user } = useQuery({
    queryKey: ['currentUser'],
    queryFn: () => api.get('/auth/verify-session').then(res => res.data),
    initialData: initialUser,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return (
    <div>
      {/* Dashboard content */}
    </div>
  );
}
```

### 3. API Client Configuration

**Shared API Client:**
```typescript
// src/utils/apiClient.ts
import axios, { AxiosInstance } from 'axios';

const getApiBaseUrl = (): string => {
  // Server-side detection
  if (typeof window === 'undefined') {
    return process.env.API_URL || 'http://localhost:8000';
  }
  
  // Client-side detection
  if (import.meta.env.DEV && !import.meta.env.VITE_API_URL?.includes('dev.atlasvip.cloud')) {
    return 'http://localhost:8000';
  }
  
  return import.meta.env.VITE_API_URL || 'https://dev.atlasvip.cloud';
};

export const api: AxiosInstance = axios.create({
  baseURL: getApiBaseUrl(),
  withCredentials: true,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  // Add any auth headers if needed
  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);
```

### 4. Authentication Integration

**Astro Middleware for Session Verification:**
```typescript
// src/middleware.ts
import { defineMiddleware } from 'astro:middleware';
import { api } from './utils/apiClient';

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, cookies, redirect } = context;
  
  // Skip auth for public routes
  const publicRoutes = ['/', '/new-user-setup'];
  if (publicRoutes.includes(url.pathname)) {
    return next();
  }
  
  try {
    // Verify session on server-side
    const response = await api.post('/auth/verify-session');
    
    if (response.data?.status === 'authenticated') {
      // User is authenticated, continue
      context.locals.user = response.data.data;
      return next();
    }
  } catch (error) {
    console.error('Auth verification failed:', error);
  }
  
  // Redirect to landing page if not authenticated
  return redirect('/');
});
```

**Client-Side Authentication Hook:**
```typescript
// src/hooks/useAuth.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../utils/apiClient';

export function useAuth() {
  const queryClient = useQueryClient();
  
  const { data: user, isLoading } = useQuery({
    queryKey: ['currentUser'],
    queryFn: async () => {
      const response = await api.post('/auth/verify-session');
      return response.data?.status === 'authenticated' ? response.data.data : null;
    },
    retry: false,
    staleTime: 5 * 60 * 1000,
  });
  
  const loginMutation = useMutation({
    mutationFn: async (telegramData: { init_data: string; auth_method: 'telegram' }) => {
      const response = await api.post('/auth/telegram/login', telegramData);
      return response.data;
    },
    onSuccess: (data) => {
      if (data.user) {
        queryClient.setQueryData(['currentUser'], data.user);
      }
    },
  });
  
  const logoutMutation = useMutation({
    mutationFn: () => api.post('/auth/logout'),
    onSuccess: () => {
      queryClient.setQueryData(['currentUser'], null);
      queryClient.clear();
      window.location.href = '/';
    },
  });
  
  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    login: loginMutation.mutate,
    logout: logoutMutation.mutate,
    isLoggingIn: loginMutation.isPending,
  };
}
```

### 5. Data Fetching Patterns

**Server-Side Rendering (SSR) for Initial Load:**
```typescript
// src/pages/dashboard/earn.astro
---
import { api } from '../../utils/apiClient';
import EarnTab from '../../components/react/EarnTab';

let initialData = null;

try {
  const [tasksResponse, cardsResponse] = await Promise.all([
    api.get('/api/tasks/available'),
    api.get('/api/cards/unified')
  ]);
  
  initialData = {
    tasks: tasksResponse.data,
    cards: cardsResponse.data,
  };
} catch (error) {
  console.error('Failed to fetch earn data:', error);
}
---

<EarnTab 
  client:load 
  initialTasks={initialData?.tasks}
  initialCards={initialData?.cards}
/>
```

**Client-Side Hydration with React Query:**
```typescript
// src/components/react/EarnTab.tsx
export default function EarnTab({ initialTasks, initialCards }: Props) {
  const { data: tasks } = useQuery({
    queryKey: ['tasks'],
    queryFn: () => api.get('/api/tasks/available').then(res => res.data),
    initialData: initialTasks,
  });
  
  const { data: cards } = useQuery({
    queryKey: ['cards'],
    queryFn: () => api.get('/api/cards/unified').then(res => res.data),
    initialData: initialCards,
  });
  
  // Component logic...
}
```

### 6. Real-Time Features

**WebSocket Integration:**
```typescript
// src/utils/websocket.ts
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private queryClient: QueryClient;
  
  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }
  
  connect() {
    if (typeof window === 'undefined') return;
    
    const wsUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000/ws';
    this.ws = new WebSocket(wsUrl);
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      // Update React Query cache based on WebSocket messages
      switch (data.type) {
        case 'dashboard_update':
          this.queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
          break;
        case 'task_completed':
          this.queryClient.invalidateQueries({ queryKey: ['tasks'] });
          break;
        // Handle other real-time updates
      }
    };
  }
  
  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

### 7. Error Handling

**Global Error Boundary:**
```typescript
// src/components/react/ErrorBoundary.tsx
import { QueryErrorResetBoundary } from '@tanstack/react-query';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';

function ErrorFallback({ error, resetErrorBoundary }: any) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="text-center">
        <h2 className="text-xl font-bold text-white mb-4">Something went wrong</h2>
        <p className="text-gray-400 mb-4">{error.message}</p>
        <button
          onClick={resetErrorBoundary}
          className="px-4 py-2 bg-purple-600 text-white rounded-lg"
        >
          Try again
        </button>
      </div>
    </div>
  );
}

export default function ErrorBoundary({ children }: { children: React.ReactNode }) {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ReactErrorBoundary
          FallbackComponent={ErrorFallback}
          onReset={reset}
        >
          {children}
        </ReactErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  );
}
```

## Performance Optimizations

### 1. Request Deduplication
- Use React Query's automatic deduplication
- Implement request caching strategies
- Optimize API call patterns

### 2. Data Prefetching
- Prefetch critical data on server-side
- Use React Query's prefetching capabilities
- Implement intelligent cache warming

### 3. Bundle Optimization
- Split API client from heavy dependencies
- Lazy load non-critical API modules
- Optimize axios bundle size

## Security Considerations

### 1. CSRF Protection
- Maintain existing CSRF token handling
- Ensure proper cookie configuration
- Validate all API requests

### 2. Authentication Security
- Keep JWT tokens in HTTP-only cookies
- Implement proper session management
- Handle token refresh securely

### 3. Data Validation
- Validate all API responses
- Implement proper error handling
- Sanitize user inputs

## Testing Strategy

### 1. API Integration Tests
- Test all API endpoints
- Verify authentication flows
- Test error scenarios

### 2. Component Integration Tests
- Test React Query integration
- Verify data flow
- Test loading states

### 3. End-to-End Tests
- Test complete user flows
- Verify Telegram integration
- Test real-time features

## Migration Checklist

- [ ] Set up API client configuration
- [ ] Implement authentication middleware
- [ ] Migrate React Query setup
- [ ] Test all API endpoints
- [ ] Implement error handling
- [ ] Set up WebSocket integration
- [ ] Test real-time features
- [ ] Optimize performance
- [ ] Add comprehensive testing
- [ ] Document API changes

## Conclusion

The API integration strategy maintains full compatibility with the existing FastAPI backend while leveraging Astro's server-side rendering capabilities for improved performance. The hybrid approach ensures optimal data loading patterns and maintains all real-time functionality.
