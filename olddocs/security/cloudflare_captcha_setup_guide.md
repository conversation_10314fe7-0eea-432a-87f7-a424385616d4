# Cloudflare CAPTCHA (Turnstile) Setup Guide

This guide provides step-by-step instructions for setting up Cloudflare Turnstile CAPTCHA protection for critical endpoints in your API.

## What is Cloudflare Turnstile?

Cloudflare Turnstile is a CAPTCHA alternative that provides user-friendly verification to help protect your website or API from spam and abuse. Unlike traditional CAPTCHAs, Turnstile is designed to be more user-friendly while still providing effective protection.

## Implementation Overview

We've already implemented the backend middleware for Cloudflare Turnstile, which will:

1. Intercept requests to critical endpoints (login, registration, admin login)
2. Check for the presence of a Cloudflare Turnstile token
3. Verify the token with Cloudflare's API
4. Allow or reject the request based on the verification result

## Prerequisites

- A Cloudflare account (free tier is sufficient)
- Access to your application's environment variables
- A domain registered with Cloudflare (recommended but not required)

## Step 1: Create a Cloudflare Turnstile Site Key

1. Log in to your Cloudflare account at [dash.cloudflare.com](https://dash.cloudflare.com)
2. Navigate to **Security** > **Turnstile**
3. Click **Add Site**
4. Fill in the form:
   - **Name**: Your application name (e.g., "Atlas VIP API")
   - **Domains**: Add your API domain(s)
   - **Widget Mode**: Select "Invisible" for a frictionless experience or "Managed" for more explicit verification
   - **Behavior**: Choose "Non-Interactive" for most API integrations
5. Click **Create**
6. After creation, you'll be shown your **Site Key** and **Secret Key**. Save these securely.

## Step 2: Configure Environment Variables

Add the following environment variables to your application:

```bash
# Cloudflare Turnstile configuration
ENABLE_CLOUDFLARE_CAPTCHA=true
CLOUDFLARE_SITE_KEY=your_site_key_here
CLOUDFLARE_SECRET_KEY=your_secret_key_here
```

You can add these to your `.env` file or set them directly in your deployment environment.

## Step 3: Update Frontend Forms

For each protected endpoint, update your frontend forms to include the Cloudflare Turnstile widget:

### 1. Add the Cloudflare Turnstile script to your HTML head:

```html
<script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
```

### 2. Add the widget to your login/registration forms:

```html
<form id="loginForm" method="post">
  <!-- Your existing form fields -->
  <input type="text" name="username" placeholder="Username" required>
  <input type="password" name="password" placeholder="Password" required>
  
  <!-- Cloudflare Turnstile widget -->
  <div class="cf-turnstile" data-sitekey="YOUR_SITE_KEY" data-callback="onCaptchaSuccess"></div>
  
  <button type="submit" id="submitButton" disabled>Login</button>
</form>

<script>
  // Enable submit button when CAPTCHA is solved
  function onCaptchaSuccess(token) {
    document.getElementById('submitButton').disabled = false;
  }
  
  // Handle form submission with CAPTCHA token
  document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const username = document.querySelector('[name="username"]').value;
    const password = document.querySelector('[name="password"]').value;
    
    // Get the CAPTCHA token
    const token = document.querySelector('[name="cf-turnstile-response"]').value;
    
    try {
      const response = await fetch('/auth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username,
          password,
          'cf-turnstile-response': token  // Include the token in your request
        })
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Successful login
        window.location.href = '/dashboard';
      } else {
        // Handle error
        alert(data.detail || 'Login failed');
        
        // Reset CAPTCHA on error
        turnstile.reset();
        document.getElementById('submitButton').disabled = true;
      }
    } catch (error) {
      console.error('Error:', error);
      alert('Network error occurred');
    }
  });
</script>
```

### 3. Customize the widget appearance:

You can customize the widget appearance using data attributes:

```html
<div class="cf-turnstile" 
  data-sitekey="YOUR_SITE_KEY" 
  data-callback="onCaptchaSuccess"
  data-theme="light"  <!-- or "dark" -->
  data-size="normal"  <!-- or "compact" -->
  data-appearance="always" <!-- or "execute" or "interaction-only" -->
></div>
```

## Step 4: Test Your Implementation

We've provided a test script to verify your Cloudflare Turnstile integration. Run it with:

```bash
cd /root/project/backend/security_tests
./run_security_tests.sh --cloudflare --captcha
```

This will test:
1. Your environment configuration
2. Whether protected endpoints require CAPTCHA
3. Whether verification works correctly

## Troubleshooting

### Problem: The middleware is not detecting CAPTCHA tokens

- Check that you're sending the token with the correct field name: `cf-turnstile-response`
- Ensure your JSON payload is correctly formatted

### Problem: Verification always fails

- Double-check your `CLOUDFLARE_SECRET_KEY` environment variable
- Ensure your domain is correctly configured in the Cloudflare Turnstile settings
- Check the logs for specific error codes

### Problem: "Invalid domain" error

- Make sure the domain you're testing from is added to the allowed domains in Turnstile settings
- For local development, you can add `localhost` to the allowed domains

## Best Practices

1. **Use invisible mode** for better user experience when possible
2. **Implement fallbacks** for users who can't solve CAPTCHAs
3. **Monitor failure rates** to detect potential issues
4. **Rotate your keys periodically** for better security
5. **Use rate limiting** in conjunction with CAPTCHA for better protection

## Additional Resources

- [Cloudflare Turnstile Documentation](https://developers.cloudflare.com/turnstile/)
- [Turnstile API Reference](https://developers.cloudflare.com/turnstile/get-started/client-side-rendering/)
- [Frontend Integration Examples](https://developers.cloudflare.com/turnstile/get-started/client-side-rendering/)

## Security Considerations

- Never expose your Secret Key in client-side code
- Always verify CAPTCHA tokens on the server side
- Don't rely solely on CAPTCHA for security; use it as part of a layered security approach
- Keep your Cloudflare account secured with strong passwords and 2FA

---

By following this guide, you should have a robust CAPTCHA protection system for your critical endpoints, helping to prevent automated attacks while maintaining a good user experience. 