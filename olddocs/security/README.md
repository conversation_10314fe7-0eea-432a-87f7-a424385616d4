# Security Test Suite

A comprehensive test suite for verifying security features in the FastAPI application.

## Features

- Security Headers Testing
  - Content Security Policy (CSP)
  - X-XSS-Protection
  - X-Content-Type-Options
  - X-Frame-Options
  - Referrer-Policy
  - Permissions-Policy
  - Cache-Control

- Rate Limiting Tests
  - Login rate limiting
  - API endpoint rate limiting
  - Distributed attack protection
  - Rate limit headers verification

- CAPTCHA Tests
  - Token verification
  - Integration with critical endpoints
  - Login and registration protection
  - Invalid token handling

## Prerequisites

- Python 3.8+
- pip
- virtualenv (recommended)

## Installation

1. Create and activate a virtual environment (recommended):
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Configure environment variables:
   - Copy `.env.example` to `.env`
   - Update the values in `.env` with your configuration

## Configuration

The test suite uses a configuration file (`test_config.py`) that includes:

- Base API URL
- Security header requirements
- Rate limit settings
- CAPTCHA configuration
- Test data and tokens

Update these settings according to your application's requirements.

## Running Tests

### Run All Tests

To run the complete test suite:

```bash
python run_security_tests.py
```

### Run Individual Test Categories

To run specific test categories:

```bash
# Security Headers Tests
python test_security_headers.py

# Rate Limiting Tests
python test_rate_limits.py

# CAPTCHA Tests
python test_captcha.py
```

## Test Results

Test results are saved in two formats:

1. JSON file: `test_results_YYYYMMDD_HHMMSS.json`
   - Contains detailed test results
   - Includes timing information
   - Preserves all response data

2. Console output:
   - Real-time test progress
   - Color-coded success/failure indicators
   - Summary of test results

## Test Categories

### Security Headers Tests

- Verifies presence and configuration of security headers
- Tests CSP directives
- Checks for proper header values
- Validates across all critical endpoints

### Rate Limiting Tests

- Tests login attempt limits
- Verifies API rate limits
- Simulates distributed attacks
- Checks rate limit headers
- Tests lockout duration

### CAPTCHA Tests

- Verifies token validation
- Tests integration with endpoints
- Checks login/registration protection
- Validates token handling
- Tests error scenarios

## Contributing

1. Follow the existing code structure
2. Add tests to the appropriate category
3. Update documentation for new tests
4. Maintain consistent error handling
5. Add new configuration options to `test_config.py`

## Error Handling

The test suite implements comprehensive error handling:

- Detailed error messages
- Error logging
- Test result preservation
- Graceful failure handling

## Logging

Test results and errors are logged to:

- Console (real-time feedback)
- JSON result files
- Log files with timestamps

## Best Practices

1. Run tests in a controlled environment
2. Use test mode for CAPTCHA verification
3. Reset rate limits between test runs
4. Monitor system resources during tests
5. Review logs for unexpected behavior

## Troubleshooting

Common issues and solutions:

1. Rate limit persistence:
   - Clear Redis cache
   - Reset rate limit counters
   - Wait for lockout expiration

2. CAPTCHA verification:
   - Check test token configuration
   - Verify API connectivity
   - Ensure test mode is enabled

3. Connection issues:
   - Verify API URL
   - Check network connectivity
   - Confirm server status

## License

This test suite is part of the main application and follows its licensing terms. 