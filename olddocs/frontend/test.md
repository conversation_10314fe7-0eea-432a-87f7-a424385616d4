# Update system and install required packages
sudo apt update && sudo apt upgrade -y
sudo apt install -y python3-pip nodejs npm nginx certbot python3-certbot-nginx

# Install Node.js 18.x (for better compatibility)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
# Create directory structure
mkdir -p /var/www/vpn-dashboard
cd /var/www/vpn-dashboard

# Clone your repository (replace with your repo URL)
git clone <your-repo-url> .

# Setup Python virtual environment
python3 -m venv venv
source venv/bin/activate

# Install requirements
cd backend
pip install -r requirements.txt

# Create systemd service for backend
sudo nano /etc/systemd/system/vpn-dashboard.service
[Unit]
Description=VPN Dashboard Backend
After=network.target

[Service]
User=www-data
Group=www-data
WorkingDirectory=/var/www/vpn-dashboard/backend
Environment="PATH=/var/www/vpn-dashboard/venv/bin"
ExecStart=/var/www/vpn-dashboard/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8000

[Install]
WantedBy=multi-user.target

# Build frontend
cd /var/www/vpn-dashboard/frontend

# Install dependencies and build
npm install
npm run build


# Create Nginx config
sudo nano /etc/nginx/sites-available/vpn-dashboard


server {
    listen 80;
    server_name your-domain.com;

    # Frontend
    location / {
        root /var/www/vpn-dashboard/frontend/build;
        try_files $uri $uri/ /index.html;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
# Enable and start services
sudo ln -s /etc/nginx/sites-available/vpn-dashboard /etc/nginx/sites-enabled/
sudo systemctl enable vpn-dashboard
sudo systemctl start vpn-dashboard
sudo systemctl restart nginx

# Setup SSL (replace with your domain)
sudo certbot --nginx -d your-domain.com

DATABASE_URL=postgresql://user:password@localhost/dbname
SECRET_KEY=your-secret-key


REACT_APP_API_URL=https://your-domain.com/api

sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 22/tcp
sudo ufw enable

sudo chown -R www-data:www-data /var/www/vpn-dashboard
sudo chmod -R 755 /var/www/vpn-dashboard


# View backend logs
sudo journalctl -u vpn-dashboard -f

# View nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log