Task System API Routes Documentation
============================

1. Authentication
----------------
POST /auth/token
- Purpose: Get access token for authentication
- Body: { username: string, password: string }
- Response: { access_token: string }

2. User Task Management
----------------------
GET /api/tasks/available
- Purpose: Get list of available tasks for the user
- Headers: Authorization: Bearer {token}
- Response: Task[]

POST /api/tasks/{taskId}/start
- Purpose: Start a task
- Headers: Authorization: Bearer {token}
- Response: TaskCompletion
- Notes: Opens social platform URL after successful start

POST /api/tasks/{taskId}/verify
- Purpose: Verify task completion
- Headers: Authorization: Bearer {token}
- Body: {
    verification_code?: string,  // For YouTube/Instagram view tasks
    platform_data?: {           // For social follow tasks
        username: string
    }
  }
- Response: TaskVerificationResult

POST /api/tasks/{taskId}/claim
- Purpose: Claim task reward
- Headers: Authorization: Bearer {token}
- Response: TaskCompletion

3. Daily Check-in System
-----------------------
GET /api/tasks/next-check-in
- Purpose: Get next check-in time info
- Headers: Authorization: Bearer {token}
- Response: {
    can_check_in: boolean,
    next_check_in: string  // ISO date
  }

POST /api/tasks/check-in
- Purpose: Perform daily check-in
- Headers: Authorization: Bearer {token}
- Response: CheckInResult

4. Admin Task Management
-----------------------
GET /api/tasks/admin/tasks/list
- Purpose: Get all tasks (admin only)
- Headers: Authorization: Bearer {token}
- Response: Task[]

POST /api/tasks/admin/tasks/create
- Purpose: Create new task
- Headers: Authorization: Bearer {token}
- Body: TaskCreate
- Response: Task

PATCH /api/tasks/admin/tasks/{taskId}
- Purpose: Update existing task
- Headers: Authorization: Bearer {token}
- Body: TaskUpdate
- Response: Task

POST /api/tasks/admin/tasks/{taskId}/toggle
- Purpose: Toggle task active status
- Headers: Authorization: Bearer {token}
- Response: Task
- Notes: Immediately updates task status without requiring full update

DELETE /api/tasks/admin/tasks/{taskId}
- Purpose: Delete single task
- Headers: Authorization: Bearer {token}
- Response: 204 No Content
- Notes: Cascades deletion to related records

DELETE /api/tasks/admin/tasks
- Purpose: Mass delete tasks
- Headers: Authorization: Bearer {token}
- Body: { task_ids: number[] }
- Response: 204 No Content
- Notes: 
  * Cascades deletion to all related records
  * Returns 404 if no tasks found
  * Handles transaction rollback on error

Admin Task Management Features:
-----------------------------
1. Task Selection:
   - Individual task selection
   - Select all functionality
   - Batch operations on selected tasks

2. Task Operations:
   - Create/Edit/Delete single task
   - Mass delete selected tasks
   - Toggle task active status
   - Reset task verification

3. Task Monitoring:
   - View completion statistics
   - Track success rates
   - Monitor user engagement
   - View verification attempts

5. Task Verification Rules
-------------------------
1. Social Follow Tasks (Telegram/Instagram/Twitter):
   - Requires waiting period (typically 1 hour)
   - Platform-specific verification (e.g., Telegram membership check)
   - Maximum verification attempts: 3

2. View Tasks (YouTube/Instagram):
   - Requires verification code
   - 10-minute viewing period
   - Maximum verification attempts: 3

3. Daily Tasks:
   - Auto-verification
   - 24-hour cooldown
   - Streak tracking

4. Referral Tasks:
   - Tracks referred user count
   - Auto-completes when target reached
   - Includes earnings tracking

Telegram Task Validation:
- POST /api/tasks/validate/telegram
- Body: { username: string }
- Response: { is_member: boolean, join_url: string }
- Validation Steps:
  1. Check user's Telegram membership via bot API
  2. Verify minimum 1 hour membership duration
  3. Max 3 verification attempts
  4. Auto-open Telegram deep link on task start

YouTube View Validation:
- POST /api/tasks/validate/youtube
- Body: { verification_code: string }
- Validation:
  1. Code must match generated pattern
  2. 10-minute view duration required
  3. Max 3 attempts

Instagram Validation:
- POST /api/tasks/validate/instagram
- Body: { username: string, post_url: string }
- Checks:
  1. Account follows target profile
  2. Post view duration > 1 minute
  3. Profile public visibility

Task Types and Requirements
-------------------------
1. TELEGRAM_CHANNEL:
   - Platform verification
   - Channel membership check
   - Minimum follow duration

2. YOUTUBE_VIEW:
   - Verification code
   - Minimum watch time
   - View count tracking

3. INSTAGRAM_FOLLOW:
   - Username verification
   - Follow duration check
   - Platform API verification

4. DAILY_CHECKIN:
   - Time-based verification
   - Streak tracking
   - Auto-reward distribution

5. REFERRAL:
   - User count tracking
   - Commission calculation
   - Auto-completion

UI/UX Implementation Notes
-------------------------
1. Task Display:
   - Group by category
   - Show reward prominently
   - Display time requirements
   - Progress indicators

2. Task Detail Slide:
   - Bottom-up animation
   - Clear instructions
   - Progress tracking
   - Timer display
   - Verification form

3. Admin Management:
   - Task CRUD operations
   - Analytics dashboard
   - User progress tracking
   - Reward management

4. Task State Management:
   - Centralized state
   - Real-time updates
   - Progress persistence
   - Error handling

5. Social Integration:
   - Direct platform links
   - Auto-open relevant apps
   - Deep linking support
   - Platform-specific handlers 