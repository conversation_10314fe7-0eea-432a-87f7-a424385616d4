# Nginx Configuration for AtlasVPN Project

## Overview
This directory contains the nginx configuration files for the AtlasVPN project, supporting both React and Astro frontends during the migration period.

## Files
- `atlas-vpn-site.conf` - Main nginx site configuration for dev.atlasvip.cloud
- `README.md` - This documentation file

## Configuration Details

### Server Configuration
- **Domain**: dev.atlasvip.cloud
- **SSL**: Let's Encrypt certificates
- **HTTP/2**: Enabled

### Frontend Routing
- **React Frontend**: `https://dev.atlasvip.cloud/` → `localhost:3000`
- **Astro Frontend**: `https://dev.atlasvip.cloud/astro/` → `localhost:3005`
- **Backend API**: `https://dev.atlasvip.cloud/api/` → `localhost:8000`
- **WebSocket**: `https://dev.atlasvip.cloud/ws/` → `localhost:8000`

### WebSocket Support
- **React HMR**: `/__vite_hmr` → `localhost:3000`
- **Astro HMR**: `/astro/__vite_hmr` → `localhost:3005`

## Deployment Instructions

### 1. Copy Configuration to Nginx
```bash
sudo cp nginx-config/atlas-vpn-site.conf /etc/nginx/sites-available/atlas-vpn-site.conf
```

### 2. Test Configuration
```bash
sudo nginx -t
```

### 3. Reload Nginx
```bash
sudo systemctl reload nginx
```

## Development Setup

### Start React Frontend
```bash
cd frontend
pnpm run dev  # Runs on port 3000
```

### Start Astro Frontend
```bash
cd frontend-astro
pnpm run dev  # Runs on port 3005
```

## Testing

### Test React Frontend
```bash
curl -I https://dev.atlasvip.cloud/
```

### Test Astro Frontend
```bash
curl -I https://dev.atlasvip.cloud/astro/dashboard/home
```

## Backup and Rollback

### Create Backup
```bash
sudo cp /etc/nginx/sites-available/atlas-vpn-site.conf /etc/nginx/sites-available/atlas-vpn-site.conf.backup-$(date +%Y%m%d_%H%M%S)
```

### Restore from Project
```bash
sudo cp nginx-config/atlas-vpn-site.conf /etc/nginx/sites-available/atlas-vpn-site.conf
sudo nginx -t && sudo systemctl reload nginx
```

## Security Features
- HSTS with preload
- Content Security Policy
- XSS Protection
- Content Type Options
- Referrer Policy
- File access restrictions

## Performance Optimizations
- Gzip compression
- Static asset caching
- Connection keep-alive
- HTTP/2 support
