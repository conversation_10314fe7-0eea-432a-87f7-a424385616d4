---
description: 
globs: 
alwaysApply: false
---
# API Integration Guide

## API Architecture

The frontend uses **TanStack Query** for all API interactions with a FastAPI backend on port 8000.

### Core API Files

- **API Client**: [frontend/src/utils/apiClient.ts](mdc:frontend/src/utils/apiClient.ts) - Axios-based client with auth
- **Query Client**: [frontend/src/main.tsx](mdc:frontend/src/main.tsx) - TanStack Query configuration

### API Hooks Pattern

All API calls use custom hooks following this pattern:

```typescript
// Example: useCurrentUserQuery
export function useCurrentUserQuery() {
  return useQuery<User>({
    queryKey: ['currentUser'],
    queryFn: () => fetchData('/api/user/current'),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
```

### Key API Hooks

#### Authentication
- **[frontend/src/hooks/authHooks.ts](mdc:frontend/src/hooks/authHooks.ts)** - User auth and session management
  - `useCurrentUserQuery()` - Get current user
  - `useTelegramLoginMutation()` - Telegram WebApp login

#### Dashboard Data
- **[frontend/src/hooks/dashboardHooks.ts](mdc:frontend/src/hooks/dashboardHooks.ts)** - Dashboard statistics
  - `useDashboardStatsQuery()` - User stats and metrics
  - `useUserTransactionsQuery()` - Transaction history

#### VPN Services
- **[frontend/src/hooks/vpnHooks.ts](mdc:frontend/src/hooks/vpnHooks.ts)** - VPN management
  - `useVpnSubscriptionsQuery()` - User's VPN subscriptions
  - `useVpnPackagesQuery()` - Available VPN packages
  - `useVpnPanelsQuery()` - Marzban panel management

#### Game Cards
- **[frontend/src/hooks/cardHooks.ts](mdc:frontend/src/hooks/cardHooks.ts)** - Game card system
  - `useUnifiedCardsQuery()` - User cards and catalog
  - `useClaimAllProfitsMutation()` - Claim card profits

#### Social Features
- **[frontend/src/hooks/referralHooks.ts](mdc:frontend/src/hooks/referralHooks.ts)** - Referral system
- **[frontend/src/hooks/taskHooks.ts](mdc:frontend/src/hooks/taskHooks.ts)** - Task/quest system
- **[frontend/src/hooks/chatHooks.ts](mdc:frontend/src/hooks/chatHooks.ts)** - Chat and messaging

### API Endpoints

#### Core Endpoints
- `GET /api/user/current` - Current user info
- `POST /api/auth/telegram` - Telegram login
- `GET /api/user/dashboard/stats` - Dashboard statistics

#### VPN Endpoints
- `GET /api/subscriptions` - User VPN subscriptions
- `GET /api/packages` - Available VPN packages
- `GET /api/panels` - Marzban panels
- `POST /api/subscriptions/purchase` - Purchase VPN package

#### Game Endpoints
- `GET /api/cards/all` - Unified cards (owned + catalog)
- `POST /api/cards/claim-all` - Claim all card profits
- `POST /api/cards/upgrade` - Upgrade card level

### Error Handling

All API hooks include:
- **Graceful degradation** - Return empty arrays/default values on 404
- **Global error handling** - Toast notifications for errors
- **Retry logic** - Automatic retries for network failures
- **Loading states** - Proper loading indicators

### Performance Optimizations

- **Stale-while-revalidate** - 5-minute stale time for most queries
- **Conditional fetching** - Only fetch when user is authenticated
- **Tab-based loading** - Only load data for active tabs
- **Background refetch** - Keep data fresh when app regains focus

### Development Tips

1. **Always use hooks** - Never call API directly in components
2. **Handle loading states** - Show spinners during data fetching
3. **Error boundaries** - Wrap components with error handling
4. **Optimistic updates** - Update UI before API response for better UX
