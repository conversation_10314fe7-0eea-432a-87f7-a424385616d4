---
description: 
globs: 
alwaysApply: false
---
# Astro Migration Guide

## Migration Overview

Complete guide for migrating from **React 19 + Vite** to **Astro** for 10x better performance in Telegram Mini Apps.

### Performance Comparison

| Metric | Current React | Astro Target | Improvement |
|--------|---------------|--------------|-------------|
| **Bundle Size** | 1.2MB | ~50KB | **96% smaller** |
| **Load Time** | 3-5s | 0.5s | **90% faster** |
| **First Paint** | 2s | 0.3s | **85% faster** |
| **Interactive** | 4s | 0.8s | **80% faster** |

### Migration Strategy

#### Phase 1: Setup Astro Project ✅
- **Location**: [frontend-astro/](mdc:frontend-astro) - New Astro project created
- **Config**: [frontend-astro/astro.config.mjs](mdc:frontend-astro/astro.config.mjs) - Optimized for Telegram
- **Layout**: [frontend-astro/src/layouts/Layout.astro](mdc:frontend-astro/src/layouts/Layout.astro) - Base layout
- **Landing**: [frontend-astro/src/components/LandingPage.tsx](mdc:frontend-astro/src/components/LandingPage.tsx) - React component in Astro

#### Phase 2: Component Migration
Convert React components to Astro with selective hydration:

```astro
---
// Astro component (server-rendered)
import { formatCurrency } from '../utils/format';
const { user, stats } = Astro.props;
---

<div class="dashboard-card">
  <h2>{user.name}</h2>
  <p>Balance: {formatCurrency(stats.balance)}</p>
  
  <!-- Only hydrate interactive parts -->
  <InteractiveButton client:load />
</div>
```

### Component Migration Map

#### Core Components
- **DashboardLayout** → `src/layouts/DashboardLayout.astro`
- **Header** → `src/components/Header.astro` (static)
- **BottomNavigation** → `src/components/BottomNav.tsx` (client:load)

#### Page Components
- **LandingPage** → `src/pages/index.astro`
- **HomeTab** → `src/pages/dashboard/home.astro`
- **EarnTab** → `src/pages/dashboard/earn.astro`
- **PremiumTab** → `src/pages/dashboard/premium.astro`
- **WalletTab** → `src/pages/dashboard/wallet.astro`
- **FriendsTab** → `src/pages/dashboard/friends.astro`

#### Interactive Components (Keep React)
- **Avatar3DViewer** → `src/components/Avatar3DViewer.tsx` (client:visible)
- **VersePage** → `src/pages/verse.astro` with React islands
- **NewServiceForm** → `src/components/NewServiceForm.tsx` (client:load)

### API Integration Migration

#### Current Pattern (React)
```typescript
// React hook pattern
const { data: user } = useCurrentUserQuery();
```

#### Astro Pattern
```astro
---
// Server-side data fetching
import { api } from '../utils/apiClient';

let user;
try {
  const response = await api.get('/api/user/current');
  user = response.data;
} catch (error) {
  user = null;
}
---

{user ? (
  <UserDashboard user={user} />
) : (
  <LoginPrompt client:load />
)}
```

### Hydration Strategy

#### Client Directives
- **`client:load`** - Hydrate immediately (forms, interactive buttons)
- **`client:visible`** - Hydrate when visible (3D components, heavy widgets)
- **`client:idle`** - Hydrate when browser idle (non-critical features)
- **`client:media`** - Hydrate based on media query (mobile-specific)

#### Example Usage
```astro
<!-- Static content (no JavaScript) -->
<UserProfile user={user} />

<!-- Interactive form (immediate hydration) -->
<ContactForm client:load />

<!-- Heavy 3D component (lazy hydration) -->
<Avatar3DViewer client:visible />

<!-- Mobile-only component -->
<MobileMenu client:media="(max-width: 768px)" />
```

### State Management Migration

#### Current: Zustand + TanStack Query
```typescript
// Current complex state management
const user = useCurrentUserQuery();
const stats = useDashboardStatsQuery();
const cards = useUnifiedCardsQuery();
```

#### Astro: Server-first with Islands
```astro
---
// Fetch all data server-side
const [user, stats, cards] = await Promise.all([
  fetchUser(),
  fetchStats(),
  fetchCards()
]);
---

<!-- Pass data to interactive islands -->
<Dashboard 
  user={user} 
  stats={stats} 
  cards={cards}
  client:load 
/>
```

### Bundle Optimization

#### Remove Heavy Dependencies
- **Three.js** → Only load in 3D pages with `client:visible`
- **Framer Motion** → Replace with CSS animations where possible
- **React Router** → Use Astro's file-based routing
- **TanStack Query** → Server-side data fetching

#### Keep Essential Libraries
- **Telegram SDK** → Still needed for WebApp integration
- **Axios** → For client-side API calls
- **Tailwind** → Styling (but smaller bundle)

### Migration Steps

#### Step 1: Static Pages First
1. Convert landing page to Astro
2. Migrate dashboard layout
3. Convert simple tabs (Home, Wallet)

#### Step 2: Interactive Features
1. Identify truly interactive components
2. Convert to React islands with proper hydration
3. Optimize 3D components for lazy loading

#### Step 3: API Integration
1. Move data fetching to server-side
2. Keep mutations client-side
3. Implement proper error handling

#### Step 4: Performance Testing
1. Measure bundle sizes
2. Test load times in Telegram
3. Optimize hydration timing

### Development Workflow

#### Commands
```bash
# Start Astro dev server
cd frontend-astro && pnpm run dev

# Build and analyze
pnpm run build

# Preview production build
pnpm run preview
```

#### File Structure
```
frontend-astro/
├── src/
│   ├── layouts/          # Astro layouts
│   ├── pages/            # File-based routing
│   ├── components/       # Mixed Astro + React
│   └── utils/            # Shared utilities
├── public/               # Static assets
└── astro.config.mjs      # Astro configuration
```

### Testing Strategy

#### Performance Metrics
- **Bundle size** < 50KB initial
- **Load time** < 1s in Telegram
- **Interactive** < 2s
- **Memory usage** < 50MB

#### Compatibility Testing
- **Telegram Desktop** - Primary development
- **Telegram Mobile** - iOS and Android
- **Browser fallback** - Non-Telegram environments

### Rollback Plan

If migration issues arise:
1. **Keep current React app** running on port 3000
2. **Run Astro app** on port 3001 for testing
3. **Switch Nginx** between versions easily
4. **Gradual migration** - migrate page by page

### Expected Benefits

#### Performance
- **10x faster** initial load
- **95% smaller** JavaScript bundles
- **Better SEO** (though not critical for Telegram)
- **Improved mobile** performance

#### Developer Experience
- **Simpler architecture** - Less complex state management
- **Better performance** by default
- **Easier debugging** - Less client-side complexity
- **Future-proof** - Modern web standards
