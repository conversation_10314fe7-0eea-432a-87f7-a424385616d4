# Development Guide for Astro Migration

## Overview

This guide provides comprehensive instructions for developing and maintaining the Astro-migrated VIPVerse Telegram mini app, including setup, development workflows, and best practices.

## Development Environment Setup

### Prerequisites

**Required Software:**
- Node.js 18+ (LTS recommended)
- npm 9+ or yarn 1.22+
- Git
- VS Code (recommended) with extensions:
  - Astro
  - TypeScript and JavaScript Language Features
  - Tailwind CSS IntelliSense
  - ES7+ React/Redux/React-Native snippets

**System Requirements:**
- RAM: 8GB minimum, 16GB recommended
- Storage: 5GB free space
- OS: Windows 10+, macOS 10.15+, or Linux

### Project Setup

**1. Clone and Install Dependencies:**
```bash
cd /opt/atlasvpn/frontend-astro
npm install

# Install additional development dependencies
npm install -D @types/node @types/react @types/react-dom
npm install -D eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin
npm install -D prettier prettier-plugin-astro prettier-plugin-tailwindcss
```

**2. Environment Configuration:**
```bash
# Copy environment template
cp .env.example .env.local

# Configure environment variables
cat > .env.local << EOF
# API Configuration
VITE_API_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000/ws

# Development Settings
NODE_ENV=development
VITE_DEV_MODE=true

# Telegram Configuration (for testing)
VITE_TELEGRAM_BOT_NAME=your_bot_name
EOF
```

**3. VS Code Configuration:**
```json
// .vscode/settings.json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "files.associations": {
    "*.astro": "astro"
  },
  "emmet.includeLanguages": {
    "astro": "html"
  }
}
```

**4. Development Scripts:**
```json
// package.json scripts
{
  "scripts": {
    "dev": "astro dev --host 0.0.0.0 --port 4321",
    "build": "astro build",
    "preview": "astro preview",
    "type-check": "tsc --noEmit",
    "lint": "eslint src --ext .ts,.tsx,.astro",
    "lint:fix": "eslint src --ext .ts,.tsx,.astro --fix",
    "format": "prettier --write src/**/*.{ts,tsx,astro,css,md}",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "clean": "rm -rf dist .astro node_modules/.vite"
  }
}
```

## Development Workflow

### 1. Starting Development Server

**Backend Setup (Terminal 1):**
```bash
cd /opt/atlasvpn/backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

**Frontend Setup (Terminal 2):**
```bash
cd /opt/atlasvpn/frontend-astro
npm run dev
```

**Access Points:**
- Frontend: http://localhost:4321
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs

### 2. Project Structure

```
frontend-astro/
├── src/
│   ├── components/
│   │   ├── react/              # React islands
│   │   │   ├── Dashboard/
│   │   │   │   ├── DashboardLayout.tsx
│   │   │   │   ├── HomeTab.tsx
│   │   │   │   ├── EarnTab.tsx
│   │   │   │   ├── WalletTab.tsx
│   │   │   │   ├── FriendsTab.tsx
│   │   │   │   └── PremiumTab.tsx
│   │   │   ├── Game/
│   │   │   │   ├── CardSystem.tsx
│   │   │   │   ├── TaskSystem.tsx
│   │   │   │   └── VerseWorld.tsx
│   │   │   ├── Auth/
│   │   │   │   ├── LandingPage.tsx
│   │   │   │   └── UserSetup.tsx
│   │   │   └── UI/
│   │   │       ├── LoadingSpinner.tsx
│   │   │       ├── ErrorBoundary.tsx
│   │   │       └── Modal.tsx
│   │   └── astro/              # Astro components
│   │       ├── Layout/
│   │       │   ├── Header.astro
│   │       │   ├── Footer.astro
│   │       │   └── Navigation.astro
│   │       └── UI/
│   │           ├── Button.astro
│   │           ├── Card.astro
│   │           └── Icon.astro
│   ├── layouts/
│   │   ├── BaseLayout.astro
│   │   ├── DashboardLayout.astro
│   │   └── VerseLayout.astro
│   ├── pages/
│   │   ├── index.astro
│   │   ├── new-user-setup.astro
│   │   ├── dashboard/
│   │   │   ├── index.astro
│   │   │   ├── home.astro
│   │   │   ├── earn.astro
│   │   │   ├── wallet.astro
│   │   │   ├── friends.astro
│   │   │   └── premium.astro
│   │   ├── verse/
│   │   │   └── index.astro
│   │   └── [...slug].astro
│   ├── stores/                 # Zustand stores
│   │   ├── uiStore.ts
│   │   ├── chatStore.ts
│   │   └── taskStore.ts
│   ├── hooks/                  # React hooks
│   │   ├── useAuth.ts
│   │   ├── useWebSocket.ts
│   │   └── useLocalStorage.ts
│   ├── utils/
│   │   ├── apiClient.ts
│   │   ├── telegram.ts
│   │   ├── formatters.ts
│   │   └── constants.ts
│   ├── types/
│   │   ├── api.ts
│   │   ├── game.ts
│   │   └── telegram.ts
│   ├── styles/
│   │   ├── global.css
│   │   └── components.css
│   └── middleware.ts
├── public/
│   ├── icons/
│   ├── images/
│   └── sounds/
├── docs/                       # Documentation
├── astro.config.mjs
├── tailwind.config.mjs
├── tsconfig.json
├── package.json
└── README.md
```

### 3. Component Development Patterns

**Astro Component Example:**
```astro
---
// src/components/astro/UI/Card.astro
export interface Props {
  title: string;
  description?: string;
  variant?: 'default' | 'premium' | 'success';
  class?: string;
}

const { title, description, variant = 'default', class: className } = Astro.props;

const variantClasses = {
  default: 'bg-gray-900/50 border-gray-600/20',
  premium: 'bg-gradient-to-br from-purple-900/50 to-blue-900/50 border-purple-500/20',
  success: 'bg-gradient-to-br from-green-900/50 to-emerald-900/50 border-green-500/20'
};
---

<div class={`rounded-xl p-4 border ${variantClasses[variant]} ${className}`}>
  <h3 class="font-semibold text-white mb-2">{title}</h3>
  {description && <p class="text-sm text-gray-300">{description}</p>}
  <slot />
</div>
```

**React Island Example:**
```typescript
// src/components/react/UI/InteractiveCard.tsx
import { motion } from 'framer-motion';
import { useState } from 'react';

interface Props {
  title: string;
  description?: string;
  onAction?: () => void;
  actionLabel?: string;
  children?: React.ReactNode;
}

export default function InteractiveCard({ 
  title, 
  description, 
  onAction, 
  actionLabel = 'Action',
  children 
}: Props) {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <motion.div
      className="bg-gradient-to-br from-gray-900/50 to-gray-800/50 rounded-xl p-4 border border-gray-600/20 cursor-pointer"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-white">{title}</h3>
          {description && (
            <p className="text-sm text-gray-300 mt-1">{description}</p>
          )}
        </div>
        
        {onAction && (
          <motion.button
            onClick={onAction}
            className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm transition-colors"
            animate={{ opacity: isHovered ? 1 : 0.8 }}
          >
            {actionLabel}
          </motion.button>
        )}
      </div>
      
      {children && <div className="mt-4">{children}</div>}
    </motion.div>
  );
}
```

### 4. State Management Patterns

**Zustand Store Example:**
```typescript
// src/stores/gameStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface GameState {
  selectedTab: string;
  notifications: Notification[];
  isLoading: boolean;
  setSelectedTab: (tab: string) => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
  setLoading: (loading: boolean) => void;
}

export const useGameStore = create<GameState>()(
  persist(
    (set, get) => ({
      selectedTab: 'home',
      notifications: [],
      isLoading: false,
      
      setSelectedTab: (tab) => set({ selectedTab: tab }),
      
      addNotification: (notification) => set((state) => ({
        notifications: [...state.notifications, notification]
      })),
      
      removeNotification: (id) => set((state) => ({
        notifications: state.notifications.filter(n => n.id !== id)
      })),
      
      setLoading: (loading) => set({ isLoading: loading }),
    }),
    {
      name: 'game-store',
      partialize: (state) => ({ selectedTab: state.selectedTab }),
    }
  )
);
```

**React Query Hook Example:**
```typescript
// src/hooks/useGameData.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../utils/apiClient';

export function useGameData() {
  const queryClient = useQueryClient();
  
  const { data: dashboardStats, isLoading } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: () => api.get('/api/user/dashboard/stats').then(res => res.data),
    refetchInterval: 30000, // Refresh every 30 seconds
    staleTime: 15000, // Consider data stale after 15 seconds
  });
  
  const { data: userCards } = useQuery({
    queryKey: ['userCards'],
    queryFn: () => api.get('/api/cards/unified').then(res => res.data),
    refetchInterval: 60000, // Refresh every minute
  });
  
  const upgradeCardMutation = useMutation({
    mutationFn: (cardId: number) => api.post(`/api/cards/${cardId}/upgrade`),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['userCards'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
    onError: (error) => {
      console.error('Card upgrade failed:', error);
    },
  });
  
  return {
    dashboardStats,
    userCards,
    isLoading,
    upgradeCard: upgradeCardMutation.mutate,
    isUpgrading: upgradeCardMutation.isPending,
  };
}
```

## Testing Strategy

### 1. Unit Testing Setup

**Vitest Configuration:**
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
  },
});
```

**Test Setup:**
```typescript
// src/test/setup.ts
import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock Telegram WebApp
global.window = Object.create(window);
Object.defineProperty(window, 'Telegram', {
  value: {
    WebApp: {
      ready: vi.fn(),
      expand: vi.fn(),
      disableVerticalSwipes: vi.fn(),
      initData: 'mock_init_data',
      initDataUnsafe: { user: { id: 123, username: 'testuser' } },
    },
  },
  writable: true,
});
```

**Component Test Example:**
```typescript
// src/components/react/__tests__/InteractiveCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import InteractiveCard from '../UI/InteractiveCard';

describe('InteractiveCard', () => {
  it('renders title and description', () => {
    render(
      <InteractiveCard 
        title="Test Card" 
        description="Test description" 
      />
    );
    
    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('Test description')).toBeInTheDocument();
  });
  
  it('calls onAction when button is clicked', () => {
    const mockAction = vi.fn();
    
    render(
      <InteractiveCard 
        title="Test Card" 
        onAction={mockAction}
        actionLabel="Click Me"
      />
    );
    
    fireEvent.click(screen.getByText('Click Me'));
    expect(mockAction).toHaveBeenCalledOnce();
  });
});
```

### 2. Integration Testing

**API Integration Test:**
```typescript
// src/hooks/__tests__/useAuth.test.tsx
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { describe, it, expect, beforeEach } from 'vitest';
import { useAuth } from '../useAuth';

describe('useAuth', () => {
  let queryClient: QueryClient;
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
  });
  
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
  
  it('should handle authentication state', async () => {
    const { result } = renderHook(() => useAuth(), { wrapper });
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });
    
    // Test authentication logic
    expect(result.current.isAuthenticated).toBeDefined();
  });
});
```

## Build and Deployment

### 1. Build Process

**Production Build:**
```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Build for production
npm run build

# Preview production build
npm run preview
```

**Build Configuration:**
```javascript
// astro.config.mjs
export default defineConfig({
  output: 'static',
  build: {
    assets: 'assets',
    assetsPrefix: process.env.CDN_URL,
  },
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'react-vendor': ['react', 'react-dom'],
            'query-vendor': ['@tanstack/react-query'],
            'three-vendor': ['three', '@react-three/fiber'],
            'animation-vendor': ['framer-motion'],
          },
        },
      },
    },
  },
});
```

### 2. Performance Optimization

**Bundle Analysis:**
```bash
# Install bundle analyzer
npm install -D rollup-plugin-visualizer

# Generate bundle report
npm run build -- --mode analyze
```

**Optimization Checklist:**
- [ ] Lazy load heavy components
- [ ] Optimize images and assets
- [ ] Minimize bundle size
- [ ] Enable compression
- [ ] Configure caching headers
- [ ] Optimize for mobile devices

## Debugging and Troubleshooting

### 1. Common Issues

**Telegram WebApp Not Loading:**
```typescript
// Debug Telegram integration
console.log('Telegram WebApp:', window.Telegram?.WebApp);
console.log('Init Data:', window.Telegram?.WebApp?.initData);

// Check if running in Telegram
if (!window.Telegram?.WebApp) {
  console.warn('Not running in Telegram WebApp');
}
```

**React Query Issues:**
```typescript
// Enable React Query DevTools in development
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function App() {
  return (
    <>
      {/* Your app */}
      {import.meta.env.DEV && <ReactQueryDevtools />}
    </>
  );
}
```

**Build Issues:**
```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build
```

### 2. Development Tools

**Browser DevTools Extensions:**
- React Developer Tools
- TanStack Query DevTools
- Astro DevTools

**VS Code Extensions:**
- Astro
- Error Lens
- GitLens
- Thunder Client (API testing)

## Best Practices

### 1. Code Organization
- Use consistent file naming conventions
- Group related components together
- Separate concerns (UI, logic, data)
- Write self-documenting code

### 2. Performance
- Lazy load non-critical components
- Optimize images and assets
- Use React.memo for expensive components
- Implement proper error boundaries

### 3. Security
- Validate all user inputs
- Sanitize data from APIs
- Use secure storage for sensitive data
- Implement proper authentication checks

### 4. Accessibility
- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers

## Conclusion

This development guide provides the foundation for building and maintaining the Astro-migrated VIPVerse application. Follow these patterns and practices to ensure consistent, maintainable, and performant code.
