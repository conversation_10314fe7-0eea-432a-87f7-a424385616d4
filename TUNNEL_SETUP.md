# VS Code Tunnel Service Setup & Security Guide

## 🚀 Quick Start

Your VS Code tunnel is now running as a systemd service! You can access it at:
**https://vscode.dev/tunnel/atlasvpn-server**

## 🔧 Management Commands

Use these aliases for easy management:

```bash
# Check tunnel status
tunnel status

# Show connection info  
tunnel info

# Restart tunnel service
tunnel restart

# Show security configuration
tunnel security

# View recent logs
tunnel logs

# Configure firewall security
tunnel-firewall
```

## 🔒 Security Features

### Authentication
- **GitHub OAuth Required**: Only users authenticated with GitHub can access the tunnel
- **Single Session**: Only one user can connect at a time
- **Session Timeout**: Automatic logout after inactivity

### Encryption
- **End-to-End Encryption**: AES 256 CTR mode encryption
- **Secure Transport**: All communication over HTTPS
- **No Inbound Ports**: Tunnel uses outbound connections only

### Network Security
- **Firewall Protected**: UFW configured to block unnecessary traffic
- **Outbound Only**: No listening ports on your server
- **DNS & HTTPS Only**: Limited outbound connections

## 📋 Service Management

### Systemd Service
```bash
# Check service status
systemctl status vscode-tunnel.service

# Start/stop/restart service
systemctl start vscode-tunnel.service
systemctl stop vscode-tunnel.service
systemctl restart vscode-tunnel.service

# View service logs
journalctl -u vscode-tunnel.service -f
```

### Service Configuration
- **Location**: `/etc/systemd/system/vscode-tunnel.service`
- **Auto-start**: Enabled on boot
- **Auto-restart**: Service restarts automatically if it crashes
- **User**: Runs as root with restricted permissions

## 🔐 First-Time Setup

1. **Access the tunnel URL**: https://vscode.dev/tunnel/atlasvpn-server
2. **GitHub Authentication**: You'll be redirected to GitHub for OAuth
3. **Grant Permissions**: Authorize VS Code to access your GitHub account
4. **Start Coding**: You'll be connected to your server environment

## 📁 Directory Structure

```
/opt/atlasvpn/
├── code                          # VS Code CLI binary
├── tunnel-security.sh            # Security management script
├── firewall-tunnel.sh            # Firewall configuration script
├── .vscode/
│   ├── cli/                      # VS Code CLI data
│   └── tunnel-config.json        # Tunnel configuration
├── .runtime/                     # Runtime directory
├── tmp/                          # Temporary files
├── logs/                         # Security logs
└── .local/share/                 # XDG data directory
```

## 🛡️ Security Best Practices

### Access Control
- Only share the tunnel URL with trusted users
- Regularly review GitHub OAuth applications
- Monitor access logs for suspicious activity

### Server Security
- Keep your server updated
- Use strong SSH keys
- Monitor system logs regularly
- Run the firewall configuration script

### Tunnel Security
- Don't expose sensitive files in the workspace
- Use environment variables for secrets
- Regularly restart the tunnel service
- Monitor for unusual network activity

## 🚨 Troubleshooting

### Service Not Starting
```bash
# Check service status
tunnel status

# View detailed logs
tunnel logs

# Restart service
tunnel restart
```

### Connection Issues
```bash
# Verify service is running
systemctl status vscode-tunnel.service

# Check network connectivity
curl -I https://vscode.dev

# Verify GitHub authentication
# Clear browser cache and try again
```

### Permission Issues
```bash
# Check directory permissions
ls -la /opt/atlasvpn/.vscode/

# Fix permissions if needed
chmod -R 755 /opt/atlasvpn/.vscode/
```

## 📊 Monitoring

### Security Logs
- Location: `/opt/atlasvpn/logs/tunnel-security.log`
- Contains: Access attempts, status checks, security events

### System Logs
```bash
# Real-time service logs
journalctl -u vscode-tunnel.service -f

# Firewall logs
grep UFW /var/log/syslog
```

## 🔄 Updates

### Updating VS Code CLI
```bash
# Download latest version
cd /opt/atlasvpn
wget https://code.visualstudio.com/sha/download?build=stable&os=cli-alpine-x64 -O code-new

# Replace binary (service will auto-restart)
mv code code-backup
mv code-new code
chmod +x code
tunnel restart
```

## 📞 Support

If you encounter issues:

1. Check the service status: `tunnel status`
2. Review logs: `tunnel logs`  
3. Verify firewall: `tunnel-firewall`
4. Restart service: `tunnel restart`

For advanced configuration, edit:
- Service file: `/etc/systemd/system/vscode-tunnel.service`
- Security config: `/opt/atlasvpn/.vscode/tunnel-config.json`

---

**Access URL**: https://vscode.dev/tunnel/atlasvpn-server
**Authentication**: GitHub OAuth Required
**Security**: AES 256 CTR End-to-End Encryption 