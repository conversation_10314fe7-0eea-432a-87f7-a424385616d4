import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Shield, AlertTriangle, Ban, Activity, Search } from 'lucide-react'

interface SecurityLog {
  id: number
  action_type: string
  user_id?: number
  username?: string
  ip_address: string
  user_agent?: string
  status: string
  risk_level: string
  details?: any
  location_info?: any
  attempt_count: number
  is_blocked: boolean
  blocked_until?: string
  created_at: string
}

interface SecurityStats {
  total_events: number
  high_risk_events: number
  blocked_ips: number
  failed_logins: number
  risk_distribution: Array<{ risk_level: string; count: number }>
}

interface BlockedToken {
  id: number
  user_id: number
  username?: string
  revocation_reason: string
  revoked_at: string
  expires_at: string
  revoked_by_ip: string
}

export function Security() {
  const [searchTerm, setSearchTerm] = useState('')
  const [logTypeFilter, setLogTypeFilter] = useState<string>('all')
  const [riskFilter, setRiskFilter] = useState<string>('all')

  const { data: securityLogs, isLoading: isLogsLoading } = useQuery<SecurityLog[]>({
    queryKey: ['security-logs'],
    queryFn: () => api.get('/admin/system/security/logs').then(res => res.data)
  })

  const { data: securityStats, isLoading: isStatsLoading } = useQuery<SecurityStats>({
    queryKey: ['security-stats'],
    queryFn: () => api.get('/admin/system/security/stats').then(res => res.data)
  })

  const { data: blockedTokens } = useQuery<BlockedToken[]>({
    queryKey: ['blocked-tokens'],
    queryFn: () => api.get('/admin/system/security/blocked-tokens').then(res => res.data)
  })

  // Filter logs based on search
  const filteredLogs = securityLogs?.filter(log => {
    const matchesSearch = log.ip_address.includes(searchTerm) ||
                         log.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.action_type.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesSearch
  })

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    isLoading,
    variant = 'default'
  }: { 
    title: string
    value: string | number
    description: string
    icon: any
    isLoading: boolean
    variant?: 'default' | 'warning' | 'danger'
  }) => (
    <Card className={variant === 'warning' ? 'border-yellow-200' : variant === 'danger' ? 'border-red-200' : ''}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${variant === 'warning' ? 'text-yellow-600' : variant === 'danger' ? 'text-red-600' : 'text-muted-foreground'}`} />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        ) : (
          <>
            <div className={`text-2xl font-bold ${variant === 'warning' ? 'text-yellow-700' : variant === 'danger' ? 'text-red-700' : ''}`}>
              {value}
            </div>
            <p className="text-xs text-muted-foreground">{description}</p>
          </>
        )}
      </CardContent>
    </Card>
  )

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel.toLowerCase()) {
      case 'low': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-red-100 text-red-800'
      case 'critical': return 'bg-red-200 text-red-900'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success': return 'bg-green-100 text-green-800'
      case 'failure': return 'bg-red-100 text-red-800'
      case 'blocked': return 'bg-red-200 text-red-900'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isLogsLoading || isStatsLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Security</h1>
          <p className="text-gray-600">Monitor security events and threats</p>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Security</h1>
          <p className="text-gray-600">Monitor security events, threats, and system protection</p>
        </div>
      </div>

      {/* Security Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Security Overview</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Events"
            value={securityStats?.total_events || 0}
            description="Last 7 days"
            icon={Activity}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="High Risk Events"
            value={securityStats?.high_risk_events || 0}
            description="Requires attention"
            icon={AlertTriangle}
            isLoading={isStatsLoading}
            variant="warning"
          />
          <StatCard
            title="Blocked IPs"
            value={securityStats?.blocked_ips || 0}
            description="Currently blocked"
            icon={Ban}
            isLoading={isStatsLoading}
            variant="danger"
          />
          <StatCard
            title="Failed Logins"
            value={securityStats?.failed_logins || 0}
            description="Last 7 days"
            icon={Shield}
            isLoading={isStatsLoading}
          />
        </div>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="logs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="logs">Security Logs</TabsTrigger>
          <TabsTrigger value="tokens">Blocked Tokens</TabsTrigger>
          <TabsTrigger value="analytics">Risk Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="logs" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search by IP, username, or action..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={logTypeFilter} onValueChange={setLogTypeFilter}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Action Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Actions</SelectItem>
                      <SelectItem value="login">Login</SelectItem>
                      <SelectItem value="logout">Logout</SelectItem>
                      <SelectItem value="api_access">API Access</SelectItem>
                      <SelectItem value="admin_action">Admin Action</SelectItem>
                      <SelectItem value="failed_auth">Failed Auth</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={riskFilter} onValueChange={setRiskFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Risk Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Risks</SelectItem>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Logs Table */}
          <Card>
            <CardHeader>
              <CardTitle>Security Events ({filteredLogs?.length || 0})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Risk Level</TableHead>
                    <TableHead>Attempts</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs?.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(log.created_at).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {log.action_type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {log.username ? (
                          <div>
                            <div className="font-medium">{log.username}</div>
                            <div className="text-sm text-gray-600">ID: {log.user_id}</div>
                          </div>
                        ) : (
                          <span className="text-gray-500">Anonymous</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="font-mono text-sm">{log.ip_address}</div>
                        {log.is_blocked && (
                          <Badge variant="destructive" className="mt-1">
                            Blocked
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(log.status)}>
                          {log.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRiskLevelColor(log.risk_level)}>
                          {log.risk_level}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-center">
                          {log.attempt_count}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredLogs && filteredLogs.length === 0 && (
                <div className="text-center py-12">
                  <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No security events found</h3>
                  <p className="text-gray-600">
                    {searchTerm || logTypeFilter !== 'all' || riskFilter !== 'all' 
                      ? 'Try adjusting your search or filters.' 
                      : 'No security events recorded in the selected time period.'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tokens" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revoked Tokens ({blockedTokens?.length || 0})</CardTitle>
              <CardDescription>
                List of revoked authentication tokens
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Revocation Reason</TableHead>
                    <TableHead>Revoked At</TableHead>
                    <TableHead>Expires At</TableHead>
                    <TableHead>Revoked By IP</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {blockedTokens?.map((token) => (
                    <TableRow key={token.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{token.username}</div>
                          <div className="text-sm text-gray-600">ID: {token.user_id}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {token.revocation_reason}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(token.revoked_at).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {new Date(token.expires_at).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="font-mono text-sm">{token.revoked_by_ip}</div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {blockedTokens && blockedTokens.length === 0 && (
                <div className="text-center py-12">
                  <Ban className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No revoked tokens</h3>
                  <p className="text-gray-600">No authentication tokens have been revoked.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Risk Level Distribution</CardTitle>
              <CardDescription>
                Breakdown of security events by risk level
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {securityStats?.risk_distribution?.map((item) => (
                  <div key={item.risk_level} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Badge className={getRiskLevelColor(item.risk_level)}>
                        {item.risk_level}
                      </Badge>
                      <span className="text-sm text-gray-600">
                        {item.count} events
                      </span>
                    </div>
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          item.risk_level === 'high' || item.risk_level === 'critical' 
                            ? 'bg-red-500' 
                            : item.risk_level === 'medium' 
                              ? 'bg-yellow-500' 
                              : 'bg-green-500'
                        }`}
                        style={{ 
                          width: `${Math.min(100, (item.count / (securityStats?.total_events || 1)) * 100)}%` 
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
} 