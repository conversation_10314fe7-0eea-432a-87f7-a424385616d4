import { useQuery } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Users, CheckSquare, CreditCard, DollarSign, TrendingUp, Server } from 'lucide-react'

interface DashboardStats {
  total_users: number
  new_users_today: number
  active_subscriptions: number
  total_revenue: number
  revenue_today: number
  package_distribution: Array<{ name: string; count: number }>
}

interface TaskStats {
  total_tasks: number
  active_tasks: number
  total_completions: number
  completed_tasks: number
  pending_tasks: number
  completion_rate: number
}

interface CardStats {
  total_catalog_cards: number
  total_user_cards: number
  users_with_cards: number
  avg_cards_per_user: number
  total_passive_income_rate: number
}

export function Dashboard() {
  const { data: dashboardStats, isLoading: isDashboardLoading } = useQuery<DashboardStats>({
    queryKey: ['dashboard-stats'],
    queryFn: () => api.get('/admin/dashboard').then(res => res.data)
  })

  const { data: taskStats, isLoading: isTaskLoading } = useQuery<TaskStats>({
    queryKey: ['task-stats'],
    queryFn: () => api.get('/admin/tasks/stats').then(res => res.data)
  })

  const { data: cardStats, isLoading: isCardLoading } = useQuery<CardStats>({
    queryKey: ['card-stats'],
    queryFn: () => api.get('/admin/cards/stats').then(res => res.data)
  })

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    isLoading 
  }: { 
    title: string
    value: string | number
    description: string
    icon: any
    isLoading: boolean
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        ) : (
          <>
            <div className="text-2xl font-bold">{value}</div>
            <p className="text-xs text-muted-foreground">{description}</p>
          </>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Overview of your admin panel</p>
      </div>

      {/* User Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">User Statistics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Users"
            value={dashboardStats?.total_users || 0}
            description="All registered users"
            icon={Users}
            isLoading={isDashboardLoading}
          />
          <StatCard
            title="New Users Today"
            value={dashboardStats?.new_users_today || 0}
            description="Users registered today"
            icon={TrendingUp}
            isLoading={isDashboardLoading}
          />
          <StatCard
            title="Active Subscriptions"
            value={dashboardStats?.active_subscriptions || 0}
            description="Currently active VPN subscriptions"
            icon={Server}
            isLoading={isDashboardLoading}
          />
          <StatCard
            title="Total Revenue"
            value={`$${dashboardStats?.total_revenue?.toFixed(2) || '0.00'}`}
            description="All-time revenue"
            icon={DollarSign}
            isLoading={isDashboardLoading}
          />
        </div>
      </div>

      {/* Task Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Task Statistics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Tasks"
            value={taskStats?.total_tasks || 0}
            description="All tasks in system"
            icon={CheckSquare}
            isLoading={isTaskLoading}
          />
          <StatCard
            title="Active Tasks"
            value={taskStats?.active_tasks || 0}
            description="Currently active tasks"
            icon={CheckSquare}
            isLoading={isTaskLoading}
          />
          <StatCard
            title="Completed Tasks"
            value={taskStats?.completed_tasks || 0}
            description="Successfully completed"
            icon={CheckSquare}
            isLoading={isTaskLoading}
          />
          <StatCard
            title="Completion Rate"
            value={`${taskStats?.completion_rate || 0}%`}
            description="Task completion percentage"
            icon={TrendingUp}
            isLoading={isTaskLoading}
          />
        </div>
      </div>

      {/* Card Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Card System Statistics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Catalog Cards"
            value={cardStats?.total_catalog_cards || 0}
            description="Cards available in catalog"
            icon={CreditCard}
            isLoading={isCardLoading}
          />
          <StatCard
            title="User Cards"
            value={cardStats?.total_user_cards || 0}
            description="Cards owned by users"
            icon={CreditCard}
            isLoading={isCardLoading}
          />
          <StatCard
            title="Users with Cards"
            value={cardStats?.users_with_cards || 0}
            description="Users who own cards"
            icon={Users}
            isLoading={isCardLoading}
          />
          <StatCard
            title="Passive Income Rate"
            value={`$${cardStats?.total_passive_income_rate?.toFixed(2) || '0.00'}/hr`}
            description="Total hourly income from cards"
            icon={DollarSign}
            isLoading={isCardLoading}
          />
        </div>
      </div>

      {/* Package Distribution */}
      {dashboardStats?.package_distribution && dashboardStats.package_distribution.length > 0 && (
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Package Distribution</h2>
          <Card>
            <CardHeader>
              <CardTitle>Active Subscriptions by Package</CardTitle>
              <CardDescription>Distribution of active VPN subscriptions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardStats.package_distribution.map((pkg, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm font-medium">{pkg.name}</span>
                    <span className="text-sm text-muted-foreground">{pkg.count} subscriptions</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
} 