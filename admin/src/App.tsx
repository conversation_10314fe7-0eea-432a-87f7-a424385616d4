import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from '@/components/ui/toaster'
import { AdminLayout } from '@/components/layout/AdminLayout'
import { Dashboard } from '@/pages/Dashboard'
import { Users } from '@/pages/Users'
import { Tasks } from '@/pages/Tasks'
import { Cards } from '@/pages/Cards'
import { Referrals } from '@/pages/Referrals'
import { VPN } from '@/pages/VPN'
import { Security } from '@/pages/Security'
import { System } from '@/pages/System'
import { Login } from '@/pages/Login'
import { AuthProvider } from '@/lib/auth'
import { ProtectedRoute } from '@/components/ProtectedRoute'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-background">
            <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />
              
              {/* Protected admin routes */}
              <Route path="/sb" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <Dashboard />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/sb/users" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <Users />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/sb/tasks" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <Tasks />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/sb/cards" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <Cards />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/sb/referrals" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <Referrals />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/sb/vpn" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <VPN />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/sb/security" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <Security />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              <Route path="/sb/system" element={
                <ProtectedRoute>
                  <AdminLayout>
                    <System />
                  </AdminLayout>
                </ProtectedRoute>
              } />
              
              {/* Redirect root to dashboard */}
              <Route path="/" element={<Navigate to="/sb" replace />} />
            </Routes>
            
            <Toaster />
          </div>
        </Router>
      </AuthProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  )
}

export default App
