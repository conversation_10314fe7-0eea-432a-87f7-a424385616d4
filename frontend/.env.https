# VIPVerse Frontend Environment Configuration - HTTPS DEVELOPMENT
# Use this configuration when testing with Telegram WebApp on https://dev.atlasvip.cloud

VITE_API_URL=https://dev.atlasvip.cloud
VITE_WS_BASE_URL=wss://dev.atlasvip.cloud/ws
VITE_ENVIRONMENT=development
VITE_APP_NAME=VIPVerse
VITE_APP_VERSION=1.0.0
VITE_DEBUG=true
VITE_TELEGRAM_BOT_NAME=@VIPVerseBot

# To use this configuration:
# 1. Copy this file to .env: cp .env.https .env
# 2. Restart the development server: pnpm dev
# 3. Access via: https://dev.atlasvip.cloud
