export enum Role {
  user = 'user',
  reseller = 'reseller',
  admin = 'admin'
}

export enum TransactionType {
  subscription_purchase = "subscription_purchase",
  subscription_renewal = "subscription_renewal",
  wallet_topup = "wallet_topup",
  referral_commission = "referral_commission",
  admin_adjustment = "admin_adjustment"
}

export interface Transaction {
  id: number;
  type: TransactionType;
  amount: number;
  created_at: string;
  user_id: number;
  reseller_id?: number;
  subscription_id?: number;
  package_name?: string;
  package_price?: number;
  package_data_limit?: number;
  package_expire_days?: number;
  description?: string;
  balance_after: number;
  status: string;
}

// Add these to your existing types
export interface User {
  id: number;
  username: string;
  role: Role;
  wallet_balance: number;
  telegram_id?: number;
  first_name?: string;
  last_name?: string;
  email?: string;
  is_active: boolean;
  created_at: string;
  telegram_photo_url?: string;
  referral_code: string;
  referred_by?: number;
  referred_by_username?: string;
  discount_percent: number;
  vpn_subscriptions: VPNSubscription[];
}

export interface MarzbanPanel {
  id: number;
  name: string;
  api_url: string;
  admin_username: string;
  is_active: boolean;
  stats?: any;
}

export interface VPNPackage {
  id: number;
  name: string;
  description: string;
  price: number;
  data_limit: number;
  expire_days: number;
  allowed_panels: MarzbanPanel[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface VPNSubscription {
  id: number;
  user_id: number;
  package_id: number;
  marzban_username: string;
  subscription_url: string;
  data_limit: number;
  data_used: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
  package: VPNPackage;
  user: User;
  is_active: boolean;
}

// Add this to your existing types
export interface LoginCredentials {
  username: string;
  password: string;
}

// Add this interface
export interface CreatePackageData {
  name: string;
  description: string;
  price: number;
  data_limit: number;
  expire_days: number;
  allowed_panels: MarzbanPanel[];
  is_active: boolean;
}

export interface PanelFormData {
  name: string;
  api_url: string;
  admin_username: string;
  admin_password?: string;
  is_active: boolean;
}

export interface PanelModalProps {
  panel?: MarzbanPanel;
  onClose: () => void;
  onSave: (data: PanelFormData) => Promise<void>;
} 

// Add or update these interfaces
interface NewServiceFormData {
  package_id: number;
  panel_id: number;
} 

// Add these types
export interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  text: string;
  color: string;
}

export interface UsageStatus {
  isActive: boolean;
  isExpired: boolean;
  isLimited: boolean;
  statusText: string;
  colorClass: string;
  usagePercent: number;
  timeRemaining: TimeRemaining;
  dataUsed: number;
  dataLimit: number;
  lifetimeUsed: number;
  lastOnline: string | null;
}

export interface SubscriptionUsage {
  data: {
    data_limit: number;
    data_used: number;
    expire: number;
    status: string;
    online_at: string | null;
  };
}

declare global {
  interface Window {
    Telegram: {
      WebApp: {
        ready: () => void;
        BackButton: {
          show: () => void;
          hide: () => void;
          onClick: (callback: () => void) => void;
        };
        MainButton: {
          show: () => void;
          hide: () => void;
          setText: (text: string) => void;
          onClick: (callback: () => void) => void;
          offClick: () => void;
          showProgress: (leaveActive: boolean) => void;
          hideProgress: () => void;
        };
        showAlert: (message: string) => void;
        showPopup: (params: { 
          title: string; 
          message: string; 
          buttons: Array<{ id: string; type: string; text: string; }> 
        }) => void;
        openTelegramLink: (url: string) => void;
        HapticFeedback: {
          notificationOccurred: (type: 'error' | 'success' | 'warning') => void;
          impactOccurred: (style: 'light' | 'medium' | 'heavy') => void;
          selectionChanged: () => void;
        };
      };
    };
  }
} 