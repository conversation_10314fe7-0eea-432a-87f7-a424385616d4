/* Remove external font imports and use optimized local fonts */
/* @import url('https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Cormorant+Garamond:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap'); */

/* Import optimized font utilities */
@import './styles/fonts.css';

/* Tailwind CSS v4 import */
@import "tailwindcss";

/* Tailwind v4 Theme Configuration */
@theme {
  /* Base Colors */
  --color-base-950: #0F0A1F;
  --color-base-900: #1A1333;
  --color-base-800: #251A40;
  --color-base-700: #312247;
  
  /* Accent Colors */
  --color-accent-500: #6D4AFF;
  --color-accent-400: #8467FF;
  
  /* Highlight Colors */
  --color-highlight-300: #FF84E8;
  --color-highlight-200: #84CAFF;
  
  /* Fonts */
  --font-display: "Tektur", "system-ui", "sans-serif";
  --font-sans: "Oxanium", "system-ui", "sans-serif";
  
  /* Letter Spacing */
  --letter-spacing-widest: 0.25em;
  
  /* Animations */
  --animate-check-mark: check-mark 0.5s ease-out forwards;
  --animate-fade-in: fade-in 0.5s ease-out forwards;
  --animate-slide-up: slide-up 0.5s ease-out forwards;
  --animate-fog-rotate: fog-rotate 20s linear infinite;
  --animate-shine-left: shine-left 3s ease-out infinite;
  --animate-shine-right: shine-right 3s ease-out infinite;
  --animate-confetti: confetti 5s ease-in-out forwards;
  --animate-shimmer: shimmer 2s infinite linear;
}

@layer base {
  body {
    @apply bg-linear-to-br from-base-950 via-base-900 to-base-900 text-white min-h-screen;
  }

  h1 {
    @apply text-2xl font-display font-bold tracking-wide text-white;
  }
  h2 {
    @apply text-xl font-display font-bold tracking-wide text-white;
  }
  h3 {
    @apply text-lg font-display font-bold tracking-wide text-white;
  }
  h4,
  h5,
  h6 {
    @apply text-base font-display font-bold tracking-wide text-white;
  }
}

@layer components {
  /* Card Styles */
  .card {
    @apply bg-linear-to-br from-white/5 to-white/[0.02] backdrop-blur-lg 
           rounded-lg p-4
           border border-white/10 
           hover:border-white/20
           shadow-[0_8px_30px_rgb(0,0,0,0.12)]
           transition-all duration-300;
  }

  /* Button Styles */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 font-display
           disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none;
  }

  .btn-primary {
    @apply bg-linear-to-r from-accent-500 to-highlight-300 text-white
           hover:opacity-90
           shadow-lg shadow-accent-500/20
           border border-transparent;
  }

  .btn-secondary {
    @apply bg-white/5 hover:bg-white/10 text-white
           border border-white/10 hover:border-white/20;
  }

  /* Input Styles */
  .input {
    @apply block w-full px-4 py-2 rounded-lg
           bg-base-800 border border-accent-500/20
           focus:border-accent-500 focus:ring-accent-500
           text-white placeholder-gray-400
           transition-all duration-200;
  }

  /* Menu Styles */
  .menu-item {
    @apply block w-full px-4 py-2 text-left
           text-white hover:bg-accent-500/10
           transition-colors duration-200;
  }

  .menu-item-active {
    @apply bg-accent-500/20 text-white;
  }

  /* Glass Effects */
  .glass {
    @apply bg-linear-to-br from-white/10 to-white/5 backdrop-blur-lg 
           border border-white/10
           hover:border-white/20
           transition-all duration-300;
  }

  /* Status Badges */
  .badge {
    @apply px-2.5 py-0.5 rounded-full text-sm font-medium;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-error {
    @apply bg-red-100 text-red-800;
  }

  /* Table Styles */
  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-gray-700;
  }
}

/* Custom utilities using v4 @utility directive */
@utility animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

@utility transition-smooth {
  transition: all 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes check-mark {
  0% { 
    stroke-dasharray: 0,100;
    opacity: 0;
  }
  100% { 
    stroke-dasharray: 100,100;
    opacity: 1;
  }
}

@keyframes fade-in {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slide-up {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes fog-rotate {
  0% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.5); }
  100% { transform: rotate(360deg) scale(1); }
}

@keyframes shine-left {
  0% { 
    transform: translateX(100%);
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  100% {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes shine-right {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes confetti {
  0% { 
    transform: translateY(0) rotate(0deg);
    opacity: 1;
  }
  100% { 
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(400%); }
}

/* Custom gradient backgrounds */
.bg-aurora {
  background: linear-gradient(
    135deg,
    rgba(109, 74, 255, 0.1) 0%,
    rgba(132, 202, 255, 0.1) 50%,
    rgba(255, 132, 232, 0.1) 100%
  );
}

.bg-glass {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%);
}
