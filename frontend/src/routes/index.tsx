import React, { Suspense, lazy } from 'react';
import { Navigate, RouteObject } from 'react-router-dom';
import NewUserSetupPage from '../pages/NewUserSetupPage';
import { ProtectedRoute, NewUserRoute } from './ProtectedRoutes';
import DashboardLayout from '../components/DashboardLayout';
import VerseLayout from '../components/verse/VerseLayout';
import LandingShell from '@/components/LandingShell';

// Lazy load LandingPage so the heavy dependencies (framer-motion, toast, etc.) are
// NOT shipped with the very first JS chunk loaded by Telegram. They will be
// downloaded only after the user reaches the route.
const LandingPage = lazy(() => import('../pages/LandingPage'));

// --- Lazy Load Tab Components ---
const TabLoadingFallback = () => (
  <div className="flex justify-center items-center h-[60vh] w-full">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
  </div>
);

const HomeTab = lazy(() => import('../components/HomeTabPage'));
const EarnTabWrapper = lazy(() => import('../components/EarnTabWrapper'));
const PremiumTabWrapper = lazy(() => import('../components/PremiumTabWrapper'));
const WalletTabPageWrapper = lazy(() => import('../components/WalletTabPageWrapper'));
const FriendsTabWrapper = lazy(() => import('../components/FriendsTabWrapper'));

// Lazy load VersePage to prevent early React Three Fiber loading
const VersePage = lazy(() => import('../pages/VersePage'));

// --- Helper for Suspense ---
const Suspended: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Suspense fallback={<TabLoadingFallback />}>{children}</Suspense>
);

export const routes: RouteObject[] = [
  {
    path: '/',
    element: (
      <Suspense fallback={<LandingShell />}>
        <div className="min-h-screen bg-gray-900">
          <LandingPage />
        </div>
      </Suspense>
    ),
  },
  {
    path: '/new-user-setup',
    element: (
      <div className="min-h-screen bg-gray-900">
        <NewUserRoute>
          <NewUserSetupPage />
        </NewUserRoute>
      </div>
    ),
  },
  {
    path: '/home',
    element: <Navigate to="/dashboard/home" replace />,
  },
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <Suspended>
          <DashboardLayout />
        </Suspended>
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="home" replace />,
      },
      {
        path: 'home',
        element: (
          <Suspended>
            <HomeTab />
          </Suspended>
        ),
      },
      {
        path: 'earn',
        element: (
          <Suspended>
            <EarnTabWrapper />
          </Suspended>
        ),
      },
      {
        path: 'premium',
        element: (
          <Suspended>
            <PremiumTabWrapper />
          </Suspended>
        ),
      },
      {
        path: 'wallet',
        element: (
          <Suspended>
            <WalletTabPageWrapper />
          </Suspended>
        ),
      },
      {
        path: 'friends',
        element: (
          <Suspended>
            <FriendsTabWrapper />
          </Suspended>
        ),
      },
    ],
  },
  {
    path: '/verse',
    element: (
      <ProtectedRoute>
        <VerseLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: (
          <Suspended>
            <VersePage />
          </Suspended>
        ),
      },
    ],
  },
  {
    path: '/chat',
    element: <Navigate to="/verse" replace />,
  },
  {
    path: '/chat/:id',
    element: <Navigate to="/verse" replace />,
  },
  {
    path: '*',
    element: <Navigate to="/" replace />,
  },
];

export default routes;
