import { useQuery, useMutation, useQueryClient, QueryFunctionContext } from '@tanstack/react-query';
import { fetchData, mutateData, ApiClientQueryKey } from '../utils/apiClient';
import type { VPNSubscription, VPNPackage, MarzbanPanel } from '../types';
import type { ApiResponse } from '../types';

// --- Subscriptions Query ---
export type VpnSubscriptionsQueryKey = ApiClientQueryKey;
export function useVpnSubscriptionsQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  const queryKey: VpnSubscriptionsQueryKey = [
    'vpnSubscriptions',
    { endpoint: '/api/user/subscriptions' },
  ];

  const queryFn = async (
    context: QueryFunctionContext<VpnSubscriptionsQueryKey>
  ): Promise<VPNSubscription[]> => {
    try {
      return await fetchData<VPNSubscription[]>(context);
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return empty array if no subscriptions found
        return [];
      }
      throw error;
    }
  };

  return useQuery<VPNSubscription[], Error, VPNSubscription[], VpnSubscriptionsQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? 5 * 60 * 1000,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
    // Use global retry configuration
  });
}

// --- Packages Query ---
export type VpnPackagesQueryKey = ApiClientQueryKey;

/**
 * Hook to fetch available VPN packages.
 */
export function useVpnPackagesQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  const queryKey: VpnPackagesQueryKey = ['vpnPackages', { endpoint: '/api/packages' }];

  const queryFn = async (
    context: QueryFunctionContext<VpnPackagesQueryKey>
  ): Promise<VPNPackage[]> => {
    try {
      return await fetchData<VPNPackage[]>(context);
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return empty array if no packages found
        return [];
      }
      throw error;
    }
  };

  return useQuery<VPNPackage[], Error, VPNPackage[], VpnPackagesQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? 10 * 60 * 1000,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
    // Use global retry configuration
  });
}

// --- Panels Query ---
export type VpnPanelsQueryKey = ApiClientQueryKey;

/**
 * Hook to fetch available Marzban panels.
 */
export function useVpnPanelsQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  const queryKey: VpnPanelsQueryKey = ['vpnPanels', { endpoint: '/api/panels' }];

  const queryFn = async (
    context: QueryFunctionContext<VpnPanelsQueryKey>
  ): Promise<MarzbanPanel[]> => {
    try {
      return await fetchData<MarzbanPanel[]>(context);
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return empty array if no panels found
        return [];
      }
      throw error;
    }
  };

  return useQuery<MarzbanPanel[], Error, MarzbanPanel[], VpnPanelsQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? 10 * 60 * 1000,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
    // Use global retry configuration
  });
}

// --- Purchase Mutation ---
/**
 * Purchase a VPN package.
 */
export function usePurchaseVpnPackageMutation() {
  const queryClient = useQueryClient();
  return useMutation<boolean, Error, { packageId: number; panelId: number }, unknown>({
    mutationFn: (params: { packageId: number; panelId: number }) =>
      mutateData<boolean>({
        endpoint: '/api/user/purchase',
        variables: { package_id: params.packageId, panel_id: params.panelId },
      }),
    onSuccess: () => {
      // Only invalidate VPN and dashboard related queries, not auth state
      queryClient.invalidateQueries({ queryKey: ['vpnSubscriptions'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
  });
}
