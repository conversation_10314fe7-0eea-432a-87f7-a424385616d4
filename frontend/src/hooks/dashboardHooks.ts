import { useQuery, QueryFunctionContext, QueryKey } from '@tanstack/react-query';
import { fetchData, ApiClientQueryKey } from '../utils/apiClient';
// import type { ApiResponse } from '../types'; // Not needed if fetchData unwraps
import type { DashboardStats } from '../types'; // Updated import
import type { Transaction } from '../types'; // Assuming Transaction type is in ../types
// No longer need ApiResponse from '../types' for the direct data type

const TWO_MINUTES_IN_MS = 2 * 60 * 1000;

// Define the specific query key type for this hook
export type DashboardStatsQueryKey = ApiClientQueryKey; // Matches [string, { endpoint: string; params?: Record<string, any> }]

/**
 * Hook to fetch dashboard statistics.
 */
export function useDashboardStatsQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
  // onSuccess, onError, etc. can be added here if needed by consumers
}) {
  const queryKey: DashboardStatsQueryKey = [
    'dashboardStats',
    { endpoint: '/api/user/dashboard/stats' },
  ];

  const queryFn = async (
    context: QueryFunctionContext<DashboardStatsQueryKey>
  ): Promise<DashboardStats> => {
    try {
      // fetchData now directly returns DashboardStats
      return await fetchData<DashboardStats>(context);
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return default dashboard stats if not found
        return {
          active_subscriptions: 0,
          total_data_used: 0,
          total_data_limit: 0,
          wallet_balance: 0,
          total_accumulated_card_profit: 0,
          total_passive_hourly_income: 0,
        } as DashboardStats;
      }
      throw error;
    }
  };

  return useQuery<DashboardStats, Error, DashboardStats, DashboardStatsQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? TWO_MINUTES_IN_MS,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
    // Use global retry configuration
  });
}

// Define the specific query key type for this hook
export type UserTransactionsQueryKey = ApiClientQueryKey; // Matches [string, { endpoint: string; params?: Record<string, any> }]

/**
 * Hook to fetch user transactions.
 */
export function useUserTransactionsQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  const queryKey: UserTransactionsQueryKey = [
    'userTransactions',
    { endpoint: '/api/user/transactions' },
  ];

  const queryFn = async (
    context: QueryFunctionContext<UserTransactionsQueryKey>
  ): Promise<Transaction[]> => {
    try {
      // fetchData now directly returns Transaction[]
      return await fetchData<Transaction[]>(context);
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return empty array if no transactions found
        return [];
      }
      throw error;
    }
  };

  return useQuery<Transaction[], Error, Transaction[], UserTransactionsQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? TWO_MINUTES_IN_MS,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
    // Use global retry configuration
  });
}
