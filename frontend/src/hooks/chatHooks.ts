import { useQuery, useMutation, useQueryClient, QueryFunctionContext } from '@tanstack/react-query';
import { api } from '../utils/apiClient';
import type { ChatConversation, ChatMessage } from '../types/chat';

// Fetch all chat conversations
const fetchConversations = async (): Promise<ChatConversation[]> => {
  const response = await api.get<ChatConversation[]>('/chat/conversations');
  return response.data;
};

export const useChatConversationsQuery = () =>
  useQuery<ChatConversation[]>({
    queryKey: ['chatConversations'],
    queryFn: fetchConversations,
    select: data => data,
    staleTime: 5 * 60 * 1000,
  });

// Fetch messages for a given chatId ('public' or private id)
const fetchMessages = async (context: QueryFunctionContext): Promise<ChatMessage[]> => {
  const [, chatId] = context.queryKey as ['chatMessages', number | 'public'];
  const endpoint =
    chatId === 'public' ? '/chat/public/messages' : `/chat/private/${chatId}/messages`;
  const response = await api.get<ChatMessage[]>(endpoint);
  return response.data;
};

export const useChatMessagesQuery = (chatId: number | 'public') =>
  useQuery<ChatMessage[]>({
    queryKey: ['chatMessages', chatId],
    queryFn: fetchMessages,
    select: data => data,
    enabled: typeof chatId !== 'undefined',
    staleTime: 1 * 60 * 1000,
  });

// Send a message with optimistic update
export const useSendMessageMutation = (chatId: number | 'public') => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (content: string) =>
      api.post('/chat/messages', { content, receiver_id: chatId === 'public' ? null : chatId }),
    onMutate: async (content: string) => {
      const key = ['chatMessages', chatId];
      await queryClient.cancelQueries({ queryKey: key });
      const previous = queryClient.getQueryData<ChatMessage[]>(key);
      const optimistic: ChatMessage = {
        id: Date.now(),
        content,
        created_at: new Date().toISOString(),
        is_public: chatId === 'public',
        sender_id: 'optimistic',
        receiver_id: chatId === 'public' ? null : String(chatId),
      } as ChatMessage;
      queryClient.setQueryData<ChatMessage[]>(key, old =>
        old ? [...old, optimistic] : [optimistic]
      );
      return { previous };
    },
    onError: (_err: unknown, _vars: string, context?: { previous?: ChatMessage[] }) => {
      if (context?.previous) {
        queryClient.setQueryData(['chatMessages', chatId], context.previous);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['chatMessages', chatId] });
    },
  });
};
