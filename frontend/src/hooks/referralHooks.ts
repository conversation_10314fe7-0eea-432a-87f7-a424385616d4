import { useQuery, QueryFunctionContext } from '@tanstack/react-query';
import { fetchData, ApiClientQueryKey } from '../utils/apiClient';
import type { ReferralInfo, ReferredUser } from '../types/referral';

const FIVE_MINUTES_IN_MS = 5 * 60 * 1000;
const THREE_MINUTES_IN_MS = 3 * 60 * 1000;

// Query Key Type for Referral Info
export type ReferralInfoQueryKey = ApiClientQueryKey;

/**
 * Hook to fetch referral information.
 */
export function useReferralInfoQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  const queryKey: ReferralInfoQueryKey = ['referralInfo', { endpoint: '/api/referrals/info' }];

  const queryFn = async (context: QueryFunctionContext<ReferralInfoQueryKey>): Promise<ReferralInfo> => {
    try {
      return await fetchData<ReferralInfo>(context);
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return default referral info if not found
        return {
          referral_code: '',
          total_referrals: 0,
          total_earnings: 0,
          referral_link: '',
        } as ReferralInfo;
      }
      throw error;
    }
  };

  return useQuery<ReferralInfo, Error, ReferralInfo, ReferralInfoQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? FIVE_MINUTES_IN_MS,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
    // Use global retry configuration
  });
}

// Query Key Type for Referred Users
export type ReferredUsersQueryKey = ApiClientQueryKey;

/**
 * Hook to fetch list of referred users.
 */
export function useReferredUsersQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  const queryKey: ReferredUsersQueryKey = ['referredUsers', { endpoint: '/api/referrals/users' }];

  const queryFn = async (
    context: QueryFunctionContext<ReferredUsersQueryKey>
  ): Promise<ReferredUser[]> => {
    try {
      return await fetchData<ReferredUser[]>(context);
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return empty array if no referred users found
        return [];
      }
      throw error;
    }
  };

  return useQuery<ReferredUser[], Error, ReferredUser[], ReferredUsersQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? THREE_MINUTES_IN_MS,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
    // Use global retry configuration
  });
}
