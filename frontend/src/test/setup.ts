import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import { afterEach, vi } from 'vitest'

// Cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup()
})

// Mock Telegram WebApp API
Object.defineProperty(window, 'Telegram', {
  value: {
    WebApp: {
      ready: vi.fn(),
      expand: vi.fn(),
      close: vi.fn(),
      MainButton: {
        text: '',
        color: '',
        textColor: '',
        isVisible: false,
        isActive: true,
        setText: vi.fn(),
        onClick: vi.fn(),
        offClick: vi.fn(),
        show: vi.fn(),
        hide: vi.fn(),
        enable: vi.fn(),
        disable: vi.fn(),
      },
      BackButton: {
        isVisible: false,
        onClick: vi.fn(),
        offClick: vi.fn(),
        show: vi.fn(),
        hide: vi.fn(),
      },
      initData: '',
      initDataUnsafe: {},
      version: '6.0',
      platform: 'unknown',
      colorScheme: 'light',
      themeParams: {},
      isExpanded: false,
      viewportHeight: 600,
      viewportStableHeight: 600,
      headerColor: '',
      backgroundColor: '',
      isClosingConfirmationEnabled: false,
      sendData: vi.fn(),
      switchInlineQuery: vi.fn(),
      openLink: vi.fn(),
      openTelegramLink: vi.fn(),
      openInvoice: vi.fn(),
      showPopup: vi.fn(),
      showAlert: vi.fn(),
      showConfirm: vi.fn(),
      showScanQrPopup: vi.fn(),
      closeScanQrPopup: vi.fn(),
      readTextFromClipboard: vi.fn(),
      requestWriteAccess: vi.fn(),
      requestContact: vi.fn(),
      invokeCustomMethod: vi.fn(),
    },
  },
  writable: true,
})

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
})) 