export interface TelegramVerifiedData {
  initData: string;
  telegramData: {
    id: string;
    username?: string;
    first_name?: string;
    last_name?: string;
    photo_url?: string;
  };
  timestamp: number;
}

export interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
  notifications: boolean;
}

// Type guard for verified data
export function isTelegramVerifiedData(data: unknown): data is TelegramVerifiedData {
  const d = data as TelegramVerifiedData;
  return !!(
    d &&
    typeof d.initData === 'string' &&
    typeof d.timestamp === 'number' &&
    d.telegramData &&
    typeof d.telegramData.id === 'string'
  );
}
