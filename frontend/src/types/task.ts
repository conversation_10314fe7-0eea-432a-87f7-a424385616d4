import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-icons/fa';

export enum TaskType {
  DAILY_CHECKIN = 'DAILY_CHECKIN',
  YOUTUBE_VIEW = 'YOUTUBE_VIEW',
  INSTAGRAM_FOLLOW = 'INSTAGRAM_FOLLOW',
  INSTAGRAM_VIEW = 'INSTAGRAM_VIEW',
  TELEGRAM_CHANNEL = 'TELEGRAM_CHANNEL',
  TWITTER_FOLLOW = 'TWITTER_FOLLOW',
  WEBSITE_VISIT = 'WEBSITE_VISIT',
  REFERRAL = 'REFERRAL',
}

export enum RewardType {
  DATA_BONUS = 'DATA_BONUS',
  DAYS_BONUS = 'DAYS_BONUS',
  WALLET_BONUS = 'WALLET_BONUS',
}

export enum TaskStatus {
  AVAILABLE = 'AVAILABLE',
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED',
}

export interface SocialTask {
  platform: string;
  platform_id: string;
  platform_url: string;
  verify_key: string;
  required_duration: number;
  auto_complete_after?: number;
  max_verification_attempts?: number;
  verification_interval?: number;
  is_active?: boolean;
}

export interface Task {
  id: number;
  name: string;
  description: string;
  type: TaskType;
  status: TaskStatus;
  reward_type: RewardType;
  reward_value: number;
  target_value: number;
  is_claimed: boolean;
  created_at: string;
  completion_id?: number;
  started_at?: string;
  expires_at?: string;
  last_verified_at?: string;
  current_progress?: number;
  verification_attempts?: number;
  is_active: boolean;
  // Daily task fields
  cycle_length?: number;
  daily_rewards?: number[];
  cycle_bonus_reward?: number;
  reset_streak_after_hours?: number;
  total_cycle_reward?: number;
  // Add social task field
  social_task?: SocialTask;
  // Add average completion time field
  avg_completion_time?: number | null;
  // Add missing fields from linter errors
  platform_url?: string;
  platform_id?: string;
  verify_key?: string;
  max_verification_attempts?: number;
  verification_cooldown?: number;
  claim_available_at?: string;
  claimed_at?: string;
  required_duration?: number;
}

export interface TaskCompletion {
  id: number;
  task_id: number;
  user_id: number;
  status: TaskStatus;
  started_at: string;
  current_progress: number;
  verification_attempts: number;
  is_claimed: boolean;
  expires_at?: string;
  last_verified_at?: string;
  // Add missing fields
  completed_at?: string;
  claimed_at?: string;
  claim_available_at?: string;
  message?: string;
}

export interface VerificationData {
  code?: string; // For view tasks
  username?: string; // For follow tasks
  [key: string]: any; // For other verification data
}

export interface TaskVerificationResult {
  success: boolean;
  status: TaskStatus;
  message?: string;
  error_code?: string;
  remaining_attempts?: number;
  verification_time_left?: number; // Seconds until next verification
  next_verification_time?: string;
  verification_data?: Record<string, any>;
  // Add missing fields
  verification_attempts?: number;
  claim_available_at?: string;
}

export interface CreateTaskData {
  name: string;
  description: string;
  type: TaskType;
  reward_type: RewardType;
  reward_value: number;
  target_value: number;
  is_active: boolean;
  // Adding fields directly here to fix linter errors
  platform_url?: string;
  platform_id?: string;
  verify_key?: string;
  required_duration?: number;
  max_verification_attempts?: number;
  verification_cooldown?: number;
  // Daily task specific fields
  daily_multiplier?: number;
  streak_requirement?: number;
  auto_verify_delay?: number;
  verification_frequency?: number;
  allow_reward_revocation?: boolean;
  minimum_duration?: number;
  cycle_length?: number;
  daily_rewards?: number[];
  cycle_bonus_reward?: number;
  reset_streak_after_hours?: number;
  // Also keep the social_task object for backward compatibility
  social_task?: {
    platform: string;
    platform_id: string;
    platform_url: string;
    verify_key: string;
    required_duration: number;
    auto_complete_after?: number;
    max_verification_attempts?: number;
    verification_interval?: number;
    is_active?: boolean;
  };
}

export interface UpdateTaskData extends Partial<CreateTaskData> {}

export interface TaskVerification {
  id: number;
  task_completion_id: number;
  status: TaskStatus;
  platform_data?: Record<string, any>;
  next_verification?: string;
  created_at: string;
}

export interface TaskReward {
  task_id: number;
  reward_type: RewardType;
  reward_value: number;
  claimed_at: string;
}

export interface TaskAnalytics {
  total_completions: number;
  completed_tasks: number;
  completion_rate: number;
  reward_distribution: Record<RewardType, number>;
}

export interface TaskGroup {
  type: TaskType;
  label: string;
  tasks: Task[];
}

export interface DailyTaskStreak {
  id: number;
  user_id: number;
  currentStreak: number;
  longestStreak: number;
  currentCycleDay: number;
  totalCheckIns: number;
  lastCheckIn: string | null;
  lastStreakBreak: string | null;
  firstCheckTime: string | null;
  daily_rewards?: number[];
  task_id: number;
}

export interface DailyCheckInResponse {
  streak: DailyTaskStreak;
  completion: TaskCompletion;
  reward: number | null;
  multiplier: number;
  next_check_in: string;
  cycle_completion: boolean;
  cycle_reward: number | null;
}
