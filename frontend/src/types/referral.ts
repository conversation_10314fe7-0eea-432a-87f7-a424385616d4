export interface ReferralInfo {
  referral_code: string;
  total_referrals: number;
  active_referrals: number;
  total_earnings: number;
  referral_link: string;
}

export interface ReferredUser {
  username: string;
  joinedAt: string;
  isActive: boolean;
  totalSpent: number;
  commissionEarned: number;
  telegramPhotoUrl?: string;
}

// Extended interface for referral task status
export interface ReferralTaskStatus {
  status: string;
  currentProgress: number;
  target: number;
  totalEarnings: number;
  lastVerifiedAt: string | null;
  isCompleted: boolean;
  isClaimed: boolean;
  claimedAt: string | null;
}
