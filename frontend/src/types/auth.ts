// Centralized authentication types for single source of truth

export interface AuthUser {
  id: number;
  username: string;
  role: string;
  telegram_id?: string;
  wallet_balance: number;
  is_active: boolean;
  telegram_photo_url?: string;
  referral_code?: string;
  referred_by?: number;
  discount_percent: number;
  last_login?: string;
}

export interface AuthState {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface TelegramAuthData {
  init_data: string;
  auth_method: 'telegram';
}

export interface AuthResponse {
  access_token?: string;
  token_type?: string;
  role?: string;
  username?: string;
  id?: number;
  telegram_id?: string;
}

export interface TelegramLoginResponse {
  data: {
    isNewUser: boolean;
    user?: AuthUser;
    telegram_data?: {
      id: string;
      username?: string;
      first_name?: string;
      last_name?: string;
    };
  };
  message: string;
} 