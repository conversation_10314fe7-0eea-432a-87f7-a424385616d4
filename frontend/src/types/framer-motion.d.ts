declare module 'framer-motion' {
  export interface MotionProps {
    initial?: any;
    animate?: any;
    exit?: any;
    variants?: any;
    whileHover?: any;
    whileTap?: any;
    whileInView?: any;
    whileFocus?: any;
    whileDrag?: any;
    transition?: any;
    layout?: boolean;
    layoutId?: string;
    drag?: boolean | 'x' | 'y';
    dragConstraints?: any;
    dragElastic?: any;
    dragMomentum?: boolean;
    dragTransition?: any;
    onDragStart?: (event: any, info: any) => void;
    onDragEnd?: (event: any, info: any) => void;
    onDrag?: (event: any, info: any) => void;
    children?: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
    onClick?: (e?: React.MouseEvent<HTMLElement>) => void;
    onAnimationStart?: () => void;
    onAnimationComplete?: () => void;
    key?: string | number;
    disabled?: boolean;
    type?: string;
    ref?: React.Ref<any>;
    id?: string;
    role?: string;
    'aria-label'?: string;
    'aria-selected'?: boolean;
    tabIndex?: number;
  }

  export interface AnimatePresenceProps {
    children: React.ReactNode;
    mode?: 'sync' | 'wait' | 'popLayout';
    initial?: boolean;
    custom?: any;
    exitBeforeEnter?: boolean;
    onExitComplete?: () => void;
  }

  export const motion: {
    div: React.FC<MotionProps>;
    button: React.FC<MotionProps>;
    nav: React.FC<MotionProps>;
    p: React.FC<MotionProps>;
    span: React.FC<MotionProps>;
    ul: React.FC<MotionProps>;
    li: React.FC<MotionProps>;
    header: React.FC<MotionProps>;
    footer: React.FC<MotionProps>;
    main: React.FC<MotionProps>;
    section: React.FC<MotionProps>;
    article: React.FC<MotionProps>;
    aside: React.FC<MotionProps>;
    form: React.FC<MotionProps>;
    input: React.FC<MotionProps>;
    label: React.FC<MotionProps>;
    select: React.FC<MotionProps>;
    option: React.FC<MotionProps>;
    textarea: React.FC<MotionProps>;
    img: React.FC<MotionProps>;
    svg: React.FC<MotionProps>;
    path: React.FC<MotionProps>;
    circle: React.FC<MotionProps>;
    rect: React.FC<MotionProps>;
    h1: React.FC<MotionProps>;
    h2: React.FC<MotionProps>;
    h3: React.FC<MotionProps>;
    h4: React.FC<MotionProps>;
    h5: React.FC<MotionProps>;
    h6: React.FC<MotionProps>;
    a: React.FC<MotionProps>;
  };

  export const AnimatePresence: React.FC<AnimatePresenceProps>;
}
