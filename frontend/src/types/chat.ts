export interface ChatMessage {
  id: number;
  sender_id: string;
  receiver_id: string | null;
  content: string;
  created_at: string;
  is_public: boolean;
  is_read?: boolean;
  sender_nickname?: string;
  receiver_nickname?: string;
  conversation_id?: string;
  sender_avatar?: string | null;
}

export interface ChatConversation {
  id: number;
  username: string;
  avatar_url: string | null;
  last_message_at: string | null;
  unread_count?: number;
  is_online?: boolean;
}

export interface UserSearchResult {
  id: number;
  username: string;
  avatar_url: string | null;
}

export interface UserDisplayInfo {
  avatar_url: string | null;
  username: string;
}

export interface MessageInput {
  content: string;
  receiver_id?: number | null;
}

export type MessageEvent =
  | {
      id: number;
      sender_id: string;
      receiver_id: string | null;
      content: string;
      created_at: string;
      is_public: boolean;
      sender_nickname?: string;
      receiver_nickname?: string;
      conversation_id?: string;
    }
  | {
      error: string;
    }
  | {
      notification: string;
    };

export interface OnlineUser {
  id: string;
  username: string;
}

export enum ChatViewMode {
  FULL = 'full',
  HIDDEN = 'hidden',
}

export interface ChatContextType {
  messages: ChatMessage[];
  conversations: ChatConversation[];
  selectedChat: number | null;
  isLoading: boolean;
  viewMode: ChatViewMode;

  // Actions
  sendMessage: (message: MessageInput) => Promise<void>;
  selectChat: (userId: number | null) => Promise<void>;
  searchUsers: (query: string) => Promise<UserSearchResult[]>;
  setViewMode: (mode: ChatViewMode) => void;
  loadMoreMessages: () => Promise<void>;
}

export interface EmojiClickData {
  id: string;
  name: string;
  native: string;
  unified: string;
  keywords: string[];
  shortcodes: string;
}
