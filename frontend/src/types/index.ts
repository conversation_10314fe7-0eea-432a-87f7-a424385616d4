// Telegram WebApp types
export interface WebAppUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  is_bot?: boolean;
}

export interface WebAppInitData {
  query_id: string;
  user: WebAppUser;
  auth_date: string;
  hash: string;
}

export interface TelegramWebApp {
  initData: string;
  initDataUnsafe: WebAppInitData;
  platform: string;
  colorScheme: string;
  themeParams: {
    bg_color: string;
    text_color: string;
    hint_color: string;
    link_color: string;
    button_color: string;
    button_text_color: string;
    secondary_bg_color: string;
  };
  isExpanded: boolean;
  viewportHeight: number;
  viewportStableHeight: number;
  headerColor: string;
  backgroundColor: string;
  BackButton: {
    show: () => void;
    hide: () => void;
    onClick: (callback: () => void) => void;
    offClick: (callback: () => void) => void;
    isVisible: boolean;
  };
  MainButton: {
    text: string;
    color: string;
    textColor: string;
    isVisible: boolean;
    isActive: boolean;
    isProgressVisible: boolean;
    setText: (text: string) => void;
    onClick: (callback: () => void) => void;
    offClick: (callback: () => void) => void;
    show: () => void;
    hide: () => void;
    enable: () => void;
    disable: () => void;
    showProgress: (leaveActive?: boolean) => void;
    hideProgress: () => void;
    setParams: (params: {
      text?: string;
      color?: string;
      text_color?: string;
      is_active?: boolean;
      is_visible?: boolean;
    }) => void;
  };
  ready: () => void;
  expand: () => void;
  close: () => void;
  enableClosingConfirmation: () => void;
  disableClosingConfirmation: () => void;
  isClosingConfirmationEnabled: boolean;
  onEvent: (eventType: string, eventHandler: () => void) => void;
  offEvent: (eventType: string, eventHandler: () => void) => void;
  sendData: (data: string) => void;
  showPopup: (params: {
    title?: string;
    message: string;
    buttons?: {
      id: string;
      type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
      text: string;
    }[];
  }) => Promise<{ button_id: string }>;
  showAlert: (message: string) => void;
  showConfirm: (message: string) => Promise<boolean>;
  openLink: (url: string) => void;
}

declare global {
  interface Window {
    Telegram?: {
      WebApp: TelegramWebApp;
    };
    telegramReady?: boolean;
    hideLoadingScreen?: () => void;
    telegramError?: any;
  }
}

// Add these to your existing types
export enum Role {
  admin = 'admin',
  reseller = 'reseller',
  user = 'user',
}
export interface User {
  id: number;
  username: string;
  email?: string;
  role: 'admin' | 'reseller' | 'user';
  telegram_id?: string;
  isNewUser?: number; // 1 for new user, 0 for existing user
  wallet_balance: number;
  first_name?: string;
  last_name?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  telegram_photo_url?: string;
  referral_code: string;
  referred_by?: number;
  referred_by_username?: string;
  discount_percent: number;
  vpn_subscriptions: VPNSubscription[];
  device_platform?: string;
  device_id?: string;
  last_login?: string;
}

export interface MarzbanSystemStats {
  total_users: number;
  active_users: number;
  total_bandwidth: number;
  uptime: number;
  version: string;
  mem_total: number;
  mem_used: number;
  cpu_usage: number;
}

export interface MarzbanPanel {
  id: number;
  name: string;
  api_url: string;
  admin_username: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  stats?: MarzbanSystemStats;
}

export interface VPNPackage {
  id: number;
  name: string;
  description: string;
  price: number;
  data_limit: number;
  expire_days: number;
  allowed_panels: MarzbanPanel[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface VPNSubscription {
  id: number;
  user_id: number;
  package_id: number;
  marzban_username: string;
  subscription_url: string;
  data_limit: number;
  data_used: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
  package: VPNPackage;
  user: User;
  is_active: boolean;
}

// Add this to your existing types
export interface LoginCredentials {
  username: string;
  password: string;
}

// Add this interface
export interface CreatePackageData {
  name: string;
  description: string;
  price: number;
  data_limit: number;
  expire_days: number;
  allowed_panels: MarzbanPanel[];
  is_active: boolean;
}

export interface PanelModalProps {
  panel?: MarzbanPanel | null;
  onClose: () => void;
  onSave: (data: PanelFormData) => Promise<void>;
}

export interface PanelFormData {
  name: string;
  api_url: string;
  admin_username: string;
  admin_password: string;
  is_active: boolean;
}

export enum TransactionType {
  subscription_purchase = 'subscription_purchase',
  subscription_renewal = 'subscription_renewal',
  wallet_topup = 'wallet_topup',
  referral_commission = 'referral_commission',
  admin_adjustment = 'admin_adjustment',
  card_profit_claim = 'card_profit_claim',
  card_level_up = 'card_level_up',
  card_purchase = 'card_purchase',
}

export interface Transaction {
  id: number;
  user_id: number;
  type: TransactionType;
  amount: number;
  created_at: string;
  status: 'pending' | 'completed' | 'failed';
  description: string;
  balance_after: number;
  reseller_id?: number;
  subscription_id?: number;
  package_name?: string;
  package_price?: number;
  package_data_limit?: number;
  package_expire_days?: number;
}

// Add UsageStatus interface
export interface UsageStatus {
  isActive: boolean;
  dataUsagePercent: number;
  timeRemainingPercent: number;
  lastOnline?: string;
}

// Add these to your existing types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  error?: string;
  detail?: string;
  csrf_token?: string;  // CSRF token for authentication responses
}

export interface VerifySetupResponse {
  valid: boolean;
  telegram_data?: {
    id: number;
    username?: string;
    first_name?: string;
    last_name?: string;
    photo_url?: string;
  };
}

export interface AuthApi {
  login: (
    credentials:
      | {
          username: string;
          password: string;
        }
      | {
          init_data: string;
          auth_method: 'telegram';
        }
  ) => Promise<any>;
  logout: () => Promise<any>;
  verifySession: () => Promise<any>;
  refresh: () => Promise<any>;
  register: (initData: string, username: string) => Promise<any>;
  verifySetupToken: (token: string) => Promise<ApiResponse<VerifySetupResponse>>;
}

export interface FormInputProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  placeholder?: string;
  required?: boolean;
  autoComplete?: string;
  disabled?: boolean;
  type?: 'text' | 'password' | 'email' | 'number';
  min?: number;
  max?: number;
  pattern?: string;
  name?: string;
  id?: string;
  label?: string;
  error?: string;
}

// Update TelegramData interface to match
export interface TelegramData {
  telegram_id: string;
  username: string | undefined;
  first_name: string | undefined;
  last_name: string | undefined;
  photo_url: string | undefined;
}

export interface VerifiedData {
  initData: string;
  telegramData: TelegramData;
  timestamp: number;
}

// Tab Types
export type TabId =
  | 'home'
  | 'services'
  | 'packages'
  | 'wallet'
  | 'friends'
  | 'earn'
  | 'cards'
  | 'transactions'
  | 'premium'
  | 'verse';

export interface BottomNavigationProps {
  activeTab: TabId;
  onTabChange: (tabId: TabId) => void;
  className?: string;
}

// ---> Add and Export DashboardStats Interface <---
export interface DashboardStats {
  active_subscriptions: number;
  total_data_used: number;
  total_data_limit: number;
  wallet_balance: number;
  total_accumulated_card_profit?: number;
  total_passive_hourly_income?: number;
}
