import React, { useEffect } from 'react';
import { useRoutes, useNavigate, useLocation } from 'react-router-dom';
import routes from './routes';
import { useCurrentUserQuery } from './hooks/authHooks';
import LoadingSpinner from './components/LoadingSpinner';
import DevPerformanceMonitor from './components/DevPerformanceMonitor';

function App() {
  const routing = useRoutes(routes);
  const navigate = useNavigate();
  const location = useLocation();
  const { data: currentUser, isLoading, isError, error } = useCurrentUserQuery();

  // Add debug logging
  console.log('[App] Render:', {
    currentPath: location.pathname,
    isLoading,
    isError,
    hasUser: !!currentUser,
    error: error?.message
  });

  // Hide loading screen once authentication is resolved
  useEffect(() => {
    if (!isLoading) {
      // Authentication has been resolved (either success or failure)
      console.log('[App] Authentication resolved, hiding loading screen...');
      
      // Mark auth as complete for performance monitoring
      document.body.setAttribute('data-auth-complete', 'true');
      
      setTimeout(() => {
        if (window.hideLoadingScreen) {
          window.hideLoadingScreen();
          console.log('[App] Loading screen hidden after auth resolution');
        } else {
          // Fallback: hide loading screen manually
          const loadingElement = document.getElementById('loading');
          if (loadingElement) {
            loadingElement.style.display = 'none';
            console.log('[App] Loading screen hidden manually after auth resolution');
          }
        }
      }, 500); // Small delay to ensure smooth transition
    }
  }, [isLoading]);

  // Enhanced navigation logic with better error handling
  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) {
      console.log('[App] Authentication loading...');
      return;
    }

    const currentPath = location.pathname;
    console.log('[App] Navigation check:', { 
      currentPath, 
      isAuthenticated: !!currentUser, 
      isError,
      error: error?.message 
    });

    if (currentUser) {
      // User is authenticated
      if (currentPath === '/') {
        console.log('[App] Authenticated user on landing, redirecting to dashboard');
        navigate('/dashboard', { replace: true });
      }
    } else {
      // User is not authenticated
      const publicPaths = ['/', '/new-user-setup'];
      const isOnPublicPath = publicPaths.some(path => currentPath.startsWith(path));
      
      if (!isOnPublicPath) {
        console.log('[App] Unauthenticated user on protected route, redirecting to landing');
        navigate('/', { replace: true });
      }
    }
  }, [currentUser, isLoading, location.pathname, navigate, isError]);

  // Show loading spinner during initial authentication
  if (isLoading) {
    console.log('[App] Showing loading spinner');
    return (
      <div className="min-h-screen bg-[#030303] flex items-center justify-center">
        <div className="flex flex-col items-center justify-center text-center p-5">
          <div className="text-[3.5rem] mb-6 animate-[floatIcon_3s_ease-in-out_infinite]">🚀</div>
          <h1 className="text-[2.25rem] font-bold text-gray-100 mb-3 tracking-tight">VIPVerse</h1>
          <p className="text-base text-zinc-400 mb-8">Welcome to the future</p>
          <div className="w-14 h-14 border-4 border-purple-700/40 border-t-purple-500 rounded-full animate-spin mb-7"></div>
          <p className="text-[0.95rem] text-purple-500 font-medium animate-[pulseText_1.8s_infinite_alternate]">Initializing application...</p>
        </div>
      </div>
    );
  }

  console.log('[App] Rendering routes');
  return (
    <div className="min-h-screen bg-gray-900">
      {routing}
      <DevPerformanceMonitor />
    </div>
  );
}

export default App;
