// Only used for WebSocket bridge and cache updates. All other chat state is managed by TanStack Query and uiChatStore.
import { create } from 'zustand';
import { ChatMessage, ChatConversation } from '../types/chat';
import type { User } from '../types';
import { api } from '../utils/apiClient';
import { toast } from 'react-hot-toast';

// Constants with improved WebSocket URL construction
const getWebSocketUrl = (): string => {
  const wsUrl = import.meta.env.VITE_WS_BASE_URL;
  const apiUrl = import.meta.env.VITE_API_URL;

  if (wsUrl) {
    return wsUrl;
  }

  // Fallback: construct WebSocket URL from API URL
  if (apiUrl) {
    return apiUrl.replace(/^http/, 'ws') + '/ws';
  }

  // Final fallback for development
  return import.meta.env.DEV ? 'ws://localhost:8000/ws' : 'wss://dev.atlasvip.cloud/ws';
};

const API_BASE_URL = import.meta.env.VITE_API_URL || '';
const WS_BASE_URL = getWebSocketUrl();

// Debug environment variables on module load
console.log('[ChatStore] Environment variables loaded:', {
  VITE_API_URL: import.meta.env.VITE_API_URL,
  VITE_WS_BASE_URL: import.meta.env.VITE_WS_BASE_URL,
  API_BASE_URL,
  WS_BASE_URL,
  NODE_ENV: import.meta.env.NODE_ENV,
  DEV: import.meta.env.DEV
});

// Debug logging function
const debugLog = (message: string, data?: any) => {
  if (import.meta.env.DEV) {
    console.log(`[Chat Debug] ${message}`, data || '');
  }
};

/**
 * Get a cookie value by name.
 * @param {string} name
 * @returns {string|null}
 */
function getCookie(name: string): string | null {
  try {
    const match = document.cookie.match(new RegExp('(^| )' + name + '=([^;]+)'));
    return match ? match[2] : null;
  } catch (e) {
    console.error(`Error reading cookie '${name}':`, e);
    return null;
  }
}

/**
 * Simplified function to get the authentication token ONLY from the cookie.
 * The responsibility of refreshing on 401 is handled by the calling functions.
 * Returns the JWT token string from the cookie or null if not found.
 */
// const getAuthTokenFromCookie = (): string | null => {
//   try {
//     const cookieToken = getCookie('auth_token');
//     if (cookieToken) {
//       debugLog('[getAuthTokenFromCookie] Retrieved token from cookie.');
//       return cookieToken;
//     } else {
//       debugLog('[getAuthTokenFromCookie] No auth_token cookie found.');
//       return null;
//     }
//   } catch (e) {
//     debugLog('[getAuthTokenFromCookie] Error accessing cookie for token', e);
//     return null;
//   }
// };

// Fetch one-time WebSocket token
async function fetchWebSocketToken(): Promise<string | null> {
  debugLog('Attempting to fetch one-time WebSocket token...');
  const url = '/chat/ws-token'; // Using relative path as API_BASE_URL is handled by the api instance
  try {
    debugLog(`Making POST request to ${url} with client_type=websocket using API instance`);

    const response = await api.post<{ token: string }>(
      url,
      {}, // Empty body for POST
      { params: { client_type: 'websocket' } } // Query parameters
    );

    // Axios successful response is in response.data
    debugLog('Successfully fetched WS token.');
    return response.data.token;
  } catch (error: any) {
    debugLog('Error fetching WebSocket token:', error);

    if (error.isAxiosError && error.response) {
      const { status, data } = error.response;
      debugLog(`WebSocket token response status: ${status}`);

      if (status === 401) {
        debugLog('Unauthorized fetching WS token. Throwing error to be handled by caller.');
        throw new Error('Authentication failed while fetching WebSocket token');
      } else if (status === 422) {
        debugLog('WebSocket token 422 error details:', data);
        // No need to parse JSON manually, data is already parsed by Axios
      } else {
        debugLog(`Failed to fetch WS token: ${status}`, data);
      }
    } else {
      // Network error or other non-Axios error
      debugLog('Non-Axios error fetching WebSocket token:', error.message);
    }

    // Ensure the original behavior of re-throwing specific auth errors is maintained if one was thrown
    if (error instanceof Error && error.message.startsWith('Authentication failed')) {
      throw error;
    }

    return null; // Return null for other errors
  }
}

// Type for the websocket connection state
interface WebSocketState {
  socket: WebSocket | null;
  isConnected: boolean;
  reconnectAttempt: number;
  currentUser: User | null;

  // Actions
  connectWebSocket: () => void;
  disconnectWebSocket: () => void;
  setCurrentUser: (user: User | null) => void;
}

// Create a heartbeat interval tracker for WebSocket keep-alive
let wsHeartbeatInterval: NodeJS.Timeout | null = null;

export const useChatWebSocketStore = create<WebSocketState>((set, get) => ({
  socket: null,
  isConnected: false,
  reconnectAttempt: 0,
  currentUser: null,

  setCurrentUser: (user: User | null) => set({ currentUser: user }),

  disconnectWebSocket: () => {
    const { socket } = get();
    if (socket) {
      socket.close(1000, 'User disconnected');
    }
    if (wsHeartbeatInterval) {
      clearInterval(wsHeartbeatInterval);
      wsHeartbeatInterval = null;
    }
    set({ socket: null, isConnected: false, reconnectAttempt: 0 });
  },

  connectWebSocket: async () => {
    const currentUser = get().currentUser;
    const { socket, reconnectAttempt } = get();

    if (
      !currentUser ||
      (socket &&
        (socket.readyState === WebSocket.CONNECTING || socket.readyState === WebSocket.OPEN))
    ) {
      return;
    }

    const MAX_RECONNECT_ATTEMPTS = 5;
    if (reconnectAttempt > MAX_RECONNECT_ATTEMPTS) {
      setTimeout(() => set({ reconnectAttempt: 0 }), 60000);
      return;
    }

    try {
      debugLog('connectWebSocket: Attempting to fetch one-time token. Current VITE_WS_BASE_URL:', WS_BASE_URL);
      const oneTimeToken = await fetchWebSocketToken();
      debugLog('connectWebSocket: Fetched one-time token:', oneTimeToken);

      if (!oneTimeToken) {
        set(state => ({ reconnectAttempt: state.reconnectAttempt + 1 }));
        return;
      }

      const chatEndpoint = `${WS_BASE_URL}/ws/chat/${encodeURIComponent(oneTimeToken)}`;
      debugLog('connectWebSocket: Constructed chatEndpoint:', chatEndpoint);
      const newSocket = new WebSocket(chatEndpoint);
      let connectionTimeout: NodeJS.Timeout;

      connectionTimeout = setTimeout(() => {
        if (newSocket && newSocket.readyState !== WebSocket.OPEN) {
          newSocket.close(1000, 'Connection timeout');
          invalidateQueries(['chatMessages']);
          invalidateQueries(['chatConversations']);
          set(state => ({ reconnectAttempt: state.reconnectAttempt + 1 }));
        }
      }, 10000);

      newSocket.onopen = () => {
        clearTimeout(connectionTimeout);
        set({ socket: newSocket, isConnected: true, reconnectAttempt: 0 });

        if (newSocket.readyState === WebSocket.OPEN) {
          try {
            invalidateQueries(['chatConversations']);
            invalidateQueries(['chatMessages']);

            if (wsHeartbeatInterval) clearInterval(wsHeartbeatInterval);
            wsHeartbeatInterval = setInterval(() => {
              const currentSocket = get().socket;
              if (currentSocket && currentSocket.readyState === WebSocket.OPEN) {
                try {
                  currentSocket.send(JSON.stringify({ type: 'ping' }));
                } catch (error) {
                  debugLog('Error sending ping', error);
                }
              } else {
                if (wsHeartbeatInterval) {
                  clearInterval(wsHeartbeatInterval);
                  wsHeartbeatInterval = null;
                }
              }
            }, 45000);
          } catch (error) {
            debugLog('Error during WebSocket onopen sequence:', error);
          }
        }
      };

      newSocket.onclose = event => {
        set({ socket: null, isConnected: false });
        if (wsHeartbeatInterval) {
          clearInterval(wsHeartbeatInterval);
          wsHeartbeatInterval = null;
        }
        if (event.code !== 1000 && event.code !== 1001 && event.code !== 1008) {
          const currentCloseUser = get().currentUser;
          if (currentCloseUser) {
            const currentReconnectAttempt = get().reconnectAttempt;
            if (currentReconnectAttempt < MAX_RECONNECT_ATTEMPTS) {
              const backoffTime =
                Math.min(1000 * Math.pow(2, currentReconnectAttempt), 30000) + Math.random() * 1000;
              set({ reconnectAttempt: currentReconnectAttempt + 1 });
              setTimeout(() => {
                const { socket: currentSocketAgain } = get();
                if (!currentSocketAgain && get().currentUser) {
                  get().connectWebSocket();
                }
              }, backoffTime);
            } else {
              set({ reconnectAttempt: 0 });
            }
          }
        } else {
          set({ reconnectAttempt: 0 });
        }
      };

      newSocket.onerror = _error => {
        clearTimeout(connectionTimeout);
      };

      newSocket.onmessage = event => {
        try {
          const data = JSON.parse(event.data as string);

          switch (data.type) {
            case 'chat_message':
              const newMessage = data.message as ChatMessage;

              const msgQueryKeyChatId = newMessage.is_public
                ? 'public'
                : (newMessage.conversation_id || 'unknown');
              // const msgEndpoint = newMessage.is_public
              //   ? '/chat/public/messages'
              //   : `/chat/private/${newMessage.conversation_id}/messages`;

              invalidateQueries(['chatMessages', msgQueryKeyChatId]);
              invalidateQueries(['chatConversations']);
              break;

            case 'conversations':
              const newConvos = (data.conversations || []) as ChatConversation[];
              newConvos.sort(
                (a, b) =>
                  new Date(b.last_message_at || 0).getTime() -
                  new Date(a.last_message_at || 0).getTime()
              );
              invalidateQueries(['chatConversations']);
              break;

            case 'messages':
              const rcvdMsgs = (data.messages || []) as ChatMessage[];
              const forChatIdFromWS = data.chat_id;

              const bulkMsgQueryKeyChatId =
                forChatIdFromWS === null || forChatIdFromWS === 0 ? 'public' : forChatIdFromWS;
              invalidateQueries(['chatMessages', bulkMsgQueryKeyChatId]);
              break;

            case 'error':
              if (data.message?.toLowerCase().includes('auth')) {
                get().disconnectWebSocket();
              }
              toast.error(`Chat error: ${data.message}`);
              break;

            case 'online_users':
              invalidateQueries(['chatConversations']);
              break;

            case 'pong':
              break;

            default:
              debugLog('Unknown WS msg type:', data.type);
          }
        } catch (error) {
          debugLog('Error processing WS message:', error);
        }
      };
    } catch (error) {
      if (error instanceof Error && error.message.startsWith('Auth')) {
        toast.error('Chat auth failed.');
      }
    }
  },
}));

// Helper to get query client instance (will be set by the app)
let globalQueryClient: any = null;
export const setQueryClient = (client: any) => {
  globalQueryClient = client;
};

const invalidateQueries = (queryKey: (string | number)[]) => {
  if (globalQueryClient) {
    globalQueryClient.invalidateQueries({ queryKey });
  }
};

export const getTotalUnreadCount = (): number => {
  if (!globalQueryClient) return 0;
  const conversations = globalQueryClient.getQueryData(['chatConversations']) as ChatConversation[] | undefined;
  return (conversations || []).reduce((sum, convo) => sum + (convo.unread_count || 0), 0);
};

export const useChatStore = useChatWebSocketStore;
