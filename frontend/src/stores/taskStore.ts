import { create } from 'zustand';
import { Task, TaskCompletion, TaskStatus } from '../types/task';

interface TaskState {
  tasks: Task[];
  completions: TaskCompletion[];
  isLoading: boolean;
  error: string | null;
}

interface TaskActions {
  setTasks: (tasks: Task[]) => void;
  setCompletions: (completions: TaskCompletion[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateTaskStatus: (taskId: number, status: TaskStatus) => void;
  addCompletion: (completion: TaskCompletion) => void;
  updateCompletion: (completionId: number, updates: Partial<TaskCompletion>) => void;
}

export type TaskStore = TaskState & TaskActions;

export const useTaskStore = create<TaskStore>((set, get) => ({
  // State
  tasks: [],
  completions: [],
  isLoading: false,
  error: null,

  // Actions
  setTasks: (tasks) => set({ tasks }),
  
  setCompletions: (completions) => set({ completions }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  setError: (error) => set({ error }),
  
  updateTaskStatus: (taskId, status) => set((state) => ({
    tasks: state.tasks.map(task => 
      task.id === taskId ? { ...task, status } : task
    )
  })),
  
  addCompletion: (completion) => set((state) => ({
    completions: [...state.completions, completion]
  })),
  
  updateCompletion: (completionId, updates) => set((state) => ({
    completions: state.completions.map(completion =>
      completion.id === completionId ? { ...completion, ...updates } : completion
    )
  })),
})); 