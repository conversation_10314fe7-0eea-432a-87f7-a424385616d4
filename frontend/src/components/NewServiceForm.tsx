import React, { useState, useEffect } from 'react';
import { VPNPackage, MarzbanPanel } from '../types';
import { api } from '../utils/apiClient';
import { toast } from 'react-hot-toast';
import Select from 'react-select';
import LoadingSpinner from './LoadingSpinner';
import { motion, AnimatePresence } from 'framer-motion';
import { haptics } from '../utils/haptics';
import { initTelegramWebApp } from '../utils/telegram';
import { useCurrentUserQuery } from '../hooks/authHooks';

interface NewServiceFormProps {
  onClose: () => void;
  onSuccess: () => Promise<void>;
  vpnPackage: VPNPackage | null;
  availablePackages: VPNPackage[];
  availablePanels: MarzbanPanel[];
}

const NewServiceForm: React.FC<NewServiceFormProps> = ({
  onClose,
  onSuccess,
  vpnPackage,
  availablePackages,
  availablePanels,
}) => {
  const { data: user } = useCurrentUserQuery();
  const [selectedPackage, setSelectedPackage] = useState<VPNPackage | null>(vpnPackage);
  const [selectedPanel, setSelectedPanel] = useState<MarzbanPanel | null>(null);
  const [customName, setCustomName] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (vpnPackage) {
      setSelectedPackage(vpnPackage);
    }
  }, [vpnPackage]);

  const showTelegramNotification = (message: string, isError = false) => {
    const telegramWebApp = window?.Telegram?.WebApp;
    if (telegramWebApp) {
      if (isError) {
        haptics.notification('error');
        telegramWebApp.showAlert(message);
      } else {
        haptics.notification('success');
        telegramWebApp.showPopup({
          title: '🎉 Success',
          message: message,
          buttons: [{ id: 'ok', type: 'ok', text: 'OK' }],
        });
      }
    } else {
      if (isError) {
        toast.error(message);
      } else {
        toast.success(message);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    haptics.impact('medium');

    if (!selectedPackage || !selectedPanel || !customName.trim()) {
      showTelegramNotification('Please fill in all required fields', true);
      return;
    }

    if (!user?.id) {
      showTelegramNotification('User not authenticated', true);
      return;
    }

    try {
      setLoading(true);
      await api.post(`/api/user/subscriptions/${user.id}/new`, {
        package_id: selectedPackage.id,
        panel_id: selectedPanel.id,
        custom_name: `${customName.trim()}-${user.id}`,
      });

      haptics.notification('success');
      showTelegramNotification('🚀 Service created successfully! Your VPN configuration is ready.');
      await onSuccess();
      onClose();
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to create service';
      showTelegramNotification(errorMessage, true);
    } finally {
      setLoading(false);
    }
  };

  const handleButtonClick = () => {
    haptics.impact('light');
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-6 bg-white/5 backdrop-blur-xl rounded-xl border border-white/10"
    >
      <h3 className="text-xl font-medium text-white mb-4">Add New Service</h3>
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Package Selection */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">Select Package</label>
          <Select
            options={availablePackages}
            value={selectedPackage}
            onChange={value => setSelectedPackage(value as VPNPackage)}
            getOptionLabel={(option: VPNPackage) => `${option.name} - $${option.price}`}
            getOptionValue={(option: VPNPackage) => option.id.toString()}
            isSearchable={false}
            className="react-select-container"
            classNamePrefix="react-select"
            isDisabled={!!vpnPackage}
            styles={{
              control: base => ({
                ...base,
                background: 'rgba(255, 255, 255, 0.05)',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  borderColor: 'rgba(168, 85, 247, 0.4)',
                },
              }),
              menu: base => ({
                ...base,
                background: 'rgba(30, 30, 30, 0.95)',
                backdropFilter: 'blur(10px)',
              }),
              option: (base, state) => ({
                ...base,
                background: state.isFocused ? 'rgba(168, 85, 247, 0.2)' : 'transparent',
                color: 'white',
              }),
              singleValue: base => ({
                ...base,
                color: 'white',
              }),
            }}
          />
        </div>

        {/* Panel Selection */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">Select Panel</label>
          <Select
            options={availablePanels}
            value={selectedPanel}
            onChange={value => setSelectedPanel(value as MarzbanPanel)}
            getOptionLabel={(option: MarzbanPanel) => option.name}
            getOptionValue={(option: MarzbanPanel) => option.id.toString()}
            isSearchable={false}
            className="react-select-container"
            classNamePrefix="react-select"
            styles={{
              control: base => ({
                ...base,
                background: 'rgba(255, 255, 255, 0.05)',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                '&:hover': {
                  borderColor: 'rgba(168, 85, 247, 0.4)',
                },
              }),
              menu: base => ({
                ...base,
                background: 'rgba(30, 30, 30, 0.95)',
                backdropFilter: 'blur(10px)',
              }),
              option: (base, state) => ({
                ...base,
                background: state.isFocused ? 'rgba(168, 85, 247, 0.2)' : 'transparent',
                color: 'white',
              }),
              singleValue: base => ({
                ...base,
                color: 'white',
              }),
            }}
          />
        </div>

        {/* Custom Name Input */}
        <div>
          <label className="block text-sm font-medium text-white/80 mb-2">VPN Username</label>
          <input
            type="text"
            value={customName}
            onChange={e => setCustomName(e.target.value)}
            placeholder="Enter VPN username"
            className="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 
                     text-white placeholder:text-white/40 focus:border-purple-500 
                     focus:ring-1 focus:ring-purple-500"
            required
          />
          <p className="mt-1 text-sm text-white/60">
            This will be used as your VPN connection username
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={() => {
              handleButtonClick();
              onClose();
            }}
            className="px-4 py-2 text-sm font-medium text-white bg-white/10 rounded-lg 
                     hover:bg-white/20 transition-colors"
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="submit"
            onClick={handleButtonClick}
            className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r 
                     from-purple-500/20 to-blue-500/20 rounded-lg
                     hover:from-purple-500/30 hover:to-blue-500/30 
                     transition-colors flex items-center space-x-2"
            disabled={loading}
          >
            {loading ? (
              <>
                <LoadingSpinner size="sm" />
                <span>Creating...</span>
              </>
            ) : (
              <span>Create Service</span>
            )}
          </button>
        </div>
      </form>
    </motion.div>
  );
};

export default NewServiceForm;
