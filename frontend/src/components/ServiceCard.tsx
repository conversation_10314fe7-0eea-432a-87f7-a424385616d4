import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { VPNSubscription } from '../types';
import { formatBytes } from '../utils/format';
import LoadingSpinner from './LoadingSpinner';
import { QRCodeSVG } from 'qrcode.react';
import { toast } from 'react-hot-toast';
import {
  getSubscriptionUsageStatus,
  formatLastOnline,
  UsageStatus,
} from '../utils/subscriptionUtils';

interface ServiceCardProps {
  subscription: VPNSubscription;
  onRefresh: () => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ subscription, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [usageStatus, setUsageStatus] = useState<UsageStatus | null>(null);
  const [showQR, setShowQR] = useState(false);

  useEffect(() => {
    if (!subscription) {
      setError('Invalid subscription data');
    }
  }, [subscription]);

  useEffect(() => {
    fetchUsageStatus();
    const interval = setInterval(fetchUsageStatus, 30000);
    return () => clearInterval(interval);
  }, [subscription.id]);

  const fetchUsageStatus = async () => {
    try {
      setLoading(true);
      const status = await getSubscriptionUsageStatus(subscription);
      setUsageStatus(status);
    } catch (error) {
      console.error('Failed to fetch usage status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCopyConfig = async () => {
    try {
      await navigator.clipboard.writeText(subscription.subscription_url);

      if (window.Telegram?.WebApp) {
        window.Telegram.WebApp.showAlert(
          'Configuration link copied! Please paste it in your VPN client application.'
        );
      } else {
        toast.success('Configuration copied to clipboard!');
      }
    } catch (error) {
      console.error('Failed to copy configuration:', error);
      toast.error('Failed to copy configuration');
    }
  };

  const formatGigaBytes = (bytes: number): string => {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)}GB`;
  };

  const getStatusStyles = (status: UsageStatus | null) => {
    if (!status) return 'bg-base-900/50 border-base-800/30';

    return status.isActive
      ? 'bg-gradient-to-br from-green-950/50 to-emerald-900/50 border-green-800/30'
      : 'bg-gradient-to-br from-red-950/50 to-rose-900/50 border-red-800/30';
  };

  if (!subscription) {
    return (
      <div className="bg-red-500/10 backdrop-blur-lg rounded-xl p-6 border border-red-500/20">
        <p className="text-red-400">Error loading subscription</p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`rounded-xl overflow-hidden border backdrop-blur-xl 
        shadow-lg transition-all duration-300 ${getStatusStyles(usageStatus)}`}
    >
      {/* Header Section */}
      <div className="p-6 pb-4">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h3 className="text-lg font-medium text-white">{subscription.marzban_username}</h3>
            <p className="text-sm text-white/60 mt-1">{subscription.package.name}</p>
          </div>
          <div
            className={`px-3 py-1.5 rounded-full text-xs font-medium ${usageStatus?.colorClass || 'bg-gray-500/20 text-gray-400'}`}
          >
            {usageStatus?.statusText || 'Unknown'}
          </div>
        </div>

        {/* Usage Stats */}
        <div className="space-y-4">
          {/* Data Usage */}
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span className="text-white/60">Data Usage</span>
              <span className="text-white font-medium">
                {loading ? (
                  <LoadingSpinner size="sm" />
                ) : (
                  `${formatGigaBytes(usageStatus?.dataUsed || 0)} / ${formatGigaBytes(usageStatus?.dataLimit || 0)}`
                )}
              </span>
            </div>
            <div className="h-2 bg-white/5 rounded-full overflow-hidden">
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: `${usageStatus?.usagePercent || 0}%` }}
                transition={{ duration: 0.5 }}
                className={`h-full ${
                  (usageStatus?.usagePercent || 0) > 90
                    ? 'bg-gradient-to-r from-yellow-500 to-red-500'
                    : 'bg-gradient-to-r from-purple-500 to-blue-500'
                }`}
              />
            </div>
          </div>

          {/* Lifetime Usage */}
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Total Usage</span>
            <span className="text-white font-medium">
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : (
                `${formatGigaBytes(usageStatus?.lifetimeUsed || 0)}`
              )}
            </span>
          </div>

          {/* Last Online */}
          <div className="flex justify-between text-sm">
            <span className="text-white/60">Last Online</span>
            <span
              className={`text-white/80 ${
                usageStatus?.lastOnline &&
                new Date(usageStatus.lastOnline).getTime() > Date.now() - 60000
                  ? 'text-green-400'
                  : ''
              }`}
            >
              {loading ? (
                <LoadingSpinner size="sm" />
              ) : (
                formatLastOnline(usageStatus?.lastOnline || null)
              )}
            </span>
          </div>

          {/* Time Remaining */}
          <div className="flex justify-between items-center text-sm">
            <span className="text-white/60">Time Remaining</span>
            <span className={`font-medium ${usageStatus?.timeRemaining?.color || 'text-white/60'}`}>
              {usageStatus?.timeRemaining?.text || 'Calculating...'}
            </span>
          </div>
        </div>
      </div>

      {/* Actions Section */}
      <div className="px-6 pb-6 flex space-x-3">
        <button
          onClick={() => setShowQR(!showQR)}
          className="flex-1 py-2.5 px-4 rounded-lg text-white/80 
            bg-base-900/50 hover:bg-base-900/70 
            transition-colors text-sm font-medium"
        >
          {showQR ? 'Hide QR' : 'Show QR'}
        </button>
        <button
          onClick={handleCopyConfig}
          className="flex-1 py-2.5 px-4 rounded-lg text-white 
            bg-gradient-to-r from-purple-900/50 to-blue-900/50 
            hover:from-purple-900/70 hover:to-blue-900/70 
            transition-colors text-sm font-medium"
        >
          Copy Config
        </button>
      </div>

      {/* QR Code Section */}
      {showQR && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="px-6 pb-6"
        >
          <div className="bg-white rounded-lg p-4 flex justify-center">
            <QRCodeSVG value={subscription.subscription_url} size={200} level="H" includeMargin />
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default ServiceCard;
