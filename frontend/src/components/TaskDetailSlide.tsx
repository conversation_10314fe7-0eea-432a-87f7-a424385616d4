import React, { useEffect, useState, useMemo, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createPortal } from 'react-dom';
import {
  Task,
  TaskType,
  TaskStatus,
  VerificationData,
  TaskVerificationResult,
  RewardType,
} from '../types/task';
import {
  useStartTaskMutation,
  useVerifyTaskMutation,
  useClaimRewardMutation,
  usePerformDailyCheckInMutation,
} from '../hooks/taskHooks';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { haptics } from '../utils/haptics';
import { tg } from '../utils/telegram';
import {
  FaSpinner,
  FaCheck,
  FaTelegram,
  FaYoutube,
  FaInstagram,
  FaTwitter,
  FaGlobe,
  FaLink,
  FaUserFriends,
  FaClock,
  FaGift,
  FaExternalLinkAlt,
  FaCalendarCheck,
  FaCheckCircle,
  FaCoins,
  FaTimes,
  FaPlayCircle,
} from 'react-icons/fa';
import type { TelegramWebApp } from '../types/telegram';
import { createVerificationData } from '../utils/task-helpers';
import { isLowEndDevice } from '../utils/performance';

// Use haptics from utils directly instead
const useHaptics = () => haptics;

// Use the proper TelegramWebApp type from telegram.d.ts
// No need to extend TelegramWebApp type - removed custom interface

// Add extended TaskVerificationResult interface to handle missing properties
interface ExtendedTaskVerificationResult extends TaskVerificationResult {
  task?: Task;
  wait_time?: number;
  cooldown?: number;
  error_message?: string;
}

interface ExtendedTask extends Task {
  is_verified?: boolean;
  wait_time_seconds?: number;
  reward_amount?: number;
  reward_currency?: string;
}

interface TaskDetailSlideProps {
  task: ExtendedTask;
  isOpen: boolean;
  onClose: () => void;
  onTaskUpdated: (task: ExtendedTask) => void;
}

// Add special test mode handling
const isTestMode =
  process.env.NODE_ENV === 'development' || window.location.search.includes('test=true');

// Simple formatting utility as replacement for imported one
const formatWaitTime = (seconds: number) => {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;

  if (minutes < 60) {
    return `${minutes}m ${remainingSeconds > 0 ? `${remainingSeconds}s` : ''}`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;

  return `${hours}h ${remainingMinutes > 0 ? `${remainingMinutes}m` : ''}`;
};

// Define PanInfo type (can be shared or redefined)
interface PanInfo {
  point: { x: number; y: number };
  delta: { x: number; y: number };
  offset: { x: number; y: number };
  velocity: { x: number; y: number };
}

export const TaskDetailSlide: React.FC<TaskDetailSlideProps> = ({
  task,
  isOpen,
  onClose,
  onTaskUpdated,
}) => {
  const queryClient = useQueryClient();

  // Initialize mutation hooks
  const startTaskMutation = useStartTaskMutation();
  const verifyTaskMutation = useVerifyTaskMutation();
  const claimRewardMutation = useClaimRewardMutation();
  const performDailyCheckInMutation = usePerformDailyCheckInMutation();

  const [loading, setLoading] = useState(false);
  const [verificationAttempts, setVerificationAttempts] = useState(
    task?.verification_attempts || 0
  );
  const [isVerifying, setIsVerifying] = useState(false);
  const [isClaimingReward, setIsClaimingReward] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [verificationDataInput, setVerificationDataInput] = useState('');
  const [verificationCooldownLeft, setVerificationCooldownLeft] = useState<number>(0);
  const [claimCooldownLeft, setClaimCooldownLeft] = useState<number>(0);
  const [showCelebration, setShowCelebration] = useState(false);
  const [claimedRewardAmount, setClaimedRewardAmount] = useState<number | null>(null);
  const [claimedRewardType, setClaimedRewardType] = useState<RewardType | null>(null);
  const [referralStats, setReferralStats] = useState<any>(null);
  const [avgCompletionTime, setAvgCompletionTime] = useState<number | null>(null);
  const [waitTimeUntilClaim, setWaitTimeUntilClaim] = useState<number>(0);
  const [needsVerificationInput, setNeedsVerificationInput] = useState<boolean>(false);
  const [visitTimerSeconds, setVisitTimerSeconds] = useState<number>(0); // State for website visit timer
  const [slideY, setSlideY] = useState('0%'); // State for drag position
  const slideRef = useRef<HTMLDivElement>(null); // Ref for the slide element
  const isLowEnd = isLowEndDevice(); // Check device performance

  // --- Derived State ---
  const isNavigationTask = useMemo(
    () =>
      task &&
      [
        TaskType.TELEGRAM_CHANNEL,
        TaskType.YOUTUBE_VIEW,
        TaskType.INSTAGRAM_FOLLOW,
        TaskType.INSTAGRAM_VIEW,
        TaskType.TWITTER_FOLLOW,
        TaskType.WEBSITE_VISIT,
      ].includes(task.type) &&
      task.platform_url,
    [task?.type, task?.platform_url]
  );

  // --- Effects ---
  useEffect(() => {
    // Reset state when task changes or slide opens/closes
    if (isOpen && task?.id) {
      setVerificationDataInput('');
      setVerificationAttempts(task?.verification_attempts || 0);
      setVerificationCooldownLeft(0);
      setClaimCooldownLeft(0);
      setIsVerifying(false);
      setShowCelebration(false);
      setReferralStats(null);
      setWaitTimeUntilClaim(0);
      setIsClaimingReward(false);
      setVisitTimerSeconds(0); // Reset visit timer
      setIsProcessing(false); // Reset processing flag

      // Load task-specific data when the slide is open and the task is loaded
      if (task.type === TaskType.REFERRAL) {
        // loadReferralStats(); // TODO: Re-evaluate how referral stats are fetched. This used checkReferralStatus.
        // If status is part of the main Task object, useTasksQuery invalidation should handle it.
        // If it's separate data, a new query hook is needed.
      }

      // Check for claim cooldown when task is completed but not claimed
      if (task.status === TaskStatus.COMPLETED && !task.is_claimed && task.claim_available_at) {
        const now = Date.now();
        const claimTime = new Date(task.claim_available_at).getTime();
        if (claimTime > now) {
          setClaimCooldownLeft(Math.floor((claimTime - now) / 1000));
        }
      }

      // --- Website Visit Timer Logic ---
      if (
        task.type === TaskType.WEBSITE_VISIT &&
        task.status === TaskStatus.PENDING &&
        task.started_at
      ) {
        const requiredDuration = task.required_duration || 20; // Use task config or default to 20s
        const startTime = new Date(task.started_at).getTime();
        const now = Date.now();
        const elapsedSeconds = Math.floor((now - startTime) / 1000);
        const remainingSeconds = Math.max(0, requiredDuration - elapsedSeconds);

        setVisitTimerSeconds(remainingSeconds);
      }
      // --- End Website Visit Timer Logic ---
    }

    // Set average completion time
    if (task?.avg_completion_time !== undefined && task.avg_completion_time !== null) {
      setAvgCompletionTime(task.avg_completion_time < 0 ? null : task.avg_completion_time);
    }

    // Determine if this task type needs verification input and is in PENDING status
    const needsInput =
      task?.status === TaskStatus.PENDING &&
      [
        TaskType.YOUTUBE_VIEW,
        TaskType.INSTAGRAM_VIEW,
        TaskType.INSTAGRAM_FOLLOW,
        TaskType.TWITTER_FOLLOW,
      ].includes(task.type);

    setNeedsVerificationInput(needsInput);
  }, [task, isOpen]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      if (tg && !tg.isExpanded) {
        tg.expand();
      }
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Effect for Visit Timer Countdown
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    if (
      isOpen &&
      task?.type === TaskType.WEBSITE_VISIT &&
      task.status === TaskStatus.PENDING &&
      visitTimerSeconds > 0
    ) {
      intervalId = setInterval(() => {
        setVisitTimerSeconds(prev => {
          const nextValue = prev - 1;
          if (nextValue <= 0) {
            if (intervalId) clearInterval(intervalId);
            toast.success('Website visit duration met! You can now verify.');
            haptics.notification('success');
            return 0;
          }
          return nextValue;
        });
      }, 1000);
    }

    // Cleanup function
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isOpen, task?.type, task?.status, visitTimerSeconds]); // Dependencies for the timer

  // Reset slide position when opening
  useEffect(() => {
    if (isOpen) {
      setSlideY('0%');
    }
  }, [isOpen]);

  // Effect to handle Telegram vertical swipes
  useEffect(() => {
    if (isOpen) {
      // Check if tg and swipe functions exist and version is sufficient
      if (
        tg &&
        tg.isVersionAtLeast &&
        tg.isVersionAtLeast('6.1') &&
        typeof tg.disableVerticalSwipes === 'function'
      ) {
        tg.disableVerticalSwipes();
      }
      document.body.style.overflow = 'hidden'; // Prevent body scroll
    } else {
      // Check if tg and swipe functions exist and version is sufficient
      if (
        tg &&
        tg.isVersionAtLeast &&
        tg.isVersionAtLeast('6.1') &&
        typeof tg.enableVerticalSwipes === 'function'
      ) {
        tg.enableVerticalSwipes();
      }
      document.body.style.overflow = ''; // Restore body scroll
    }
    // Cleanup
    return () => {
      // Check if tg and swipe functions exist and version is sufficient
      if (
        tg &&
        tg.isVersionAtLeast &&
        tg.isVersionAtLeast('6.1') &&
        typeof tg.enableVerticalSwipes === 'function'
      ) {
        tg.enableVerticalSwipes();
      }
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // --- Data Fetching & Handlers ---

  const handlePlatformLink = (url: string) => {
    if (!url) return;

    try {
      // Check version before calling haptics
      if (tg && tg.isVersionAtLeast && tg.isVersionAtLeast('6.1')) {
        haptics.impact('light');
      }

      if (tg) {
        tg.openLink(url);
      } else {
        window.open(url, '_blank');
      }

      if (task?.social_task?.required_duration && task.status === TaskStatus.PENDING) {
        toast.success(
          `Stay on the page for at least ${task.social_task.required_duration} seconds`
        );
      }
    } catch (error) {
      console.error('Failed to open link:', error);
      try {
        window.open(url, '_blank');
      } catch (e) {
        toast.error('Unable to open link. Please try again.');
      }
    }
  };

  const handleStartTask = async () => {
    if (!task || !task.id) return;
    haptics.impact('medium');
    setIsProcessing(true);

    try {
      // The startTaskMutation hook is defined to return TaskCompletion
      const completionResult = await startTaskMutation.mutateAsync(task.id);

      // Invalidate queries to refetch the full task list, which will update the task prop
      queryClient.invalidateQueries({ queryKey: ['tasks'] });

      toast.success(completionResult.message || 'Task started!');
      haptics.notification('success');

      // If it's a website visit task and there's a required duration, start the timer
      // We need to get required_duration from the original task prop as TaskCompletion doesn't have it
      if (
        task.type === TaskType.WEBSITE_VISIT &&
        task.required_duration &&
        task.required_duration > 0
      ) {
        setVisitTimerSeconds(task.required_duration);
      }

      if (isNavigationTask && task.platform_url) {
        handlePlatformLink(task.platform_url);
      }
      // onTaskUpdated might not have the full updated task here, parent should rely on useTasksQuery
      // If a partial update is needed immediately, we'd have to construct it carefully.
      // For now, relying on query invalidation.
      // onTaskUpdated(updatedTaskData as ExtendedTask);
    } catch (error: any) {
      console.error('Failed to start task:', error);
      toast.error(error.message || 'Failed to start task');
      haptics.notification('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDailyCheckin = async () => {
    if (!task || !task.id) return;
    haptics.impact('medium');
    setIsProcessing(true);

    try {
      // performDailyCheckInMutation returns DailyCheckInResponse
      const checkInResponse = await performDailyCheckInMutation.mutateAsync(task.id);

      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dailyStreak', task.id] }); // Invalidate streak too

      toast.success('Daily check-in successful!'); // Generic message, or use checkInResponse.completion.message
      haptics.notification('success');

      if (checkInResponse.reward && checkInResponse.reward > 0) {
        // Assuming reward_type can be inferred or is generic for daily check-ins
        // For celebration, we need amount and type. DailyCheckInResponse has `reward` (amount)
        // but not explicitly `reward_type`. We might need to default it or extend the response.
        setClaimedRewardAmount(checkInResponse.reward);
        setClaimedRewardType(RewardType.WALLET_BONUS); // Example: default or determine from task
        setShowCelebration(true);
      }

      // onTaskUpdated: The DailyCheckInResponse.completion is a TaskCompletion, not a full ExtendedTask.
      // The parent should rely on the useTasksQuery refetch.
    } catch (error: any) {
      console.error('Failed to perform daily check-in:', error);
      toast.error(error.message || 'Failed to check-in');
      haptics.notification('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleVerifyTask = async () => {
    if (!task || !task.id || task.status !== TaskStatus.PENDING) return;
    haptics.impact('medium');
    setIsProcessing(true);

    if (task.type === TaskType.WEBSITE_VISIT && visitTimerSeconds > 0) {
      toast.error(
        `Please wait for the visit timer: ${formatWaitTime(visitTimerSeconds)} remaining.`
      );
      setIsProcessing(false);
      return;
    }

    let verData: VerificationData | undefined;
    if (needsVerificationInput) {
      if (!verificationDataInput.trim()) {
        toast.error('Please provide the required verification information.');
        setIsProcessing(false);
        return;
      }
      verData = createVerificationData(task, { verificationCode: verificationDataInput.trim() });
    }

    try {
      // verifyTaskMutation returns TaskVerificationResult
      const verificationResult = await verifyTaskMutation.mutateAsync({
        taskId: task.id,
        data: verData,
      });

      queryClient.invalidateQueries({ queryKey: ['tasks'] });

      if (verificationResult.success) {
        toast.success(verificationResult.message || 'Task verified!');
        haptics.notification('success');
        // TaskVerificationResult includes 'status' (new TaskStatus) and 'claim_available_at'
        // If verification leads to completion, and there's a claim cooldown
        if (
          verificationResult.status === TaskStatus.COMPLETED &&
          verificationResult.claim_available_at
        ) {
          const now = Date.now();
          const claimTime = new Date(verificationResult.claim_available_at).getTime();
          if (claimTime > now) {
            setClaimCooldownLeft(Math.floor((claimTime - now) / 1000));
          }
        }
      } else if (
        verificationResult.verification_time_left &&
        verificationResult.verification_time_left > 0
      ) {
        // This was 'cooldown' status in the old logic, maps to verification_time_left
        setVerificationCooldownLeft(verificationResult.verification_time_left);
        toast.error(
          verificationResult.message ||
            `Please wait ${formatWaitTime(verificationResult.verification_time_left)} before trying again.`
        );
        haptics.notification('warning');
      } else {
        toast.error(verificationResult.message || 'Verification failed. Please try again.');
        haptics.notification('error');
      }

      setVerificationDataInput('');
      // onTaskUpdated: TaskVerificationResult is not a full ExtendedTask.
      // Relies on query invalidation.
    } catch (error: any) {
      console.error('Verification failed:', error);
      toast.error(error.message || 'An error occurred during verification.');
      haptics.notification('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleClaimReward = async () => {
    if (!task || !task.id || task.status !== TaskStatus.COMPLETED || task.is_claimed) return;

    if (claimCooldownLeft > 0) {
      toast(`Claim available in: ${formatWaitTime(claimCooldownLeft)}`, { icon: 'ℹ️' });
      return;
    }
    haptics.impact('medium');
    setIsProcessing(true);

    try {
      // claimRewardMutation is typed to return TaskCompletion in hooks,
      // but the original UI logic expected more (reward_amount, reward_currency directly from it)
      // Let's assume the API for claim might return a richer object or we rely on query invalidation.
      // For now, let's assume it returns something like { success: boolean, message?: string, claimed_amount?: number, reward_type?: RewardType }
      // OR, the hook should be useMutation<Task, Error, number> if API returns the full task
      // Sticking to current hook type TaskCompletion:
      const claimResult = await claimRewardMutation.mutateAsync(task.id); // Returns TaskCompletion

      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['currentUser'] }); // Claiming reward affects user balance
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });

      if (claimResult.is_claimed) {
        // TaskCompletion has is_claimed
        toast.success(claimResult.message || 'Reward claimed!');
        haptics.notification('success');
        // To show celebration, we need reward_amount and reward_type.
        // These are on the original `task` prop.
        setClaimedRewardAmount(task.reward_value ?? null);
        setClaimedRewardType(task.reward_type ?? null);
        setShowCelebration(true);
      } else {
        toast.error(claimResult.message || 'Failed to claim reward. Please try again.');
        haptics.notification('error');
      }
      // onTaskUpdated: claimResult is TaskCompletion, not ExtendedTask. Relies on query invalidation.
    } catch (error: any) {
      console.error('Failed to claim reward:', error);
      toast.error(error.message || 'Failed to claim reward');
      haptics.notification('error');
    } finally {
      setIsProcessing(false);
    }
  };

  // Custom close handler that prevents closing during async operations
  const handleClose = () => {
    if (isProcessing) {
      // Don't close while an async operation is in progress
      toast.loading('Processing task...'); // Optional: give feedback
      return;
    }
    onClose();
  };

  // Handle drag gesture
  const handleDragEnd = useCallback(
    (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
      // If dragged down more than 100px, close the slide
      if (info.offset.y > 100) {
        onClose(); // Let AnimatePresence handle the exit animation
      } else {
        // Reset position if not dragged enough to close
        setSlideY('0%');
      }
    },
    [onClose]
  );

  // --- Status Helper Functions ---
  const getStatusLabel = () => {
    if (!task?.status) return 'Loading';
    const statusValue = String(task.status || '');
    if (task.is_claimed) return 'Claimed';
    if (statusValue === TaskStatus.COMPLETED)
      return claimCooldownLeft > 0 ? 'Cooling Down' : 'Ready to Claim';
    if (statusValue === TaskStatus.PENDING) return 'In Progress';
    if (statusValue === TaskStatus.ACTIVE) return 'Start Now';
    if (statusValue === TaskStatus.FAILED) return 'Failed';
    if (statusValue === TaskStatus.EXPIRED) return 'Expired';
    if (statusValue === TaskStatus.REVOKED) return 'Revoked';
    return `Unknown`;
  };

  const getStatusColors = () => {
    if (!task?.status) return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    const statusValue = String(task.status || '');
    if (task.is_claimed) return 'bg-green-500/20 text-green-300 border-green-500/30';
    if (statusValue === TaskStatus.COMPLETED)
      return claimCooldownLeft > 0
        ? 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
        : 'bg-purple-500/20 text-purple-300 border-purple-500/30';
    if (statusValue === TaskStatus.PENDING)
      return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30';
    if (statusValue === TaskStatus.ACTIVE) return 'bg-blue-500/20 text-blue-300 border-blue-500/30';
    if (statusValue === TaskStatus.FAILED) return 'bg-red-500/20 text-red-300 border-red-500/30';
    if (statusValue === TaskStatus.EXPIRED)
      return 'bg-gray-500/20 text-gray-300 border-gray-500/30';
    if (statusValue === TaskStatus.REVOKED) return 'bg-red-700/20 text-red-400 border-red-700/30';
    return 'bg-white/10 text-white/60 border-white/20';
  };

  const getStatusIcon = () => {
    if (!task?.status) return <FaSpinner className="animate-spin" />;
    const statusValue = String(task.status || '');
    if (task.is_claimed) return <FaCheckCircle />;
    if (statusValue === TaskStatus.COMPLETED)
      return claimCooldownLeft > 0 ? (
        <FaClock className="animate-pulse" />
      ) : (
        <FaGift className="animate-bounce" />
      );
    if (statusValue === TaskStatus.PENDING) return <FaSpinner className="animate-spin" />;
    if (statusValue === TaskStatus.ACTIVE) return <FaExternalLinkAlt />;
    if (statusValue === TaskStatus.FAILED) return <span>❌</span>;
    if (statusValue === TaskStatus.EXPIRED) return <FaClock />;
    if (statusValue === TaskStatus.REVOKED) return <span>🚫</span>;
    return <FaSpinner className="animate-spin" />;
  };

  // --- UI Rendering Functions ---

  const getTaskIcon = () => {
    if (!task) return null;
    switch (task.type) {
      case TaskType.TELEGRAM_CHANNEL:
        return <FaTelegram className="text-blue-400" />;
      case TaskType.YOUTUBE_VIEW:
        return <FaYoutube className="text-red-500" />;
      case TaskType.INSTAGRAM_FOLLOW:
      case TaskType.INSTAGRAM_VIEW:
        return <FaInstagram className="text-pink-500" />;
      case TaskType.TWITTER_FOLLOW:
        return <FaTwitter className="text-blue-400" />;
      case TaskType.WEBSITE_VISIT:
        return <FaGlobe className="text-green-400" />;
      case TaskType.REFERRAL:
        return <FaUserFriends className="text-purple-400" />;
      case TaskType.DAILY_CHECKIN:
        return <FaCalendarCheck className="text-purple-400" />;
      default:
        return null;
    }
  };

  const renderVerificationInput = () => {
    if (!task || !needsVerificationInput || task.status !== TaskStatus.PENDING) {
      return null;
    }

    let placeholder = 'Enter verification data';
    let helper = '';

    if (task.type === TaskType.YOUTUBE_VIEW) {
      placeholder = 'Enter verification code';
      helper = 'Find the code displayed in the video';
    } else if (task.type === TaskType.INSTAGRAM_VIEW) {
      placeholder = 'Enter verification code';
      helper = 'Find the code in the post caption or comments';
    } else if (task.type === TaskType.INSTAGRAM_FOLLOW) {
      placeholder = 'Your Instagram username';
      helper = 'Enter the username you used to follow';
    } else if (task.type === TaskType.TWITTER_FOLLOW) {
      placeholder = 'Your Twitter/X username';
      helper = 'Enter without the @ symbol';
    }

    return (
      <div className="bg-white/5 p-3 rounded-lg">
        <input
          type="text"
          placeholder={placeholder}
          value={verificationDataInput}
          onChange={e => setVerificationDataInput(e.target.value)}
          className="w-full p-2 bg-white/10 border border-purple-500/20 rounded text-white focus:border-purple-500 focus:outline-none text-sm"
        />
        {helper && <p className="text-xs text-white/50 mt-1 ml-1">{helper}</p>}
      </div>
    );
  };

  const renderProgressInfo = () => {
    if (!task || (task.status !== TaskStatus.PENDING && task.type !== TaskType.REFERRAL))
      return null;
    if (task.type === TaskType.DAILY_CHECKIN) return null;

    let progressPercentage = 0;
    let progressText = 'Progress';
    let progressColor = 'from-purple-500 to-pink-500';

    if (task.type === TaskType.REFERRAL && referralStats) {
      progressPercentage = Math.min(
        100,
        (referralStats.current_progress / task.target_value) * 100
      );
      progressText = `${referralStats.current_progress}/${task.target_value} Referrals`;
      progressColor = 'from-blue-500 to-indigo-500';
    } else if (task.current_progress !== undefined && task.target_value) {
      progressPercentage = Math.min(100, (task.current_progress / task.target_value) * 100);
      progressText = `Progress: ${task.current_progress}/${task.target_value}`;
    }

    return (
      <div className="space-y-2">
        <div className="flex justify-between text-xs text-white/60">
          <span>{progressText}</span>
          <span>{Math.round(progressPercentage)}%</span>
        </div>
        <div className="h-3 bg-white/5 rounded-full overflow-hidden border border-white/10">
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 1, ease: 'easeOut' }}
            className={`h-full bg-gradient-to-r ${progressColor} rounded-full`}
          />
        </div>
      </div>
    );
  };

  const renderTimingInfo = () => {
    if (!task || task.type === TaskType.DAILY_CHECKIN) return null;

    // For tasks with claim cooldown (verified but waiting to claim)
    if (task.status === TaskStatus.COMPLETED && claimCooldownLeft > 0) {
      return (
        <div className="bg-purple-500/10 rounded-lg overflow-hidden">
          <div className="flex items-center justify-between p-3 pb-2">
            <div className="flex items-center gap-1.5">
              <FaClock className="text-purple-400 text-xs animate-pulse" />
              <span className="text-sm text-white/90">Claim available in</span>
            </div>
            <span className="text-purple-300 font-mono font-medium text-sm">
              {formatWaitTime(claimCooldownLeft)}
            </span>
          </div>
          <div className="w-full bg-white/5 h-1.5">
            <motion.div
              className="bg-gradient-to-r from-purple-600 to-pink-500 h-1.5"
              initial={{ width: '100%' }}
              animate={{ width: `${(claimCooldownLeft / (30 * 60)) * 100}%` }}
              transition={{ duration: 1 }}
            />
          </div>
        </div>
      );
    }

    // For website visit tasks with a timer
    if (
      task.type === TaskType.WEBSITE_VISIT &&
      visitTimerSeconds > 0 &&
      task.status === TaskStatus.PENDING
    ) {
      return (
        <div className="bg-blue-500/10 rounded-lg overflow-hidden">
          <div className="flex items-center justify-between p-3 pb-2">
            <div className="flex items-center gap-1.5">
              <FaClock className="text-blue-400 text-xs animate-pulse" />
              <span className="text-sm text-white/90">Visit required</span>
            </div>
            <span className="text-blue-300 font-mono font-medium text-sm">
              {formatWaitTime(visitTimerSeconds)}
            </span>
          </div>
          <div className="w-full bg-white/5 h-1.5">
            <motion.div
              className="bg-gradient-to-r from-blue-600 to-cyan-500 h-1.5"
              initial={{ width: '0%' }}
              animate={{
                width: `${(1 - visitTimerSeconds / (task.required_duration || 20)) * 100}%`,
              }}
              transition={{ duration: 1 }}
            />
          </div>
        </div>
      );
    }

    return null;
  };

  const renderActionButton = () => {
    if (!task) return null;

    const currentIsProcessing =
      startTaskMutation.isPending ||
      verifyTaskMutation.isPending ||
      claimRewardMutation.isPending ||
      performDailyCheckInMutation.isPending ||
      isProcessing;

    // --- Daily Check-in Tasks ---
    if (task.type === TaskType.DAILY_CHECKIN) {
      return (
        <button
          className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 disabled:opacity-70 disabled:cursor-not-allowed"
          onClick={handleDailyCheckin}
          disabled={currentIsProcessing || task.status === TaskStatus.COMPLETED}
        >
          {currentIsProcessing ? (
            <FaSpinner className="animate-spin mr-2" />
          ) : (
            <FaCalendarCheck className="mr-2" />
          )}
          {task.status === TaskStatus.COMPLETED ? 'Checked In' : 'Check In Now'}
        </button>
      );
    }

    // --- Standard Tasks --- (like WEBSITE_VISIT, SOCIAL_MEDIA_ENGAGEMENT etc.)
    switch (task.status) {
      case TaskStatus.AVAILABLE:
        return (
          <button
            className="w-full bg-gradient-to-r from-purple-600 to-indigo-600 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-50 disabled:opacity-70 disabled:cursor-not-allowed"
            onClick={handleStartTask}
            disabled={currentIsProcessing}
          >
            {currentIsProcessing ? (
              <FaSpinner className="animate-spin mr-2" />
            ) : (
              <FaPlayCircle className="mr-2" />
            )}
            Start Task
          </button>
        );
      case TaskStatus.PENDING:
        if (verificationCooldownLeft > 0) {
          return (
            <div className="w-full bg-gray-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg flex items-center justify-center">
              <FaClock className="mr-2 animate-pulse" />
              Retry in: {formatWaitTime(verificationCooldownLeft)}
            </div>
          );
        }
        return (
          <button
            className="w-full bg-gradient-to-r from-green-500 to-teal-500 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:from-green-600 hover:to-teal-600 transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 disabled:opacity-70 disabled:cursor-not-allowed"
            onClick={handleVerifyTask}
            disabled={
              currentIsProcessing || (task.type === TaskType.WEBSITE_VISIT && visitTimerSeconds > 0)
            }
          >
            {currentIsProcessing ? (
              <FaSpinner className="animate-spin mr-2" />
            ) : (
              <FaCheckCircle className="mr-2" />
            )}
            {task.type === TaskType.WEBSITE_VISIT && visitTimerSeconds > 0
              ? `Verifying in ${formatWaitTime(visitTimerSeconds)}`
              : 'Verify Task'}
          </button>
        );
      case TaskStatus.COMPLETED:
        if (task.is_claimed) {
          return (
            <div className="w-full bg-gray-700 text-green-400 font-semibold py-3 px-6 rounded-xl shadow-lg flex items-center justify-center">
              <FaCheckCircle className="mr-2 text-green-400" />
              Reward Claimed
            </div>
          );
        }
        if (claimCooldownLeft > 0) {
          return (
            <div className="w-full bg-gray-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg flex items-center justify-center">
              <FaClock className="mr-2 animate-pulse" />
              Claim in: {formatWaitTime(claimCooldownLeft)}
            </div>
          );
        }
        return (
          <button
            className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:from-yellow-600 hover:to-orange-600 transition-all duration-300 ease-in-out transform hover:scale-105 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50 disabled:opacity-70 disabled:cursor-not-allowed"
            onClick={handleClaimReward}
            disabled={currentIsProcessing}
          >
            {currentIsProcessing ? (
              <FaSpinner className="animate-spin mr-2" />
            ) : (
              <FaGift className="mr-2" />
            )}
            Claim Reward
          </button>
        );
      case TaskStatus.FAILED:
      case TaskStatus.EXPIRED:
      case TaskStatus.REVOKED:
        return (
          <div className="w-full bg-gray-700 text-red-400 font-semibold py-3 px-6 rounded-xl shadow-lg flex items-center justify-center">
            Task {task.status.toLowerCase()}
          </div>
        );
      default:
        // Log an error for unhandled or unexpected status
        console.error(
          `[TaskDetailSlide] Unhandled task status in renderActionButton: ${task.status}`
        );
        return (
          <div className="w-full bg-gray-600 text-gray-300 font-semibold py-3 px-6 rounded-xl shadow-lg flex items-center justify-center">
            Status: {task.status ? task.status.toString().toUpperCase() : 'UNKNOWN'}
          </div>
        ); // Fallback UI for unhandled status
    }
  };

  const renderTaskStatusIndicator = () => {
    if (!task?.status) {
      return (
        <div className="p-3 rounded-lg bg-gray-500/20 text-gray-300 border-gray-500/30 flex items-center gap-3 justify-between backdrop-blur-sm shadow-sm">
          <div className="flex items-center gap-2">
            <FaSpinner className="animate-spin" />
            <span className="font-medium">Loading</span>
          </div>
        </div>
      );
    }

    return (
      <div
        className={`p-3 rounded-lg ${getStatusColors()} flex items-center gap-3 justify-between backdrop-blur-sm shadow-sm`}
      >
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <span className="font-medium">{getStatusLabel()}</span>
        </div>
        {verificationAttempts > 0 && task.status === TaskStatus.PENDING && (
          <div className="flex items-center gap-1 bg-white/10 px-2 py-1 rounded-md text-xs">
            <span>
              {verificationAttempts}/{task.max_verification_attempts || 3}
            </span>
          </div>
        )}
      </div>
    );
  };

  const renderCelebration = () => {
    if (!showCelebration || claimedRewardAmount === null || claimedRewardType === null) {
      return null;
    }

    const rewardText = `${claimedRewardAmount} ${claimedRewardType.replace(/_/g, ' ')}`;

    return (
      <motion.div
        key="celebration-overlay"
        initial={{ opacity: 0, scale: 0.8, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: 50 }}
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        className="absolute inset-0 bg-black/50 backdrop-blur-sm flex flex-col items-center justify-center z-[200] pointer-events-none"
      >
        <div className="bg-gradient-to-br from-purple-600 to-pink-600 p-6 rounded-xl shadow-lg text-center">
          <FaGift className="text-yellow-300 text-5xl mx-auto mb-4 animate-bounce" />
          <h3 className="text-2xl font-bold text-white mb-2">Reward Claimed!</h3>
          <p className="text-lg font-semibold text-yellow-300">+{rewardText}</p>
        </div>
      </motion.div>
    );
  };

  // Add Pull Indicator Component
  const PullIndicator = () => (
    <div className="w-full flex justify-center mb-1 -mt-1 opacity-60 pt-1">
      <div className="w-10 h-1 bg-white/30 rounded-full"></div>
    </div>
  );

  if (!task) {
    return null;
  }

  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <motion.div
          key="task-slide-backdrop"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-md z-[150] flex items-end sm:items-center justify-center will-change-transform overscroll-none"
          style={{ touchAction: 'none', overscrollBehavior: 'contain' }}
          onClick={handleClose}
        >
          {/* @ts-ignore TODO: Resolve framer-motion type conflict with drag/ref */}
          <motion.div
            key="task-slide-content"
            initial={{ y: '100%' }}
            animate={{ y: slideY }}
            exit={{ y: '100%' }}
            transition={{
              type: isLowEnd ? 'tween' : 'spring',
              stiffness: isLowEnd ? undefined : 300,
              damping: isLowEnd ? undefined : 30,
              mass: isLowEnd ? undefined : 0.8,
              duration: isLowEnd ? 0.25 : undefined,
            }}
            drag="y"
            dragConstraints={{ top: 0, bottom: 0 }}
            dragElastic={{ top: 0, bottom: 0.5 }}
            onDragEnd={handleDragEnd}
            {...({} as any)} // Linter workaround
            className="relative w-full sm:max-w-lg bg-gradient-to-b from-gray-900 to-[#13131e] rounded-t-2xl sm:rounded-2xl border-t-2 border-purple-500/50 shadow-xl shadow-purple-500/30 overflow-hidden flex flex-col max-h-[95vh] will-change-transform"
            onClick={e => {
              if (e) e.stopPropagation();
            }}
          >
            {/* Inner div to hold content and the ref */}
            <div ref={slideRef} className="w-full h-full flex flex-col overflow-hidden">
              {/* Pull Indicator */}
              <PullIndicator />

              {/* Close Button */}
              <button
                onClick={handleClose}
                className="absolute top-3 right-3 z-20 p-1.5 rounded-full text-white/50 bg-black/30 hover:text-white hover:bg-white/10 transition-all"
                aria-label="Close task details"
              >
                <FaTimes className="h-4 w-4" />
              </button>

              {/* Content */}
              <div
                className="flex-grow overflow-y-auto p-4 space-y-4 custom-scrollbar will-change-scroll"
                style={{ overscrollBehavior: 'contain' }}
              >
                {/* Task Title & Reward Info */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div className="text-lg">
                      {getTaskIcon() ?? <FaGift className="text-purple-400" />}
                    </div>
                    <h2 className="text-lg font-semibold text-white truncate">{task.name}</h2>
                  </div>
                  <div className="text-xs py-1 px-2.5 bg-purple-500/20 text-purple-300 rounded-full flex items-center gap-1 border border-purple-500/30">
                    <FaGift className="text-purple-400 text-xs" />
                    <span>
                      {task.reward_value} {task.reward_type.replace(/_/g, ' ')}
                    </span>
                  </div>
                </div>

                {/* Task status indicator */}
                <div className="flex items-center justify-between mb-3">
                  {renderTaskStatusIndicator()}
                  <span className="text-xs text-white/50 bg-white/5 py-1 px-2 rounded-full">
                    {task.type.replace(/_/g, ' ')}
                  </span>
                </div>

                {/* Timing information - prominently displayed */}
                {renderTimingInfo()}

                {/* Description - only shown if not in verified state and condensed */}
                {task.description &&
                  (task.status !== TaskStatus.COMPLETED || claimCooldownLeft <= 0) && (
                    <p className="text-white/60 text-sm leading-relaxed bg-white/5 p-3 rounded-lg border border-white/10">
                      {task.description}
                    </p>
                  )}

                {/* Progress indicator - simplified */}
                {task.type === TaskType.REFERRAL && referralStats && (
                  <div className="bg-white/5 p-3 rounded-lg border border-white/10">
                    <div className="flex justify-between items-center mb-1.5">
                      <span className="text-sm text-white/70">Referral Progress</span>
                      <span className="text-xs font-mono bg-white/10 px-2 py-0.5 rounded">
                        {referralStats.current_progress}/{task.target_value}
                      </span>
                    </div>
                    <div className="h-2.5 bg-white/10 rounded-full overflow-hidden">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{
                          width: `${Math.min(100, (referralStats.current_progress / task.target_value) * 100)}%`,
                        }}
                        transition={{ duration: 0.8, ease: 'easeOut' }}
                        className="h-full bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full"
                      />
                    </div>
                  </div>
                )}

                {/* Verification input field - modernized */}
                {renderVerificationInput()}
              </div>

              {/* Action button - simplified */}
              <div className="p-4 border-t border-white/10 bg-gradient-to-t from-black/50 to-transparent flex-shrink-0">
                {renderActionButton()}
              </div>

              {/* Celebration Overlay */}
              <AnimatePresence>{renderCelebration()}</AnimatePresence>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>,
    document.body
  );
};

interface InfoCardProps {
  label: string;
  value: string | number;
  icon?: React.ReactNode;
}

const InfoCard: React.FC<InfoCardProps> = ({ label, value, icon }) => (
  <div className="bg-white/5 rounded-lg p-3 border border-white/10">
    <div className="text-xs text-white/60 mb-1">{label}</div>
    <div className="text-white font-medium text-sm flex items-center gap-1.5">
      {icon}
      <span className="truncate">{value}</span>
    </div>
  </div>
);

export default TaskDetailSlide;
