import React, { useState, useEffect, useCallback, useRef, useId } from 'react';
import { DailyTaskStreak, TaskStatus } from '../types/task';
import {
  FaF<PERSON>,
  FaCheck,
  FaClock,
  FaGift,
  FaTrophy,
  FaCale<PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
} from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { haptics } from '../utils/haptics';
import { motion, AnimatePresence } from 'framer-motion';
import {
  useDailyStreakQuery,
  usePerformDailyCheckInMutation,
  useStartTaskMutation,
  useTasksQuery,
} from '../hooks/taskHooks';
import LoadingSpinner from './LoadingSpinner';

interface DailyStreakCardProps {
  taskId: number;
  onStreakUpdate?: (streak: DailyTaskStreak) => void;
  glowColor?: string;
}

export const DailyStreakCard: React.FC<DailyStreakCardProps> = ({
  taskId,
  onStreakUpdate,
  glowColor = '#a855f7',
}) => {
  const {
    data: dailyStreak,
    isLoading: loadingStreak,
    isError: streakError,
    refetch: refetchDailyStreak,
  } = useDailyStreakQuery(taskId, { enabled: true });
  const performDailyCheckIn = usePerformDailyCheckInMutation();
  const startTask = useStartTaskMutation();
  const { data: tasks = [], isLoading: loadingTasks } = useTasksQuery({ enabled: true });

  const [canCheckIn, setCanCheckIn] = useState(false);
  const [timeLeft, setTimeLeft] = useState<string | null>(null);
  const [isCheckingIn, setIsCheckingIn] = useState(false);
  const [isStartingTask, setIsStartingTask] = useState(false);
  const [hasCheckedInToday, setHasCheckedInToday] = useState(false);
  const [streakDataLoaded, setStreakDataLoaded] = useState(false);

  // Refs for tracking fetch state
  const hasInitiatedFetch = useRef(false);
  const currentTaskIdRef = useRef<number | null>(null);
  const isFetchingRef = useRef(false);
  const lastFetchTimeRef = useRef<number | null>(null);
  const shouldRefetchRef = useRef(false);

  const gradientId = useId();

  // Find the current task completion status from the main task list
  const taskCompletionStatus = tasks.find(t => t.id === taskId)?.status;
  const isTaskStarted =
    taskCompletionStatus === TaskStatus.PENDING || taskCompletionStatus === TaskStatus.COMPLETED;

  // Moved updateCheckInStatus definition before its usage in useEffect
  const updateCheckInStatus = useCallback((lastCheckIn: Date | null) => {
    if (!lastCheckIn) {
      setHasCheckedInToday(false);
      setCanCheckIn(true);
      return null;
    }

    const now = new Date();
    const todayDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
    const lastCheckDate = new Date(
      Date.UTC(lastCheckIn.getUTCFullYear(), lastCheckIn.getUTCMonth(), lastCheckIn.getUTCDate())
    );

    // Compare date parts only (ignore time)
    const isCheckedInToday = todayDate.getTime() === lastCheckDate.getTime();

    setHasCheckedInToday(isCheckedInToday);
    setCanCheckIn(!isCheckedInToday);

    let nextCheckTime: Date | null = null;
    if (isCheckedInToday) {
      // Next check is at midnight UTC
      nextCheckTime = new Date(
        Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + 1)
      );
    }

    return nextCheckTime;
  }, []);

  // Process streak data once it's loaded or changes
  useEffect(() => {
    if (dailyStreak && taskId === currentTaskIdRef.current) {
      console.log(
        `[DailyStreakCard] Processing loaded streak data for taskId=${taskId}`,
        dailyStreak
      );
      setStreakDataLoaded(true);
      updateCheckInStatus(dailyStreak.lastCheckIn ? new Date(dailyStreak.lastCheckIn) : null);
    } else if (!dailyStreak && taskId === currentTaskIdRef.current && !loadingStreak) {
      console.log(`[DailyStreakCard] Streak data is null for taskId=${taskId} after load.`);
      setStreakDataLoaded(false);
      setCanCheckIn(false);
      setHasCheckedInToday(false);
    }
  }, [dailyStreak, taskId, loadingStreak, updateCheckInStatus]);

  const fetchDataWithThrottle = useCallback(
    async (force = false) => {
      if (!taskId) return;

      if (isFetchingRef.current && !force) {
        shouldRefetchRef.current = true;
        return;
      }
      const now = Date.now();
      if (lastFetchTimeRef.current && now - lastFetchTimeRef.current < 2000 && !force) {
        shouldRefetchRef.current = true;
        return;
      }

      try {
        isFetchingRef.current = true;
        lastFetchTimeRef.current = now;
        shouldRefetchRef.current = false;

        console.log(
          `[DailyStreakCard] Refetching streak data for taskId=${taskId} (Force: ${force})`
        );
        await refetchDailyStreak();
      } catch (error) {
        console.error('[DailyStreakCard] Error fetching streak:', error);
      } finally {
        isFetchingRef.current = false;
        if (shouldRefetchRef.current) {
          setTimeout(() => {
            fetchDataWithThrottle();
          }, 100);
        }
      }
    },
    [taskId, refetchDailyStreak]
  );

  // Initialize fetch when taskId changes or component mounts
  useEffect(() => {
    if (!taskId) return;

    if (taskId !== currentTaskIdRef.current) {
      console.log(`[DailyStreakCard] TaskId changed to ${taskId}`);
      currentTaskIdRef.current = taskId;
      hasInitiatedFetch.current = false;
      setStreakDataLoaded(false);
      lastFetchTimeRef.current = null;
      setCanCheckIn(false);
      setHasCheckedInToday(false);
      setTimeLeft(null);
    }

    if (!hasInitiatedFetch.current && !loadingStreak) {
      console.log(`[DailyStreakCard] Initiating fetch for taskId=${taskId}`);
      hasInitiatedFetch.current = true;
      fetchDataWithThrottle();
    }
  }, [taskId, loadingStreak, fetchDataWithThrottle]);

  // Timer for countdown if already checked in
  useEffect(() => {
    if (!streakDataLoaded || !hasCheckedInToday) {
      setTimeLeft(null);
      return;
    }

    let intervalId: number | undefined;
    const now = new Date();
    const nextCheckTime = new Date(
      Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() + 1)
    );

    const updateTimer = () => {
      const currentTime = new Date();
      const diffMs = nextCheckTime.getTime() - currentTime.getTime();

      if (diffMs <= 0) {
        setTimeLeft(null);
        setCanCheckIn(true);
        setHasCheckedInToday(false);
        if (intervalId) clearInterval(intervalId);
      } else {
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
        setTimeLeft(
          `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
        );
      }
    };

    updateTimer();
    intervalId = window.setInterval(updateTimer, 1000);

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [streakDataLoaded, hasCheckedInToday]);

  // --- Action Handlers ---
  const handleStartTask = async () => {
    if (!taskId || isStartingTask || loadingTasks) return;
    setIsStartingTask(true);
    try {
      haptics.impact('light');
      console.log(`[DailyStreakCard] Calling startTask for taskId=${taskId}`);
      await startTask.mutateAsync(taskId);
      toast.success('Daily task started!');
    } catch (error: any) {
      console.error('[DailyStreakCard] Error starting task:', error);
    } finally {
      setIsStartingTask(false);
    }
  };

  const handleCheckIn = async () => {
    if (
      !canCheckIn ||
      isCheckingIn ||
      !taskId ||
      !streakDataLoaded ||
      taskCompletionStatus !== TaskStatus.PENDING
    ) {
      console.warn(
        `[DailyStreakCard] Check-in blocked. Conditions: canCheckIn=${canCheckIn}, isCheckingIn=${isCheckingIn}, taskId=${taskId}, streakDataLoaded=${streakDataLoaded}, taskStatus=${taskCompletionStatus}`
      );
      if (taskCompletionStatus !== TaskStatus.PENDING && canCheckIn) {
        toast.error('Task status issue, please refresh.', { icon: '⚠️' });
      }
      return;
    }

    setIsCheckingIn(true);
    try {
      haptics.impact('medium');
      console.log(`[DailyStreakCard] Calling performDailyCheckIn for taskId=${taskId}`);
      const response = await performDailyCheckIn.mutateAsync(taskId);

      haptics.notification('success');
      toast.success('Checked in successfully!');

      if (onStreakUpdate && response?.streak) {
        onStreakUpdate(response.streak);
      }

      if (response?.cycle_completion) {
        toast.success(`Cycle complete! Bonus: ${response.cycle_reward}`, {
          icon: '🎉',
          duration: 5000,
        });
      }
    } catch (error: any) {
      haptics.notification('error');
      console.error('[DailyStreakCard] Error during check-in:', error);
    } finally {
      setIsCheckingIn(false);
    }
  };

  // --- Render Logic ---

  if (loadingStreak || (!streakDataLoaded && loadingTasks && !hasInitiatedFetch.current)) {
    return (
      <div className="relative bg-gradient-to-br from-black/70 via-base-900/60 to-base-900/80 rounded-xl overflow-hidden h-48 backdrop-blur-sm flex flex-col items-center justify-center min-h-0 shadow-lg shadow-purple-500/15 border border-purple-500/20">
        <LoadingSpinner size="md" />
        <p className="text-xs text-white/50 mt-2">Loading daily status...</p>
      </div>
    );
  }

  if (streakError && !dailyStreak) {
    return (
      <div className="relative bg-gradient-to-br from-red-900/30 via-red-950/50 to-red-950/70 rounded-xl overflow-hidden p-4 backdrop-blur-sm shadow-lg shadow-red-500/20 border border-red-500/30">
        <div className="text-red-300 text-xs mb-1 font-medium">Error loading streak data</div>
        <p className="text-red-400/80 text-xs mb-3">{streakError}</p>
        <button
          onClick={() => fetchDataWithThrottle(true)}
          className="text-purple-400 text-xs hover:underline"
        >
          Retry
        </button>
      </div>
    );
  }

  const currentStreak = dailyStreak?.currentStreak ?? 0;
  const longestStreak = dailyStreak?.longestStreak ?? 0;

  let buttonText = 'Loading...';
  let buttonAction = () => {};
  let isButtonDisabled = true;
  let showSpinner = false;

  if (isStartingTask || isCheckingIn || loadingStreak || loadingTasks) {
    buttonText = isStartingTask ? 'Starting...' : isCheckingIn ? 'Checking in...' : 'Loading...';
    isButtonDisabled = true;
    showSpinner = true;
  } else if (!isTaskStarted) {
    buttonText = 'Start Daily Task';
    buttonAction = handleStartTask;
    isButtonDisabled = false;
  } else if (hasCheckedInToday) {
    buttonText = `Next: ${timeLeft || '...'}`;
    buttonAction = () => {};
    isButtonDisabled = true;
  } else if (canCheckIn && taskCompletionStatus === TaskStatus.PENDING) {
    buttonText = 'Check In';
    buttonAction = handleCheckIn;
    isButtonDisabled = false;
  } else {
    buttonText = 'Waiting...';
    isButtonDisabled = true;
    if (taskCompletionStatus !== TaskStatus.PENDING) {
      buttonText = 'Status Error';
    }
  }

  // Adopt CornerGlowCard styling
  const glowPositionClasses = 'bottom-0 left-0';
  const shadowColorClass = 'shadow-purple-500/15';

  return (
    <div
      className={`relative bg-gradient-to-br from-black/70 via-base-900/60 to-base-900/80 rounded-xl overflow-hidden h-full backdrop-blur-sm flex flex-col min-h-0 shadow-lg ${shadowColorClass} text-white`}
    >
      {/* Corner Glow Logic from CornerGlowCard */}
      {glowColor && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <svg
            className={`absolute h-40 w-40 opacity-80 ${glowPositionClasses}`}
            viewBox="0 0 100 100"
          >
            <defs>
              <radialGradient id={gradientId} cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor={glowColor} stopOpacity="0.5" />
                <stop offset="100%" stopColor={glowColor} stopOpacity="0" />
              </radialGradient>
            </defs>
            <circle cx="50" cy="50" r="50" fill={`url(#${gradientId})`} />
          </svg>
        </div>
      )}

      {/* Content Area - Replaced CardContent with div and padding */}
      <div className="relative p-6 flex flex-col flex-1">
        <div className="flex items-center justify-between">
          <div className="flex flex-col">
            <h3 className="text-lg font-semibold">Daily Streak</h3>
            <div className="flex items-center mt-2 space-x-2">
              <FaFire className="h-6 w-6 text-yellow-300" />
              <span className="text-2xl font-bold">{currentStreak}</span>
              <span className="text-sm opacity-80">days</span>
            </div>

            {longestStreak > 0 && (
              <p className="text-xs mt-2 opacity-80">Longest streak: {longestStreak} days</p>
            )}
          </div>

          {/* Replaced Button with styled HTML button */}
          <button
            onClick={buttonAction}
            disabled={isButtonDisabled}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-opacity duration-200 ease-in-out bg-white/20 hover:bg-white/30 text-white border-none flex items-center justify-center min-w-[120px] ${isButtonDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:opacity-90'}`}
          >
            {showSpinner && <FaSpinner className="animate-spin mr-2" />}
            {buttonText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DailyStreakCard;
