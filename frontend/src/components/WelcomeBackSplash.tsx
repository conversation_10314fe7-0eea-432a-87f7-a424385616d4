import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaTimes } from 'react-icons/fa';
import { formatCurrency } from '../utils/format';

interface WelcomeBackSplashProps {
  isOpen: boolean;
  userName: string | undefined;
  accumulatedProfit: number;
  onClaim: () => void;
  onDismiss: () => void;
  isClaiming: boolean;
}

const WelcomeBackSplash: React.FC<WelcomeBackSplashProps> = ({
  isOpen,
  userName,
  accumulatedProfit,
  onClaim,
  onDismiss,
  isClaiming,
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          key="splash-backdrop"
          className="fixed inset-0 bg-black/80 backdrop-blur-lg z-[200] flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            key="splash-content"
            className="w-full max-w-md bg-gradient-to-br from-purple-900 via-indigo-900 to-black rounded-2xl border border-purple-500/30 shadow-2xl shadow-purple-500/20 p-6 text-center relative overflow-hidden"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ type: 'spring', damping: 15, stiffness: 100, delay: 0.1 }}
          >
            {/* Optional: Close button */}
            <button
              onClick={onDismiss}
              className="absolute top-3 right-3 z-10 p-1.5 rounded-full text-white/50 bg-black/30 hover:text-white hover:bg-white/10 transition-all"
              aria-label="Close welcome message"
            >
              <FaTimes className="h-4 w-4" />
            </button>

            {/* Placeholder for 3D element/celebration animation */}
            <div className="absolute inset-0 opacity-10 overflow-hidden">
              {/* Example: Simple particle burst */}
              {Array.from({ length: 20 }).map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                  initial={{
                    x: '50%',
                    y: '50%',
                    scale: 0,
                    opacity: 0,
                  }}
                  animate={{
                    x: `${Math.random() * 100}%`,
                    y: `${Math.random() * 100}%`,
                    scale: [0, 1, 0],
                    opacity: [0, 0.8, 0],
                    transition: {
                      duration: Math.random() * 1 + 0.5,
                      delay: 0.2 + Math.random() * 0.3,
                      ease: 'easeOut',
                    },
                  }}
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                />
              ))}
            </div>

            {/* Content */}
            <div className="relative z-10">
              <div className="mb-4">
                <h1 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-pink-400 to-yellow-400 mb-2">
                  Welcome Back{userName ? `, ${userName}` : ''}!
                </h1>
                <p className="text-white/80 text-sm">You've earned profit while you were away.</p>
              </div>

              <div className="bg-white/10 rounded-lg p-4 mb-5 border border-white/10">
                <div className="text-xs text-yellow-300/80 mb-1 font-medium">
                  Profit Ready to Claim
                </div>
                <div className="text-2xl font-bold text-white flex items-center justify-center gap-2">
                  <FaCoins className="text-yellow-400" />
                  <span>{formatCurrency(accumulatedProfit)}</span>
                </div>
                <p className="text-xs text-white/60 mt-1">(Up to 3 hours)</p>
              </div>

              <div className="flex flex-col gap-3">
                <button
                  onClick={onClaim}
                  disabled={isClaiming}
                  className="w-full py-2.5 rounded-lg font-semibold flex items-center justify-center gap-2 text-sm bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600 shadow-lg shadow-purple-500/20 disabled:opacity-70"
                >
                  {isClaiming ? (
                    <>
                      <FaSpinner className="animate-spin" />
                      <span>Claiming...</span>
                    </>
                  ) : (
                    <>
                      <FaCoins />
                      <span>Claim Now</span>
                    </>
                  )}
                </button>
                <button
                  onClick={onDismiss}
                  className="w-full py-2 rounded-lg font-medium text-sm bg-white/5 hover:bg-white/10 text-white/80 hover:text-white transition-colors"
                >
                  Maybe Later
                </button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default WelcomeBackSplash;
