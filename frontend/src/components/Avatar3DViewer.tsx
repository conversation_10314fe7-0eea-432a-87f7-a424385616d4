import React, { useRef, useEffect, useState, useCallback } from 'react';
import { getAvatarUrl, formatCharacterName } from '../utils/avatarUtils';

interface Avatar3DViewerProps {
  filename: string | null;
  size: { width: number; height: number };
  className?: string;
  onClick?: () => void;
  useGyroscope?: boolean;
}

const Avatar3DViewer = React.memo<Avatar3DViewerProps>(function Avatar3DViewer({
  filename,
  size,
  className = '',
  onClick,
  useGyroscope = false,
}) {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const angleRef = useRef(0);
  const isUnmountedRef = useRef(false);
  const [rotation, setRotation] = useState({ x: 0, y: 0 });
  
  // Memoize expensive calculations
  const avatarUrl = React.useMemo(() => getAvatarUrl(filename), [filename]);
  const displayName = React.useMemo(() => filename ? formatCharacterName(filename) : '', [filename]);

  // Memoized orientation handler
  const handleOrientation = useCallback((event: DeviceOrientationEvent) => {
    if (isUnmountedRef.current) return;
    
    if (event.beta && event.gamma) {
      setRotation({
        x: Math.min(Math.max(event.beta - 45, -45), 45),
        y: Math.min(Math.max(event.gamma, -45), 45),
      });
    }
  }, []);

  // Optimized animation function with proper cleanup
  const animate = useCallback(() => {
    if (isUnmountedRef.current) return;
    
    angleRef.current += 0.02;
    setRotation({
      x: Math.sin(angleRef.current) * 10,
      y: Math.cos(angleRef.current) * 10,
    });
    
    // Schedule next frame only if component is still mounted
    if (!isUnmountedRef.current) {
      animationFrameRef.current = requestAnimationFrame(animate);
    }
  }, []);

  useEffect(() => {
    isUnmountedRef.current = false;
    
    // Cleanup any existing animation
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (useGyroscope && window.Telegram?.WebApp) {
      // Use device orientation for Telegram WebApp
      window.addEventListener('deviceorientation', handleOrientation);
      
      return () => {
        window.removeEventListener('deviceorientation', handleOrientation);
      };
    } else {
      // Fallback to automatic rotation with proper cleanup
      animationFrameRef.current = requestAnimationFrame(animate);
      
      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      };
    }
  }, [useGyroscope, handleOrientation, animate]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isUnmountedRef.current = true;
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, []);

  // Memoized style for container
  const containerStyle = React.useMemo(() => ({
    width: size.width,
    height: size.height,
    perspective: '1000px',
  }), [size.width, size.height]);

  // Memoized style for inner div
  const innerDivStyle = React.useMemo(() => ({
    transformStyle: 'preserve-3d' as const,
    transform: `rotateX(${rotation.x}deg) rotateY(${rotation.y}deg)`,
  }), [rotation.x, rotation.y]);

  // Memoized image style
  const imageStyle = React.useMemo(() => ({
    backfaceVisibility: 'hidden' as const,
    WebkitBackfaceVisibility: 'hidden' as const,
    filter: 'contrast(1.1) brightness(0.95)',
  }), []);

  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      style={containerStyle}
      onClick={onClick}
      role={onClick ? 'button' : undefined}
      title={displayName}
    >
      <div
        className="w-full h-full transition-transform duration-100 ease-out"
        style={innerDivStyle}
      >
        <img
          src={avatarUrl}
          alt={displayName}
          className="w-full h-full object-contain bg-transparent"
          style={imageStyle}
          draggable={false}
          loading="lazy"
        />
      </div>
    </div>
  );
});

export default Avatar3DViewer;
