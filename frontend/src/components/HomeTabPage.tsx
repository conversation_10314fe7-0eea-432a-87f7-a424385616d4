import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import Avatar3DViewer from './Avatar3DViewer';
import { FaCoins, FaBolt } from 'react-icons/fa';
import { toast } from 'react-hot-toast';
import { formatCurrency } from '../utils/format';
import type { TabId, DashboardStats, User } from '../types';
import LoadingSpinner from './LoadingSpinner';
import { isLowEndDevice } from '../utils/performance';
import Section from './layout/Section';
import CornerGlowCard from './CornerGlowCard';
import { useClaimAllProfitsMutation } from '../hooks/cardHooks';

// --- Prop Interfaces ---
interface HomeTabProps {
  onViewAllServices?: () => void;
  onTabChange?: (tab: TabId) => void;
  user: import('../types').User | undefined;
  dashboardStats?: DashboardStats;
}

interface CornerAccentProps {
  position: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

interface CyberpunkStatDisplayProps {
  label: string;
  value: string;
  icon: React.ReactNode;
  color: 'gold' | 'purple' | 'blue';
  animate?: boolean;
  circleRef?: React.RefObject<SVGCircleElement>;
}

interface CyberpunkProfitCardProps {
  profit: number;
  onClaimClick: () => void;
  isClaiming: boolean;
}

interface CyberpunkStatsCardProps {
  subscriptions: number;
  balance: number;
  hourlyRate: number;
}

interface StatProjectionItemProps {
  label: string;
  value: string;
  period: string;
  bgColor: string;
  highlight?: boolean;
}

interface HomeTabPageProps {
  dashboardStats?: DashboardStats;
  user?: User;
  isLoadingDashboardStats?: boolean;
  isLoadingUser?: boolean;
  onTabChange?: (tabId: string) => void;
}

// --- Helper Components - Memoized for performance ---

const GridBackground: React.FC = React.memo(() => (
  <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
    <pattern id="smallGrid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path
        d="M 10 0 L 0 0 0 10"
        fill="none"
        stroke="currentColor"
        strokeWidth="0.5"
        opacity="0.2"
      />
    </pattern>
    <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
      <rect width="50" height="50" fill="url(#smallGrid)" />
      <path d="M 50 0 L 0 0 0 50" fill="none" stroke="currentColor" strokeWidth="1" opacity="0.3" />
    </pattern>
    <rect width="100%" height="100%" fill="url(#grid)" />
  </svg>
));
GridBackground.displayName = 'GridBackground';

const CornerAccent: React.FC<CornerAccentProps> = React.memo(({ position }) => {
  const positionStyles = {
    'top-left': 'top-0 left-0 rotate-0',
    'top-right': 'top-0 right-0 rotate-90',
    'bottom-left': 'bottom-0 left-0 -rotate-90',
    'bottom-right': 'bottom-0 right-0 rotate-180',
  };

  return (
    <div className={`absolute ${positionStyles[position]} w-8 h-8 opacity-70`}>
      <svg
        width="32"
        height="32"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M 0 12 V 0 H 12" stroke="#a855f7" strokeWidth="1" />
        <path d="M 0 8 V 0 H 8" stroke="#a855f7" strokeWidth="2" />
      </svg>
    </div>
  );
});
CornerAccent.displayName = 'CornerAccent';

const CyberpunkStatDisplay: React.FC<CyberpunkStatDisplayProps> = React.memo(
  ({ label, value, icon, color, animate = false, circleRef }) => {
    const [glitchOffset, setGlitchOffset] = useState({ x: 0, y: 0 });

    useEffect(() => {
      if (!animate) return;

      let animationInterval: ReturnType<typeof setInterval>;

      animationInterval = setInterval(() => {
        if (Math.random() > 0.9) {
          setGlitchOffset({
            x: Math.random() * 4 - 2,
            y: Math.random() * 4 - 2,
          });
          setTimeout(() => setGlitchOffset({ x: 0, y: 0 }), 100);
        }
      }, 500);

      return () => clearInterval(animationInterval);
    }, [animate]);

    const colorScheme = useMemo(
      () => ({
        gold: {
          gradientFrom: 'from-yellow-400',
          gradientTo: 'to-amber-600',
          iconColor: 'text-yellow-300',
          borderColor: 'border-yellow-500/30',
          bgGlow: 'bg-yellow-500/5',
        },
        purple: {
          gradientFrom: 'from-violet-400',
          gradientTo: 'to-purple-600',
          iconColor: 'text-purple-300',
          borderColor: 'border-purple-500/30',
          bgGlow: 'bg-purple-500/5',
        },
        blue: {
          gradientFrom: 'from-blue-400',
          gradientTo: 'to-indigo-600',
          iconColor: 'text-blue-300',
          borderColor: 'border-blue-500/30',
          bgGlow: 'bg-blue-500/5',
        },
      }),
      []
    );

    const { gradientFrom, gradientTo, iconColor, borderColor, bgGlow } = colorScheme[color];

    return (
      <div
        className={`relative bg-black/60 backdrop-blur-sm rounded-lg overflow-hidden border ${borderColor} shadow-[0_0_12px_rgba(168,85,247,0.25)] group hover:scale-[1.02] transition-all duration-300`}
      >
        <div className="absolute inset-0 overflow-hidden">
          <div
            className={`absolute inset-0 ${bgGlow} opacity-30 group-hover:opacity-50 transition-opacity`}
          ></div>
          <div
            className={`absolute -inset-1 bg-gradient-to-r ${gradientFrom} ${gradientTo} opacity-30 blur-xl group-hover:opacity-40 transition-all`}
          ></div>
        </div>
        <div className="relative h-full px-3 py-2 flex flex-col">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div
                className={`w-6 h-6 rounded-md flex items-center justify-center ${iconColor} bg-black/40`}
              >
                {icon}
              </div>
              <div className="text-xs uppercase tracking-widest text-white/70 font-semibold truncate">
                {label}
              </div>
            </div>
            {animate && (
              <div className="w-5 h-5 relative">
                <svg viewBox="0 0 24 24" className="w-full h-full transform -rotate-90">
                  <circle cx="12" cy="12" r="9" fill="none" stroke="#1f1f2e" strokeWidth="3" />
                  <circle
                    cx="12"
                    cy="12"
                    r="9"
                    fill="none"
                    stroke={color === 'gold' ? '#facc15' : '#a855f7'}
                    strokeWidth="2"
                    strokeDasharray="56.5"
                    strokeDashoffset="0"
                    strokeLinecap="round"
                    className="animate-circle-fill"
                    ref={circleRef}
                  />
                </svg>
              </div>
            )}
          </div>
          <div
            className="mt-1 text-base sm:text-xl font-bold tracking-wide text-white group-hover:scale-[1.03] transition-transform truncate"
            style={{
              transform: animate
                ? `translate(${glitchOffset.x}px, ${glitchOffset.y}px)`
                : undefined,
            }}
          >
            {value}
          </div>
        </div>
      </div>
    );
  }
);
CyberpunkStatDisplay.displayName = 'CyberpunkStatDisplay';

const StatProjectionItem: React.FC<StatProjectionItemProps> = React.memo(
  ({ label, value, period, bgColor, highlight = false }) => (
    <div className={`${bgColor} rounded-md p-2 ${highlight ? 'ring-1 ring-accent-500/50' : ''}`}>
      <div className="text-[10px] uppercase tracking-wider text-white/60 font-medium">{label}</div>
      <div className="text-white font-bold mt-0.5 text-xs sm:text-sm truncate">{value}</div>
      <div className="text-[9px] text-white/50 mt-0.5">{period}</div>
    </div>
  )
);
StatProjectionItem.displayName = 'StatProjectionItem';

const CyberpunkStatsCard: React.FC<CyberpunkStatsCardProps> = React.memo(
  ({ subscriptions, balance, hourlyRate }) => {
    const dailyEarnings = useMemo(() => (hourlyRate ?? 0) * 24, [hourlyRate]);
    const weeklyEarnings = useMemo(() => dailyEarnings * 7, [dailyEarnings]);
    const monthlyEarnings = useMemo(() => dailyEarnings * 30, [dailyEarnings]);

    return (
      <CornerGlowCard
        borderColorClass="border-purple-500/20"
        shadowColorClass="shadow-purple-500/15"
        glowColor="#a855f7"
        glowPosition="bottom-left"
      >
        <div className="mb-2">
          <div className="flex items-center gap-2">
            <FaBolt className="text-purple-400 text-sm" />
            <div className="text-sm uppercase tracking-wider text-purple-300 font-bold">
              Income Projections
            </div>
          </div>
          <div className="mt-1 h-px bg-gradient-to-r from-purple-500/50 via-purple-400/20 to-transparent" />
        </div>
        <div className="grid grid-cols-2 gap-2 overflow-hidden flex-1">
          <StatProjectionItem
            label="Daily"
            value={formatCurrency(dailyEarnings)}
            period="24h"
            bgColor="bg-gradient-to-br from-base-800/80 to-base-900/80"
          />
          <StatProjectionItem
            label="Weekly"
            value={formatCurrency(weeklyEarnings)}
            period="7d"
            bgColor="bg-gradient-to-br from-base-800/80 to-base-900/80"
          />
          <StatProjectionItem
            label="Monthly"
            value={formatCurrency(monthlyEarnings)}
            period="30d"
            bgColor="bg-gradient-to-br from-base-800/80 to-base-900/80"
            highlight={true}
          />
          <StatProjectionItem
            label="Services"
            value={subscriptions.toString()}
            period="active"
            bgColor="bg-gradient-to-br from-base-800/80 to-base-900/80"
          />
        </div>
      </CornerGlowCard>
    );
  }
);
CyberpunkStatsCard.displayName = 'CyberpunkStatsCard';

const CyberpunkProfitCard: React.FC<CyberpunkProfitCardProps> = React.memo(
  ({ profit, onClaimClick, isClaiming }) => {
    const hasProfit = profit > 0.0001;

    return (
      <CornerGlowCard
        borderColorClass="border-yellow-500/20"
        shadowColorClass="shadow-yellow-500/15"
        glowColor="#eab308"
        glowPosition="top-right"
      >
        <div className="mb-2">
          <div className="flex items-center gap-2">
            <FaCoins className="text-yellow-400 text-sm" />
            <div className="text-sm uppercase tracking-wider text-yellow-300 font-bold">
              Earnings Hub
            </div>
          </div>
          <div className="mt-1 h-px bg-gradient-to-r from-yellow-500/50 via-yellow-400/20 to-transparent" />
        </div>
        <div className="flex-1 flex flex-col items-center justify-center text-center py-2 overflow-hidden">
          {hasProfit ? (
            <>
              <motion.div
                className="text-2xl sm:text-3xl font-bold text-white mb-1"
                initial={{ scale: 0.8 }}
                animate={{ scale: 1, transition: { delay: 0.6, duration: 0.5 } }}
              >
                <span className="text-yellow-300">{formatCurrency(profit)}</span>
              </motion.div>
              <div className="text-white/70 text-xs mb-3">Available to claim</div>
              <motion.button
                type="button"
                onClick={onClaimClick}
                disabled={isClaiming}
                className={`px-4 py-2 bg-gradient-to-r from-yellow-500 to-amber-600 text-white font-medium text-sm hover:from-yellow-400 hover:to-amber-500 shadow-[0_0_15px_rgba(234,179,8,0.3)] hover:shadow-[0_0_20px_rgba(234,179,8,0.5)] transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none disabled:hover:scale-100 disabled:hover:from-yellow-500 disabled:hover:to-amber-600`}
                style={{ clipPath: 'polygon(0 0, 100% 0, 95% 100%, 5% 100%)' }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="flex items-center justify-center gap-2">
                  {isClaiming ? <LoadingSpinner size="sm" /> : <FaCoins />}
                  {isClaiming ? 'Claiming...' : 'CLAIM NOW'}
                </span>
              </motion.button>
            </>
          ) : (
            <>
              <div className="text-4xl mb-2 opacity-40">💸</div>
              <div className="text-white/60 text-sm mb-1">No Profit Yet</div>
              <div className="text-white/50 text-xs">Upgrade cards to earn</div>
            </>
          )}
        </div>
      </CornerGlowCard>
    );
  }
);
CyberpunkProfitCard.displayName = 'CyberpunkProfitCard';

// --- Main HomeTab Component ---
const HomeTab: React.FC<HomeTabPageProps> = ({
  onTabChange,
  user,
  dashboardStats,
  isLoadingDashboardStats = false,
  isLoadingUser = false,
}) => {
  const isLowEnd = useMemo(() => isLowEndDevice(), []);
  const [liveProfit, setLiveProfit] = useState(dashboardStats?.total_accumulated_card_profit || 0);
  const [lastStatsUpdate, setLastStatsUpdate] = useState(Date.now());
  const profitIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const homeTabMarkerRef = useRef<HTMLDivElement>(null);
  const claimAllProfitsMutation = useClaimAllProfitsMutation();
  const isClaimingAll = claimAllProfitsMutation.isPending;

  useEffect(() => {
    if (!dashboardStats) return;
    const hourlyRate = dashboardStats.total_passive_hourly_income ?? 0;
    const baseProfit = dashboardStats.total_accumulated_card_profit ?? 0;
    if (lastStatsUpdate > 0 && hourlyRate > 0) {
      const now = Date.now();
      const effectiveLastUpdate = Math.min(now, lastStatsUpdate);
      const secondsElapsedSinceFetch = (now - effectiveLastUpdate) / 1000;
      const profitSinceFetch = (hourlyRate / 3600) * Math.max(0, secondsElapsedSinceFetch);
      setLiveProfit(baseProfit + profitSinceFetch);
    } else {
      setLiveProfit(baseProfit);
    }
  }, [dashboardStats, lastStatsUpdate]);

  useEffect(() => {
    if (dashboardStats) setLastStatsUpdate(Date.now());
  }, [dashboardStats]);

  const hourlyProfit = useMemo(
    () => dashboardStats?.total_passive_hourly_income || 0,
    [dashboardStats]
  );
  const updateLiveProfit = useCallback(() => {
    const profitIncrement = hourlyProfit / 36000;
    if (profitIncrement > 0) {
      setLiveProfit((prev: number) => prev + profitIncrement);
    }
  }, [hourlyProfit]);

  useEffect(() => {
    if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);
    if (hourlyProfit > 0) {
      profitIntervalRef.current = setInterval(updateLiveProfit, 100);
    }
    return () => {
      if (profitIntervalRef.current) clearInterval(profitIntervalRef.current);
    };
  }, [hourlyProfit, updateLiveProfit]);

  const handleCardClaim = useCallback(
    async (e?: React.MouseEvent) => {
      if (e) e.preventDefault();
      if (isClaimingAll) return;
      try {
        await claimAllProfitsMutation.mutateAsync();
      } catch (err) {}
    },
    [claimAllProfitsMutation, isClaimingAll]
  );

  const currentBalance = useMemo(() => dashboardStats?.wallet_balance ?? 0, [dashboardStats]);
  const avatarFilename = user?.telegram_photo_url
    ? `${user.telegram_photo_url}.webp`
    : 'Lovely Angel.webp';

  if (isLoadingDashboardStats || isLoadingUser) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner />
      </div>
    );
  }

  if (!dashboardStats || !user) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-gray-400">No dashboard data available.</p>
      </div>
    );
  }

  return (
    <div ref={homeTabMarkerRef} className="home-tab-marker relative min-h-[60vh]">
      <motion.div
        key="main-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1, transition: { duration: 0.5 } }}
        className="relative z-10"
      >
        <Section className="!mb-4">
          <div className="text-center">
            <h1 className="text-xl sm:text-2xl font-bold text-white tracking-wide font-display">
              Welcome back, <span className="text-purple-300">{user?.username || 'User'}</span>!
            </h1>
            <p className="text-sm text-white/70 mt-1 mb-3">Your digital command center.</p>
            <div className="mt-3 w-full max-w-xs sm:max-w-xs mx-auto">
              <CyberpunkStatDisplay
                label="Balance"
                value={formatCurrency(currentBalance)}
                icon={<FaCoins />}
                color="purple"
                animate={false}
              />
            </div>
          </div>
        </Section>

        <Section className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0, transition: { delay: 0.2, duration: 0.6 } }}
          >
            <CyberpunkProfitCard
              profit={liveProfit}
              onClaimClick={handleCardClaim}
              isClaiming={isClaimingAll}
            />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0, transition: { delay: 0.3, duration: 0.6 } }}
          >
            <CyberpunkStatsCard
              subscriptions={dashboardStats?.active_subscriptions ?? 0}
              balance={currentBalance}
              hourlyRate={hourlyProfit}
            />
          </motion.div>
        </Section>

        <Section title="Quick Access">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3">
            <motion.button
              onClick={() => onTabChange?.('earn')}
              className="bg-gradient-to-br from-base-800/70 to-base-900/70 p-3 rounded-lg text-center border border-white/10 hover:border-purple-500/50 hover:bg-base-800/90 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0, transition: { delay: 0.4 } }}
            >
              <FaCoins className="mx-auto text-purple-400 text-xl mb-1" />
              <span className="text-xs font-medium text-white/80 block">Manage Cards</span>
            </motion.button>
            <motion.button
              onClick={() => onTabChange?.('premium')}
              className="bg-gradient-to-br from-base-800/70 to-base-900/70 p-3 rounded-lg text-center border border-white/10 hover:border-purple-500/50 hover:bg-base-800/90 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0, transition: { delay: 0.5 } }}
            >
              <FaBolt className="mx-auto text-purple-400 text-xl mb-1" />
              <span className="text-xs font-medium text-white/80 block">VPN Services</span>
            </motion.button>
            <motion.button
              onClick={() => onTabChange?.('wallet')}
              className="bg-gradient-to-br from-base-800/70 to-base-900/70 p-3 rounded-lg text-center border border-white/10 hover:border-purple-500/50 hover:bg-base-800/90 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0, transition: { delay: 0.6 } }}
            >
              <svg
                className="w-5 h-5 mx-auto text-purple-400 mb-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
              <span className="text-xs font-medium text-white/80 block">Wallet</span>
            </motion.button>
            <motion.button
              onClick={() => onTabChange?.('friends')}
              className="bg-gradient-to-br from-base-800/70 to-base-900/70 p-3 rounded-lg text-center border border-white/10 hover:border-purple-500/50 hover:bg-base-800/90 transition-all duration-300"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0, transition: { delay: 0.7 } }}
            >
              <svg
                className="w-5 h-5 mx-auto text-purple-400 mb-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              <span className="text-xs font-medium text-white/80 block">Friends</span>
            </motion.button>
          </div>
        </Section>
      </motion.div>
    </div>
  );
};

export default HomeTab;
