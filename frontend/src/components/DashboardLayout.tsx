/**
 * @file DashboardLayout.tsx
 * @description Main layout component for the dashboard, handling navigation, background effects,
 *              and rendering the active tab component while keeping others mounted.
 */

// --- Imports ---
import React, { useRef, useEffect, useState, useMemo, useCallback, Suspense, lazy } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation, useNavigate, Outlet } from 'react-router-dom';
import BottomNavigation from './BottomNavigation';
import DashboardBackground from './DashboardBackground';
import Header from './Header';
import {
  useTelegramViewport,
  getViewportHeight,
  tg,
  disableVerticalSwipes,
  /* enableVerticalSwipes, */ useTelegramSafeArea,
  useAppVisibility,
} from '../utils/telegram'; // enableVerticalSwipes might not be needed
import { useAudio } from '../hooks/use_audio';
import { TabId } from '../types';
import { useCurrentUserQuery } from '../hooks/authHooks';
import { useDashboardStatsQuery, useUserTransactionsQuery } from '../hooks/dashboardHooks';
import { useUnifiedCardsQuery, useClaimAllProfitsMutation } from '../hooks/cardHooks';
import { useTasksQuery } from '../hooks/taskHooks';
import {
  useVpnSubscriptionsQuery,
  useVpnPackagesQuery,
  useVpnPanelsQuery,
} from '../hooks/vpnHooks';
import { useReferralInfoQuery, useReferredUsersQuery } from '../hooks/referralHooks';
import EarnTab from './EarnTab';
import FriendsTab from './FriendsTab';
import WalletTabPage from './WalletTabPage';
import PremiumTab from './PremiumTab';
import WelcomeBackSplash from './WelcomeBackSplash';
import { toast } from 'react-hot-toast'; // Added import for toast
import { useUiStore } from '../stores/uiStore';
import PerformanceMonitor from './PerformanceMonitor';

// Lazy load HomeTabPage to match the routes configuration
const HomeTabPage = lazy(() => import('./HomeTabPage'));

// DEVELOPMENT MODE OPTIMIZATION: Skip heavy components in dev
const isDevelopment = import.meta.env.DEV;
const isMinimalMode = import.meta.env.VITE_MINIMAL === 'true';

// For WebSocket readyState constants
const WS_CONNECTING = 0;
const WS_OPEN = 1;
const WS_CLOSING = 2;
const WS_CLOSED = 3;

// --- Type Definition ---
export type BottomNavTabId = 'home' | 'earn' | 'friends' | 'wallet' | 'premium';

// --- Enhanced Loading Component ---
const SimpleLoadingScreen: React.FC<{ isVisible: boolean }> = ({ isVisible }) => {
  const [loadingText, setLoadingText] = useState('Loading dashboard...');
  const [dots, setDots] = useState('');

  useEffect(() => {
    if (!isVisible) return;

    const textInterval = setInterval(() => {
      setLoadingText(prev => {
        const texts = [
          'Loading dashboard...',
          'Connecting to server...',
          'Preparing your data...',
          'Almost ready...'
        ];
        const currentIndex = texts.indexOf(prev);
        return texts[(currentIndex + 1) % texts.length];
      });
    }, 2000);

    const dotsInterval = setInterval(() => {
      setDots(prev => prev.length >= 3 ? '' : prev + '.');
    }, 500);

    return () => {
      clearInterval(textInterval);
      clearInterval(dotsInterval);
    };
  }, [isVisible]);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-purple-900"
    >
      <div className="text-center">
        <motion.div 
          className="text-4xl mb-4"
          animate={{ 
            scale: [1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          🚀
        </motion.div>
        <h2 className="text-xl font-semibold text-white mb-4">VIPVerse</h2>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
        <p className="text-purple-300/80 text-sm">
          {loadingText}{dots}
        </p>
        <div className="mt-4 text-xs text-purple-400/60">
          If this takes too long, try refreshing the page
        </div>
      </div>
    </motion.div>
  );
};

// --- Component Mapping ---
const tabComponents: Record<BottomNavTabId, React.ComponentType<any>> = {
  home: HomeTabPage,
  earn: EarnTab,
  friends: FriendsTab,
  wallet: WalletTabPage,
  premium: PremiumTab,
};

const ALL_TABS: BottomNavTabId[] = ['home', 'earn', 'friends', 'wallet', 'premium'];

// --- Tab Component ---
interface TabProps {
  tabId: BottomNavTabId;
  isActive: boolean;
  component: React.ComponentType<any>;
  componentProps?: Record<string, any>;
}

const Tab: React.FC<TabProps> = React.memo(
  ({ tabId, isActive, component: Component, componentProps = {} }) => {
    const TabContent = () => {
      if (tabId === 'home') {
        // HomeTabPage is lazy-loaded, so wrap with Suspense
        return (
          <Suspense fallback={
            <div className="flex justify-center items-center h-[200px]">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-purple-500"></div>
            </div>
          }>
            <Component {...componentProps} />
          </Suspense>
        );
      }
      return <Component {...componentProps} />;
    };

    return (
      <div
        data-tab-id={tabId}
        style={{
          display: isActive ? 'block' : 'none',
          height: '100%',
          width: '100%',
          opacity: isActive ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
        }}
        className="overflow-auto overscroll-contain px-4"
      >
        <div className="flex flex-col pb-4">
          {Component ? (
            <TabContent />
          ) : (
            <div>Error: Component for {tabId} not found.</div>
          )}
        </div>
      </div>
    );
  }
);

Tab.displayName = 'Tab';

// --- Prop Interface ---
interface DashboardLayoutProps {
  loading?: boolean;
}

// --- Main Component Definition ---
const DashboardLayoutComponent: React.FC<DashboardLayoutProps> = ({ loading }) => {
  // --- Hooks Initialization ---
  const location = useLocation();
  const navigate = useNavigate();
  const {
    data: user,
    isLoading: isUserQueryLoading,
    isSuccess: isAuthSuccess,
    isError: isAuthError,
    error: authError
  } = useCurrentUserQuery();

  // Get UI states from unified auth store
  const showWelcomeBackSplash = useUiStore(state => state.showWelcomeBackSplash);
  const lastSplashCheck = useUiStore(state => state.lastSplashCheck);
  const setShowWelcomeBackSplash = useUiStore(state => state.setShowWelcomeBackSplash);

  const safeAreaInsets = useTelegramSafeArea();
  useAudio({ url: '/music/start.mp3', loop: false, autoPlay: true });

  // --- Simplified Loading State ---
  const [isInitialDataLoaded, setIsInitialDataLoaded] = useState(false);

  // --- App Visibility Hook ---
  const [isAppCurrentlyVisible, isAppCurrentlyMinimized] = useAppVisibility();

  useEffect(() => {
    // App is backgrounded if it's not visible or if it's minimized
    const appIsEffectivelyBackgrounded = !isAppCurrentlyVisible || isAppCurrentlyMinimized;
    useUiStore.getState().setIsBackgrounded(appIsEffectivelyBackgrounded);
  }, [isAppCurrentlyVisible, isAppCurrentlyMinimized]);

  // --- State Derivation ---
  const activeTab = useMemo<BottomNavTabId>(() => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const startIndex = pathSegments[0] === 'dashboard' ? 1 : 0;
    const tabId = pathSegments[startIndex] as BottomNavTabId;
    return ALL_TABS.includes(tabId) ? tabId : 'home';
  }, [location.pathname]);

  // --- TanStack Query Hooks ---
  const {
    data: dashboardStats,
    isLoading: isLoadingDashboardStats,
    isFetching: isFetchingDashboardStats,
    isSuccess: isDashboardStatsSuccess,
  } = useDashboardStatsQuery({ enabled: isAuthSuccess && !!user });

  const {
    data: unifiedCards,
    isLoading: isLoadingUnifiedCards,
    isFetching: isFetchingUnifiedCards,
    isSuccess: isUnifiedCardsSuccess,
  } = useUnifiedCardsQuery({ enabled: isAuthSuccess && !!user, staleTime: 60000 });

  const {
    data: tasks,
    isLoading: isLoadingTasks,
    isFetching: isFetchingTasks,
    isSuccess: isTasksSuccess,
  } = useTasksQuery({ enabled: isAuthSuccess && !!user && activeTab === 'earn', staleTime: 60000 });

  const {
    data: vpnSubscriptions,
    isLoading: isLoadingVpnSubscriptions,
    isFetching: isFetchingVpnSubscriptions,
    isSuccess: isVpnSubscriptionsSuccess,
  } = useVpnSubscriptionsQuery({ enabled: isAuthSuccess && !!user && activeTab === 'premium' });

  const { data: vpnPackages, isLoading: isLoadingVpnPackages } = useVpnPackagesQuery({
    enabled: isAuthSuccess && !!user && activeTab === 'premium',
  });
  const { data: vpnPanels, isLoading: isLoadingVpnPanels } = useVpnPanelsQuery({
    enabled: isAuthSuccess && !!user && activeTab === 'premium',
  });

  const { data: userTransactions, isLoading: isLoadingTransactionsHook } = useUserTransactionsQuery(
    { enabled: isAuthSuccess && !!user && activeTab === 'wallet' }
  );

  const { data: referralInfo, isLoading: isLoadingReferralInfo } = useReferralInfoQuery({
    enabled: isAuthSuccess && !!user && activeTab === 'friends',
  });
  const { data: referredUsers, isLoading: isLoadingReferredUsers } = useReferredUsersQuery({
    enabled: isAuthSuccess && !!user && activeTab === 'friends',
  });

  const claimAllProfitsMutation = useClaimAllProfitsMutation();

  // --- Local State and Refs ---
  const [viewportHeight, setViewportHeight] = useState(getViewportHeight());
  const mainContentRef = useRef<HTMLDivElement>(null);

  // --- Custom Hooks ---
  useTelegramViewport(); // Manages viewport changes from Telegram

  // --- Simplified Loading State Management ---
  useEffect(() => {
    // More robust loading state management
    // Don't wait indefinitely for dashboard stats - set a timeout
    const loadingTimeout = setTimeout(() => {
      if (!isInitialDataLoaded) {
        console.log('[DashboardLayout] Loading timeout reached - forcing load completion');
        setIsInitialDataLoaded(true);
      }
    }, 10000); // 10 second timeout

    // Only wait for authentication to be resolved
    // Dashboard stats can load in the background
    if (isAuthSuccess && !!user && !isInitialDataLoaded) {
      console.log('[DashboardLayout] Auth successful - hiding loading screen');
      clearTimeout(loadingTimeout);
      setIsInitialDataLoaded(true);
    }

    // If auth fails or user is null, also hide loading
    if (!isUserQueryLoading && !user && !isInitialDataLoaded) {
      console.log('[DashboardLayout] Auth failed or no user - hiding loading screen');
      clearTimeout(loadingTimeout);
      setIsInitialDataLoaded(true);
    }

    return () => clearTimeout(loadingTimeout);
  }, [isAuthSuccess, user, isUserQueryLoading, isInitialDataLoaded]);

  // --- Callbacks ---
  const setViewportHeightDebounced = useCallback((height: number) => {
    setViewportHeight(prev => {
      if (Math.abs(prev - height) < 10) return prev;
      return height;
    });
  }, []);

  const handleResize = useCallback(() => {
    const height = tg?.viewportStableHeight || tg?.viewportHeight || window.innerHeight;
    setViewportHeightDebounced(height);

    if (tg && !tg.isExpanded) {
      tg.expand();
    }
  }, [setViewportHeightDebounced]);

  // --- Effect Hooks ---
  useEffect(() => {
    disableVerticalSwipes();
  }, []);

  useEffect(() => {
    handleResize();

    if (tg) {
      tg.onEvent('viewportChanged', handleResize);
    }
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      if (tg) {
        tg.offEvent('viewportChanged', handleResize);
      }
    };
  }, [handleResize]);

  // --- Memoized Elements ---
  const backgroundElements = useMemo(
    () => (
      <div className="absolute inset-0 will-change-transform">
        <div className="absolute inset-0 bg-gradient-radial from-purple-500/5 via-transparent to-transparent opacity-80 transform-gpu" />
        <div className="absolute bottom-0 inset-x-0 h-2/3 bg-gradient-to-t from-black/50 via-purple-900/20 to-transparent transform-gpu" />
        <div className="absolute inset-y-0 left-0 w-1/3 bg-gradient-to-r from-purple-900/30 to-transparent opacity-40 transform-gpu" />
        <div className="absolute inset-y-0 right-0 w-1/3 bg-gradient-to-l from-purple-900/30 to-transparent opacity-40 transform-gpu" />
      </div>
    ),
    []
  );

  const headerHeight = 64;
  const bottomNavHeight = 64;

  const containerStyle = useMemo(
    () => ({
      height: viewportHeight,
      maxHeight: viewportHeight,
      paddingTop: safeAreaInsets.top > 0 ? `${safeAreaInsets.top}px` : '0',
      paddingBottom: safeAreaInsets.bottom > 0 ? `${safeAreaInsets.bottom}px` : '0',
    }),
    [viewportHeight, safeAreaInsets]
  );

  // --- Event Handlers ---
  const handleTabChange = useCallback(
    (tabId: BottomNavTabId) => {
      if (tabId === activeTab) return;
      navigate(`/dashboard/${tabId}`);
    },
    [navigate, activeTab]
  );

  // Memoize the tabs content to prevent unnecessary re-renders
  const tabsContent = useMemo(() => {
    return ALL_TABS.map(tabId => {
      const Component = tabComponents[tabId];
      const isActive = activeTab === tabId;
      let componentProps: Record<string, any> = {};
      componentProps.user = user;
      componentProps.onTabChange = handleTabChange;
      if (tabId === 'home') {
        componentProps.dashboardStats = dashboardStats;
        componentProps.isLoadingDashboardStats =
          isLoadingDashboardStats || isFetchingDashboardStats;
        componentProps.isLoadingUser = isUserQueryLoading;
      } else if (tabId === 'earn') {
        componentProps.cardTabProps = {
          unifiedCards: unifiedCards ?? [],
          loading: isLoadingUnifiedCards || isFetchingUnifiedCards,
          processingCardId: null,
          isClaimingAll: false,
          totalAccumulatedProfit: (unifiedCards ?? []).reduce(
            (sum, card) =>
              sum + (card.is_owned && card.user_card ? card.user_card.accumulated_profit : 0),
            0
          ),
          totalHourlyProfit: (unifiedCards ?? []).reduce(
            (sum, card) =>
              sum + (card.is_owned && card.user_card ? card.user_card.current_hourly_profit : 0),
            0
          ),
          onClaimProfit: () => {},
          onUpgradeCard: async () => {},
          onPurchaseCard: () => {},
          onCardSelect: () => {},
        };
        componentProps.taskTabProps = {
          tasks: tasks ?? [],
          loading: isLoadingTasks || isFetchingTasks,
          onTaskSelect: () => {},
        };
        componentProps.isLoadingTasks = isLoadingTasks || isFetchingTasks;
        componentProps.isLoadingCards = isLoadingUnifiedCards || isFetchingUnifiedCards;
        componentProps.user = user;
        componentProps.onTabChange = handleTabChange;
      } else if (tabId === 'friends') {
        componentProps.referralInfo = referralInfo;
        componentProps.referredUsers = referredUsers;
        componentProps.isLoadingReferrals = isLoadingReferralInfo || isLoadingReferredUsers;
      } else if (tabId === 'wallet') {
        componentProps.transactions = userTransactions;
        componentProps.isLoadingTransactions = isLoadingTransactionsHook;
      } else if (tabId === 'premium') {
        componentProps.vpnSubscriptions = vpnSubscriptions;
        componentProps.vpnPackages = vpnPackages;
        componentProps.vpnPanels = vpnPanels;
        componentProps.isLoadingPremium =
          isLoadingVpnSubscriptions || isLoadingVpnPackages || isLoadingVpnPanels;
      }
      return (
        <Tab
          key={tabId}
          tabId={tabId}
          isActive={isActive}
          component={Component}
          componentProps={componentProps}
        />
      );
    });
  }, [
    activeTab,
    handleTabChange,
    user,
    dashboardStats,
    tasks,
    unifiedCards,
    isLoadingTasks,
    isFetchingTasks,
    isLoadingUnifiedCards,
    isFetchingUnifiedCards,
    referralInfo,
    referredUsers,
    isLoadingReferralInfo,
    isLoadingReferredUsers,
    userTransactions,
    isLoadingTransactionsHook,
    vpnSubscriptions,
    vpnPackages,
    vpnPanels,
    isLoadingVpnSubscriptions,
    isLoadingVpnPackages,
    isLoadingVpnPanels,
    isLoadingDashboardStats,
    isFetchingDashboardStats,
    isUserQueryLoading,
  ]);

  const userName = user?.first_name || user?.username;

  // Calculate accumulated profit and last claim time from unifiedCards
  const { accumulatedProfit, lastClaimTime } = useMemo(() => {
    if (!unifiedCards) return { accumulatedProfit: 0, lastClaimTime: 0 };
    let totalProfit = 0;
    let maxClaimTime = 0;
    unifiedCards.forEach(card => {
      if (card.is_owned && card.user_card) {
        totalProfit += card.user_card.accumulated_profit || 0;
        const claimTimestamp = new Date(card.user_card.last_profit_claim).getTime();
        if (claimTimestamp > maxClaimTime) {
          maxClaimTime = claimTimestamp;
        }
      }
    });
    return { accumulatedProfit: totalProfit, lastClaimTime: maxClaimTime };
  }, [unifiedCards]);

  // Show splash if profit > 0, last claim < 3h ago, and not shown in last 10min
  useEffect(() => {
    const now = Date.now();
    const cardsAvailable = !!unifiedCards;

    if (
      cardsAvailable &&
      accumulatedProfit > 0 &&
      (lastClaimTime === 0 || now - lastClaimTime > 60 * 1000) &&
      !showWelcomeBackSplash &&
      now - lastSplashCheck > 10 * 60 * 1000
    ) {
      setShowWelcomeBackSplash(true);
    }
  }, [
    accumulatedProfit,
    lastClaimTime,
    showWelcomeBackSplash,
    lastSplashCheck,
    setShowWelcomeBackSplash,
    unifiedCards,
  ]);

  // Claim handler
  const handleClaim = async () => {
    try {
      await claimAllProfitsMutation.mutateAsync();
      setShowWelcomeBackSplash(false);
    } catch (error) {
      console.error('Failed to claim profits:', error);
      toast.error('Failed to claim profits. Please try again.');
    }
  };

  const handleSplashDismiss = useCallback(() => {
    setShowWelcomeBackSplash(false);
  }, [setShowWelcomeBackSplash]);

  // Show loading screen during initial load - only for auth, with timeout protection
  const showLoadingScreen = !isInitialDataLoaded && (isUserQueryLoading || (!user && !isAuthSuccess));

  // Add debug logging for auth state
  useEffect(() => {
    console.log('[DashboardLayout] Auth state:', {
      isUserQueryLoading,
      isAuthSuccess,
      isAuthError,
      hasUser: !!user,
      error: authError?.message,
      isInitialDataLoaded
    });
  }, [isUserQueryLoading, isAuthSuccess, isAuthError, user, authError, isInitialDataLoaded]);

  return (
    <>
      <SimpleLoadingScreen isVisible={showLoadingScreen} />
      
      <div
        className="relative flex flex-col overflow-hidden bg-gradient-to-br from-[#050507] via-[#0a0018] to-[#1a0038]"
        style={containerStyle}
      >
        {backgroundElements}

        <div
          className="absolute left-0 inset-y-0 w-4 z-20 pointer-events-none bg-gradient-to-r from-black/50 via-black/15 to-transparent"
          style={{
            top: headerHeight + safeAreaInsets.top,
            bottom: bottomNavHeight + safeAreaInsets.bottom,
          }}
        />

        <div
          className="absolute right-0 inset-y-0 w-4 z-20 pointer-events-none bg-gradient-to-l from-black/50 via-black/15 to-transparent"
          style={{
            top: headerHeight + safeAreaInsets.top,
            bottom: bottomNavHeight + safeAreaInsets.bottom,
          }}
        />

        <div className="relative flex flex-col h-full">
          <Header
            activeTab={activeTab}
            className="fixed inset-x-0 z-10 h-[64px] bg-black/70 backdrop-blur-md shadow-lg shadow-black/40"
            style={{
              top: safeAreaInsets.top,
            }}
          />

          <main
            ref={mainContentRef}
            className="flex-1 overflow-hidden relative"
            style={{
              paddingTop: `${headerHeight}px`,
              paddingBottom: `${bottomNavHeight}px`,
            }}
          >
            <div className="h-full flex flex-col">{tabsContent}</div>
          </main>

          <BottomNavigation
            activeTab={activeTab}
            onTabChange={handleTabChange}
            className="fixed bottom-0 inset-x-0 z-10 h-[64px] bg-black/80 backdrop-blur-md shadow-[0_-4px_16px_rgba(0,0,0,0.5)]"
            style={{
              bottom: safeAreaInsets.bottom,
            }}
          />
        </div>

        {/* Splash overlay */}
        <AnimatePresence>
          {showWelcomeBackSplash && (
            <WelcomeBackSplash
              isOpen={showWelcomeBackSplash}
              userName={userName}
              accumulatedProfit={accumulatedProfit}
              onClaim={handleClaim}
              onDismiss={handleSplashDismiss}
              isClaiming={claimAllProfitsMutation.isPending}
            />
          )}
        </AnimatePresence>

        {/* Performance Monitor (dev only) */}
        <PerformanceMonitor />
      </div>
    </>
  );
};

export default DashboardLayoutComponent;
