import React, { useId } from 'react';

interface CornerGlowCardProps {
  children: React.ReactNode;
  borderColorClass?: string; // Prop kept for potential future use, but not applied
  shadowColorClass?: string; // e.g., 'shadow-yellow-500/15'
  glowColor?: string; // Hex color e.g., '#eab308'
  glowPosition?: 'top-right' | 'bottom-left';
  className?: string;
}

function CornerGlowCard({
  children,
  borderColorClass, // Removed default, prop no longer used for border
  shadowColorClass = 'shadow-purple-500/15',
  glowColor,
  glowPosition = 'bottom-left',
  className = '',
}: CornerGlowCardProps) {
  // Generate a unique ID for the SVG gradient to prevent conflicts
  const gradientId = useId();

  const glowPositionClasses = {
    'top-right': 'top-0 right-0 translate-x-8 -translate-y-8',
    'bottom-left': 'bottom-0 left-0 -translate-x-8 translate-y-8',
  };

  return (
    <div
      className={`relative bg-gradient-to-br from-black/70 via-base-900/60 to-base-900/80 rounded-xl overflow-hidden h-full backdrop-blur-sm flex flex-col min-h-0 shadow-lg ${shadowColorClass} ${className}`}
    >
      {/* Brighter Corner Glow */}
      {glowColor && (
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <svg
            className={`absolute h-40 w-40 opacity-80 ${glowPositionClasses[glowPosition]}`}
            viewBox="0 0 100 100"
          >
            <defs>
              <radialGradient id={gradientId} cx="50%" cy="50%" r="50%">
                <stop offset="0%" stopColor={glowColor} stopOpacity="0.5" />
                <stop offset="100%" stopColor={glowColor} stopOpacity="0" />
              </radialGradient>
            </defs>
            <circle cx="50" cy="50" r="50" fill={`url(#${gradientId})`} />
          </svg>
        </div>
      )}

      {/* Content Area */}
      {/* Added p-3 for padding consistency with original cards */}
      <div className="relative flex flex-col h-full flex-1 p-3">{children}</div>
    </div>
  );
}

export default CornerGlowCard;
