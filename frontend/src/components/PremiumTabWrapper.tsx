import React from 'react';
import PremiumTab from './PremiumTab';
import { useCurrentUserQuery } from '../hooks/authHooks';
import LoadingSpinner from './LoadingSpinner';

const PremiumTabWrapper: React.FC = () => {
  const { data: user, isLoading: isLoadingUser } = useCurrentUserQuery();

  if (isLoadingUser) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <PremiumTab
      isLoadingPremium={false}
      user={user || undefined}
    />
  );
};

export default PremiumTabWrapper; 