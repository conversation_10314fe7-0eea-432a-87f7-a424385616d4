import React, { useEffect, useState } from 'react';

interface PerformanceMetrics {
  authTime: number;
  dashboardTime: number;
  totalLoadTime: number;
  renderCount: number;
}

const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    authTime: 0,
    dashboardTime: 0,
    totalLoadTime: 0,
    renderCount: 0
  });
  const [startTime] = useState(Date.now());
  const [showMetrics, setShowMetrics] = useState(false);

  // Use a ref to track renders without causing re-renders
  const renderCountRef = React.useRef(0);

  // Only increment render count, don't update state
  renderCountRef.current += 1;

  useEffect(() => {
    // Track performance metrics
    const updateMetrics = () => {
      const now = Date.now();
      setMetrics(prev => ({
        ...prev,
        totalLoadTime: now - startTime,
        renderCount: renderCountRef.current
      }));
    };

    // Update metrics every 5 seconds instead of every second
    const interval = setInterval(updateMetrics, 5000);

    // Show metrics in development after 10 seconds
    const showTimer = setTimeout(() => {
      if (import.meta.env.DEV) {
        setShowMetrics(true);
      }
    }, 10000);

    return () => {
      clearInterval(interval);
      clearTimeout(showTimer);
    };
  }, [startTime]); // Only depend on startTime which never changes

  // Only show in development mode
  if (!import.meta.env.DEV || !showMetrics) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white text-xs p-2 rounded-lg backdrop-blur-sm z-50 max-w-xs">
      <div className="font-semibold mb-1">Performance Metrics</div>
      <div>Total Load: {(metrics.totalLoadTime / 1000).toFixed(1)}s</div>
      <div>Renders: {renderCountRef.current}</div>
      <div>Memory: {(() => {
        try {
          const mem = (performance as any).memory;
          return mem ? `${(mem.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB` : 'N/A';
        } catch {
          return 'N/A';
        }
      })()}</div>
      <button 
        onClick={() => setShowMetrics(false)}
        className="mt-1 text-purple-400 hover:text-purple-300"
      >
        Hide
      </button>
    </div>
  );
};

export default PerformanceMonitor; 