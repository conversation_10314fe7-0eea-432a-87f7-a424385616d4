import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useCurrentUserQuery } from '../hooks/authHooks';
import { useReferralInfoQuery, useReferredUsersQuery } from '../hooks/referralHooks';
import LoadingSpinner from './LoadingSpinner';
import { formatCurrency } from '../utils/format';
import { haptics } from '../utils/haptics';
import { QRCodeSVG } from 'qrcode.react';
import { showPopup } from '../utils/telegram';
import type { Telegram, TelegramWebApp, InlineQueryResultArticle } from '../types/telegram';
import Section from './layout/Section';
import Avatar3DViewer from './Avatar3DViewer';
import {
  getReferralInfo,
  createReferralMessagePlain,
  createReferralMessageHTML,
} from '../utils/referral';
import { useDashboardStatsQuery } from '../hooks/dashboardHooks';
import type { ReferralInfo, ReferredUser } from '../types/referral';
import type { User } from '../types';

interface FriendsTabProps {
  referralInfo?: ReferralInfo;
  referredUsers?: ReferredUser[];
  isLoadingReferrals: boolean;
  user?: User;
  onTabChange?: (tabId: string) => void;
}

const FriendsTab: React.FC<FriendsTabProps> = ({
  referralInfo,
  referredUsers = [],
  isLoadingReferrals,
  user,
  onTabChange,
}) => {
  const { data: stats, isLoading: statsLoading } = useDashboardStatsQuery();

  const [activeTab, setActiveTab] = useState<'stats' | 'share' | 'users'>('stats');

  const [showQR, setShowQR] = useState(false);

  // Get referral information using the utility function
  const { referralCode, referralLink } = useMemo(() => {
    if (!user || !referralInfo) return { referralCode: null, referralLink: null };
    return getReferralInfo(referralInfo, user);
  }, [referralInfo, user]);

  const handleShare = async () => {
    if (!referralLink) return;

    try {
      haptics.impact();
      const webapp = window.Telegram?.WebApp;
      if (!webapp) {
        throw new Error('Telegram WebApp is not available');
      }

      const userId = webapp.initDataUnsafe?.user?.id;
      if (!userId) {
        throw new Error('User ID not available');
      }

      // Create message using utility function
      const plainTextMessage = createReferralMessagePlain(referralLink);

      const inlineResult: InlineQueryResultArticle = {
        type: 'article',
        id: `ref_${Date.now()}`,
        title: 'Join VPN Service',
        description: 'Get secure, fast, and reliable VPN service',
        url: referralLink,
        input_message_content: {
          message_text: plainTextMessage,
          parse_mode: 'HTML', // Use HTML instead of MarkdownV2 for better compatibility
          disable_web_page_preview: false,
        },
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: 'Join Now',
                url: referralLink,
              },
            ],
          ],
        },
      };

      try {
        const preparedMessage = await (webapp as any).savePreparedInlineMessage({
          user_id: userId,
          result: inlineResult,
          allow_user_chats: true,
          allow_group_chats: true,
          allow_channel_chats: true,
        });

        const shared = await (webapp as any).shareMessage(preparedMessage.id);

        if (!shared) {
          throw new Error('Share failed');
        }
      } catch (shareError) {
        console.error('Share failed, trying fallback:', shareError);
        (webapp as any).switchInlineQuery(plainTextMessage, ['users', 'groups', 'channels']);
      }
    } catch (error) {
      console.error('Error sharing:', error);
      try {
        await navigator.clipboard.writeText(referralLink);
        showPopup({
          title: 'Link Copied',
          message: 'Referral link has been copied to clipboard!',
          buttons: [
            {
              id: 'ok',
              type: 'ok',
              text: 'OK',
            },
          ],
        });
      } catch (clipboardError) {
        showPopup({
          title: 'Sharing Failed',
          message: 'Please try copying the link manually.',
          buttons: [
            {
              id: 'ok',
              type: 'ok',
              text: 'OK',
            },
          ],
        });
      }
    }
  };

  const handleCopyLink = async () => {
    if (!referralLink) return;
    haptics.impact('light');
    try {
      await navigator.clipboard.writeText(referralLink);
      showPopup({
        title: 'Success',
        message: 'Referral link copied to clipboard!',
        buttons: [
          {
            id: 'ok-button',
            type: 'ok',
            text: 'OK',
          },
        ],
      });
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  // Calculate referral stats
  const totalReferrals = referralInfo?.total_referrals ?? 0;
  const activeReferrals = referralInfo?.active_referrals ?? 0;
  const totalEarnings = referralInfo?.total_earnings ?? 0;

  // Combined loading state for initial view
  const initialDataLoading =
    (isLoadingReferrals && !referralInfo) || (statsLoading && !stats) || !user;

  if (initialDataLoading) {
    return (
      <div className="flex justify-center items-center h-[200px]">
        <LoadingSpinner size="md" />
      </div>
    );
  }

  return (
    <div className="space-y-6 pb-4">
      {/* Tab Navigation */}
      <div className="flex bg-slate-800/40 rounded-xl p-1 mb-4">
        <button
          onClick={() => setActiveTab('stats')}
          className={`flex-1 py-2 rounded-lg text-sm font-medium transition-colors ${
            activeTab === 'stats'
              ? 'bg-purple-600/70 text-white'
              : 'text-slate-300 hover:bg-slate-700/50'
          }`}
        >
          Statistics
        </button>
        <button
          onClick={() => setActiveTab('share')}
          className={`flex-1 py-2 rounded-lg text-sm font-medium transition-colors ${
            activeTab === 'share'
              ? 'bg-purple-600/70 text-white'
              : 'text-slate-300 hover:bg-slate-700/50'
          }`}
        >
          Share
        </button>
        <button
          onClick={() => setActiveTab('users')}
          className={`flex-1 py-2 rounded-lg text-sm font-medium transition-colors ${
            activeTab === 'users'
              ? 'bg-purple-600/70 text-white'
              : 'text-slate-300 hover:bg-slate-700/50'
          }`}
        >
          Friends
        </button>
      </div>

      {activeTab === 'stats' ? (
        <>
          <Section title="Referral Stats">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gradient-to-br from-purple-900/30 to-purple-800/20 p-4 rounded-xl border border-purple-500/20"
              >
                <p className="text-sm text-purple-200">Total Friends</p>
                <p className="text-2xl font-bold text-white">{totalReferrals}</p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-gradient-to-br from-blue-900/30 to-blue-800/20 p-4 rounded-xl border border-blue-500/20"
              >
                <p className="text-sm text-blue-200">Active Friends</p>
                <p className="text-2xl font-bold text-white">{activeReferrals}</p>
                <p className="text-xs text-blue-300">Currently active users</p>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-gradient-to-br from-emerald-900/30 to-emerald-800/20 p-4 rounded-xl border border-emerald-500/20 mb-4"
            >
              <p className="text-sm text-emerald-200">Total Earnings</p>
              <p className="text-2xl font-bold text-white">{formatCurrency(totalEarnings)}</p>
              <p className="text-xs text-emerald-300">From referral rewards</p>
            </motion.div>

            <div className="bg-slate-800/40 p-4 rounded-xl border border-slate-500/20">
              <h3 className="text-sm font-medium text-slate-300 mb-2">Your Referral Code</h3>
              <div className="flex items-center justify-between bg-slate-700/40 p-2 rounded-lg">
                <p className="text-lg font-mono text-white tracking-wider">
                  {referralCode || 'N/A'}
                </p>
                <button
                  onClick={handleCopyLink}
                  disabled={!referralLink}
                  className="p-2 rounded-lg bg-purple-500/20 text-purple-200 hover:bg-purple-500/30 transition-colors disabled:opacity-50"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </Section>

          <Section title="How It Works">
            <div className="bg-slate-800/40 p-4 rounded-xl space-y-3">
              <div className="flex items-start gap-3">
                <div className="bg-purple-600/30 p-2 rounded-full text-white flex-shrink-0">1</div>
                <p className="text-sm text-slate-300">Share your referral link with friends</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="bg-purple-600/30 p-2 rounded-full text-white flex-shrink-0">2</div>
                <p className="text-sm text-slate-300">
                  When they join using your link, they're added to your referrals
                </p>
              </div>
              <div className="flex items-start gap-3">
                <div className="bg-purple-600/30 p-2 rounded-full text-white flex-shrink-0">3</div>
                <p className="text-sm text-slate-300">
                  You earn rewards for each active referral and when they make purchases
                </p>
              </div>
            </div>
          </Section>
        </>
      ) : activeTab === 'share' ? (
        <Section title="Share Your Referral Link">
          <div className="bg-gradient-to-br from-purple-900/20 to-transparent p-4 rounded-xl border border-purple-500/20 mb-4">
            <div className="flex justify-between items-center mb-4">
              <button className="text-xs text-purple-300 hover:text-purple-200 flex items-center gap-1">
                Refresh
              </button>
              <button
                onClick={() => setShowQR(!showQR)}
                className="p-2 rounded-lg bg-purple-500/20 text-purple-200 hover:bg-purple-500/30 transition-colors"
              >
                {showQR ? 'Hide QR' : 'Show QR'}
              </button>
            </div>

            <AnimatePresence>
              {showQR && referralLink && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="flex justify-center mb-4"
                >
                  <div className="p-4 bg-white rounded-xl">
                    <QRCodeSVG value={referralLink} size={200} level="H" includeMargin />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            <div className="flex gap-2">
              <button
                onClick={handleShare}
                className="flex-1 py-2 px-4 rounded-lg bg-gradient-to-r from-purple-600/80 to-purple-400/80 
                       text-white text-sm font-medium hover:from-purple-600/90 hover:to-purple-400/90 
                       transition-colors disabled:opacity-70"
              >
                Share Invite
              </button>
              <button
                onClick={handleCopyLink}
                className="py-2 px-4 rounded-lg bg-white/10 text-white/80 text-sm font-medium hover:bg-white/20 transition-colors disabled:opacity-70"
              >
                Copy Link
              </button>
            </div>
          </div>

          <div className="bg-slate-800/40 p-4 rounded-xl">
            <h3 className="text-sm font-medium text-slate-300 mb-2">Message Preview</h3>
            <div className="bg-slate-700/40 p-4 rounded-lg text-sm text-slate-300 space-y-2">
              <p>
                🌟 <strong>Join me on this amazing VPN service!</strong>
              </p>
              <p>
                Get secure, fast, and reliable VPN service. Use my referral link to get started:
              </p>
              <p className="text-blue-300 break-all">{referralLink || 'Loading link...'}</p>
            </div>
          </div>
        </Section>
      ) : (
        <Section title="Referred Friends">
          {isLoadingReferrals && !((referredUsers ?? []).length > 0) ? (
            <div className="flex justify-center py-8">
              <LoadingSpinner size="md" />
            </div>
          ) : !(referredUsers ?? []).length ? (
            <div className="bg-slate-800/40 p-6 rounded-xl text-center">
              <p className="text-slate-300 mb-2">You haven't referred any friends yet</p>
              <button
                onClick={() => setActiveTab('share')}
                className="px-4 py-2 rounded-lg bg-purple-600/70 text-white text-sm font-medium hover:bg-purple-600/80 transition-colors"
              >
                Share Your Link
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-sm font-medium text-slate-300">
                  {(referredUsers ?? []).length}{' '}
                  {(referredUsers ?? []).length === 1 ? 'Friend' : 'Friends'} Referred
                </h3>
                <button className="text-xs text-purple-300 hover:text-purple-200 flex items-center gap-1 disabled:opacity-50">
                  Refresh
                </button>
              </div>
              {(referredUsers ?? []).map((referredUser, index) => (
                <motion.div
                  key={`${referredUser.username}-${index}`}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className={`bg-slate-800/40 p-3 rounded-xl border ${
                    referredUser.isActive ? 'border-green-500/20' : 'border-slate-600/20'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="relative overflow-hidden rounded-full border-2 border-slate-700 w-10 h-10">
                      {referredUser.telegramPhotoUrl ? (
                        <Avatar3DViewer
                          filename={`${referredUser.telegramPhotoUrl}.webp`}
                          size={{ width: 40, height: 40 }}
                        />
                      ) : (
                        <div className="w-10 h-10 bg-purple-600/30 flex items-center justify-center text-white font-bold">
                          {referredUser.username.substring(0, 1).toUpperCase()}
                        </div>
                      )}
                      {referredUser.isActive && (
                        <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border border-slate-800"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="text-white font-medium">{referredUser.username}</p>
                          <p className="text-xs text-slate-400">
                            Joined: {new Date(referredUser.joinedAt).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-xs text-slate-300">
                            Spent:{' '}
                            <span className="text-blue-300">
                              ${formatCurrency(referredUser.totalSpent)}
                            </span>
                          </p>
                          <p className="text-xs text-emerald-300">
                            Earned:{' '}
                            <span className="text-emerald-300">
                              ${formatCurrency(referredUser.commissionEarned)}
                            </span>
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </Section>
      )}
    </div>
  );
};

export default FriendsTab;
