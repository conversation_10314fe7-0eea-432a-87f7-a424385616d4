import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="min-h-screen flex items-center justify-center bg-base-900 p-4"
        >
          <div className="w-full max-w-2xl bg-base-800/50 backdrop-blur-lg p-6 rounded-xl border border-white/10 space-y-4">
            <h2 className="text-2xl font-bold text-red-500">Application Error</h2>

            {/* Error Message */}
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white">Error Message:</h3>
              <pre className="bg-base-900/50 p-3 rounded-lg text-red-400 text-sm overflow-auto">
                {this.state.error?.message || 'Unknown error'}
              </pre>
            </div>

            {/* Stack Trace - Only in development */}
            {import.meta.env.DEV && this.state.error?.stack && (
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">Stack Trace:</h3>
                <pre className="bg-base-900/50 p-3 rounded-lg text-yellow-400/80 text-sm overflow-auto whitespace-pre-wrap">
                  {this.state.error.stack}
                </pre>
              </div>
            )}

            {/* Component Stack - Only in development */}
            {import.meta.env.DEV && this.state.errorInfo?.componentStack && (
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-white">Component Stack:</h3>
                <pre className="bg-base-900/50 p-3 rounded-lg text-blue-400/80 text-sm overflow-auto whitespace-pre-wrap">
                  {this.state.errorInfo.componentStack}
                </pre>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-3 pt-4">
              <button
                onClick={this.handleRetry}
                className="px-4 py-2 bg-accent-500/20 hover:bg-accent-500/30 text-accent-400 
                         rounded-lg border border-accent-500/30 transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-white/5 hover:bg-white/10 text-white/80 
                         rounded-lg border border-white/10 transition-colors"
              >
                Reload Page
              </button>
            </div>

            {/* Development Note */}
            {import.meta.env.DEV && (
              <p className="text-white/40 text-sm">
                Note: This detailed error information is only visible in development mode.
              </p>
            )}
          </div>
        </motion.div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
