/**
 * @file SceneSetup.tsx
 * @description R3F component for setting up camera, lighting, controls, and environment.
 */

// @ts-nocheck
import React, { useRef, useMemo, useEffect, useState, useCallback } from 'react';
import { OrbitControls, useTexture, Effects } from '@react-three/drei';
import * as THREE from 'three';
import { useThree, useFrame, extend, useLoader } from '@react-three/fiber';
import { UnrealBloomPass } from 'three/examples/jsm/postprocessing/UnrealBloomPass';
import { TextureLoader } from 'three';

// Extend UnrealBloomPass for JSX usage
extend({ UnrealBloomPass });

// Extend Three.js objects for JSX usage
extend(THREE);

// Props interface
interface SceneSetupProps {
  isPerformanceMode: boolean;
  isAppVisible?: boolean;
}

const SceneSetup = React.memo(function SceneSetup({
  isPerformanceMode,
  isAppVisible = true,
}: SceneSetupProps): React.ReactElement {
  const { size, setFrameloop, invalidate } = useThree();
  const frameRateRef = useRef(60); // Default to 60fps
  const isActiveRef = useRef(true);
  const controlsRef = useRef<any>(null);
  const bloomRef = useRef<any>(null);
  const pulseTimeRef = useRef(0);
  const lastFrameTimeRef = useRef(0);

  // Detect if we're on a touch device - memoized
  const isTouchDevice = useMemo(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }, []);

  // Set frame rate based on performance mode - memoized
  const targetFrameRate = useMemo(() => {
    return isPerformanceMode ? 30 : 60;
  }, [isPerformanceMode]);

  // Update frame rate when performance mode changes
  useEffect(() => {
    frameRateRef.current = targetFrameRate;
  }, [targetFrameRate]);

  // Pause/resume rendering based on app visibility with better performance
  useEffect(() => {
    const shouldBeActive = isAppVisible && document.visibilityState !== 'hidden';
    isActiveRef.current = shouldBeActive;

    // Control the animation loop more efficiently
    if (!shouldBeActive) {
      setFrameloop('demand'); // Only render on demand (stops animation loop)
    } else {
      setFrameloop('always'); // Resume normal animation loop
      invalidate(); // Force a single frame render when resuming
    }
  }, [isAppVisible, setFrameloop, invalidate]);

  // Handle document visibility changes directly with cleanup
  useEffect(() => {
    const handleVisibilityChange = () => {
      const newState = document.visibilityState !== 'hidden';
      isActiveRef.current = newState && isAppVisible;

      if (document.visibilityState === 'hidden') {
        setFrameloop('demand');
      } else if (isAppVisible) {
        setFrameloop('always');
        invalidate(); // Force render when visible again
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isAppVisible, setFrameloop, invalidate]);

  // Configure OrbitControls after initialization - memoized configuration
  const controlsConfig = useMemo(() => {
    if (isTouchDevice) {
      return {
        touches: {
          ONE: THREE.TOUCH.ROTATE,
          TWO: THREE.TOUCH.DOLLY_PAN,
        },
        rotateSpeed: 0.9,
        panSpeed: 1.2,
        dampingFactor: isPerformanceMode ? 0.15 : 0.08,
      };
    } else {
      return {
        mouseButtons: {
          LEFT: THREE.MOUSE.ROTATE,
          MIDDLE: THREE.MOUSE.DOLLY,
          RIGHT: THREE.MOUSE.PAN,
        },
        rotateSpeed: 0.7,
        panSpeed: 1.0,
        dampingFactor: 0.1,
      };
    }
  }, [isTouchDevice, isPerformanceMode]);

  useEffect(() => {
    if (controlsRef.current) {
      Object.assign(controlsRef.current, controlsConfig);
      
      // Common settings for both device types
      controlsRef.current.enableRotate = true;
      controlsRef.current.minDistance = 5;
      controlsRef.current.maxPolarAngle = Math.PI - 0.1;
      controlsRef.current.minPolarAngle = 0.1;
      controlsRef.current.enableDamping = !isPerformanceMode;
    }
  }, [controlsConfig, isPerformanceMode]);

  // Load the background texture using drei's useTexture hook
  const backgroundTexture = useTexture('/img/bk.jpg');

  // Optimize texture in performance mode - memoized
  const optimizedTexture = useMemo(() => {
    if (backgroundTexture) {
      const texture = backgroundTexture.clone();
      texture.minFilter = isPerformanceMode
        ? THREE.NearestFilter
        : THREE.LinearMipmapLinearFilter;
      texture.magFilter = isPerformanceMode ? THREE.NearestFilter : THREE.LinearFilter;
      texture.generateMipmaps = !isPerformanceMode;
      texture.anisotropy = isPerformanceMode ? 1 : 4;
      return texture;
    }
    return backgroundTexture;
  }, [backgroundTexture, isPerformanceMode]);

  // Compute dynamic level of detail based on performance mode - memoized
  const sphereDetail = useMemo(
    () => ({
      segments: isPerformanceMode ? 16 : 32,
      skySphereSegments: isPerformanceMode ? 30 : 60,
      skySphereRings: isPerformanceMode ? 20 : 40,
    }),
    [isPerformanceMode]
  );

  // Initialize bloom with base values
  useEffect(() => {
    if (bloomRef.current && !isPerformanceMode) {
      // Set base parameters
      bloomRef.current.threshold = 0.05;
      bloomRef.current.strength = 0.5;
      bloomRef.current.radius = 0.4;
    }
  }, [isPerformanceMode]);

  // Optimized frame loop with throttling for better React 19 compatibility
  const frameCallback = useCallback((state, delta) => {
    const now = performance.now();
    const targetFrameTime = 1000 / frameRateRef.current;
    
    // Skip frame if we're rendering too fast or app is not active
    if (!isActiveRef.current || !isAppVisible) {
      return;
    }

    // Throttle frame rate in performance mode
    if (isPerformanceMode && now - lastFrameTimeRef.current < targetFrameTime) {
      return;
    }

    lastFrameTimeRef.current = now;

    // Only update bloom if not in performance mode and bloom is available
    if (bloomRef.current && !isPerformanceMode) {
      // Update pulse time
      pulseTimeRef.current += delta;

      // Calculate subtle pulse for bloom strength (0.4 to 0.6 range)
      const baseStrength = 0.5;
      const pulseAmount = 0.08;
      const pulseFrequency = 1.2;

      // Apply gentle pulse to bloom strength with smoothing
      const targetStrength =
        baseStrength + Math.sin(pulseTimeRef.current * pulseFrequency) * pulseAmount;
      bloomRef.current.strength = THREE.MathUtils.lerp(
        bloomRef.current.strength,
        targetStrength,
        delta * 1.5
      );

      // Reset after every full cycle (2π) to prevent accumulation errors
      if (pulseTimeRef.current > Math.PI * 2) {
        pulseTimeRef.current = 0;
      }
    }
  }, [isPerformanceMode, isAppVisible]);

  // Use frame hook with the memoized callback
  useFrame(frameCallback);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (optimizedTexture && optimizedTexture !== backgroundTexture) {
        optimizedTexture.dispose();
      }
    };
  }, [optimizedTexture, backgroundTexture]);

  return (
    <>
      {/* Ambient light with optimized intensity */}
      <ambientLight intensity={isPerformanceMode ? 0.4 : 0.3} />

      {/* Conditionally render directional light */}
      {!isPerformanceMode && <directionalLight position={[10, 20, 10]} intensity={0.5} />}

      {/* Planet layers with dynamic level of detail */}
      <group>
        {/* Core wireframe */}
        <mesh>
          <sphereGeometry args={[16, sphereDetail.segments, sphereDetail.segments]} />
          <meshBasicMaterial color="#ffffff" wireframe opacity={0.08} transparent />
        </mesh>

        {/* Inner subtle layer - skip in performance mode */}
        {!isPerformanceMode && (
          <mesh>
            <sphereGeometry args={[16.1, sphereDetail.segments, sphereDetail.segments]} />
            <meshBasicMaterial color="#ffffff" opacity={0.03} transparent side={THREE.BackSide} />
          </mesh>
        )}
      </group>

      {/* Skysphere Background with optimized geometry */}
      <mesh scale={[-1, 1, 1]}>
        <sphereGeometry args={[500, sphereDetail.skySphereSegments, sphereDetail.skySphereRings]} />
        <meshBasicMaterial
          map={optimizedTexture}
          side={THREE.BackSide}
          color="#888888"
          toneMapped={!isPerformanceMode}
        />
      </mesh>

      {/* OrbitControls with mobile support */}
      <OrbitControls
        ref={controlsRef}
        maxDistance={500}
        enableDamping={!isPerformanceMode}
        zoomSpeed={0.8}
        enablePan={true}
        keyPanSpeed={7.0}
        screenSpacePanning={true}
        autoRotate={false}
        minAzimuthAngle={-Infinity}
        maxAzimuthAngle={Infinity}
        maxPolarAngle={Math.PI - 0.2}
        minPolarAngle={0.2}
        enableZoom={true}
        enableRotate={true}
        makeDefault
      />

      {/* Bloom effect using UnrealBloomPass - only if not in performance mode */}
      {!isPerformanceMode && (
        <Effects>
          <unrealBloomPass
            ref={bloomRef}
            threshold={0.05}
            strength={0.5}
            radius={0.4}
            args={[new THREE.Vector2(size.width, size.height), 0.5, 0.4, 0.85]}
          />
        </Effects>
      )}
    </>
  );
});

export default SceneSetup;
