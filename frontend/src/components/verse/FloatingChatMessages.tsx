import React, { useMemo } from 'react';
import { ChatMessage } from '../../types/chat';
import { motion, AnimatePresence } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';

interface FloatingChatMessagesProps {
  messages: ChatMessage[];
}

const FloatingChatMessages: React.FC<FloatingChatMessagesProps> = ({ messages }) => {
  // Filter only public messages (redundant if parent filters, but safer)
  const publicMessages = useMemo(() => messages.filter(m => m.is_public), [messages]);

  return (
    <div
      // Changed positioning: bottom-4, right-4, smaller width (w-60)
      className="absolute bottom-4 right-4 w-60 z-10 pointer-events-none flex flex-col items-end"
      // Make container non-interactive, align items to the right
    >
      <AnimatePresence initial={false}>
        {publicMessages.map((message, index) => (
          <motion.div
            key={message.id}
            initial={{ opacity: 0, y: 10, scale: 0.95 }} // Adjusted initial animation for bottom position
            animate={{ opacity: 1 - (publicMessages.length - 1 - index) * 0.2, y: 0, scale: 1 }} // Slightly stronger fade
            exit={{ opacity: 0, y: 10, scale: 0.95 }} // Adjusted exit animation
            transition={{ duration: 0.2, ease: 'easeOut' }}
            // Even smaller padding (p-1), smaller margin (mb-1)
            className="bg-black/60 backdrop-blur-sm border border-white/10 rounded shadow text-white p-1 mb-1 max-w-full pointer-events-auto"
            // Allow clicks on individual messages
          >
            <div className="flex items-center justify-between text-[9px] mb-0.5">
              {' '}
              {/* Even smaller text */}
              <span className="font-medium text-gray-300 truncate mr-1">
                {message.sender_nickname || 'Anonymous'}
              </span>
              <span className="text-gray-400 flex-shrink-0">
                {formatDistanceToNow(new Date(message.created_at), {
                  addSuffix: true,
                  includeSeconds: true,
                })}{' '}
                {/* Added includeSeconds */}
              </span>
            </div>
            <p className="text-[11px] text-gray-100 break-words">{message.content}</p>{' '}
            {/* Smaller content text */}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default FloatingChatMessages;
