import React, { useMemo, memo, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaHome, FaCoins, FaBox, FaBolt, FaWallet, FaUserFriends, FaGlobe } from 'react-icons/fa';
import { BottomNavTabId } from './DashboardLayout';
import { useNavigate } from 'react-router-dom';

// NEW: Import chat store and selector
import { useChatStore, getTotalUnreadCount } from '../stores/chatStore';

// Extend the type to include 'verse' even though it's handled differently
type NavTabId = BottomNavTabId | 'verse';

interface NavItem {
  id: NavTabId;
  icon: React.ReactNode;
  label: string;
  path: string; // Add path for direct navigation
}

export interface BottomNavigationProps {
  activeTab: BottomNavTabId; // Still receives the dashboard tab ID
  onTabChange: (tabId: BottomNavTabId) => void; // Original handler for dashboard tabs
  className?: string;
  style?: React.CSSProperties;
}

// Update NAV_ITEMS to include paths
const NAV_ITEMS: NavItem[] = [
  {
    id: 'home',
    icon: <FaHome className="w-4 h-4" />,
    label: 'Home',
    path: '/dashboard/home',
  },
  {
    id: 'earn',
    icon: <FaCoins className="w-4 h-4" />,
    label: 'Earn',
    path: '/dashboard/earn',
  },
  {
    id: 'friends',
    icon: <FaUserFriends className="w-4 h-4" />,
    label: 'Friends',
    path: '/dashboard/friends',
  },
  {
    id: 'wallet',
    icon: <FaWallet className="w-4 h-4" />,
    label: 'Wallet',
    path: '/dashboard/wallet',
  },
  {
    id: 'premium',
    icon: <FaBolt className="w-4 h-4" />,
    label: 'Premium',
    path: '/dashboard/premium',
  },
  {
    id: 'verse',
    icon: <FaGlobe className="w-4 h-4" />,
    label: 'Verse',
    path: '/verse', // Direct path to the new top-level route
  },
];

// Optimize animation settings for smoother transitions
const NAV_ANIMATION = {
  initial: { y: 100 },
  animate: { y: 0 },
  transition: { type: 'spring', bounce: 0.2, duration: 0.5 },
};

// More performant transition for tab indicator with specific timing
const TAB_INDICATOR_TRANSITION = {
  type: 'spring',
  bounce: 0.15,
  stiffness: 260,
  damping: 20,
  duration: 0.35,
  layoutDependency: false,
};

// Active background transition settings for better movement
const ACTIVE_BG_TRANSITION = {
  layout: true,
  type: 'spring',
  stiffness: 300,
  damping: 30,
  duration: 0.3,
};

// Styles for tab items based on active state
const getTabItemStyles = (isActive: boolean) => ({
  container: isActive
    ? 'text-white'
    : 'text-white/50 hover:text-white/80 transition-colors duration-300',
  icon: isActive
    ? 'text-accent-400 mb-1 relative z-10 filter drop-shadow-[0_0_3px_rgba(168,85,247,0.4)]'
    : 'text-white/60 mb-1 transition-colors duration-300 group-hover:text-white/80',
  label: isActive
    ? 'text-xs font-medium text-transparent bg-clip-text bg-gradient-to-r from-accent-200 to-accent-400'
    : 'text-xs font-medium text-white/60 group-hover:text-white/80 transition-colors duration-300',
});

const BottomNavigation: React.FC<BottomNavigationProps> = memo(
  ({ activeTab, onTabChange, className, style }) => {
    const navigate = useNavigate(); // Use navigate hook

    // NEW: Get total unread count from chat store
    const totalUnreadCount = useChatStore(getTotalUnreadCount);

    // Updated handler to use navigate for Verse, onTabChange for others
    const handleTabClick = useCallback(
      (item: NavItem) => {
        if (item.id === 'verse') {
          navigate(item.path); // Navigate directly for Verse
        } else if (item.id !== activeTab) {
          onTabChange(item.id); // Use existing handler for dashboard tabs
        }
      },
      [activeTab, onTabChange, navigate]
    );

    // Pre-calculate styles based on active tab (only dashboard tabs can be truly "active" here)
    const tabStyles = useMemo(() => {
      return NAV_ITEMS.map(item => ({
        id: item.id,
        // isActive should only be true for dashboard tabs matching activeTab
        isActive: item.id !== 'verse' && activeTab === item.id,
        className: `relative flex flex-col items-center justify-center min-w-[3rem] py-1
                 transition-all duration-200 ${
                   item.id !== 'verse' && activeTab === item.id
                     ? 'text-accent-400'
                     : 'text-white/60 hover:text-white'
                 }`,
      }));
    }, [activeTab]);

    // Memoize shine elements to prevent recreation
    const shineEffects = useMemo(
      () => (
        <div className="absolute inset-x-0 top-0 h-px overflow-hidden">
          {/* Static border */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent" />

          {/* Center container */}
          <div className="absolute inset-0 flex justify-center">
            {/* Left-moving shine */}
            <div className="w-1/2 h-full relative overflow-hidden">
              <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-transparent via-white/30 to-transparent animate-shine-left" />
            </div>

            {/* Right-moving shine */}
            <div className="w-1/2 h-full relative overflow-hidden">
              <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/30 to-transparent animate-shine-right" />
            </div>
          </div>
        </div>
      ),
      []
    );

    return (
      <nav className={`fixed bottom-0 left-0 right-0 z-40 h-18 ${className || ''}`} style={style}>
        {/* Creative decorative elements */}
        <div className="absolute bottom-0 inset-x-0 h-full pointer-events-none overflow-hidden">
          <div className="absolute -bottom-6 left-1/4 w-32 h-32 rounded-full bg-accent-500/5 blur-xl"></div>
          <div className="absolute -bottom-8 right-1/3 w-28 h-28 rounded-full bg-accent-600/5 blur-xl"></div>
          <div className="absolute bottom-0 inset-x-0 h-px bg-gradient-to-r from-transparent via-accent-500/20 to-transparent"></div>
          <div className="absolute -bottom-8 inset-x-0 h-8 bg-gradient-to-t from-accent-500/5 to-transparent"></div>
        </div>

        {/* Main container with glass effect */}
        <div className="relative h-full bg-black/80 backdrop-blur-md border-t border-accent-500/10 shadow-[0_-4px_20px_rgba(0,0,0,0.4)]">
          {/* Thinner Shine Effect for Top Border */}
          <div className="absolute top-0 inset-x-0 h-0.5 bg-gradient-to-r from-transparent via-accent-500/15 to-transparent">
            <div className="absolute inset-0 flex justify-center">
              {/* Left-moving shine - Reduced size */}
              <div className="w-1/3 h-full relative overflow-hidden">
                <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-transparent via-white/20 to-transparent animate-shine-left" />
              </div>
              {/* Right-moving shine - Reduced size */}
              <div className="w-1/3 h-full relative overflow-hidden">
                <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shine-right" />
              </div>
            </div>
          </div>

          <div className="container mx-auto px-2 h-full">
            <div className="flex justify-around items-center h-full">
              {NAV_ITEMS.map(item => {
                // isActive needs to check against activeTab, but Verse is never active in this context
                const isActive = item.id !== 'verse' && item.id === activeTab;
                const tabItemStyles = getTabItemStyles(isActive);

                return (
                  <button
                    key={item.id}
                    className={`relative flex flex-col items-center justify-center h-full w-full group ${tabItemStyles.container}`}
                    onClick={() => handleTabClick(item)} // Pass the full item to handler
                    aria-selected={isActive}
                    style={{ transition: 'background-color 0.25s ease' }}
                  >
                    {/* Active indicator background with improved animation */}
                    <AnimatePresence>
                      {isActive && (
                        <motion.div
                          layoutId="activeTabBackground"
                          className="absolute inset-0 bg-gradient-to-t from-accent-500/15 via-transparent to-transparent rounded-lg"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          transition={ACTIVE_BG_TRANSITION}
                        />
                      )}
                    </AnimatePresence>

                    {/* Icon Container - Animate position DOWN when active */}
                    <motion.div
                      className="relative mb-1"
                      animate={{
                        y: isActive ? 8 : 0,
                        scale: isActive ? 1.1 : 1,
                        opacity: isActive ? 1 : 0.7,
                      }}
                      transition={TAB_INDICATOR_TRANSITION}
                    >
                      <span className={tabItemStyles.icon}>{item.icon}</span>
                      {/* NEW: Notification Badge for Verse Icon */}
                      {item.id === 'verse' && totalUnreadCount > 0 && (
                        <motion.span
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          exit={{ scale: 0 }}
                          className="absolute -top-1 -right-1 transform translate-x-1/2 -translate-y-1/2 bg-red-500 text-white text-[10px] font-bold rounded-full h-4 w-4 flex items-center justify-center shadow-md"
                          style={{ minWidth: '1rem' }} // Ensure minimum width
                        >
                          {totalUnreadCount > 9 ? '9+' : totalUnreadCount}
                        </motion.span>
                      )}
                      {/* Active glowing indicator */}
                      <AnimatePresence>
                        {isActive && (
                          <motion.div
                            layoutId="activeTabIndicator"
                            className="absolute -bottom-2 left-1/2 -translate-x-1/2 w-1 h-1 bg-accent-300 rounded-full shadow-[0_0_6px_2px_theme(colors.accent.300 / 0.7)]"
                            initial={{ opacity: 0, scale: 0.5 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.5 }}
                            transition={TAB_INDICATOR_TRANSITION}
                          />
                        )}
                      </AnimatePresence>
                    </motion.div>
                    <span className={`${tabItemStyles.label} relative z-10`}>{item.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </nav>
    );
  }
);

BottomNavigation.displayName = 'BottomNavigation';

export default BottomNavigation;
