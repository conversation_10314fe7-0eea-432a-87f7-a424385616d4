import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { ChatConversation, ChatMessage } from '../../types/chat';
import { MessageSquare, Users } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Spinner } from '../ui/spinner';
import { cn } from '../../utils/cn';
import { useNavigate } from 'react-router-dom';

interface ConversationListProps {
  conversations: ChatConversation[];
  isLoading: boolean;
  messages: ChatMessage[];
  onSelectChat: (userId: number | null) => void;
  currentUserId: string;
}

// Simple and focused timezone fix for formatMessageTime
const formatMessageTime = (timestamp: string | undefined | null): string => {
  if (!timestamp) return '';

  try {
    // Ensure the timestamp has 'Z' for UTC if missing
    const utcTimestamp = timestamp.endsWith('Z') ? timestamp : timestamp + 'Z';
    const date = new Date(utcTimestamp);
    const now = new Date();

    // Calculate time differences in correct units
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    // Just now (less than a minute ago)
    if (diffMinutes < 1) {
      return 'just now';
    }

    // Minutes ago (less than an hour)
    if (diffHours < 1) {
      return `${diffMinutes}m ago`;
    }

    // Hours ago (less than a day)
    if (diffDays < 1) {
      return `${diffHours}h ago`;
    }

    // Days ago (less than a week)
    if (diffDays < 7) {
      return diffDays === 1 ? 'yesterday' : `${diffDays}d ago`;
    }

    // More than a week ago - show date
    return date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
    });
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'some time ago';
  }
};

const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  isLoading: isLoadingConversations,
  messages,
  onSelectChat,
  currentUserId,
}) => {
  const listContainerRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  const sortedConversations = useMemo(() => {
    if (!conversations.length) return [];

    return [...conversations].sort((a, b) => {
      const unreadA = a.unread_count || 0;
      const unreadB = b.unread_count || 0;
      if (unreadA > 0 && unreadB === 0) return -1;
      if (unreadA === 0 && unreadB > 0) return 1;
      if (unreadA !== unreadB) return unreadB - unreadA;

      if (a.is_online && !b.is_online) return -1;
      if (!a.is_online && b.is_online) return 1;

      const timeA = a.last_message_at ? new Date(a.last_message_at).getTime() : 0;
      const timeB = b.last_message_at ? new Date(b.last_message_at).getTime() : 0;
      return timeB - timeA;
    });
  }, [conversations]);

  const handleSelectChat = useCallback(
    (userId: number | null) => {
      onSelectChat(userId);
    },
    [onSelectChat]
  );

  const getLastMessageForConversation = useCallback(
    (conversationId: number): ChatMessage | null => {
      const conversationIdStr = String(conversationId);
      const currentUserIdStr = currentUserId;

      if (!currentUserIdStr) return null;

      // Find all messages where the current user and conversation partner are involved
      const privateMessages = (messages || []).filter(
        m =>
          !m.is_public &&
          // Messages from conversation partner to current user
          ((m.sender_id === conversationIdStr && m.receiver_id === currentUserIdStr) ||
            // Messages from current user to conversation partner
            (m.receiver_id === conversationIdStr && m.sender_id === currentUserIdStr))
      );

      // Return the last message (most recent) if any messages exist
      return privateMessages.length > 0 ? privateMessages[privateMessages.length - 1] : null;
    },
    [messages, currentUserId]
  );

  const formatTime = useCallback((timestamp: string | undefined | null): string => {
    return formatMessageTime(timestamp);
  }, []);

  // Helper function to truncate and clean the message content for preview
  const getMessagePreview = useCallback((content: string): string => {
    // Remove markdown image syntax
    let cleanContent = content.replace(/!\[.*?\]\(.*?\)/g, '[Image]');

    // Replace image URLs with [Image]
    cleanContent = cleanContent.replace(/https?:\/\/.*?\.(jpg|jpeg|png|gif|webp)/gi, '[Image]');

    // Replace long URLs with [Link]
    cleanContent = cleanContent.replace(/https?:\/\/\S{10,}/g, '[Link]');

    // Truncate long messages
    return cleanContent.length > 35 ? cleanContent.substring(0, 32) + '...' : cleanContent;
  }, []);

  if (isLoadingConversations && conversations.length === 0) {
    return (
      <div className="flex items-center justify-center h-full p-4">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full overflow-hidden bg-muted/10">
      {/* Header - No search, just title */}
      <div className="p-3 border-b border-muted bg-background shrink-0">
        <h2 className="text-lg font-semibold text-foreground">Conversations</h2>
        <p className="text-xs text-muted-foreground mt-1">
          Start new chats by clicking user avatars in public chat
        </p>
      </div>

      <div
        ref={listContainerRef}
        className="flex-1 overflow-y-auto overscroll-contain no-scrollbar"
        style={{ touchAction: 'pan-y', WebkitOverflowScrolling: 'touch' }}
      >
        {sortedConversations.length > 0 ? (
          <div className="space-y-1 p-2">
            <ConversationItem
              key="public-chat"
              conversation={{
                id: 0,
                username: 'Public Chat',
                is_public: true,
                last_message_at: messages.filter(m => m.is_public).slice(-1)[0]?.created_at,
              }}
              onSelect={() => handleSelectChat(null)}
              lastMessage={messages.filter(m => m.is_public).slice(-1)[0]}
              isSelected={false}
              formatMessagePreview={getMessagePreview}
              currentUserId={currentUserId}
            />

            {sortedConversations
              .filter(c => !('is_public' in c && c.is_public === true))
              .map(conversation => (
                <ConversationItem
                  key={conversation.id}
                  conversation={conversation}
                  onSelect={handleSelectChat}
                  lastMessage={getLastMessageForConversation(conversation.id)}
                  isSelected={false}
                  formatMessagePreview={getMessagePreview}
                  currentUserId={currentUserId}
                />
              ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-8 text-center h-full text-muted-foreground">
            <MessageSquare className="h-12 w-12 mb-4 opacity-40" />
            <p className="text-sm font-semibold mb-1">No Private Conversations</p>
            <p className="text-xs mb-3">
              Click on user avatars in public chat to start private conversations.
            </p>
            <Button
              size="sm"
              onClick={() => handleSelectChat(null)}
              variant="outline"
              className="bg-primary/10 border-primary/30 hover:bg-primary/20 text-primary"
            >
              <Users className="h-4 w-4 mr-2" /> Join Public Chat
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

interface ConversationItemProps {
  conversation:
    | ChatConversation
    | { id: number; username: string; is_public?: boolean; last_message_at?: string };
  onSelect: (userId: number | null) => void;
  lastMessage: ChatMessage | null;
  isSelected: boolean;
  formatMessagePreview: (content: string) => string;
  currentUserId: string;
}

const ConversationItem: React.FC<ConversationItemProps> = React.memo(
  ({ conversation, onSelect, lastMessage, isSelected, formatMessagePreview, currentUserId }) => {
    const isPublicChat = 'is_public' in conversation && conversation.is_public === true;
    const conversationId = isPublicChat ? null : conversation.id;

    return (
      <button
        onClick={() => onSelect(conversationId)}
        className={cn(
          'flex items-center space-x-3 w-full p-2.5 rounded-lg transition-all duration-150 ease-in-out text-left group',
          isSelected
            ? 'bg-primary/20 shadow-sm scale-[1.01]'
            : 'hover:bg-muted/70 active:scale-[0.98]',
          (conversation as ChatConversation).is_online &&
            !isPublicChat &&
            !isSelected &&
            'hover:bg-green-500/10'
        )}
      >
        <div className="relative shrink-0">
          <Avatar
            className={cn(
              'h-10 w-10 border-2',
              isSelected ? 'border-primary/70' : 'border-border',
              (conversation as ChatConversation).is_online && !isPublicChat && 'border-green-500/70'
            )}
          >
            {isPublicChat ? (
              <AvatarFallback
                className={cn(
                  'bg-muted text-muted-foreground',
                  isSelected && 'bg-primary/30 text-primary font-semibold'
                )}
              >
                <Users size={18} />
              </AvatarFallback>
            ) : (
              <>
                <AvatarImage
                  src={(conversation as ChatConversation).avatar_url || ''}
                  alt={conversation.username}
                />
                <AvatarFallback
                  className={cn(
                    'bg-muted text-muted-foreground',
                    (conversation as ChatConversation).is_online &&
                      'bg-green-500/10 text-green-700 font-semibold'
                  )}
                >
                  {conversation.username[0]?.toUpperCase()}
                </AvatarFallback>
              </>
            )}
          </Avatar>
          {(conversation as ChatConversation).is_online && !isPublicChat && (
            <span className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-background ring-1 ring-green-500/50" />
          )}
        </div>
        <div className="flex-1 overflow-hidden">
          <div className="flex justify-between items-baseline">
            <h3
              className={cn(
                'font-semibold text-sm truncate',
                isSelected ? 'text-primary' : 'text-foreground group-hover:text-primary/90'
              )}
            >
              {conversation.username}
            </h3>
            <time
              className={cn(
                'text-[10px] whitespace-nowrap',
                isSelected
                  ? 'text-primary/80'
                  : 'text-muted-foreground group-hover:text-foreground/70'
              )}
            >
              {formatMessageTime(lastMessage?.created_at || conversation.last_message_at)}
            </time>
          </div>
          <div className="flex justify-between items-center mt-0.5">
            <p
              className={cn(
                'text-xs truncate',
                isSelected
                  ? 'text-foreground/90'
                  : 'text-muted-foreground group-hover:text-foreground/80'
              )}
            >
              {lastMessage
                ? (String(lastMessage.sender_id) === String(currentUserId) ? 'You: ' : '') +
                  formatMessagePreview(lastMessage.content)
                : isPublicChat
                  ? 'Join the public discussion'
                  : 'No messages yet'}
            </p>
            {!isPublicChat &&
              typeof (conversation as ChatConversation).unread_count === 'number' &&
              (conversation as ChatConversation).unread_count! > 0 && (
                <Badge
                  variant="destructive"
                  className="text-[10px] px-1.5 py-0 h-4 font-bold rounded-full ml-2 shrink-0"
                >
                  {(conversation as ChatConversation).unread_count}
                </Badge>
              )}
          </div>
        </div>
      </button>
    );
  }
);

export default React.memo(ConversationList);
