import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ChatMessage as ChatMessageType } from '../../types/chat';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '../../utils/cn';
import { Check, Maximize2, Minimize2 } from 'lucide-react';

interface ChatMessageProps {
  message: ChatMessageType;
  currentUserId: string;
  showSenderInfo?: boolean;
  enableUserClick?: boolean;
  onAvatarClick?: (userId: string) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  currentUserId,
  showSenderInfo = true,
  enableUserClick = false,
  onAvatarClick,
}) => {
  const isCurrentUser = message.sender_id === String(currentUserId);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [imageExpanded, setImageExpanded] = useState(false);
  const isPrivateMessage = !message.is_public;
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Track window resize for responsive layouts with debounce
  useEffect(() => {
    let isMounted = true;

    const handleResize = () => {
      // Clear the timeout if it exists
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }

      // Set a new timeout to update state after delay
      resizeTimeoutRef.current = setTimeout(() => {
        if (isMounted) {
          setWindowWidth(window.innerWidth);
        }
      }, 200); // Debounce for 200ms
    };

    window.addEventListener('resize', handleResize);

    return () => {
      isMounted = false;
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Helper to format the message timestamp
  const formatTime = useCallback((timestamp: string): string => {
    try {
      return formatDistanceToNow(new Date(timestamp), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting time:', error);
      return 'some time ago';
    }
  }, []);

  // Get short name version (first 2 characters)
  const getShortName = useCallback((name: string): string => {
    if (!name) return 'U';
    return name.substring(0, 2);
  }, []);

  // Handle avatar click to view user profile
  const handleAvatarClick = useCallback(() => {
    if (!isCurrentUser && enableUserClick && onAvatarClick) {
      onAvatarClick(message.sender_id);
    }
  }, [isCurrentUser, enableUserClick, onAvatarClick, message.sender_id]);

  // Check if the message contains a GIF or image
  const containsImage = useCallback(
    (content: string): { isImage: boolean; url: string; isGif: boolean } => {
      // Match markdown image syntax ![alt](url) or direct URLs
      const markdownMatch = content.match(/!\[(.*?)\]\((.*?)\)/);
      if (markdownMatch && markdownMatch[2]) {
        const url = markdownMatch[2];
        const isGif =
          url.toLowerCase().includes('.gif') ||
          url.toLowerCase().includes('giphy.com') ||
          url.toLowerCase().includes('tenor.com');
        return { isImage: true, url, isGif };
      }

      // Check for direct image URLs
      const urlRegex = /(https?:\/\/.*\.(?:png|jpg|jpeg|gif))/i;
      const urlMatch = content.match(urlRegex);
      if (urlMatch && urlMatch[1]) {
        const url = urlMatch[1];
        const isGif = url.toLowerCase().includes('.gif');
        return { isImage: true, url, isGif };
      }

      return { isImage: false, url: '', isGif: false };
    },
    []
  );

  // Toggle image expanded state
  const toggleImageExpand = useCallback(() => {
    setImageExpanded(prev => !prev);
  }, []);

  // Determine appropriate image dimensions based on device
  const getImageDimensions = useCallback(
    (isGif: boolean, isExpanded: boolean) => {
      // Small screens (mobile)
      if (windowWidth < 640) {
        if (isExpanded) {
          return 'max-w-full max-h-60';
        }
        return isGif ? 'max-w-28 max-h-28' : 'max-w-48 max-h-48';
      }

      // Medium and larger screens
      if (isExpanded) {
        return 'max-w-full max-h-72';
      }
      return isGif ? 'max-w-32 max-h-32' : 'max-w-56 max-h-56';
    },
    [windowWidth]
  );

  // Extract image from content if present
  const { isImage, isGif } = containsImage(message.content);

  // Process the message content - optimize for memoization
  const messageContent = React.useMemo(() => {
    const { isImage, url, isGif } = containsImage(message.content);

    if (isImage) {
      const imageDimensions = getImageDimensions(isGif, imageExpanded);

      return (
        <div className="mt-1 relative">
          {!imageLoaded && !imageError && (
            <div
              className={cn(
                'flex items-center justify-center bg-gray-900/50 rounded animate-pulse',
                isGif ? 'w-28 h-20 sm:w-32 sm:h-20' : 'w-full h-24'
              )}
            >
              <span className="text-xs text-gray-500">Loading...</span>
            </div>
          )}
          <img
            src={url}
            alt="Image in message"
            className={cn(
              'rounded cursor-pointer',
              !imageLoaded && 'hidden',
              imageDimensions,
              'object-contain'
            )}
            style={{
              transition: 'all 0.2s ease-in-out',
            }}
            onLoad={() => setImageLoaded(true)}
            onError={() => setImageError(true)}
            onClick={toggleImageExpand}
            loading="lazy"
          />
          {imageLoaded && !imageError && (
            <button
              onClick={toggleImageExpand}
              className="absolute bottom-1 right-1 bg-black/60 hover:bg-black/80 rounded-full p-1 transition-colors"
              title={imageExpanded ? 'Shrink' : 'Expand'}
            >
              {imageExpanded ? (
                <Minimize2 className="h-3 w-3 text-white/80" />
              ) : (
                <Maximize2 className="h-3 w-3 text-white/80" />
              )}
            </button>
          )}
          {imageError && (
            <div className="w-full p-1 bg-red-900/10 border border-red-900/20 rounded text-center">
              <span className="text-xs text-red-400">Failed to load image</span>
            </div>
          )}
        </div>
      );
    }

    // Process normal text message with URLs
    return (
      <div className="whitespace-pre-wrap break-words text-sm">
        {message.content.split(/\s+/).map((word, index) => {
          // Check if the word is a URL
          if (/^https?:\/\//i.test(word)) {
            return (
              <React.Fragment key={index}>
                <a
                  href={word}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-indigo-400 underline hover:text-indigo-300"
                >
                  {word}
                </a>{' '}
              </React.Fragment>
            );
          }
          return <React.Fragment key={index}>{word} </React.Fragment>;
        })}
      </div>
    );
  }, [
    message.content,
    imageExpanded,
    imageLoaded,
    imageError,
    getImageDimensions,
    containsImage,
    toggleImageExpand,
  ]);

  return (
    <div className={cn('flex mb-2', isCurrentUser ? 'justify-end' : 'justify-start')}>
      <div
        className={cn(
          'flex max-w-[85%] sm:max-w-[75%]', // Narrower on larger screens for better readability
          isCurrentUser ? 'flex-row-reverse' : 'flex-row'
        )}
      >
        {/* Avatar */}
        {showSenderInfo && (
          <div className={cn('flex-shrink-0', isCurrentUser ? 'ml-1' : 'mr-1')}>
            <div
              onClick={!isCurrentUser && enableUserClick ? handleAvatarClick : undefined}
              className={cn(
                'h-6 w-6 rounded-full overflow-hidden border',
                isCurrentUser ? 'border-indigo-800/40' : 'border-gray-800',
                !isCurrentUser &&
                  enableUserClick &&
                  'cursor-pointer hover:border-indigo-500 transition-colors hover:shadow-md hover:shadow-indigo-900/20'
              )}
              title={!isCurrentUser && enableUserClick ? 'Click to view user profile' : undefined}
            >
              {message.sender_avatar ? (
                <img
                  src={message.sender_avatar}
                  alt=""
                  className="h-full w-full object-cover"
                  loading="lazy"
                />
              ) : (
                <div className="w-full h-full bg-gray-900 flex items-center justify-center text-gray-300 text-xs">
                  {getShortName(message.sender_nickname || 'U')}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Message Bubble */}
        <div
          className={cn(
            'flex flex-col px-3 py-1.5 shadow-lg rounded-lg',
            isCurrentUser
              ? 'items-end bg-indigo-900/90 text-gray-100 border-0 shadow-indigo-900/20'
              : 'items-start bg-gray-800/90 text-gray-100 border-0 shadow-black/30',
            isImage && 'pb-2',
            // Add max-width constraints for better message appearance
            'max-w-full',
            // Responsive adjustments
            'sm:max-w-[400px]'
          )}
          style={{
            backdropFilter: 'blur(8px)',
          }}
        >
          {/* Sender info and timestamp */}
          {showSenderInfo && (
            <div className="flex justify-between w-full text-xs mb-0.5">
              <span
                className={cn(
                  'font-medium text-xs',
                  isCurrentUser ? 'text-indigo-200' : 'text-gray-300'
                )}
              >
                {isCurrentUser ? 'You' : message.sender_nickname || `User`}
              </span>
              <span className="text-gray-400 ml-4 text-[9px]">
                {formatTime(message.created_at)}
              </span>
            </div>
          )}

          {/* Message Content */}
          {messageContent}

          {/* Read Receipt for private messages */}
          {isPrivateMessage && isCurrentUser && (
            <div className="flex justify-end w-full mt-0.5">
              <span
                className={cn(
                  'inline-flex items-center text-[8px] transition-colors duration-300',
                  message.is_read ? 'text-indigo-400 animate-fadeIn' : 'text-gray-500'
                )}
              >
                <Check
                  className={cn('h-2.5 w-2.5', message.is_read && 'text-indigo-400 animate-pulse')}
                />
                <span className="ml-0.5 text-[7px]">{message.is_read ? 'Read' : 'Sent'}</span>
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(ChatMessage);
