import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useUIChatStore } from '../../stores/uiChatStore';
import { useChatStore } from '../../stores/chatStore';
import { ChatViewMode, ChatConversation, ChatMessage } from '../../types/chat';
import ConversationList from './ConversationList';
import ChatPanel from './ChatPanel';
import ChatErrorBoundary from './ChatErrorBoundary';
import { X, ArrowLeft, Users } from 'lucide-react';
import {
  useTelegramSafeArea,
  tg,
  optimizeChatUI,
  setupBackButtonHandler,
} from '../../utils/telegram';
import { useNavigate, useLocation } from 'react-router-dom';
import { useCurrentUserQuery } from '../../hooks/authHooks';
import { cn } from '../../utils/cn';
import {
  useChatConversationsQuery,
  useChatMessagesQuery,
  useSendMessageMutation,
} from '../../hooks/chatHooks';
import type { User } from '../../types';

interface ChatDialogProps {
  onClose: () => void;
  pause3D?: () => void;
  resume3D?: () => void;
}

type UiModeType = 'public' | 'private' | 'conversations';

const ChatDialog: React.FC<ChatDialogProps> = React.memo(({ onClose, pause3D, resume3D }) => {
  const viewMode = useUIChatStore(state => state.viewMode);
  const selectedChat = useUIChatStore(state => state.selectedChat);
  const setViewMode = useUIChatStore(state => state.setViewMode);
  const setSelectedChat = useUIChatStore(state => state.setSelectedChat);
  const isConnected = useChatStore(state => state.isConnected);

  const {
    data: conversations,
    isLoading: isLoadingConversations,
    isError: isErrorConversations,
    error: conversationsError,
    refetch: refetchConversations,
  } = useChatConversationsQuery();

  const {
    data: messages,
    isLoading: isLoadingMessages,
    isError: isErrorMessages,
    error: messagesError,
    refetch: refetchMessages,
  } = useChatMessagesQuery(selectedChat);

  const sendMessageMutation = useSendMessageMutation(selectedChat || 'public');

  const safeAreaInsets = useTelegramSafeArea();
  const navigate = useNavigate();
  const location = useLocation();
  const dialogRef = useRef<HTMLDivElement>(null);
  const { data: currentUser, isLoading: isCurrentUserLoading } = useCurrentUserQuery();

  const [keyboardOpen, setKeyboardOpen] = useState(false);

  const [uiMode, setUIMode] = useState<UiModeType>(
    selectedChat === null || selectedChat === 'public' ? 'public' : 'private'
  );

  const selectedConversationDetails = React.useMemo(() => {
    if (selectedChat === null || selectedChat === 'public' || !Array.isArray(conversations))
      return null;
    return conversations.find((c: ChatConversation) => c.id === selectedChat);
  }, [selectedChat, conversations]);

  // Move isMobileView and dialogOuterStyle above all uses
  const isMobileView = typeof window !== 'undefined' && window.innerWidth < 640;

  const dialogOuterStyle: React.CSSProperties = React.useMemo(
    () => ({
      position: 'absolute',
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
      height: '100%',
      background: '#0a0a0f',
      borderTopLeftRadius: isMobileView ? '12px' : '0px',
      borderTopRightRadius: isMobileView ? '12px' : '0px',
      boxShadow: '0 -3px 15px rgba(0,0,0,0.3)',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      zIndex: 60,
      WebkitTransform: 'translateZ(0)',
      touchAction: 'none',
    }),
    [isMobileView]
  );

  // --- iOS keyboard overlay fix ---
  const [dialogStyle, setDialogStyle] = useState<React.CSSProperties>(dialogOuterStyle);
  const lastViewportHeightRef = useRef<number>(0);
  const lastViewportOffsetTopRef = useRef<number>(0);
  const animationFrameRef = useRef<number | null>(null);

  useEffect(() => {
    // Initialize refs with current values from visualViewport or defaults
    if (window.visualViewport) {
      lastViewportHeightRef.current = window.visualViewport.height;
      lastViewportOffsetTopRef.current = window.visualViewport.offsetTop;
    } else {
      const initialHeight = parseFloat(
        dialogOuterStyle.height?.toString().replace('px', '') || '0'
      );
      const initialTop = parseFloat(dialogOuterStyle.top?.toString().replace('px', '') || '0');
      lastViewportHeightRef.current =
        initialHeight || (typeof window !== 'undefined' ? window.innerHeight : 0);
      lastViewportOffsetTopRef.current = initialTop || 0;
    }

    const checkKeyboardOpen = () => {
      if (window.visualViewport && typeof window !== 'undefined' && window.innerHeight > 0) {
        return (
          window.visualViewport.offsetTop > 20 ||
          window.innerHeight - window.visualViewport.height > window.innerHeight * 0.25
        );
      }
      return false;
    };

    function updateDialogHeightAndKeyboardState() {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        if (window.visualViewport) {
          const newHeight = window.visualViewport.height;
          const newOffsetTop = window.visualViewport.offsetTop;
          const newKeyboardOpen = checkKeyboardOpen();

          if (
            newHeight !== lastViewportHeightRef.current ||
            newOffsetTop !== lastViewportOffsetTopRef.current
          ) {
            console.log('[ChatDialog] visualViewport updated by rAF:', {
              height: newHeight,
              offsetTop: newOffsetTop,
              windowInnerHeight: window.innerHeight,
            });
            setDialogStyle(prevStyle => {
              const updatedStyle = {
                ...dialogOuterStyle, // Base on memoized dialogOuterStyle
                height: `${newHeight}px`,
                top: `${newOffsetTop}px`,
                bottom: 'auto',
                paddingBottom: 'env(safe-area-inset-bottom, 0px)',
              };
              // Only update if the style object actually needs to change
              if (JSON.stringify(prevStyle) !== JSON.stringify(updatedStyle)) {
                return updatedStyle;
              }
              return prevStyle;
            });
            lastViewportHeightRef.current = newHeight;
            lastViewportOffsetTopRef.current = newOffsetTop;
          }

          setKeyboardOpen(prevKeyboardOpen => {
            if (prevKeyboardOpen !== newKeyboardOpen) {
              return newKeyboardOpen;
            }
            return prevKeyboardOpen;
          });
        } else {
          console.log('[ChatDialog] visualViewport API not available in rAF');
          setDialogStyle(prevStyle => {
            if (JSON.stringify(prevStyle) !== JSON.stringify(dialogOuterStyle)) {
              return dialogOuterStyle;
            }
            return prevStyle;
          });
          setKeyboardOpen(false);
          // Reset refs if API becomes unavailable
          lastViewportHeightRef.current =
            parseFloat(dialogOuterStyle.height?.toString().replace('px', '') || '0') ||
            (typeof window !== 'undefined' ? window.innerHeight : 0);
          lastViewportOffsetTopRef.current = parseFloat(
            dialogOuterStyle.top?.toString().replace('px', '') || '0'
          );
        }
      });
    }
    updateDialogHeightAndKeyboardState(); // Initial call

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', updateDialogHeightAndKeyboardState);
    }

    return () => {
      if (window.visualViewport) {
        window.visualViewport.removeEventListener('resize', updateDialogHeightAndKeyboardState);
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isMobileView, dialogOuterStyle]); // dialogOuterStyle is now memoized, isMobileView triggers its re-memoization

  useEffect(() => {
    if (pause3D) pause3D();
    optimizeChatUI();
    document.body.classList.add('chat-open'); // Ensures styles like overflow:hidden are applied

    return () => {
      if (resume3D) resume3D();
      document.body.classList.remove('chat-open');
    };
  }, [pause3D, resume3D]);

  useEffect(
    () => setUIMode(selectedChat === null || selectedChat === 'public' ? 'public' : 'private'),
    [selectedChat]
  );

  const handleBackNavigation = useCallback(() => {
    if (uiMode === 'private' || uiMode === 'conversations') {
      setSelectedChat('public'); // Go to public chat / main conversation list view
      setUIMode(uiMode === 'private' ? 'public' : 'public'); // Simplified: conv list back also goes to public general
      return true;
    }
    // If in public chat or main view, try to navigate back or close
    if (location.pathname !== '/verse') {
      navigate('/verse');
    } else {
      onClose(); // Close dialog if already on /verse
    }
    return true;
  }, [uiMode, location.pathname, navigate, setSelectedChat, onClose]);

  useEffect(() => {
    if (!tg || viewMode !== ChatViewMode.FULL) return;
    return setupBackButtonHandler({
      isVisible: true,
      onBack: handleBackNavigation,
      skipHistoryManipulation: true,
    });
  }, [handleBackNavigation, viewMode]);

  const toggleConversationsList = useCallback(() => {
    setUIMode(current =>
      current === 'conversations'
        ? selectedChat === null || selectedChat === 'public'
          ? 'public'
          : 'private'
        : 'conversations'
    );
  }, [selectedChat]);

  const handleDirectClose = useCallback(() => {
    // More direct way to trigger close, for backdrop click
    onClose();
  }, [onClose]);

  const stopPropagation = useCallback((e: React.MouseEvent) => e.stopPropagation(), []);

  // Now do the early return
  if (viewMode !== ChatViewMode.FULL) return null;

  // Header for chat view
  const headerHeight = '56px';
  const headerStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '0 16px',
    borderBottom: '1px solid rgba(30,30,40,0.7)',
    backgroundColor: 'rgba(10,10,15,0.85)',
    backdropFilter: 'blur(6px)',
    zIndex: 10,
    height: headerHeight,
    flexShrink: 0,
    position: 'relative',
  };

  // Footer for chat status
  const footerHeight = '30px';
  const footerStyle: React.CSSProperties = {
    padding: '0 16px',
    borderTop: '1px solid rgba(30,30,40,0.4)',
    backgroundColor: 'rgba(10,10,15,0.8)',
    backdropFilter: 'blur(4px)',
    fontSize: '11px',
    color: 'rgba(255,255,255,0.6)',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: footerHeight,
    flexShrink: 0,
    paddingBottom: `max(env(safe-area-inset-bottom), 0px)`, // Account for safe area at bottom
  };

  const contentStyle: React.CSSProperties = {
    flexGrow: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
    position: 'relative',
  };

  const getHeaderTitle = () => {
    if (uiMode === 'conversations') return 'Conversations';
    if (selectedConversationDetails) return selectedConversationDetails.username;
    return 'Public Chat';
  };

  const renderContent = () => {
    if (uiMode === 'conversations') {
      return (
        <ConversationList
          conversations={conversations || []}
          isLoading={isLoadingConversations}
          messages={messages || []}
          onSelectChat={(chatIdFromList: number | null) => {
            setSelectedChat(chatIdFromList === null ? 'public' : chatIdFromList);
            setUIMode(chatIdFromList === null ? 'public' : 'private');
          }}
          currentUserId={currentUser?.id?.toString() ?? ''}
        />
      );
    }
    return (
      <ChatPanel
        messages={messages || []}
        isLoading={isLoadingMessages}
        selectedChat={selectedChat === 'public' ? null : selectedChat}
        conversations={conversations || []}
        currentUser={currentUser}
        conversationDetails={selectedConversationDetails}
        isKeyboardOpen={keyboardOpen}
        isOnline={isConnected}
        onSendMessage={async (
          content: string,
          chatId: number | null,
          type: 'text' | 'image' | 'file'
        ) => {
          await sendMessageMutation.mutateAsync(content);
        }}
      />
    );
  };

  return (
    <>
      <div className="chat-backdrop" onClick={handleDirectClose} />
      <div
        ref={dialogRef}
        style={dialogStyle}
        className={cn('chat-dialog-outer', isMobileView && 'static-container')}
        onClick={stopPropagation}
      >
        <div style={headerStyle}>
          <div className="absolute left-4 flex items-center">
            {(uiMode === 'private' || uiMode === 'conversations') && (
              <button
                onClick={() => {
                  setSelectedChat('public');
                  setUIMode('public');
                }}
                className="p-2 rounded-full hover:bg-gray-700/50 static-button"
                aria-label="Back"
              >
                <ArrowLeft size={20} />
              </button>
            )}
          </div>
          <h2 className="text-white font-semibold text-base truncate px-12">{getHeaderTitle()}</h2>
        </div>

        <div style={contentStyle}>
          <ChatErrorBoundary>{renderContent()}</ChatErrorBoundary>
        </div>

        {currentUser && !keyboardOpen && (
          <div style={footerStyle}>
            <span>
              Chatting as <span className="font-medium text-gray-300">{currentUser.username}</span>
            </span>
            <span>
              {isConnected ? 'Connected' : 'Connecting...'}{' '}
              <span
                className={cn(
                  'inline-block w-1.5 h-1.5 rounded-full ml-1',
                  isConnected ? 'bg-green-500' : 'bg-amber-500'
                )}
              />
            </span>
          </div>
        )}
      </div>
    </>
  );
});

export default ChatDialog;
