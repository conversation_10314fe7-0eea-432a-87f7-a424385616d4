import React, { useEffect } from 'react';
import { X, MessageSquare, UserCircle } from 'lucide-react';
import { useUIChatStore } from '../../stores/uiChatStore';
import { cn } from '../../utils/cn';

interface UserProfileDialogProps {
  userId: number;
  username: string;
  avatarUrl?: string | null;
  isOnline?: boolean;
  onClose: () => void;
}

const UserProfileDialog: React.FC<UserProfileDialogProps> = ({
  userId,
  username,
  avatarUrl,
  isOnline = false,
  onClose,
}) => {
  const setSelectedChat = useUIChatStore(state => state.setSelectedChat);

  // Prevent body scroll when dialog is open
  useEffect(() => {
    const originalStyle = window.getComputedStyle(document.body).overflow;
    document.body.style.overflow = 'hidden';

    return () => {
      document.body.style.overflow = originalStyle;
    };
  }, []);

  // <PERSON>le escape key to close dialog
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  // Start a private chat with this user
  const handleStartPrivateChat = () => {
    console.log(`[UserProfileDialog] Starting private chat with user ${userId}`);
    setSelectedChat(userId);
    onClose();
  };

  return (
    <div
      className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center"
      onClick={onClose}
      style={{
        zIndex: 1000, // Ensure it's above other elements
        position: 'fixed',
        touchAction: 'none', // Prevent iOS scroll issues
      }}
    >
      <div
        className="bg-gray-950 border border-gray-800 rounded-lg shadow-2xl w-full max-w-xs p-5 relative"
        onClick={e => e.stopPropagation()}
        style={{
          maxHeight: '90vh',
          overflowY: 'auto',
          position: 'relative',
        }}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-400 hover:text-white p-1 rounded-full hover:bg-gray-800/50"
          aria-label="Close dialog"
        >
          <X size={18} />
        </button>

        {/* User info */}
        <div className="flex flex-col items-center mb-5">
          <div className="relative mb-3">
            <div className="h-20 w-20 rounded-full overflow-hidden border-2 border-indigo-600/30 bg-gray-900 shadow-xl">
              {avatarUrl ? (
                <img src={avatarUrl} alt={username} className="w-full h-full object-cover" />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <UserCircle className="h-12 w-12 text-gray-600" />
                </div>
              )}
            </div>
            {isOnline && (
              <span className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-950" />
            )}
          </div>

          <h3 className="text-lg font-semibold text-white mb-1">{username}</h3>
        </div>

        {/* Action buttons */}
        <div className="flex flex-col gap-2">
          <button
            onClick={handleStartPrivateChat}
            className={cn(
              'w-full flex items-center justify-center gap-2 py-2 px-4 rounded-md',
              'bg-indigo-700 hover:bg-indigo-600 text-white font-medium text-sm',
              'transition-all shadow-lg shadow-indigo-900/20 hover:shadow-indigo-800/30',
              'border border-indigo-600/50'
            )}
          >
            <MessageSquare size={16} />
            Send Private Message
          </button>
        </div>
      </div>
    </div>
  );
};

export default React.memo(UserProfileDialog);
