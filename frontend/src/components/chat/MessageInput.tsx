import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Send, Smile, Paperclip, Mic } from 'lucide-react';
import { createPortal } from 'react-dom';
import { useTelegramSafeArea } from '../../utils/telegram';
import { cn } from '../../utils/cn';

interface MessageInputProps {
  chatId: number | null;
  onMessageSent?: () => void;
  sendMessage: (
    content: string,
    chatId: number | null,
    type: 'text' | 'image' | 'file'
  ) => Promise<void>;
  className?: string;
  isKeyboardOpen?: boolean;
}

const MessageInput: React.FC<MessageInputProps> = ({
  chatId,
  onMessageSent,
  sendMessage,
  className = '',
  isKeyboardOpen = false,
}) => {
  const safeAreaInsets = useTelegramSafeArea();
  const [message, setMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showGifPicker, setShowGifPicker] = useState(false);
  const [gifQuery, setGifQuery] = useState('');
  const [gifResults, setGifResults] = useState<any[]>([]);
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);
  const emojiButtonRef = useRef<HTMLButtonElement>(null);
  const attachButtonRef = useRef<HTMLButtonElement>(null);
  const portalRootRef = useRef<HTMLDivElement | null>(null);

  const quickEmojis = ['😊', '👍', '❤️', '😂', '🙏', '🔥', '👀', '✨'];

  useEffect(() => {
    if (isKeyboardOpen && (showEmojiPicker || showGifPicker)) {
      setShowEmojiPicker(false);
      setShowGifPicker(false);
    }
  }, [isKeyboardOpen, showEmojiPicker, showGifPicker]);

  useEffect(() => {
    const div = document.createElement('div');
    document.body.appendChild(div);
    portalRootRef.current = div;
    return () => {
      document.body.removeChild(div);
    };
  }, []);

  const toggleGifPicker = useCallback(() => {
    setShowGifPicker(prev => {
      if (!prev) messageInputRef.current?.blur();
      return !prev;
    });
    setShowEmojiPicker(false);
  }, []);

  useEffect(() => {
    if (!showGifPicker) return;
    if (gifQuery.length < 2) {
      setGifResults([]);
      return;
    }
    const timeout = setTimeout(() => {
      fetch(
        `https://api.giphy.com/v1/gifs/search?api_key=${process.env.REACT_APP_GIPHY_API_KEY}&q=${encodeURIComponent(gifQuery)}&limit=20`
      )
        .then(res => res.json())
        .then(data => setGifResults(data.data || []))
        .catch(console.error);
    }, 500);
    return () => clearTimeout(timeout);
  }, [gifQuery, showGifPicker]);

  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (!message.trim()) return;
      sendMessage(message.trim(), chatId, 'text')
        .then(() => {
          setMessage('');
          if (onMessageSent) onMessageSent();
          messageInputRef.current?.focus();
        })
        .catch(console.error);
    },
    [message, chatId, sendMessage, onMessageSent]
  );

  const adjustTextareaHeight = useCallback(() => {
    const textarea = messageInputRef.current;
    if (!textarea) return;
    textarea.style.height = 'auto';
    const newHeight = Math.min(Math.max(textarea.scrollHeight, 44), 100);
    textarea.style.height = `${newHeight}px`;
  }, []);

  useEffect(() => adjustTextareaHeight(), [message, adjustTextareaHeight]);

  const insertEmoji = useCallback((emoji: string) => {
    setMessage(prev => {
      const textarea = messageInputRef.current;
      if (textarea) {
        const start = textarea.selectionStart || 0;
        const end = textarea.selectionEnd || 0;
        const newText = prev.substring(0, start) + emoji + prev.substring(end);
        setTimeout(() => {
          if (textarea) {
            const newPosition = start + emoji.length;
            textarea.focus();
            textarea.setSelectionRange(newPosition, newPosition);
          }
        }, 0);
        return newText;
      }
      return prev + emoji;
    });
    messageInputRef.current?.focus();
  }, []);

  const toggleEmojiPicker = useCallback(() => {
    setShowEmojiPicker(prev => !prev);
    if (!showEmojiPicker) setShowGifPicker(false);
  }, [showEmojiPicker]);

  const toggleAttachMenu = useCallback(() => {
    setShowGifPicker(false);
    if (!showGifPicker) setShowEmojiPicker(false);
  }, [showGifPicker]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        showEmojiPicker &&
        emojiButtonRef.current &&
        !emojiButtonRef.current.contains(e.target as Node) &&
        !inputContainerRef.current?.querySelector('.emoji-picker')?.contains(e.target as Node)
      ) {
        setShowEmojiPicker(false);
      }
      if (
        showGifPicker &&
        attachButtonRef.current &&
        !attachButtonRef.current.contains(e.target as Node) &&
        !inputContainerRef.current?.querySelector('.gif-picker')?.contains(e.target as Node)
      ) {
        setShowGifPicker(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showEmojiPicker, showGifPicker]);

  const content = (
    <div
      ref={inputContainerRef}
      className={cn(
        'fixed bottom-0 left-0 right-0 bg-gray-950 border-t border-gray-800/30 p-2.5',
        className
      )}
      style={{ paddingBottom: `${safeAreaInsets.bottom}px` }}
    >
      <form onSubmit={handleSubmit} className="flex items-center gap-2">
        <button
          type="button"
          onClick={toggleGifPicker}
          className="p-2 text-gray-400 hover:text-white"
          aria-label="Attach GIF"
        >
          <Paperclip size={20} />
        </button>
        <button
          type="button"
          disabled
          className="p-2 text-gray-500 cursor-not-allowed"
          aria-label="Voice attachment (coming soon)"
        >
          <Mic size={20} />
        </button>

        {showGifPicker && (
          <div className="absolute bottom-full left-0 mb-2 w-64 bg-gray-800 p-2 rounded-lg shadow-lg z-20">
            <input
              type="text"
              value={gifQuery}
              onChange={e => setGifQuery(e.target.value)}
              placeholder="Search GIFs"
              className="w-full bg-gray-900 text-white p-1 rounded focus:outline-none"
            />
            <div className="grid grid-cols-4 gap-1 mt-2 overflow-y-auto max-h-48">
              {gifResults.map(gif => (
                <img
                  key={gif.id}
                  src={gif.images.fixed_width.url}
                  alt=""
                  className="w-full h-auto cursor-pointer rounded"
                  onClick={() => {
                    sendMessage(`![gif](${gif.images.fixed_width.url})`, chatId, 'image')
                      .then(() => {
                        setShowGifPicker(false);
                        setGifQuery('');
                        if (onMessageSent) onMessageSent();
                        messageInputRef.current?.focus();
                      })
                      .catch(console.error);
                  }}
                />
              ))}
            </div>
          </div>
        )}

        <div className="relative flex-1 flex items-end">
          <textarea
            ref={messageInputRef}
            value={message}
            onChange={e => setMessage(e.target.value)}
            placeholder="Message..."
            className="w-full bg-gray-900/90 text-white px-3.5 py-2.5 rounded-xl resize-none border border-gray-800/40 focus:border-indigo-600/50 focus:outline-none focus:ring-1 focus:ring-indigo-500/30 pr-11 text-sm leading-tight no-scrollbar transition-colors"
            rows={1}
            style={{ minHeight: '48px', maxHeight: '120px' }}
            autoCorrect="off"
          />
        </div>

        <button
          type="submit"
          className={cn(
            'p-2.5 rounded-full flex-none static-button transition-all',
            message.trim()
              ? 'bg-indigo-600 hover:bg-indigo-500 text-white shadow-md shadow-indigo-900/20'
              : 'bg-gray-800/70 text-gray-500 cursor-not-allowed opacity-70'
          )}
          disabled={!message.trim()}
          aria-label="Send message"
        >
          <Send size={18} className={message.trim() ? 'text-white' : 'text-gray-400'} />
        </button>
      </form>
    </div>
  );

  if (portalRootRef.current) {
    return createPortal(content, portalRootRef.current);
  }
  return content;
};

export default React.memo(MessageInput);
