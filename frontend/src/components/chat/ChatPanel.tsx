import React, { useEffect, useRef, useMemo, useCallback, useState } from 'react';
import { useUIChatStore } from '../../stores/uiChatStore';
import ChatMessage from './ChatMessage';
import MessageInput from './MessageInput';
import UserProfileDialog from './UserProfileDialog';
import { format } from 'date-fns';
import { MessageSquare, RefreshCw, WifiOff, ArrowDownCircle, Users, X } from 'lucide-react';
import { cn } from '../../utils/cn';
import { Button } from '../ui/button';
import { useNavigate } from 'react-router-dom';
import type { ChatMessage as MessageType, ChatConversation } from '../../types/chat';
import type { User } from '../../types';

interface ChatPanelProps {
  messages: MessageType[];
  isLoading: boolean;
  selectedChat: number | null;
  conversations: ChatConversation[];
  currentUser: User | null | undefined;
  conversationDetails: ChatConversation | null | undefined;
  isKeyboardOpen: boolean;
  isOnline: boolean;
  onSendMessage: (
    content: string,
    chatId: number | null,
    type: 'text' | 'image' | 'file'
  ) => Promise<void>;
}

const ChatPanel: React.FC<ChatPanelProps> = React.memo(
  ({
    messages,
    isLoading: isLoadingMessages,
    selectedChat,
    conversations,
    currentUser,
    conversationDetails,
    isKeyboardOpen,
    isOnline,
    onSendMessage,
  }) => {
    const messagesEndRef = useRef<HTMLDivElement>(null);
    const messagesContainerRef = useRef<HTMLDivElement>(null);

    const [refreshAttempt, setRefreshAttempt] = useState(0);
    const [userHasScrolledUp, setUserHasScrolledUp] = useState(false);
    const [showScrollToBottomButton, setShowScrollToBottomButton] = useState(false);
    const lastMessageCountRef = useRef(messages.length);

    const [profileUser, setProfileUser] = useState<{
      id: number;
      username: string;
      avatarUrl?: string | null;
      isOnline?: boolean;
    } | null>(null);

    const navigate = useNavigate();

    const scrollToBottom = useCallback((behavior: ScrollBehavior = 'smooth') => {
      try {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({ behavior, block: 'end' });
          setUserHasScrolledUp(false);
          setShowScrollToBottomButton(false);
        }
      } catch (error) {
        console.error('Error scrolling to bottom:', error);
      }
    }, []);

    useEffect(() => {
      if (
        messages.length > 0 &&
        messagesContainerRef.current &&
        lastMessageCountRef.current === 0
      ) {
        setTimeout(() => scrollToBottom('auto'), 100);
      }
    }, [messages, selectedChat, scrollToBottom]);

    useEffect(() => {
      if (isKeyboardOpen) {
        setTimeout(() => scrollToBottom('auto'), 300);
      }
    }, [isKeyboardOpen, scrollToBottom]);

    useEffect(() => {
      if (messages.length > lastMessageCountRef.current) {
        const container = messagesContainerRef.current;
        if (container) {
          const latestMessage = messages[messages.length - 1];
          const isCurrentUserSender =
            latestMessage && String(latestMessage.sender_id) === String(currentUser?.id);

          if (isCurrentUserSender) {
            setTimeout(() => scrollToBottom('smooth'), 50);
          } else if (!userHasScrolledUp) {
            const isNearBottom =
              container.scrollTop + container.clientHeight >= container.scrollHeight - 150;
            if (isNearBottom) {
              setTimeout(() => scrollToBottom('smooth'), 100);
            }
          }
        }
        lastMessageCountRef.current = messages.length;
      }
    }, [messages, userHasScrolledUp, scrollToBottom, currentUser]);

    useEffect(() => {
      scrollToBottom('auto');
    }, [selectedChat, scrollToBottom]);

    const todayHeader = useMemo(() => format(new Date(), 'EEEE, MMMM d'), []);
    const messagesByDate = useMemo(
      () => ({ [todayHeader]: messages || [] }),
      [messages, todayHeader]
    );
    const [loadError, setLoadError] = useState<string | null>(null);

    const handleRefresh = useCallback(() => {
      setLoadError(null);
      setRefreshAttempt(prev => prev + 1);
      // WebSocket reconnection logic should be handled by a dedicated hook or bridge, not UIChatStore.
    }, []);

    useEffect(() => {}, [refreshAttempt]);

    const handleAvatarClick = useCallback(
      (userId: string) => {
        const msg = (messages || []).find(m => m.sender_id === userId);
        if (msg) {
          const numUserId = parseInt(userId);
          if (isNaN(numUserId)) return;
          const convo = (conversations || []).find(c => c.id === numUserId);
          setProfileUser({
            id: numUserId,
            username: msg.sender_nickname || 'User',
            avatarUrl: msg.sender_avatar,
            isOnline: convo?.is_online,
          });
        }
      },
      [messages, conversations]
    );

    const closeProfileDialog = useCallback(() => setProfileUser(null), []);

    const handleScroll = useCallback(() => {
      const container = messagesContainerRef.current;
      if (!container) return;
      const scrolledUp =
        container.scrollHeight - container.scrollTop - container.clientHeight > 200;
      setUserHasScrolledUp(scrolledUp);
      setShowScrollToBottomButton(scrolledUp);
    }, []);

    useEffect(() => {
      if (selectedChat === null || !messages || messages.length === 0) return;
      if (!currentUser) return;
      const unread = messages.filter(
        msg =>
          !msg.is_public &&
          msg.sender_id === String(selectedChat) &&
          msg.receiver_id === String(currentUser.id) &&
          !msg.is_read
      );
      if (unread.length > 0) {
        const convoId = unread[0].conversation_id;
        if (!convoId) return;
        // If you need to mark messages as read, this should be handled by a server mutation or WebSocket bridge, not UIChatStore.
      }
    }, [selectedChat, messages]);

    return (
      <div className="flex flex-col h-full overflow-hidden bg-gray-950 relative">
        <div
          className="absolute flex items-center space-x-1.5 p-2"
          style={{
            top: `calc(env(safe-area-inset-top, 0px) + ${0}px)`,
            right: `calc(env(safe-area-inset-right, 0px) + ${0}px)`,
            zIndex: 20,
          }}
        >
          <button
            onClick={() => navigate('/chat')}
            className={cn(
              'p-2 rounded-full bg-gray-800/60 hover:bg-gray-700/80 text-white static-button relative backdrop-blur-sm',
              selectedChat === null && 'bg-indigo-600/70 text-indigo-200'
            )}
            aria-label="Conversations"
          >
            <Users size={20} />
            {selectedChat != null &&
            conversationDetails?.unread_count &&
            conversationDetails.unread_count > 0 ? (
              <span className="absolute -top-0.5 -right-0.5 bg-red-500 text-white text-[9px] font-bold rounded-full h-3.5 min-w-[14px] flex items-center justify-center px-0.5">
                {conversationDetails.unread_count > 9 ? '9+' : conversationDetails.unread_count}
              </span>
            ) : null}
          </button>

          <button
            onClick={() => navigate('/verse')}
            className="p-2 rounded-full bg-gray-800/60 hover:bg-gray-700/80 text-white static-button backdrop-blur-sm"
            aria-label="Close Chat"
          >
            <X size={20} />
          </button>
        </div>

        <div
          ref={messagesContainerRef}
          className="flex-1 overflow-y-auto p-3 bg-gray-900 overscroll-contain no-scrollbar"
          style={{
            touchAction: 'pan-y',
            WebkitOverflowScrolling: 'touch',
            paddingBottom: isKeyboardOpen ? '10px' : '78px',
          }}
          onScroll={handleScroll}
        >
          {selectedChat === null && conversationDetails === null && (
            <div className="mb-2 px-2 py-1 bg-gray-800/50 rounded-md text-[10px] text-gray-300 flex items-center">
              <MessageSquare className="h-3 w-3 mr-1 text-indigo-400" />
              Tip: Click on a user's avatar to view their profile
            </div>
          )}
          {isLoadingMessages ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>
              <span className="ml-2 text-xs text-gray-400">Loading...</span>
            </div>
          ) : loadError ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-400 gap-3">
              <div className="text-amber-500 rounded-full bg-amber-900/30 p-2">
                <WifiOff className="h-5 w-5" />
              </div>
              <div className="text-sm text-center">{loadError}</div>
              <Button variant="outline" size="sm" onClick={handleRefresh} className="mt-1">
                <RefreshCw className="h-3 w-3 mr-1" />
                Retry
              </Button>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-400">
              <MessageSquare className="h-10 w-10 mb-2 opacity-30" />
              <p className="text-sm">No messages yet.</p>
            </div>
          ) : (
            <>
              {Object.entries(messagesByDate).map(([date, dateMessages]) => (
                <div key={date} className="mb-2">
                  <div className="flex items-center justify-center my-3">
                    <div className="px-2 py-0.5 bg-gray-800/70 rounded-full text-[9px] text-gray-400">
                      {date}
                    </div>
                  </div>
                  {dateMessages.map((message, index) => (
                    <ChatMessage
                      key={message.id}
                      message={message}
                      currentUserId={currentUser?.id?.toString() || ''}
                      showSenderInfo={
                        index === 0 || dateMessages[index - 1]?.sender_id !== message.sender_id
                      }
                      enableUserClick={
                        selectedChat !== null && String(message.sender_id) !== String(selectedChat)
                      }
                      onAvatarClick={handleAvatarClick}
                    />
                  ))}
                </div>
              ))}
              <div ref={messagesEndRef} className="h-0.5" />
            </>
          )}
        </div>

        {showScrollToBottomButton && (
          <button
            onClick={() => scrollToBottom('smooth')}
            className={cn(
              'absolute right-4 p-2.5 bg-indigo-700/80 text-white rounded-full shadow-xl static-button',
              'hover:bg-indigo-600 active:scale-95',
              isKeyboardOpen ? 'bottom-[70px]' : 'bottom-[60px]'
            )}
            style={{ zIndex: 10 }}
            aria-label="Scroll to bottom"
          >
            <ArrowDownCircle size={20} />
          </button>
        )}

        <div className="bg-gray-950 shadow-[-2px_0_10px_rgba(0,0,0,0.3)] shrink-0 border-t border-gray-700/50">
          <MessageInput
            chatId={selectedChat}
            onMessageSent={() => scrollToBottom('smooth')}
            isKeyboardOpen={isKeyboardOpen}
            sendMessage={onSendMessage}
          />
        </div>

        {profileUser && (
          <UserProfileDialog
            userId={profileUser.id}
            username={profileUser.username}
            avatarUrl={profileUser.avatarUrl}
            isOnline={profileUser.isOnline}
            onClose={closeProfileDialog}
          />
        )}
      </div>
    );
  }
);

export default ChatPanel;
