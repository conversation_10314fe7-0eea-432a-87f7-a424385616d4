import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { DEFAULT_AVATARS, getAvatarDisplaySize, formatCharacterName } from '../utils/avatarUtils';
import Avatar3DViewer from './Avatar3DViewer';
import { XMarkIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

interface AvatarSelectorProps {
  currentAvatar: string;
  onSelect: (avatar: string) => void;
  onClose: () => void;
  isLoading?: boolean;
}

const AvatarSelector: React.FC<AvatarSelectorProps> = ({
  currentAvatar,
  onSelect,
  onClose,
  isLoading = false,
}) => {
  const [selectedAvatar, setSelectedAvatar] = useState(currentAvatar);
  const sliderRef = useRef<HTMLDivElement>(null);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  const scrollSlider = useCallback((direction: 'left' | 'right') => {
    if (!sliderRef.current) return;
    const scrollAmount = 128; // Width of each avatar item (w-32) + gap
    const targetScroll =
      sliderRef.current.scrollLeft + (direction === 'left' ? -scrollAmount : scrollAmount);
    sliderRef.current.scrollTo({
      left: targetScroll,
      behavior: 'smooth',
    });
  }, []);

  const previewSize = useMemo(() => {
    const width = Math.min(window.innerWidth * 0.8, 400);
    const height = Math.min(window.innerHeight * 0.4, 400);
    const size = Math.min(width, height);
    return { width: size, height: size };
  }, []);

  if (!mounted) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm">
      <div className="w-full max-w-4xl mx-auto flex flex-col h-full md:h-auto md:max-h-[90vh] bg-gradient-to-b from-gray-900 to-black rounded-lg shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-purple-500/20">
          <h2 className="text-xl font-bold text-white">Select Your Guardian</h2>
          <button
            onClick={onClose}
            disabled={isLoading}
            className="p-2 rounded-full hover:bg-white/10 transition-colors disabled:opacity-50"
            aria-label="Close"
          >
            <XMarkIcon className="w-6 h-6 text-white" />
          </button>
        </div>

        {/* Preview */}
        <div className="flex-1 overflow-y-auto py-6 px-4">
          <div className="flex flex-col items-center space-y-6">
            <div className="relative" style={previewSize}>
              <Avatar3DViewer
                filename={selectedAvatar}
                size={previewSize}
                className="w-full h-full rounded-lg shadow-xl"
              />
            </div>
            <h3 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600">
              {formatCharacterName(selectedAvatar)}
            </h3>
          </div>
        </div>

        {/* Avatar Selection */}
        <div className="p-4 border-t border-purple-500/20 bg-black/50">
          <div className="relative max-w-3xl mx-auto">
            {/* Navigation Buttons */}
            <button
              onClick={() => scrollSlider('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 hover:bg-white/10 transition-colors"
              aria-label="Previous"
            >
              <ChevronLeftIcon className="w-5 h-5 text-white" />
            </button>

            {/* Avatar List */}
            <div
              ref={sliderRef}
              className="overflow-x-auto scrollbar-hide py-2 px-10 flex gap-4 snap-x snap-mandatory scroll-smooth"
            >
              {DEFAULT_AVATARS.map(avatar => (
                <button
                  key={avatar}
                  onClick={() => setSelectedAvatar(avatar)}
                  disabled={isLoading}
                  className={`relative flex-shrink-0 w-32 h-32 rounded-lg overflow-hidden transition-all duration-200 ${
                    selectedAvatar === avatar
                      ? 'ring-2 ring-purple-500 ring-offset-2 ring-offset-black scale-105'
                      : 'opacity-70 hover:opacity-100 hover:scale-105'
                  }`}
                >
                  <Avatar3DViewer
                    filename={avatar}
                    size={getAvatarDisplaySize('header')}
                    className="w-full h-full"
                  />
                </button>
              ))}
            </div>

            <button
              onClick={() => scrollSlider('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 p-2 rounded-full bg-black/50 hover:bg-white/10 transition-colors"
              aria-label="Next"
            >
              <ChevronRightIcon className="w-5 h-5 text-white" />
            </button>
          </div>

          {/* Confirm Button */}
          <button
            onClick={() => {
              onSelect(selectedAvatar);
              onClose();
            }}
            disabled={isLoading}
            className="mt-6 w-full max-w-sm mx-auto py-3 px-4 bg-gradient-to-r from-purple-600 to-pink-600 
                     text-white font-bold rounded-lg transition-all duration-200
                     hover:from-purple-700 hover:to-pink-700 hover:shadow-lg hover:shadow-purple-500/20
                     disabled:opacity-50 disabled:cursor-not-allowed
                     flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <svg className="animate-spin h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                <span>Updating...</span>
              </>
            ) : (
              <span>Confirm Selection</span>
            )}
          </button>
        </div>
      </div>

      <div>
        <style>{`
          .scrollbar-hide {
            scrollbar-width: none;
            -ms-overflow-style: none;
          }
          .scrollbar-hide::-webkit-scrollbar {
            display: none;
          }
        `}</style>
      </div>
    </div>
  );
};

export default React.memo(AvatarSelector);
