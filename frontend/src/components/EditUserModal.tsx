import React, { useState } from 'react';
import { User, Role } from '../types';
import { api } from '../utils/apiClient';
import LoadingSpinner from './LoadingSpinner';

interface EditUserModalProps {
  user: User;
  onClose: () => void;
  onUpdate?: (action: () => Promise<void>) => Promise<void>;
  disabled?: boolean;
  isAdmin?: boolean;
}

const EditUserModal: React.FC<EditUserModalProps> = ({
  user,
  onClose,
  onUpdate,
  disabled,
  isAdmin,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: user.email || '',
    first_name: user.first_name || '',
    last_name: user.last_name || '',
    is_active: user.is_active,
    role: user.role,
    discount_percent: user.discount_percent || 0,
    telegram_id: user.telegram_id?.toString() || '',
    new_password: '',
  });

  const [chargeAmount, setChargeAmount] = useState<string>('');

  // Add validation state for Telegram ID
  const [telegramIdValid, setTelegramIdValid] = useState(true);
  const [telegramIdMessage, setTelegramIdMessage] = useState<string>('');

  // Add Telegram ID validation function
  const validateTelegramId = async (value: string) => {
    if (!value) {
      setTelegramIdValid(true);
      setTelegramIdMessage('');
      return;
    }

    if (!/^\d+$/.test(value)) {
      setTelegramIdValid(false);
      setTelegramIdMessage('Telegram ID must be a number');
      return;
    }

    try {
      // Skip validation if it's the same ID - convert both to strings for comparison
      if (user.telegram_id !== undefined && value === String(user.telegram_id)) {
        setTelegramIdValid(true);
        setTelegramIdMessage('');
        return;
      }

      const response = await api.get<{ exists: boolean }>(`/auth/check-username/${value}`);
      if (response.data.exists) {
        setTelegramIdValid(false);
        setTelegramIdMessage('This Telegram ID is already in use');
      } else {
        setTelegramIdValid(true);
        setTelegramIdMessage('Telegram ID is available');
      }
    } catch (error) {
      console.error('Error validating Telegram ID:', error);
      setTelegramIdValid(false);
      setTelegramIdMessage('Error checking Telegram ID');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!onUpdate || !telegramIdValid) return;

    try {
      setLoading(true);
      setError(null);

      await onUpdate(async () => {
        // Process wallet charge (handles both positive and negative values)
        const trimmedAmount = chargeAmount.trim();
        if (trimmedAmount !== '') {
          const amount = parseFloat(trimmedAmount);
          if (!isNaN(amount) && amount !== 0) {
            // Send ONLY wallet_charge in the body for this specific request
            await api.put(`/admin/users/${user.id}/wallet/charge`, {
              wallet_charge: amount,
            });
          }
        }

        // Handle other user updates
        const updateData: Record<string, any> = {};

        // Always include telegram_id in the update data, even if unchanged
        updateData.telegram_id = formData.telegram_id
          ? parseInt(formData.telegram_id.trim())
          : null;

        if (formData.email !== user.email) {
          updateData.email = formData.email.trim() || null;
        }
        if (formData.first_name !== user.first_name) {
          updateData.first_name = formData.first_name.trim() || null;
        }
        if (formData.last_name !== user.last_name) {
          updateData.last_name = formData.last_name.trim() || null;
        }
        if (formData.is_active !== user.is_active) {
          updateData.is_active = formData.is_active;
        }
        if (formData.role !== user.role) {
          updateData.role = formData.role;
        }
        if (formData.discount_percent !== user.discount_percent) {
          updateData.discount_percent = formData.discount_percent;
        }
        if (formData.new_password) {
          updateData.password = formData.new_password;
        }

        // Only send update request if there are changes
        if (Object.keys(updateData).length > 0) {
          console.log('Sending update with data:', updateData); // Debug log
          // Use the general /admin/users/{user.id} endpoint for other updates
          await api.put(`/admin/users/${user.id}`, updateData);
        }
      });

      onClose();
    } catch (err: any) {
      console.error('Error updating user:', err);
      setError(err.response?.data?.detail || 'Failed to update user');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-800 rounded-xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4 text-white">Edit User</h2>

        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded text-red-400">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Referral Code (Read-only) */}
          <div>
            <label className="block text-white/60 mb-2">Referral Code</label>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={user.referral_code || 'No referral code'}
                readOnly
                className="w-full bg-white/5 rounded p-2 text-white/90 cursor-not-allowed"
              />
              <button
                type="button"
                onClick={() => navigator.clipboard.writeText(user.referral_code || '')}
                className="p-2 hover:bg-white/10 rounded transition-colors"
                title="Copy referral code"
              >
                <svg
                  className="w-5 h-5 text-white/60"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"
                  />
                </svg>
              </button>
            </div>
          </div>

          {/* Wallet Charge Section */}
          <div className="mb-4">
            <label className="block text-white/60 mb-2">
              Modify Wallet (Current Balance: ${user.wallet_balance.toFixed(2)})
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="number"
                value={chargeAmount}
                onChange={e => setChargeAmount(e.target.value)}
                className="w-full bg-white/5 rounded p-2 text-white"
                placeholder="Enter amount"
                step="0.01"
              />
              <span className="text-white/60">$</span>
            </div>
            <p className="mt-1 text-xs text-white/40">
              Use positive values to add funds, negative values to deduct. Cannot result in negative
              balance.
            </p>
          </div>

          {/* New Password Field */}
          <div>
            <label className="block text-white/60 mb-2">New Password</label>
            <input
              type="password"
              value={formData.new_password}
              onChange={e => setFormData({ ...formData, new_password: e.target.value })}
              className="w-full bg-white/5 rounded p-2 text-white"
              placeholder="Leave empty to keep current password"
            />
          </div>

          {/* Telegram ID Field */}
          <div>
            <label className="block text-white/60 mb-2">Telegram ID</label>
            <div className="relative">
              <input
                type="text"
                value={formData.telegram_id}
                onChange={e => {
                  const value = e.target.value.replace(/[^0-9]/g, '');
                  setFormData({ ...formData, telegram_id: value });
                  validateTelegramId(value);
                }}
                onBlur={e => validateTelegramId(e.target.value)}
                className={`w-full bg-white/5 border ${
                  telegramIdValid ? 'border-white/10' : 'border-red-500/50'
                } rounded-lg px-4 py-2.5 text-white 
                focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 
                focus:ring-purple-500/50 transition-colors`}
                placeholder="Enter Telegram ID"
              />
              {telegramIdMessage && (
                <p
                  className={`mt-1 text-sm ${telegramIdValid ? 'text-green-400' : 'text-red-400'}`}
                >
                  {telegramIdMessage}
                </p>
              )}
            </div>
            <p className="mt-1 text-xs text-white/40">Current: {user.telegram_id || 'Not set'}</p>
          </div>

          {/* Other form fields */}
          <div>
            <label className="block text-white/60 mb-2">Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={e => setFormData({ ...formData, email: e.target.value })}
              className="w-full bg-white/5 rounded p-2 text-white"
            />
          </div>

          <div>
            <label className="block text-white/60 mb-2">First Name</label>
            <input
              type="text"
              value={formData.first_name}
              onChange={e => setFormData({ ...formData, first_name: e.target.value })}
              className="w-full bg-white/5 rounded p-2 text-white"
            />
          </div>

          <div>
            <label className="block text-white/60 mb-2">Last Name</label>
            <input
              type="text"
              value={formData.last_name}
              onChange={e => setFormData({ ...formData, last_name: e.target.value })}
              className="w-full bg-white/5 rounded p-2 text-white"
            />
          </div>

          <div>
            <label className="block text-white/60 mb-2">Role</label>
            <select
              value={formData.role}
              onChange={e => setFormData({ ...formData, role: e.target.value as Role })}
              className="w-full bg-white/5 rounded p-2 text-white"
            >
              <option value="user">User</option>
              <option value="reseller">Reseller</option>
              <option value="admin">Admin</option>
            </select>
          </div>

          <div>
            <label className="block text-white/60 mb-2">Discount Percent</label>
            <input
              type="number"
              value={formData.discount_percent}
              onChange={e =>
                setFormData({ ...formData, discount_percent: parseFloat(e.target.value) })
              }
              className="w-full bg-white/5 rounded p-2 text-white"
              min="0"
              max="100"
              step="1"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={formData.is_active}
              onChange={e => setFormData({ ...formData, is_active: e.target.checked })}
              className="form-checkbox"
            />
            <label className="text-white/60">Active</label>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-white/60 hover:text-white"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={disabled || loading}
              className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
            >
              {loading ? <LoadingSpinner size="sm" /> : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditUserModal;
