import React from 'react';
import EarnTab from './EarnTab';
import { useCurrentUserQuery } from '../hooks/authHooks';
import LoadingSpinner from './LoadingSpinner';

const EarnTabWrapper: React.FC = () => {
  const { data: user, isLoading: isLoadingUser } = useCurrentUserQuery();

  // Mock props for now - these would normally come from actual hooks
  const cardTabProps = {
    unifiedCards: [],
    isLoading: false,
    error: null,
  };

  const taskTabProps = {
    tasks: [],
    isLoading: false,
    error: null,
  };

  if (isLoadingUser) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <EarnTab
      cardTabProps={cardTabProps}
      taskTabProps={taskTabProps}
      isLoadingTasks={false}
      isLoadingCards={false}
      user={user || undefined}
    />
  );
};

export default EarnTabWrapper; 