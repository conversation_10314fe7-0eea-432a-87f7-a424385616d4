import React, { useEffect, useState } from 'react';

interface PerformanceMetrics {
  appStartTime: number;
  authTime: number;
  firstRenderTime: number;
  dependenciesLoadTime: number;
  totalLoadTime: number;
}

const DevPerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    appStartTime: performance.now(),
    authTime: 0,
    firstRenderTime: 0,
    dependenciesLoadTime: 0,
    totalLoadTime: 0,
  });
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development mode
    if (!import.meta.env.DEV) return;

    const startTime = performance.now();
    
    // Track first render
    setMetrics(prev => ({
      ...prev,
      firstRenderTime: performance.now() - prev.appStartTime,
    }));

    // Track when dependencies are loaded
    const checkDependencies = () => {
      if (window.React && window.ReactDOM) {
        setMetrics(prev => ({
          ...prev,
          dependenciesLoadTime: performance.now() - prev.appStartTime,
        }));
      }
    };

    // Track total load time
    const handleLoad = () => {
      setMetrics(prev => ({
        ...prev,
        totalLoadTime: performance.now() - prev.appStartTime,
      }));
    };

    // Track auth completion
    const checkAuth = () => {
      const authElement = document.querySelector('[data-auth-complete]');
      if (authElement) {
        setMetrics(prev => ({
          ...prev,
          authTime: performance.now() - prev.appStartTime,
        }));
      }
    };

    checkDependencies();
    checkAuth();
    
    window.addEventListener('load', handleLoad);
    const authInterval = setInterval(checkAuth, 100);
    const depsInterval = setInterval(checkDependencies, 100);

    // Show monitor after 2 seconds
    const showTimer = setTimeout(() => setIsVisible(true), 2000);

    return () => {
      window.removeEventListener('load', handleLoad);
      clearInterval(authInterval);
      clearInterval(depsInterval);
      clearTimeout(showTimer);
    };
  }, []);

  // Don't render in production
  if (!import.meta.env.DEV || !isVisible) return null;

  const formatTime = (ms: number) => {
    if (ms === 0) return 'Pending...';
    return ms > 1000 ? `${(ms / 1000).toFixed(2)}s` : `${ms.toFixed(0)}ms`;
  };

  const getStatusColor = (time: number) => {
    if (time === 0) return 'text-yellow-400';
    if (time < 1000) return 'text-green-400';
    if (time < 3000) return 'text-yellow-400';
    return 'text-red-400';
  };

  return (
    <div className="fixed top-4 right-4 z-[9999] bg-black/90 text-white p-3 rounded-lg text-xs font-mono border border-gray-600 max-w-xs">
      <div className="flex justify-between items-center mb-2">
        <h3 className="font-bold text-blue-400">Dev Performance</h3>
        <button 
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-1">
        <div className="flex justify-between">
          <span>First Render:</span>
          <span className={getStatusColor(metrics.firstRenderTime)}>
            {formatTime(metrics.firstRenderTime)}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Dependencies:</span>
          <span className={getStatusColor(metrics.dependenciesLoadTime)}>
            {formatTime(metrics.dependenciesLoadTime)}
          </span>
        </div>
        
        <div className="flex justify-between">
          <span>Auth Complete:</span>
          <span className={getStatusColor(metrics.authTime)}>
            {formatTime(metrics.authTime)}
          </span>
        </div>
        
        <div className="flex justify-between border-t border-gray-600 pt-1">
          <span>Total Load:</span>
          <span className={getStatusColor(metrics.totalLoadTime)}>
            {formatTime(metrics.totalLoadTime)}
          </span>
        </div>
      </div>
      
      <div className="mt-2 pt-2 border-t border-gray-600 text-[10px] text-gray-400">
        <div>Target: &lt;3s total</div>
        <div>Current: {metrics.totalLoadTime > 10000 ? '🔴 SLOW' : metrics.totalLoadTime > 3000 ? '🟡 OK' : '🟢 FAST'}</div>
      </div>
    </div>
  );
};

export default DevPerformanceMonitor; 