import React from 'react';

interface SectionProps {
  title?: string;
  children: React.ReactNode;
  className?: string; // Allow additional classes for customization
}

const Section: React.FC<SectionProps> = ({ title, children, className = '' }) => {
  return (
    <section className={`mb-6 ${className}`}>
      {title && (
        <div className="mb-3 pb-2 border-b border-purple-500/10">
          <h2 className="text-lg font-semibold text-white font-display tracking-wide">{title}</h2>
          {/* Optional: Add a subtle decorative element below the title */}
          {/* <div className="h-px w-1/4 bg-gradient-to-r from-purple-500/50 to-transparent mt-1"></div> */}
        </div>
      )}
      <div>{children}</div>
    </section>
  );
};

export default Section;
