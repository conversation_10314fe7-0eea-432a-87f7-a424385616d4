/**
 * @file VersePage.tsx
 * @description The main page component for the interactive 3D Verse map.
 */

import React, { Suspense, useEffect, lazy } from 'react';
import { motion } from 'framer-motion';
import { tg } from '../utils/telegram';

// Lazy load the heavy 3D components
const VerseLayout = lazy(() => import('../components/verse/VerseLayout'));

// Loading fallback for 3D components
const VerseLoadingFallback = () => (
  <div className="flex flex-col justify-center items-center h-screen w-full bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
    <div className="relative">
      <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-purple-400"></div>
      <div className="animate-ping absolute inset-0 rounded-full h-16 w-16 border-2 border-purple-300 opacity-20"></div>
    </div>
    <motion.p 
      className="mt-6 text-white/80 text-lg font-medium"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.5 }}
    >
      Loading Verse Experience...
    </motion.p>
    <motion.p 
      className="mt-2 text-white/60 text-sm"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 1 }}
    >
      Initializing 3D environment
    </motion.p>
  </div>
  );

// Main Page Component
const VersePage: React.FC = () => {
  // Ensure Telegram MainButton is hidden if it exists
  useEffect(() => {
    if (tg && tg.MainButton && tg.MainButton.isVisible) {
      tg.MainButton.hide();
    }

    return () => {
      // Cleanup - ensure MainButton is hidden when component unmounts
      if (tg && tg.MainButton) {
        tg.MainButton.hide();
      }
    };
  }, []);

  return (
    <Suspense fallback={<VerseLoadingFallback />}>
      <VerseLayout />
            </Suspense>
  );
};

export default VersePage;
