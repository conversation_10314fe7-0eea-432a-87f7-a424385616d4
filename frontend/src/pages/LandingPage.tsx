import React, { useEffect, useState, useCallback, useMemo, memo } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import LoadingSpinner from '../components/LoadingSpinner';
import { useTelegramLoginMutation, useCurrentUserQuery } from '../hooks/authHooks';
import { toast } from 'react-hot-toast';

const LandingPage = memo(() => {
  const navigate = useNavigate();
  const telegramLoginMutation = useTelegramLoginMutation();
  const { data: currentUser, isLoading: isAuthLoading } = useCurrentUserQuery();
  const [isTelegramReady, setIsTelegramReady] = useState(false);
  const [isNonTelegramEnvironment, setIsNonTelegramEnvironment] = useState(false);
  const [initializationAttempts, setInitializationAttempts] = useState(0);

  // Memoize animation variants to prevent recreation on each render
  const containerVariants = useMemo(() => ({
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5, ease: "easeOut" } },
  }), []);

  const buttonVariants = useMemo(() => ({
    hover: { scale: 1.03, boxShadow: "0px 5px 15px rgba(139, 92, 246, 0.3)" },
    tap: { scale: 0.97 },
  }), []);

  // Check if already authenticated - optimized with early return
  useEffect(() => {
    if (currentUser && !isAuthLoading) {
      console.log('[LandingPage] User already authenticated, redirecting');
      navigate('/dashboard', { replace: true });
    }
  }, [currentUser, isAuthLoading, navigate]);

  // Optimized Telegram WebApp initialization
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    const initializeTelegram = () => {
      try {
        if (typeof window === 'undefined') return;
        
        // Check if Telegram WebApp is available
        if (window.Telegram?.WebApp) {
          const tg = window.Telegram.WebApp;
          
          if (!window.telegramReady) {
            console.log('[LandingPage] Initializing Telegram WebApp...');
            tg.ready();
            tg.expand();
            window.telegramReady = true;
            setIsTelegramReady(true);
            console.log('[LandingPage] Telegram WebApp initialized successfully.');
          } else {
            setIsTelegramReady(true);
            console.log('[LandingPage] Telegram WebApp already initialized.');
          }
        } else {
          // Not in Telegram environment, set timeout for detection
          if (initializationAttempts < 3) {
            setInitializationAttempts(prev => prev + 1);
            timeoutId = setTimeout(initializeTelegram, 1000);
          } else {
            setIsNonTelegramEnvironment(true);
            console.log('[LandingPage] Not in Telegram environment after 3 attempts.');
          }
        }
      } catch (error) {
        console.error('[LandingPage] Failed to initialize Telegram WebApp:', error);
        setIsNonTelegramEnvironment(true);
      }
    };

    initializeTelegram();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [initializationAttempts]);

  const handleTelegramLogin = useCallback(async () => {
    try {
      if (isNonTelegramEnvironment) {
        toast.error('This app must be opened in Telegram');
        return;
      }

      if (!window.Telegram?.WebApp?.initData) {
        toast.error('Please open this app in Telegram');
        return;
      }

      const initData = window.Telegram.WebApp.initData;
      console.log('[LandingPage] Attempting Telegram login...');

      const result = await telegramLoginMutation.mutateAsync({
        init_data: initData,
        auth_method: 'telegram'
      });

      if (result.isNewUser) {
        console.log('[LandingPage] New user detected, redirecting to setup');
        navigate('/new-user-setup');
      } else {
        console.log('[LandingPage] Existing user login successful');
        // Navigation will be handled by the auth hook after successful login
      }
    } catch (error: any) {
      console.error('[LandingPage] Login failed:', error);
      
      // Show specific error messages
      const errorMessage = error?.message || 'Login failed. Please try again.';
      toast.error(errorMessage);
    }
  }, [isNonTelegramEnvironment, telegramLoginMutation, navigate]);

  const handleCreateAccount = useCallback(() => {
    if (isNonTelegramEnvironment) {
      toast.error('This app must be opened in Telegram to create an account');
      return;
    }

    if (!window.Telegram?.WebApp?.initData) {
      toast.error('Please open this app in Telegram to create an account');
      return;
    }
    navigate('/new-user-setup');
  }, [isNonTelegramEnvironment, navigate]);

  const handleOpenInTelegram = useCallback(() => {
    const botUsername = import.meta.env.VITE_TELEGRAM_BOT_USERNAME || 'your_bot_username';
    const telegramLink = `https://t.me/${botUsername}`;
    
    if (window.open) {
      window.open(telegramLink, '_blank');
    } else {
      window.location.href = telegramLink;
    }
  }, []);

  // Memoize computed values
  const showInitialLoading = useMemo(() => 
    isAuthLoading && !currentUser, 
    [isAuthLoading, currentUser]
  );
  
  const showTelegramInitializing = useMemo(() => 
    !isTelegramReady && !isNonTelegramEnvironment && initializationAttempts < 3, 
    [isTelegramReady, isNonTelegramEnvironment, initializationAttempts]
  );

  // Loading state component
  const LoadingComponent = useMemo(() => (
    <div className="min-h-screen bg-[#030303] flex items-center justify-center p-4">
      <motion.div 
        className="flex flex-col items-center justify-center text-center p-5"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="text-[3.5rem] mb-6 animate-[floatIcon_3s_ease-in-out_infinite]">🚀</div>
        <h1 className="text-[2.25rem] font-bold text-gray-100 mb-3 tracking-tight">VIPVerse</h1>
        <p className="text-base text-zinc-400 mb-8">Welcome to the future</p>
        <LoadingSpinner size="lg" />
        <p className="text-[0.95rem] text-purple-500 font-medium animate-[pulseText_1.8s_infinite_alternate] mt-4">
          {showInitialLoading ? 'Verifying session...' : 'Initializing Telegram...'}
        </p>
      </motion.div>
    </div>
  ), [containerVariants, showInitialLoading]);

  if (showInitialLoading || showTelegramInitializing) {
    return LoadingComponent;
  }

  // Non-Telegram environment component
  if (isNonTelegramEnvironment) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="max-w-md w-full bg-gray-800 rounded-2xl p-8 text-center"
        >
          <div className="text-4xl mb-6">⚠️</div>
          
          <h1 className="text-2xl font-bold text-white mb-4">
            Telegram Required
          </h1>
          
          <p className="text-gray-300 mb-6">
            VIPVerse is a Telegram Mini App and must be opened within Telegram to function properly.
          </p>

          <div className="space-y-4">
            <motion.button
              onClick={handleOpenInTelegram}
              className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              Open in Telegram
            </motion.button>

            {import.meta.env.DEV && (
              <motion.button
                onClick={() => {
                  setIsNonTelegramEnvironment(false);
                  setIsTelegramReady(true);
                }}
                className="w-full py-2 px-4 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors"
                variants={buttonVariants}
                whileHover="hover"
                whileTap="tap"
              >
                Continue Anyway (Dev Mode)
              </motion.button>
            )}
          </div>

          <div className="mt-6 pt-6 border-t border-gray-700">
            <p className="text-gray-400 text-sm">
              1. Install Telegram app<br />
              2. Search for VIPVerse bot<br />
              3. Launch the app
            </p>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#030303] flex items-center justify-center p-4">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="max-w-sm w-full bg-gradient-to-br from-gray-900 via-gray-850 to-purple-900/30 rounded-2xl p-8 text-center shadow-2xl border border-purple-700/30"
      >
        <div className="text-5xl mb-6 text-purple-400">✨</div>
        
        <h1 className="text-3xl font-bold text-white mb-3">
          Welcome to VIPVerse
        </h1>
        
        <p className="text-gray-300/90 mb-8 text-sm">
          Enter the exclusive world of premium VPN services.
        </p>

        {/* Telegram Status Indicator */}
        <div className="mb-6 p-3 rounded-lg bg-black/20 border border-purple-600/30 shadow-inner">
          <p className="text-sm font-medium">
            Status: {isTelegramReady ? (
              <span className="text-green-400">✓ Telegram Ready</span>
            ) : (
              <span className="text-yellow-400">⏳ Initializing...</span>
            )}
          </p>
        </div>

        <div className="space-y-4">
          <motion.button
            onClick={handleTelegramLogin}
            disabled={telegramLoginMutation.isPending || !isTelegramReady || isAuthLoading}
            className="w-full py-3 px-6 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-300 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            {telegramLoginMutation.isPending ? (
              <div className="flex items-center justify-center space-x-2">
                <LoadingSpinner size="sm" />
                <span>Connecting...</span>
              </div>
            ) : !isTelegramReady ? (
              'Initializing Telegram...'
            ) : (
              'Login to VIPVerse'
            )}
          </motion.button>

          <motion.button
            onClick={handleCreateAccount}
            disabled={!isTelegramReady || isAuthLoading}
            className="w-full py-3 px-6 bg-gray-700/80 hover:bg-gray-600/90 border border-gray-600 text-white font-semibold rounded-xl shadow-md transition-all duration-300 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-900"
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            Create New Account
          </motion.button>
        </div>

        <div className="mt-8 pt-6 border-t border-purple-700/20">
          <p className="text-xs text-gray-400/70">
            Secure • Anonymous • Premium
          </p>
        </div>

        {/* Debug info in development */}
        {import.meta.env.DEV && (
          <div className="mt-6 p-3 bg-black/30 rounded-lg text-left text-xs text-gray-500 overflow-x-auto">
            <p><strong>Dev Info:</strong></p>
            <p>TG WebApp: {String(!!window.Telegram?.WebApp)}</p>
            <p>TG Ready: {String(isTelegramReady)}</p>
            <p>TG InitData: {(window.Telegram?.WebApp?.initData?.length || 0) > 0 ? 'Available' : 'Missing'}</p>
            <p>Non-TG Env: {String(isNonTelegramEnvironment)}</p>
            <p>Auth Loading: {String(isAuthLoading)}</p>
            <p>Attempts: {initializationAttempts}</p>
          </div>
        )}
      </motion.div>
    </div>
  );
});

LandingPage.displayName = 'LandingPage';

export default LandingPage;
