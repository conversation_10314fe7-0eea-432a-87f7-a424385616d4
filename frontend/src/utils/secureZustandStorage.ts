/**
 * Secure Zustand Storage Adapter
 *
 * Provides encrypted storage adapter for Zustand persist middleware
 * to replace insecure localStorage/sessionStorage usage.
 *
 * <AUTHOR> Enhancement - Frontend Vulnerability Fix
 */

import { StateStorage } from 'zustand/middleware';
import { encryptedLocalStorage, encryptedSessionStorage } from './encryptedStorage';

/**
 * Create encrypted storage adapter for Zustand
 */
function createEncryptedStorage(useSessionStorage = false): StateStorage {
  const storage = useSessionStorage ? encryptedSessionStorage : encryptedLocalStorage;

  return {
    getItem: async (name: string): Promise<string | null> => {
      try {
        const data = await storage.getItem<any>(name);
        return data ? JSON.stringify(data) : null;
      } catch (error) {
        console.warn(`[SecureZustandStorage] Failed to get item: ${name}`, error);
        return null;
      }
    },
    setItem: async (name: string, value: string): Promise<void> => {
      try {
        const parsed = JSON.parse(value);
        await storage.setItem(name, parsed);
      } catch (error) {
        console.error(`[SecureZustandStorage] Failed to set/parse item: ${name}`, error);
      }
    },
    removeItem: async (name: string): Promise<void> => {
      try {
        await storage.removeItem(name);
      } catch (error) {
        console.error(`[SecureZustandStorage] Failed to remove item: ${name}`, error);
      }
    },
  };
}

/**
 * Encrypted localStorage adapter for Zustand
 */
export const createSecureJSONStorage = (useSessionStorage = false) => {
  return () => createEncryptedStorage(useSessionStorage);
};

/**
 * Pre-configured adapters
 */
export const secureLocalStorage = createSecureJSONStorage(false);
export const secureSessionStorage = createSecureJSONStorage(true);

export default {
  createSecureJSONStorage,
  secureLocalStorage,
  secureSessionStorage,
};
