import { QueryFunctionContext } from '@tanstack/react-query';
import type { ApiResponse } from '../types';
import axios, {
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
  AxiosHeaders,
} from 'axios';
import { handleError } from './errorHandler';
import { sanitizeInput } from './security';

// Define a more specific type for query keys that use this fetchData structure
export type ApiClientQueryKey = [string, { endpoint: string; params?: Record<string, any> }];
// Or a simpler one if the endpoint is just a string in the key
export type ApiClientSimpleQueryKey = [string, string, Record<string, any>?];

/**
 * Generic data fetching function for TanStack Query.
 */
export async function fetchData<TData = any>(
  context: QueryFunctionContext<ApiClientQueryKey | ApiClientSimpleQueryKey | readonly unknown[]>,
  options?: { method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'; data?: any }
): Promise<TData> {
  const queryKey = context.queryKey as ReadonlyArray<unknown>;
  let endpoint: string;
  let apiParams: Record<string, any> | undefined;

  if (
    typeof queryKey[1] === 'object' &&
    queryKey[1] !== null &&
    'endpoint' in (queryKey[1] as object)
  ) {
    const paramsObj = queryKey[1] as { endpoint: string; params?: Record<string, any> };
    endpoint = paramsObj.endpoint;
    apiParams = paramsObj.params;
  } else if (typeof queryKey[1] === 'string') {
    endpoint = queryKey[1] as string;
    if (queryKey.length > 2 && typeof queryKey[2] === 'object' && queryKey[2] !== null) {
      apiParams = queryKey[2] as Record<string, any>;
    }
  } else {
    throw new Error(
      `Invalid queryKey structure for fetchData. Expected endpoint in key[1]. QueryKey: ${JSON.stringify(queryKey)}`
    );
  }

  if (!endpoint) {
    throw new Error(`Endpoint is undefined. QueryKey: ${JSON.stringify(queryKey)}`);
  }

  const method = options?.method || 'GET';
  const data = options?.data;

  let response;
  if (method === 'POST') {
    response = await api.post<TData>(endpoint, data || apiParams);
  } else if (method === 'PUT') {
    response = await api.put<TData>(endpoint, data || apiParams);
  } else if (method === 'PATCH') {
    response = await api.patch<TData>(endpoint, data || apiParams);
  } else if (method === 'DELETE') {
    response = await api.delete<TData>(endpoint, { data: data || apiParams });
  } else {
    response = await api.get<TData>(endpoint, { params: apiParams });
  }
  return response.data;
}

/**
 * Generic data mutation function for TanStack Query.
 */
export async function mutateData<TData = any, TVariables = any>({
  endpoint,
  variables,
  method = 'POST',
}: {
  endpoint: string;
  variables?: TVariables;
  method?: 'POST' | 'PUT' | 'PATCH' | 'DELETE';
}): Promise<TData> {
  let response;
  switch (method) {
    case 'POST':
      response = await api.post<TData>(endpoint, variables);
      break;
    case 'PUT':
      response = await api.put<TData>(endpoint, variables);
      break;
    case 'PATCH':
      response = await api.patch<TData>(endpoint, variables);
      break;
    case 'DELETE':
      response = await api.delete<TData>(endpoint, { data: variables });
      break;
    default:
      throw new Error(`Unsupported mutation method: ${method}`);
  }
  return response.data;
}

/**
 * Centralized API error handler
 */
function handleApiError(error: any): never {
  let finalMessage = 'An unknown error occurred. Please try again.';
  let specificMessageFound = false;

  // Handle network errors first
  if (error?.code === 'ERR_NETWORK' || error?.code === 'NETWORK_ERROR') {
    finalMessage = 'Network connection error. Please check your internet connection and try again.';
    specificMessageFound = true;
  }
  
  // Handle timeout errors
  else if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    finalMessage = 'Request timed out. Please try again.';
    specificMessageFound = true;
  }
  
  // Handle cancelled requests
  else if (error?.code === 'ERR_CANCELED' || error?.name === 'AbortError') {
    finalMessage = 'Request was cancelled.';
    specificMessageFound = true;
  }

  // Handle axios error response
  else if (error?.response?.data) {
    const data = error.response.data;
    
    // Check for FastAPI error format
    if (data.detail) {
      if (typeof data.detail === 'string') {
        finalMessage = data.detail;
        specificMessageFound = true;
      } else if (typeof data.detail === 'object' && data.detail.message) {
        finalMessage = data.detail.message;
        specificMessageFound = true;
      }
    } else if (data.message) {
      finalMessage = data.message;
      specificMessageFound = true;
    }
  }

  // Check error.details (custom format)
  if (!specificMessageFound && error?.details) {
    if (typeof error.details === 'string' && error.details.length > 0 && error.details.length < 200) {
      finalMessage = error.details;
      specificMessageFound = true;
    } else if (typeof error.details === 'object' && error.details !== null) {
      const innerMsg = error.details.message || error.details.error || error.details.detail;
      if (typeof innerMsg === 'string' && innerMsg.length > 0 && innerMsg.length < 200) {
        finalMessage = innerMsg;
        specificMessageFound = true;
      }
    }
  }

  // Fallback to error.message
  if (!specificMessageFound && error?.message) {
    if (!error.message.toLowerCase().includes('request failed with status code')) {
      finalMessage = error.message;
    } else if (error.status) {
      finalMessage = `Request failed with status code ${error.status}`;
    } else if (error.response?.status) {
      finalMessage = `Request failed with status code ${error.response.status}`;
    }
  }

  // Handle specific HTTP status codes
  if (!specificMessageFound && error?.response?.status) {
    switch (error.response.status) {
      case 400:
        finalMessage = 'Bad request. Please check your input and try again.';
        break;
      case 401:
        finalMessage = 'Authentication required. Please log in again.';
        break;
      case 403:
        finalMessage = 'Access denied. You do not have permission to perform this action.';
        break;
      case 404:
        finalMessage = 'The requested resource was not found.';
        break;
      case 429:
        finalMessage = 'Too many requests. Please wait a moment and try again.';
        break;
      case 500:
        finalMessage = 'Server error. Please try again later.';
        break;
      case 502:
      case 503:
      case 504:
        finalMessage = 'Service temporarily unavailable. Please try again later.';
        break;
      default:
        finalMessage = `Request failed with status code ${error.response.status}`;
    }
  }

  // Create error with additional context
  const apiError = new Error(finalMessage);
  (apiError as any).status = error?.response?.status || error?.status;
  (apiError as any).response = error?.response;
  (apiError as any).code = error?.code;
  throw apiError;
}

// Get API base URL with fallback logic
const getApiBaseUrl = (): string => {
  const envUrl = import.meta.env.VITE_API_URL;

  // In development, prefer localhost for direct API calls
  if (import.meta.env.DEV && !envUrl?.includes('dev.atlasvip.cloud')) {
    return 'http://localhost:8000';
  }

  // In production or when explicitly using the domain
  return envUrl || 'https://dev.atlasvip.cloud';
};

// Create axios instance with environment-aware configuration
const apiClient = axios.create({
  baseURL: getApiBaseUrl(),
  withCredentials: true,
  timeout: import.meta.env.DEV ? 15000 : 10000, // Longer timeout in dev mode
  headers: new AxiosHeaders({
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  }),
});

// Request queue for rate limiting
let requestQueue: Array<() => Promise<any>> = [];
const MAX_REQUESTS_PER_MINUTE = 100;
let requestCount = 0;
let isRefreshing = false;
let failedQueue: any[] = [];
let refreshTokenAttemptCount = 0;
const MAX_REFRESH_ATTEMPTS = 3;

const processQueue = (error: any = null) => {
  failedQueue.forEach(prom => {
    if (error) prom.reject(error);
    else prom.resolve();
  });
  failedQueue = [];
};

// Reset request count every minute
setInterval(() => {
  requestCount = 0;
}, 60000);

// Request interceptor
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Rate limiting
    if (requestCount >= MAX_REQUESTS_PER_MINUTE) {
      console.warn('[API] Rate limit approached, queuing request');
      return new Promise((resolve) => {
        requestQueue.push(() => {
          resolve(config);
          return Promise.resolve(config);
        });
      });
    }
    requestCount++;

    // Sanitize request data
    if (config.data) {
      config.data = sanitizeRequest(config.data);
    }

    // Log request in development
    if (import.meta.env.DEV) {
      console.log('[API] Request:', {
        url: config.url,
        method: config.method,
        baseURL: config.baseURL,
      });
    }

    return config;
  },
  (error) => {
    console.error('[API] Request interceptor error:', error);
    return Promise.reject(error);
  }
);

const sanitizeRequest = (data: any) => {
  if (typeof data === 'object' && data !== null) {
    const sanitized = { ...data };
    for (const key in sanitized) {
      if (typeof sanitized[key] === 'string') {
        sanitized[key] = sanitizeInput(sanitized[key]);
      }
    }
    return sanitized;
  }
  return data;
};

// Response interceptor with token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Log successful responses in development
    if (import.meta.env.DEV) {
      console.log('[API] Response:', {
        url: response.config.url,
        status: response.status,
        data: response.data,
      });
    }
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as InternalAxiosRequestConfig & { _retry?: boolean };

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(() => {
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        if (refreshTokenAttemptCount >= MAX_REFRESH_ATTEMPTS) {
          throw new Error('Max refresh attempts reached');
        }

        refreshTokenAttemptCount++;
        await api.auth.refreshToken();
        processQueue();
        return apiClient(originalRequest);
      } catch (refreshError) {
        processQueue(refreshError);
        console.error('[API] Token refresh failed:', refreshError);
        
        // Clear auth state and redirect to login
        if (typeof window !== 'undefined') {
          window.location.href = '/';
        }
        
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    // Only log actual errors, not retry attempts
    return Promise.reject(error);
  }
);

export const getCookie = (name: string): string | null => {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  return parts.length === 2 ? parts.pop()?.split(';').shift() || null : null;
};

export const hasAuthCookies = (): boolean => {
  return !!(getCookie('auth_token') || getCookie('access_token'));
};

// API methods
export const api = {
  // ... existing API methods
  auth: {
    async login(data: { username: string; password: string }) {
      try {
        const response = await apiClient.post<ApiResponse>('/auth/token', data);
        return response.data;
      } catch (error) {
        console.error('[API] Login error:', error);
        throw error;
      }
    },

    async telegramLogin(data: { init_data: string; auth_method: 'telegram' }) {
      try {
        const response = await apiClient.post<ApiResponse>('/auth/telegram/login', data);
        return response.data;
      } catch (error) {
        // Don't log 404 errors as they're expected for new users
        if ((error as any)?.response?.status !== 404) {
          console.error('[API] Telegram login error:', error);
        }
        throw error;
      }
    },

    async logout() {
      try {
        const response = await apiClient.post<ApiResponse>('/auth/logout');
        return response.data;
      } catch (error) {
        console.error('[API] Logout error:', error);
        throw error;
      }
    },

    async refreshToken() {
      try {
        const response = await apiClient.post<ApiResponse>('/auth/refresh');
        return response.data;
      } catch (error) {
        console.error('[API] Refresh token error:', error);
        throw error;
      }
    },

    async verifySession() {
      try {
        const response = await apiClient.post<ApiResponse>('/auth/verify-session');
        return response.data;
      } catch (error) {
        // Don't log 401 errors as they're expected for unauthenticated users
        if ((error as any)?.response?.status !== 401) {
          console.error('[API] Session verification error:', error);
        }
        throw error;
      }
    }
  },

  // Generic HTTP methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return apiClient.get<T>(url, config);
  },

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return apiClient.post<T>(url, data, config);
  },

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return apiClient.put<T>(url, data, config);
  },

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return apiClient.patch<T>(url, data, config);
  },

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return apiClient.delete<T>(url, config);
  },
};

export default apiClient;
