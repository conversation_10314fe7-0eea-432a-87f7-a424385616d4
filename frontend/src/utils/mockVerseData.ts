/**
 * @file mockVerseData.ts
 * @description Generates mock data for NFT land plots in the Verse.
 */

export interface LandPlot {
  id: string;
  lat: number; // latitude in degrees
  lon: number; // longitude in degrees
  angularSize: number; // angular size in degrees (for simplicity)
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  status: 'owned' | 'for_sale' | 'unclaimed';
  owner?: string; // Mock owner ID or name
  price?: number; // Mock price if for_sale
  name: string;
  paintData?: string | null; // Stores base64 data URL of the paint texture
}

const RARITIES: LandPlot['rarity'][] = ['common', 'uncommon', 'rare', 'epic', 'legendary'];
const STATUSES: LandPlot['status'][] = ['owned', 'for_sale', 'unclaimed'];

function getRandomElement<T>(arr: T[]): T {
  return arr[Math.floor(Math.random() * arr.length)];
}

function generateMockLand(id: number, lat: number, lon: number): LandPlot {
  const rarity = getRandomElement(RARITIES);
  const status = getRandomElement(STATUSES);
  // Generate a price between $1 and $1000 for all lands
  const price = Math.floor(Math.random() * 1000) + 1;
  // Map price to angularSize: $1 -> 4, $1000 -> 14
  const minPrice = 1,
    maxPrice = 1000,
    minSize = 4,
    maxSize = 14;
  const angularSize = minSize + ((price - minPrice) / (maxPrice - minPrice)) * (maxSize - minSize);
  return {
    id: `land-${id}`,
    lat,
    lon,
    angularSize,
    rarity,
    status,
    owner: status === 'owned' ? `user-${Math.floor(Math.random() * 1000)}` : undefined,
    price, // Always present
    name: `Plot [${lat.toFixed(1)}, ${lon.toFixed(1)}]`,
    paintData: null, // Initialize paintData
  };
}

/**
 * Generates mock land data distributed on a sphere.
 * @param numLands Number of lands to generate.
 * @returns An array of LandPlot objects.
 */
export function generateMockVerseData(numLands: number = 100): LandPlot[] {
  const lands: LandPlot[] = [];
  for (let i = 0; i < numLands; i++) {
    // Distribute points on a sphere using the Fibonacci lattice
    const phi = (Math.acos(1 - (2 * (i + 0.5)) / numLands) * 180) / Math.PI; // latitude in degrees
    const theta = (((Math.PI * (1 + Math.sqrt(5)) * i) % (2 * Math.PI)) * 180) / Math.PI; // longitude in degrees
    lands.push(generateMockLand(i, phi - 90, theta - 180)); // Centered at (0,0)
  }
  return lands;
}

/**
 * Converts spherical coordinates to 3D Cartesian coordinates.
 * @param lat Latitude in degrees
 * @param lon Longitude in degrees
 * @param radius Sphere radius
 * @returns [x, y, z]
 */
export function latLonToCartesian(
  lat: number,
  lon: number,
  radius: number
): [number, number, number] {
  const phi = ((90 - lat) * Math.PI) / 180;
  const theta = ((lon + 180) * Math.PI) / 180;
  const x = radius * Math.sin(phi) * Math.cos(theta);
  const y = radius * Math.cos(phi);
  const z = radius * Math.sin(phi) * Math.sin(theta);
  return [x, y, z];
}
