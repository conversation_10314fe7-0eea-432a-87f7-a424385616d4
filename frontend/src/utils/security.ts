import DOMPurify from 'dompurify';
import type { Config } from 'dompurify';

const purifyConfig: Config = {
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
  ALLOWED_ATTR: [],
  RETURN_DOM: false,
  RETURN_DOM_FRAGMENT: false,
  RETURN_TRUSTED_TYPE: true,
};

export const sanitizeInput = (input: string): string => {
  return DOMPurify.sanitize(input, purifyConfig);
};

export const validatePayloadSize = (data: any, maxSize: number = 1024 * 1024): boolean => {
  const size = new Blob([JSON.stringify(data)]).size;
  return size <= maxSize;
};

// Add function to clear sensitive data
export const clearSensitiveData = (): void => {
  localStorage.clear();
  sessionStorage.clear();
  // Clear any other sensitive data stores you might have
  document.cookie.split(';').forEach(cookie => {
    document.cookie = cookie
      .replace(/^ +/, '')
      .replace(/=.*/, `=;expires=${new Date(0).toUTCString()};path=/`);
  });
};

interface BotDetectionData {
  timestamp: number;
  userAgent: string;
  screenResolution: string;
  colorDepth: number;
  touchPoints: number;
  deviceMemory?: number;
  hardwareConcurrency: number;
  mouseMoves: number;
  keyPresses: number;
  scrolls: number;
  touches: number;
}

export const detectBot = async (data: BotDetectionData): Promise<number> => {
  // Suspicious patterns
  const patterns = {
    // Known bot user agents
    botUserAgents: ['bot', 'crawler', 'spider', 'selenium', 'puppeteer', 'phantom'],

    // Suspicious hardware configurations
    suspiciousHardware: {
      minCores: 0, // More permissive
      maxCores: 64, // More permissive
      minMemory: 0, // More permissive
      maxMemory: 64, // More permissive
    },

    // Time-based patterns
    timePatterns: {
      maxRequestAge: 15 * 60 * 1000, // 15 minutes (more permissive)
      minRequestInterval: 50, // ms (more permissive)
    },
  };

  let score = 0;

  // Check user agent (only add score for explicit bot identifiers)
  const userAgentLower = data.userAgent.toLowerCase();
  if (patterns.botUserAgents.some(pattern => userAgentLower.includes(pattern))) {
    score += 0.4;
  }

  // Skip hardware check for Telegram user agents
  const isTelegramApp = userAgentLower.includes('telegram') || userAgentLower.includes('tgweb');

  // Check hardware configuration (only if not Telegram)
  if (!isTelegramApp) {
    if (
      data.hardwareConcurrency < patterns.suspiciousHardware.minCores ||
      data.hardwareConcurrency > patterns.suspiciousHardware.maxCores
    ) {
      score += 0.1; // Reduced weight
    }

    if (data.deviceMemory !== undefined) {
      if (
        data.deviceMemory < patterns.suspiciousHardware.minMemory ||
        data.deviceMemory > patterns.suspiciousHardware.maxMemory
      ) {
        score += 0.1; // Reduced weight
      }
    }
  }

  // Check screen properties - be more permissive
  const [width, height] = data.screenResolution.split('x').map(Number);
  if (width < 280 || height < 200 || data.colorDepth < 8) {
    // More permissive values
    score += 0.1; // Reduced weight
  }

  // Check behavioral metrics - especially important for Telegram Web App
  if (
    !isTelegramApp &&
    data.mouseMoves === 0 &&
    data.keyPresses === 0 &&
    data.scrolls === 0 &&
    data.touches === 0
  ) {
    score += 0.2; // Reduced weight
  }

  // Check request timing
  const age = Date.now() - data.timestamp;
  if (age > patterns.timePatterns.maxRequestAge || age < 0) {
    score += 0.2; // Reduced weight
  }

  // Add entropy analysis (skip for Telegram)
  if (!isTelegramApp) {
    const entropy = await calculateEntropy(data);
    if (entropy < 0.3) {
      // More permissive threshold
      score += 0.1; // Reduced weight
    }
  }

  // Final score calculation with higher threshold for blocking
  return Math.min(score, 1);
};

// Helper function to calculate entropy of behavioral data
const calculateEntropy = async (data: BotDetectionData): Promise<number> => {
  try {
    // Convert data to string for analysis
    const behaviorString = JSON.stringify({
      timestamp: data.timestamp % 1000, // Use only milliseconds
      screen: data.screenResolution,
      metrics: {
        moves: data.mouseMoves,
        keys: data.keyPresses,
        scrolls: data.scrolls,
        touches: data.touches,
      },
    });

    // Calculate Shannon entropy
    const freq: { [key: string]: number } = {};
    for (const char of behaviorString) {
      freq[char] = (freq[char] || 0) + 1;
    }

    const len = behaviorString.length;
    return (
      Object.values(freq).reduce((entropy, count) => {
        const p = count / len;
        return entropy - p * Math.log2(p);
      }, 0) / Math.log2(Object.keys(freq).length || 1)
    ); // Normalize to [0,1]
  } catch {
    return 0;
  }
};

// Check if request comes from a trusted source (like Telegram WebApp)
export const isTrustedSource = (userData: any): boolean => {
  // Check if it has Telegram-specific data
  if (userData && typeof userData === 'object') {
    // Telegram WebApp requests will have this structure
    if (userData.auth_method === 'telegram' && userData.init_data) {
      return true;
    }

    // If behavior data explicitly indicates it's from Telegram
    if (
      userData.behavior_data?.userAgent &&
      (userData.behavior_data.userAgent.toLowerCase().includes('telegram') ||
        userData.behavior_data.userAgent.toLowerCase().includes('tgweb'))
    ) {
      return true;
    }
  }

  // Add more trusted source checks as needed
  return false;
};
