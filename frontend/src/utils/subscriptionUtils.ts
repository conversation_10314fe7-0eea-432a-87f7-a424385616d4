import axios from 'axios';
import { VPNSubscription } from '../types/index';
import { formatTimeLeft } from './format';

export interface UsageStatus {
  isActive: boolean;
  isExpired: boolean;
  isLimited: boolean;
  statusText: string;
  colorClass: string;
  usagePercent: number;
  timeRemaining: {
    days: number;
    hours: number;
    minutes: number;
    text: string;
    color: string;
  };
  dataUsed: number;
  dataLimit: number;
  lifetimeUsed: number;
  lastOnline: string | null;
}

export interface TimeRemaining {
  days: number;
  hours: number;
  minutes: number;
  text: string;
  color: string;
}

export function formatLastOnline(lastOnline: string | null): string {
  if (!lastOnline) return 'Never';
  
  const lastOnlineDate = new Date(lastOnline);
  const now = new Date();
  const diffMs = now.getTime() - lastOnlineDate.getTime();
  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  
  if (diffMinutes < 1) return 'Just now';
  if (diffMinutes < 60) return `${diffMinutes}m ago`;
  
  const diffHours = Math.floor(diffMinutes / 60);
  if (diffHours < 24) return `${diffHours}h ago`;
  
  const diffDays = Math.floor(diffHours / 24);
  return `${diffDays}d ago`;
}

export function formatRemainingTime(expireDate: Date): TimeRemaining {
  const now = new Date();
  const remainingMs = expireDate.getTime() - now.getTime();

  if (remainingMs <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      text: 'Expired',
      color: 'text-red-400',
    };
  }

  const days = Math.floor(remainingMs / (24 * 60 * 60 * 1000));
  const hours = Math.floor((remainingMs % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
  const minutes = Math.floor((remainingMs % (60 * 60 * 1000)) / (60 * 1000));

  let text = '';
  let color = 'text-green-400';

  if (days > 0) {
    text = `${days}d ${hours}h remaining`;
  } else if (hours > 0) {
    text = `${hours}h ${minutes}m remaining`;
    color = 'text-yellow-400';
  } else {
    text = `${minutes}m remaining`;
    color = 'text-red-400';
  }

  return { days, hours, minutes, text, color };
}

export function getStatusColorClass(status: string, isExpired: boolean, isLimited: boolean): string {
  if (isExpired) return 'bg-red-500/20 text-red-400';
  if (isLimited) return 'bg-yellow-500/20 text-yellow-400';
  if (status === 'active') return 'bg-green-500/20 text-green-400';
  return 'bg-gray-500/20 text-gray-400';
}

export function calculateUsagePercentage(used: number, total: number): number {
  return Math.min((used / total) * 100, 100);
}

export async function getSubscriptionUsageStatus(subscription: VPNSubscription): Promise<UsageStatus> {
  try {
    const infoUrl = `${subscription.subscription_url}/info`;
    const response = await axios.get(infoUrl, { timeout: 10000 });
    const data = response.data;

    const now = Math.floor(Date.now() / 1000);
    const isExpired = data.expire < now;
    const isLimited = data.used_traffic >= data.data_limit;
    const usagePercent = calculateUsagePercentage(data.used_traffic, data.data_limit);

    return {
      isActive: data.status === 'active' && !isExpired && !isLimited,
      isExpired,
      isLimited,
      statusText: isExpired ? 'Expired' : isLimited ? 'Limited' : data.status === 'active' ? 'Active' : 'Inactive',
      colorClass: getStatusColorClass(data.status, isExpired, isLimited),
      usagePercent,
      timeRemaining: formatRemainingTime(new Date(data.expire * 1000)),
      dataUsed: data.used_traffic,
      dataLimit: data.data_limit,
      lifetimeUsed: data.used_traffic,
      lastOnline: data.online_at || null,
    };
  } catch (error) {
    console.error('Error fetching subscription usage:', error);
    return {
      isActive: false,
      isExpired: true,
      isLimited: false,
      statusText: 'Unknown',
      colorClass: 'bg-gray-500/20 text-gray-400',
      usagePercent: 0,
      timeRemaining: {
        days: 0,
        hours: 0,
        minutes: 0,
        text: 'Unknown',
        color: 'text-gray-400',
      },
      dataUsed: 0,
      dataLimit: subscription.data_limit,
      lifetimeUsed: 0,
      lastOnline: null,
    };
  }
} 