/**
 * API Connection Validation Utilities
 * Provides functions to test and validate API connections
 */

import { api } from './apiClient';

export interface ApiConnectionStatus {
  isConnected: boolean;
  baseUrl: string;
  responseTime: number;
  error?: string;
  version?: string;
}

export interface WebSocketConnectionStatus {
  isSupported: boolean;
  baseUrl: string;
  error?: string;
}

/**
 * Test API connection and get health status
 */
export async function testApiConnection(): Promise<ApiConnectionStatus> {
  const startTime = Date.now();
  const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';
  
  try {
    const response = await api.get('/api/health');
    const responseTime = Date.now() - startTime;
    
    return {
      isConnected: true,
      baseUrl,
      responseTime,
      version: response.data?.version,
    };
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    
    return {
      isConnected: false,
      baseUrl,
      responseTime,
      error: error?.message || 'Connection failed',
    };
  }
}

/**
 * Test WebSocket connection capability
 */
export function testWebSocketConnection(): WebSocketConnectionStatus {
  const baseUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000/ws';
  
  try {
    // Check if WebSocket is supported
    if (typeof WebSocket === 'undefined') {
      return {
        isSupported: false,
        baseUrl,
        error: 'WebSocket not supported in this environment',
      };
    }
    
    return {
      isSupported: true,
      baseUrl,
    };
  } catch (error: any) {
    return {
      isSupported: false,
      baseUrl,
      error: error?.message || 'WebSocket test failed',
    };
  }
}

/**
 * Get current environment configuration summary
 */
export function getEnvironmentConfig() {
  return {
    mode: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    prod: import.meta.env.PROD,
    apiUrl: import.meta.env.VITE_API_URL,
    wsUrl: import.meta.env.VITE_WS_BASE_URL,
    environment: import.meta.env.VITE_ENVIRONMENT,
    debug: import.meta.env.VITE_DEBUG,
    appName: import.meta.env.VITE_APP_NAME,
    version: import.meta.env.VITE_APP_VERSION,
  };
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfig(): { isValid: boolean; issues: string[] } {
  const issues: string[] = [];
  const config = getEnvironmentConfig();
  
  // Check required environment variables
  if (!config.apiUrl) {
    issues.push('VITE_API_URL is not set');
  }
  
  if (!config.wsUrl) {
    issues.push('VITE_WS_BASE_URL is not set');
  }
  
  // Check URL formats
  if (config.apiUrl && !config.apiUrl.match(/^https?:\/\/.+/)) {
    issues.push('VITE_API_URL must start with http:// or https://');
  }
  
  if (config.wsUrl && !config.wsUrl.match(/^wss?:\/\/.+/)) {
    issues.push('VITE_WS_BASE_URL must start with ws:// or wss://');
  }
  
  // Check for development vs production consistency
  if (config.dev && config.apiUrl?.includes('https://')) {
    console.warn('Development mode with HTTPS API URL - ensure this is intentional for Telegram testing');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
  };
}

/**
 * Log environment and connection status for debugging
 */
export async function logConnectionStatus(): Promise<void> {
  if (!import.meta.env.DEV) return;
  
  console.group('🔍 API Connection Status');
  
  // Environment config
  const envConfig = getEnvironmentConfig();
  console.log('Environment:', envConfig);
  
  // Validation
  const validation = validateEnvironmentConfig();
  if (!validation.isValid) {
    console.warn('⚠️ Environment issues:', validation.issues);
  } else {
    console.log('✅ Environment configuration is valid');
  }
  
  // API connection test
  try {
    const apiStatus = await testApiConnection();
    if (apiStatus.isConnected) {
      console.log(`✅ API connected to ${apiStatus.baseUrl} (${apiStatus.responseTime}ms)`);
      if (apiStatus.version) {
        console.log(`📦 API version: ${apiStatus.version}`);
      }
    } else {
      console.error(`❌ API connection failed: ${apiStatus.error}`);
    }
  } catch (error) {
    console.error('❌ API test error:', error);
  }
  
  // WebSocket test
  const wsStatus = testWebSocketConnection();
  if (wsStatus.isSupported) {
    console.log(`✅ WebSocket supported: ${wsStatus.baseUrl}`);
  } else {
    console.error(`❌ WebSocket issue: ${wsStatus.error}`);
  }
  
  console.groupEnd();
}
