// Simple utility for handling haptic feedback in apps
// Safely checks for existence of methods before calling

// Simple check if we're in Telegram WebApp environment
const isTelegramWebView =
  typeof window !== 'undefined' &&
  'Telegram' in window &&
  window.Telegram &&
  'WebApp' in window.Telegram;

// Log haptic support status when initialized
if (isTelegramWebView && !(window.Telegram?.WebApp as any)?.HapticFeedback) {
  console.info('Haptic feedback not supported in this Telegram WebApp version');
}

export const haptics = {
  impact: (style: 'light' | 'medium' | 'heavy' = 'medium') => {
    try {
      if (
        isTelegramWebView &&
        window.Telegram?.WebApp &&
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        (window.Telegram.WebApp as any).HapticFeedback &&
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        typeof (window.Telegram.WebApp as any).HapticFeedback.impactOccurred === 'function'
      ) {
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        (window.Telegram.WebApp as any).HapticFeedback.impactOccurred(style);
      }
    } catch (e) {
      // Silently catch any errors
    }
  },

  notification: (type: 'error' | 'success' | 'warning') => {
    try {
      if (
        isTelegramWebView &&
        window.Telegram?.WebApp &&
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        (window.Telegram.WebApp as any).HapticFeedback &&
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        typeof (window.Telegram.WebApp as any).HapticFeedback.notificationOccurred === 'function'
      ) {
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        (window.Telegram.WebApp as any).HapticFeedback.notificationOccurred(type);
      }
    } catch (e) {
      // Silently catch any errors
    }
  },

  selection: () => {
    try {
      if (
        isTelegramWebView &&
        window.Telegram?.WebApp &&
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        (window.Telegram.WebApp as any).HapticFeedback &&
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        typeof (window.Telegram.WebApp as any).HapticFeedback.selectionChanged === 'function'
      ) {
        // @ts-ignore - Ignore TypeScript errors for Telegram WebApp API
        (window.Telegram.WebApp as any).HapticFeedback.selectionChanged();
      }
    } catch (e) {
      // Silently catch any errors
    }
  },
};

export type HapticFeedback = typeof haptics;
