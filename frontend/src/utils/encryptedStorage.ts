/**
 * Encrypted Storage Utility
 *
 * Provides secure, encrypted storage for sensitive data using localStorage/sessionStorage
 * with AES encryption powered by our enhanced secureStorage utility.
 *
 * Security Features:
 * - AES-256 encryption with PBKDF2 key derivation
 * - Automatic data expiration
 * - Type-safe storage operations
 * - Secure key management
 *
 * <AUTHOR> Enhancement - Frontend Vulnerability Fix
 */

import { encryptData, decryptData } from './secureStorage';

// Storage types
type StorageType = 'localStorage' | 'sessionStorage';

// Storage interface for type safety
interface StorageItem<T = any> {
  data: T;
  timestamp: number;
  expiresAt?: number;
}

// Error types for better error handling
export class EncryptedStorageError extends Error {
  constructor(
    message: string,
    public cause?: unknown
  ) {
    super(message);
    this.name = 'EncryptedStorageError';
  }
}

/**
 * Base encrypted storage class
 */
class BaseEncryptedStorage {
  private storage: Storage;
  private keyPrefix: string;

  constructor(storageType: StorageType, keyPrefix: string = 'enc_') {
    this.storage = storageType === 'localStorage' ? localStorage : sessionStorage;
    this.keyPrefix = keyPrefix;
  }

  /**
   * Generate storage key with prefix
   */
  private generateKey(key: string): string {
    return `${this.keyPrefix}${key}`;
  }

  /**
   * Set encrypted data with optional expiration
   */
  async setItem<T>(key: string, data: T, expirationHours?: number): Promise<void> {
    try {
      const storageItem: StorageItem<T> = {
        data,
        timestamp: Date.now(),
        expiresAt: expirationHours ? Date.now() + expirationHours * 60 * 60 * 1000 : undefined,
      };

      const serialized = JSON.stringify(storageItem);
      const encrypted = await encryptData(serialized);

      this.storage.setItem(this.generateKey(key), encrypted);
    } catch (error) {
      throw new EncryptedStorageError(`Failed to encrypt and store data for key: ${key}`, error);
    }
  }

  /**
   * Get encrypted item
   */
  async getItem<T>(key: string): Promise<T | null> {
    try {
      const encrypted = this.storage.getItem(this.generateKey(key));
      if (!encrypted) return null;

      const decrypted = await decryptData(encrypted);
      const storageItem: StorageItem<T> = JSON.parse(decrypted);

      // Check expiration
      if (storageItem.expiresAt && Date.now() > storageItem.expiresAt) {
        this.removeItem(key);
        return null;
      }

      return storageItem.data;
    } catch (error) {
      // If decryption fails, remove the corrupted item silently
      this.removeItem(key);
      
      // Only log in development mode to avoid console spam
      if (import.meta.env.DEV) {
        console.warn(`Failed to decrypt data for key: ${key}`, error);
      }
      
      return null;
    }
  }

  /**
   * Remove encrypted item
   */
  removeItem(key: string): void {
    this.storage.removeItem(this.generateKey(key));
  }

  /**
   * Check if item exists and is valid (not expired)
   */
  async hasItem(key: string): Promise<boolean> {
    const data = await this.getItem(key);
    return data !== null;
  }

  /**
   * Clear all encrypted items with our prefix
   */
  clear(): void {
    const keysToRemove: string[] = [];
    for (let i = 0; i < this.storage.length; i++) {
      const key = this.storage.key(i);
      if (key && key.startsWith(this.keyPrefix)) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => this.storage.removeItem(key));
  }

  /**
   * Get metadata about stored item (timestamp, expiration)
   */
  async getItemMetadata(key: string): Promise<{ timestamp: number; expiresAt?: number } | null> {
    try {
      const encrypted = this.storage.getItem(this.generateKey(key));
      if (!encrypted) return null;

      const decrypted = await decryptData(encrypted);
      const storageItem: StorageItem = JSON.parse(decrypted);

      return {
        timestamp: storageItem.timestamp,
        expiresAt: storageItem.expiresAt,
      };
    } catch {
      return null;
    }
  }
}

/**
 * Encrypted localStorage instance
 */
export const encryptedLocalStorage = new BaseEncryptedStorage('localStorage', 'enc_local_');

/**
 * Encrypted sessionStorage instance
 */
export const encryptedSessionStorage = new BaseEncryptedStorage('sessionStorage', 'enc_session_');

/**
 * Helper functions for common patterns
 */
export const secureStorageHelpers = {
  /**
   * Store user preferences securely
   */
  async setUserPreferences(preferences: any): Promise<void> {
    await encryptedLocalStorage.setItem('user_preferences', preferences);
  },

  /**
   * Get user preferences securely
   */
  async getUserPreferences(): Promise<any | null> {
    return await encryptedLocalStorage.getItem('user_preferences');
  },

  /**
   * Store telegram data securely with expiration
   */
  async setTelegramData(data: any, expirationHours: number = 24): Promise<void> {
    await encryptedSessionStorage.setItem('telegram_data', data, expirationHours);
  },

  /**
   * Get telegram data securely
   */
  async getTelegramData(): Promise<any | null> {
    return await encryptedSessionStorage.getItem('telegram_data');
  },

  /**
   * Store UI state securely
   */
  async setUIState(state: any): Promise<void> {
    await encryptedLocalStorage.setItem('ui_state', state);
  },

  /**
   * Get UI state securely
   */
  async getUIState(): Promise<any | null> {
    return await encryptedLocalStorage.getItem('ui_state');
  },

  /**
   * Store session state securely
   */
  async setSessionState(state: any, expirationHours: number = 8): Promise<void> {
    await encryptedSessionStorage.setItem('session_state', state, expirationHours);
  },

  /**
   * Get session state securely
   */
  async getSessionState(): Promise<any | null> {
    return await encryptedSessionStorage.getItem('session_state');
  },

  /**
   * Clear all secure storage
   */
  clearAll(): void {
    encryptedLocalStorage.clear();
    encryptedSessionStorage.clear();
  },
};

/**
 * Migration helper to move from insecure to secure storage
 */
export const migrationHelpers = {
  /**
   * Migrate localStorage data to encrypted storage
   */
  async migrateLocalStorageItem(oldKey: string, newKey?: string): Promise<boolean> {
    try {
      const data = localStorage.getItem(oldKey);
      if (!data) return false;

      const parsed = JSON.parse(data);
      await encryptedLocalStorage.setItem(newKey || oldKey, parsed);

      // Remove old insecure data
      localStorage.removeItem(oldKey);
      return true;
    } catch (error) {
      console.warn(`Failed to migrate localStorage item: ${oldKey}`, error);
      return false;
    }
  },

  /**
   * Migrate sessionStorage data to encrypted storage
   */
  async migrateSessionStorageItem(oldKey: string, newKey?: string): Promise<boolean> {
    try {
      const data = sessionStorage.getItem(oldKey);
      if (!data) return false;

      const parsed = JSON.parse(data);
      await encryptedSessionStorage.setItem(newKey || oldKey, parsed);

      // Remove old insecure data
      sessionStorage.removeItem(oldKey);
      return true;
    } catch (error) {
      console.warn(`Failed to migrate sessionStorage item: ${oldKey}`, error);
      return false;
    }
  },

  /**
   * Migrate all common storage items
   */
  async migrateAllStorageItems(): Promise<void> {
    console.log('[EncryptedStorage] Starting migration from insecure storage...');

    // Migrate localStorage items
    await this.migrateLocalStorageItem('atlas-ui-storage');
    await this.migrateLocalStorageItem('user_theme');
    await this.migrateLocalStorageItem('user_language');
    await this.migrateLocalStorageItem('vpnverse_performance_mode');

    // Migrate paint data
    const paintDataKeys = Object.keys(localStorage).filter(key =>
      key.startsWith('vpnverse_land_paint_')
    );
    for (const key of paintDataKeys) {
      await this.migrateLocalStorageItem(key);
    }

    // Migrate sessionStorage items
    await this.migrateSessionStorageItem('telegram_verified_data');
    await this.migrateSessionStorageItem('telegramMiniAppState');
    await this.migrateSessionStorageItem('telegramMiniAppDeactivated');
    await this.migrateSessionStorageItem('referral_code');
    await this.migrateSessionStorageItem('login_attempt_timestamp');
    await this.migrateSessionStorageItem('check_card_profits');
    await this.migrateSessionStorageItem('isFreshLogin');

    console.log('[EncryptedStorage] Migration completed.');
  },
};

/**
 * Initialization function for encrypted storage
 */
export async function initializeEncryptedStorage(): Promise<void> {
  try {
    console.log('[EncryptedStorage] Initializing encrypted storage...');

    // Perform migration from insecure storage
    await migrationHelpers.migrateAllStorageItems();

    console.log('[EncryptedStorage] Encrypted storage initialized successfully.');
  } catch (error) {
    console.error('[EncryptedStorage] Failed to initialize encrypted storage:', error);
    throw new EncryptedStorageError('Failed to initialize encrypted storage', error);
  }
}

export default {
  localStorage: encryptedLocalStorage,
  sessionStorage: encryptedSessionStorage,
  helpers: secureStorageHelpers,
  migration: migrationHelpers,
  initialize: initializeEncryptedStorage,
};
