@import "tailwindcss";

:root {
  --accent-hue: 270;
  --accent-saturation: 100%;
  --accent-lightness: 65%;

  /* Safe area insets with defaults for non-Telegram environments */
  --safe-area-top: 0px;
  --safe-area-right: 0px;
  --safe-area-bottom: 0px;
  --safe-area-left: 0px;

  /* Content safe area insets */
  --content-safe-area-top: 0px;
  --content-safe-area-right: 0px;
  --content-safe-area-bottom: 0px;
  --content-safe-area-left: 0px;
}

@layer base {
  body {
    @apply bg-base-950 text-white font-sans antialiased;
    text-rendering: optimizeLegibility;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-display tracking-wide;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  p,
  span,
  div {
    @apply text-white/90;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  /* Apply safe area padding */
  .safe-area-padding {
    padding-top: var(--safe-area-top);
    padding-right: var(--safe-area-right);
    padding-bottom: var(--safe-area-bottom);
    padding-left: var(--safe-area-left);
  }

  /* Content safe area padding */
  .content-safe-area-padding {
    padding-top: var(--content-safe-area-top);
    padding-right: var(--content-safe-area-right);
    padding-bottom: var(--content-safe-area-bottom);
    padding-left: var(--content-safe-area-left);
  }

  /* Individual safe area paddings */
  .safe-area-pt {
    padding-top: var(--safe-area-top);
  }
  .safe-area-pr {
    padding-right: var(--safe-area-right);
  }
  .safe-area-pb {
    padding-bottom: var(--safe-area-bottom);
  }
  .safe-area-pl {
    padding-left: var(--safe-area-left);
  }

  /* Utilities for fixed positioning with safe area */
  .fixed-bottom-safe {
    position: fixed;
    bottom: var(--safe-area-bottom);
  }

  .fixed-top-safe {
    position: fixed;
    top: var(--safe-area-top);
  }
}

@layer components {
  .subscription-card {
    @apply bg-base-900/50 backdrop-blur-lg 
           rounded-lg p-4
           border border-base-800/30 
           hover:border-accent-500/20
           transition-all duration-300;
  }

  .card {
    @apply bg-base-900/50 backdrop-blur-lg 
           rounded-lg p-4
           border border-base-800/30 
           hover:border-accent-500/20
           shadow-[0_8px_30px_rgb(0,0,0,0.12)]
           transition-all duration-300;
  }

  .bg-dark-gradient {
    @apply bg-base-900/95 backdrop-blur-sm;
  }

  .bg-content-dark {
    @apply bg-base-900/95 backdrop-blur-sm;
  }

  .glass {
    @apply bg-base-900/50 backdrop-blur-lg 
           border border-white/10
           hover:border-white/20
           transition-all duration-300;
  }

  .menu-item {
    @apply w-full px-3 py-2 flex items-center text-sm text-white/80 hover:text-white
           hover:bg-white/5 rounded transition-colors duration-200;
  }

  .loading-backdrop {
    @apply fixed inset-0 z-50 bg-base-950/80 backdrop-blur-sm
           flex items-center justify-center;
  }

  .loading-container {
    @apply flex flex-col items-center space-y-4 p-6
           bg-gradient-to-b from-base-900/90 to-base-900/80
           rounded-lg border border-white/10 shadow-xl;
  }

  .bg-glass {
    @apply bg-base-900/50;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-purple-400 via-accent-500 to-purple-400 
           bg-clip-text text-transparent
           bg-[length:200%_auto]
           animate-[text-shimmer_3s_linear_infinite];
  }

  .button-primary {
    @apply px-4 py-2 bg-gradient-to-r from-accent-500 to-accent-400
           hover:from-accent-400 hover:to-accent-500
           text-white font-display tracking-wide rounded-lg
           shadow-lg shadow-accent-500/20
           transition-all duration-300
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .button-secondary {
    @apply px-4 py-2 bg-white/10 hover:bg-white/15
           text-white font-display tracking-wide rounded-lg
           shadow-lg shadow-black/20
           transition-all duration-300
           disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .input {
    @apply w-full px-4 py-2 bg-base-900/50 
           border border-white/10 rounded-lg
           text-white placeholder-white/50
           focus:outline-none focus:ring-2 focus:ring-accent-500/50
           transition-all duration-300;
  }

  .loading-screen {
    @apply fixed inset-0 z-50 bg-base-950 
           flex flex-col items-center justify-center;
  }

  .loading-rings {
    @apply relative;
    width: 280px;
    height: 280px;
  }

  .loading-ring {
    @apply absolute inset-0 border-2 border-transparent rounded-full;
  }

  .loading-ring-outer {
    @apply border-t-accent-500/20 border-r-accent-500/20
           animate-[spin_8s_linear_infinite];
  }

  .loading-ring-middle {
    @apply border-t-accent-500/40 border-r-accent-500/40
           animate-[spin_6s_linear_infinite_reverse];
  }

  .loading-ring-inner {
    @apply inset-2 border-b-accent-500/60 border-l-accent-500/60
           animate-[spin_4s_linear_infinite];
  }

  .loading-logo {
    @apply absolute inset-0 flex flex-col items-center justify-center gap-4;
  }

  .loading-logo-text {
    @apply text-5xl font-display font-bold text-gradient;
  }

  .loading-status {
    @apply text-sm text-accent-500/60 uppercase tracking-wider;
  }

  .landing-hero {
    @apply pt-20 pb-32 text-center;
  }

  .landing-title {
    @apply text-5xl sm:text-6xl font-bold font-display
           bg-gradient-to-r from-accent-400 via-accent-500 to-accent-400
           bg-clip-text text-transparent;
  }

  .landing-subtitle {
    @apply text-xl text-accent-300/80;
  }

  .landing-cards {
    @apply py-20 bg-base-900/10 backdrop-blur-sm
           border-t border-b border-accent-500/10;
  }

  .landing-card {
    @apply bg-base-900/20 rounded-xl p-6
           border border-accent-500/20
           hover:border-accent-500/40
           transition-all duration-300;
  }
}

@layer utilities {
  .bg-aurora {
    background: linear-gradient(
      135deg,
      rgba(147, 51, 234, 0.1) 0%,
      rgba(79, 70, 229, 0.1) 25%,
      rgba(236, 72, 153, 0.1) 50%,
      rgba(234, 179, 8, 0.1) 75%,
      rgba(147, 51, 234, 0.1) 100%
    );
    background-size: 400% 400%;
    animation: aurora 15s ease infinite;
  }

  ::-webkit-scrollbar-track {
    @apply bg-base-900/50;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-accent-500/30 rounded-full hover:bg-accent-500/50 transition-colors duration-300;
  }

  ::selection {
    @apply bg-accent-500/30 text-white;
  }

  .fade-enter {
    @apply opacity-0 translate-y-4;
  }

  .fade-enter-active {
    @apply opacity-100 translate-y-0
           transition-all duration-500 ease-out;
  }

  .fade-exit {
    @apply opacity-100;
  }

  .fade-exit-active {
    @apply opacity-0 translate-y-4
           transition-all duration-300 ease-in;
  }
}

/* Animations */
@keyframes pulse-border {
  0% {
    border-color: rgba(139, 92, 246, 0.2);
  }
  50% {
    border-color: rgba(139, 92, 246, 0.4);
  }
  100% {
    border-color: rgba(139, 92, 246, 0.2);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes aurora {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Animation Classes */
.active-subscription {
  animation: pulse-border 2s infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Scrollbar Base Styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

@keyframes text-shimmer {
  0% {
    background-position: -100% 50%;
  }
  100% {
    background-position: 200% 50%;
  }
}

/* Chat UI styles */
body.chat-open {
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
  -webkit-overflow-scrolling: touch;
}

/* Hide scrollbars but allow scrolling */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch;
}
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Ensure all elements handle touch properly for chat */
.chat-touch-fix * {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

/* Mobile button fix */
.chat-touch-fix button {
  /* Only use translateZ(0) if rendering glitches occur */
  /* transform: translateZ(0); */
  /* -webkit-transform: translateZ(0); */
  position: relative;
  user-select: none;
  -webkit-user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* Cleaned up input container styles */
.message-input-fixed,
.chat-input-container {
  width: 100%;
  background: #0c0c10;
  padding-bottom: env(safe-area-inset-bottom);
  /* No position, no transform, no z-index */
}

/* Only touch-action for buttons */
.static-button {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* Remove static-container and static-input disables */
/* .static-container, .static-container *, .static-input, .static-input * { ... } */

/* Remove keyboard-open overrides for input container */
/* .keyboard-open .static-input, .keyboard-open .message-input-container { ... } */

@supports (-webkit-touch-callout: none) {
  .chat-dialog-outer {
    position: absolute !important;
    height: 100% !important;
    top: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    /* No fixed or vh units on iOS */
  }
}
