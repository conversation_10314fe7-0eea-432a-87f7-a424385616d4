module.exports = {
  apps: [{
    name: "vpn-dashboard-frontend",
    script: "npm",
    args: "start",
    cwd: "./",
    env: {
      NODE_ENV: "development",
    },
    exp_backoff_restart_delay: 100,
    max_memory_restart: "1G",
    watch: false,
    time: true,
    instances: 1,
    autorestart: true,
    max_restarts: 10,
    restart_delay: 4000,
    error_file: "logs/err.log",
    out_file: "logs/out.log",
    merge_logs: true,
    log_date_format: "YYYY-MM-DD HH:mm:ss Z"
  }]
} 