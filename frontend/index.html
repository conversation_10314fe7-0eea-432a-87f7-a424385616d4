<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
    <meta name="theme-color" content="#030303" />
    <meta name="description" content="VIPVerse - Secure VIP Verse" />
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- CSP for security (frame-ancestors removed as it's ignored in meta tags) -->
    <meta http-equiv="Content-Security-Policy" content="
      default-src 'self';
      script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org https://challenges.cloudflare.com https://challenges.cloudflare.com/turnstile/ https://challenges.cloudflare.com/turnstile/v0/api.js https://cdn.jsdelivr.net blob:;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      img-src 'self' data: https:;
      font-src 'self' https://fonts.gstatic.com data:;
      connect-src 'self' wss: https: ws: http: localhost:* 127.0.0.1:*;
      frame-src 'self' https://challenges.cloudflare.com;
      object-src 'none';
      base-uri 'self';
      form-action 'self';
      upgrade-insecure-requests;
    ">
    
    <!-- DNS prefetch for external resources -->
    <link rel="dns-prefetch" href="//telegram.org">
    <link rel="dns-prefetch" href="//challenges.cloudflare.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    
    <!-- Preconnect to critical external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Critical CSS - Inline for immediate rendering -->
    <style>
      /* Base styles */
      *, *::before, *::after { 
        box-sizing: border-box; 
        border-width: 0; 
        border-style: solid; 
        border-color: currentColor; 
      }
      
      html, body { 
        margin: 0; 
        padding: 0; 
        width: 100%; 
        height: 100%; 
        overflow: hidden; 
        background: #030303; 
        color: #e0e0e0; 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji'; 
        -webkit-font-smoothing: antialiased; 
        -moz-osx-font-smoothing: grayscale; 
      }

      /* Loading Screen Container */
      .loading-screen { 
        position: fixed; 
        inset: 0; 
        display: flex; 
        flex-direction: column; 
        align-items: center; 
        justify-content: center; 
        background: #030303; 
        z-index: 99999; 
        opacity: 1; 
        transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1); 
        pointer-events: auto; 
      }
      
      .loading-screen.hidden { 
        opacity: 0; 
        pointer-events: none; 
      }

      /* Content within loading screen */
      .loading-content { 
        display: flex; 
        flex-direction: column; 
        align-items: center; 
        justify-content: center; 
        text-align: center; 
        padding: 20px; 
      }
      
      /* Rocket Icon */
      .loading-icon { 
        font-size: 3.5rem; 
        margin-bottom: 24px; 
        animation: floatIcon 3s ease-in-out infinite; 
      }
      
      @keyframes floatIcon { 
        0%, 100% { transform: translateY(0); } 
        50% { transform: translateY(-10px); } 
      }

      /* Spinner */
      .spinner { 
        width: 56px; 
        height: 56px; 
        border: 5px solid rgba(107, 33, 168, 0.25); 
        border-top-color: #8B5CF6; 
        border-radius: 50%; 
        animation: spin 1.2s linear infinite; 
        margin-bottom: 28px; 
      }
      
      @keyframes spin { 
        to { transform: rotate(360deg); } 
      }

      /* Typography */
      .app-title { 
        font-size: 2.25rem; 
        font-weight: 700; 
        color: #f3f4f6; 
        margin: 0 0 12px 0; 
        letter-spacing: -0.025em; 
      }
      
      .app-subtitle { 
        font-size: 1rem; 
        color: #a1a1aa; 
        margin: 0 0 32px 0; 
      }
      
      .loading-text { 
        font-size: 0.95rem; 
        color: #8B5CF6; 
        font-weight: 500; 
        animation: pulseText 1.8s infinite alternate; 
      }
      
      @keyframes pulseText { 
        from { opacity: 0.7; } 
        to { opacity: 1; } 
      }

      /* Background pattern */
      .loading-screen::before { 
        content: ''; 
        position: absolute; 
        top: 0; 
        left: 0; 
        width: 100%; 
        height: 100%; 
        background-image: 
          radial-gradient(circle at 20% 20%, rgba(107, 33, 168, 0.08) 0%, transparent 50%), 
          radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.06) 0%, transparent 40%); 
        z-index: -1; 
        opacity: 0.7; 
      }

      /* Root container */
      #root { 
        min-height: 100vh; 
      }
    </style>
    
    <!-- Preload critical fonts -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet"></noscript>
    
    <!-- Telegram WebApp - Critical, load immediately -->
    <script>
      // Enhanced Telegram initialization with retry logic
      window.telegramReady = false;
      window.telegramError = null;
      window.telegramRetries = 0;
      window.maxTelegramRetries = 3;
      
      function initTelegramWebApp() {
        try {
          if (window.Telegram?.WebApp) {
            const tg = window.Telegram.WebApp;
            
            // Enhanced initialization
            tg.ready();
            tg.expand();
            
            // Set theme
            if (tg.themeParams) {
              document.documentElement.style.setProperty('--tg-bg-color', tg.themeParams.bg_color || '#1a1b1e');
              document.documentElement.style.setProperty('--tg-text-color', tg.themeParams.text_color || '#ffffff');
            }
            
            window.telegramReady = true;
            console.log('[HTML] Telegram WebApp ready, version:', tg.version);
            return true;
          }
        } catch (error) {
          window.telegramError = error;
          console.warn('[HTML] Telegram init failed:', error);
        }
        return false;
      }
      
      function retryTelegramInit() {
        if (window.telegramRetries < window.maxTelegramRetries) {
          window.telegramRetries++;
          console.log(`[HTML] Retrying Telegram init (${window.telegramRetries}/${window.maxTelegramRetries})`);
          setTimeout(() => {
            if (!initTelegramWebApp()) {
              retryTelegramInit();
            }
          }, 500 * window.telegramRetries); // Exponential backoff
        } else {
          console.warn('[HTML] Telegram WebApp failed to initialize after retries');
          window.telegramError = 'Max retries exceeded';
        }
      }
      
      // Try to initialize immediately if available
      if (!initTelegramWebApp()) {
        // If immediate init fails, we'll retry after script loads
        console.log('[HTML] Telegram not ready yet, will retry after script load');
      }
    </script>
    <script 
      src="https://telegram.org/js/telegram-web-app.js" 
      onload="if (!window.telegramReady) initTelegramWebApp() || retryTelegramInit()" 
      onerror="window.telegramError='Failed to load Telegram script'; console.error('[HTML] Failed to load Telegram script')" 
      async>
    </script>
    
    <!-- Cloudflare Turnstile - Non-critical, defer -->
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
    
    <title>VIPVerse</title>
  </head>
  <body>
    <noscript>
      <div style="display:flex;align-items:center;justify-content:center;height:100vh;background:#030303;color:#e0e0e0;text-align:center;font-family:system-ui">
        <div><h1>JavaScript Required</h1><p>Please enable JavaScript to use VIPVerse</p></div>
      </div>
    </noscript>
    
    <!-- React 19 root container -->
    <div id="root"></div>
    
    <!-- Loading screen -->
    <div class="loading-screen" id="loading">
      <div class="loading-content">
        <div class="loading-icon">🚀</div>
        <h1 class="app-title">VIPVerse</h1>
        <p class="app-subtitle">Welcome to the future</p>
        <div class="spinner"></div>
        <p class="loading-text">Initializing...</p>
      </div>
    </div>
    
    <!-- React 19 + Vite 6 module loading -->
    <script type="module">
      // Performance tracking
      const startTime = performance.now();
      console.log('[HTML] Module loading started');
      
      // Hide loading screen function
      function hideLoading() {
        const loadingEl = document.getElementById('loading');
        if (loadingEl) {
          loadingEl.classList.add('hidden');
          setTimeout(() => {
            if (loadingEl.parentNode) {
              loadingEl.remove();
            }
          }, 500);
        }
      }
      
      // Make globally available
      window.hideLoadingScreen = hideLoading;
      
      // Load the React 19 app
      async function loadApp() {
        try {
          console.log('[HTML] Importing main module...');
          await import('/src/main.tsx');
          console.log(`[HTML] App loaded in ${(performance.now() - startTime).toFixed(2)}ms`);
        } catch (error) {
          console.error('[HTML] Failed to load app:', error);
          
          // Show error UI
          const rootEl = document.getElementById('root');
          if (rootEl) {
            rootEl.innerHTML = `
              <div style="display:flex;flex-direction:column;align-items:center;justify-content:center;height:100vh;background:#030303;color:#e0e0e0;text-align:center;padding:20px;font-family:system-ui">
                <div style="font-size:3rem;margin-bottom:20px">⚠️</div>
                <h1 style="margin:0 0 10px 0;font-size:1.5rem;font-weight:600">Loading Failed</h1>
                <p style="margin:0 0 20px 0;opacity:.8;font-size:.875rem">Unable to load the application. Please check your connection and try again.</p>
                <button onclick="location.reload()" style="padding:12px 24px;background:linear-gradient(135deg,#8B5CF6,#7C3AED);color:#fff;border:none;border-radius:8px;cursor:pointer;font-family:inherit;font-weight:500;font-size:.875rem;transition:transform .2s, box-shadow .2s;" onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 4px 15px rgba(139, 92, 246, 0.3)';" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
                  Reload App
                </button>
              </div>
            `;
          }
          hideLoading();
        }
      }
      
      // Start loading
      loadApp();
    </script>
  </body>
</html> 