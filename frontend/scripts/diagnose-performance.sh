#!/bin/bash

# Performance diagnostic script for VIPVerse frontend

echo "🔍 VIPVerse Performance Diagnostics"
echo "==================================="

echo "1. System Information:"
echo "   - Node version: $(node --version)"
echo "   - pnpm version: $(pnpm --version)"
echo "   - Memory: $(free -h | grep Mem | awk '{print $2 " total, " $3 " used"}')"
echo "   - CPU cores: $(nproc)"

echo ""
echo "2. Project Information:"
echo "   - Dependencies: $(cat package.json | grep -c '".*":')"
echo "   - Node modules size: $(du -sh node_modules 2>/dev/null || echo 'Not found')"
echo "   - Vite cache size: $(du -sh node_modules/.vite 2>/dev/null || echo 'No cache')"

echo ""
echo "3. Network Diagnostics:"
echo "   - Backend connectivity:"
curl -s -w "   - Response time: %{time_total}s\n" -o /dev/null https://dev.atlasvip.cloud:2083/docs || echo "   - Backend unreachable"

echo ""
echo "4. Performance Recommendations:"

# Check if running in production mode
if [ "$NODE_ENV" = "production" ]; then
    echo "   ✅ Running in production mode"
else
    echo "   ⚠️  Running in development mode (slower)"
fi

# Check memory
MEMORY_GB=$(free -g | grep Mem | awk '{print $2}')
if [ "$MEMORY_GB" -lt 4 ]; then
    echo "   ⚠️  Low memory detected (${MEMORY_GB}GB). Consider upgrading."
else
    echo "   ✅ Sufficient memory (${MEMORY_GB}GB)"
fi

# Check for heavy dependencies
HEAVY_DEPS=$(grep -E "(three|@react-three|fabric|canvas)" package.json | wc -l)
if [ "$HEAVY_DEPS" -gt 5 ]; then
    echo "   ⚠️  Many heavy dependencies detected ($HEAVY_DEPS). Consider lazy loading."
else
    echo "   ✅ Reasonable dependency count"
fi

echo ""
echo "5. Quick Fixes:"
echo "   - Run: pnpm clean:deps && pnpm dev"
echo "   - Increase Node memory: NODE_OPTIONS='--max-old-space-size=4096'"
echo "   - Use fast restart: ./scripts/fast-restart.sh"

echo ""
echo "6. Current Process Status:"
ps aux | grep -E "(vite|node)" | grep -v grep | head -5 