#!/bin/bash

# Development script for HTTPS testing with Telegram WebApp
# Uses HTTPS domain for Telegram mini app testing

echo "🚀 Starting VIPVerse - HTTPS Development Mode"
echo "=============================================="

# Set environment for HTTPS development
export NODE_ENV=development

# Copy HTTPS environment configuration
cp .env.https .env

echo "📄 Using HTTPS configuration:"
echo "   API URL: https://dev.atlasvip.cloud"
echo "   WebSocket: wss://dev.atlasvip.cloud/ws"
echo ""

# Clear Vite cache for clean start
echo "🧹 Clearing Vite cache..."
rm -rf node_modules/.vite/deps*

# Start development server
echo "⚡ Starting Vite development server..."
pnpm dev

echo ""
echo "✅ Development server started!"
echo "🌐 Frontend: https://dev.atlasvip.cloud"
echo "🔧 Backend: https://dev.atlasvip.cloud/api"
echo "📚 API Docs: https://dev.atlasvip.cloud/docs"
echo "📱 Telegram WebApp URL: https://dev.atlasvip.cloud"
