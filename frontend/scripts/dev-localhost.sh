#!/bin/bash

# Development script for localhost testing
# Uses local backend on port 8000

echo "🚀 Starting VIPVerse - Localhost Development Mode"
echo "================================================"

# Set environment for localhost development
export NODE_ENV=development

# Copy localhost environment configuration
cp .env.development .env

echo "📄 Using localhost configuration:"
echo "   API URL: http://localhost:8000"
echo "   WebSocket: ws://localhost:8000/ws"
echo ""

# Clear Vite cache for clean start
echo "🧹 Clearing Vite cache..."
rm -rf node_modules/.vite/deps*

# Start development server
echo "⚡ Starting Vite development server..."
pnpm dev

echo ""
echo "✅ Development server started!"
echo "🌐 Frontend: http://localhost:3000"
echo "🔧 Backend: http://localhost:8000"
echo "📚 API Docs: http://localhost:8000/docs"
