#!/bin/bash

# Optimized Frontend Startup Script for AtlasVPN
# This script starts the frontend with performance optimizations applied

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
FRONTEND_DIR="/opt/atlasvpn/frontend"
BACKEND_URL="http://localhost:8000"
FRONTEND_PORT=3000

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

# Check if directory exists
check_directory() {
    if [[ ! -d "$FRONTEND_DIR" ]]; then
        error "Frontend directory not found: $FRONTEND_DIR"
        exit 1
    fi
}

# Check if pnpm is installed
check_pnpm() {
    if ! command -v pnpm &> /dev/null; then
        warn "pnpm not found, installing..."
        npm install -g pnpm@9.0.0
    fi
    
    log "pnpm version: $(pnpm --version)"
}

# Check backend connectivity
check_backend() {
    log "Checking backend connectivity..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -s "$BACKEND_URL/health" >/dev/null 2>&1 || curl -s "$BACKEND_URL/docs" >/dev/null 2>&1; then
            log "✅ Backend is accessible at $BACKEND_URL"
            return 0
        fi
        
        if [[ $attempt -eq 1 ]]; then
            warn "Backend not accessible, waiting..."
        fi
        
        sleep 2
        ((attempt++))
    done
    
    warn "Backend not accessible after $max_attempts attempts"
    warn "Frontend will start anyway, but API calls may fail"
    return 1
}

# Clean up existing processes
cleanup_processes() {
    log "Cleaning up existing frontend processes..."
    
    # Kill existing Vite processes
    pkill -f "vite.*$FRONTEND_PORT" 2>/dev/null || true
    pkill -f "node.*vite" 2>/dev/null || true
    
    # Wait for processes to terminate
    sleep 2
    
    # Check if port is still in use
    if lsof -ti:$FRONTEND_PORT >/dev/null 2>&1; then
        warn "Port $FRONTEND_PORT still in use, force killing..."
        lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
        sleep 1
    fi
    
    log "Process cleanup completed"
}

# Clean dependency cache
clean_cache() {
    log "Cleaning dependency cache..."
    
    cd "$FRONTEND_DIR"
    
    # Clean Vite cache
    rm -rf node_modules/.vite/deps* 2>/dev/null || true
    rm -rf dist 2>/dev/null || true
    
    # Clean pnpm cache if needed
    if [[ "$1" == "--deep-clean" ]]; then
        warn "Performing deep clean..."
        rm -rf node_modules 2>/dev/null || true
        pnpm install
    fi
    
    log "Cache cleanup completed"
}

# Verify dependencies
verify_dependencies() {
    log "Verifying dependencies..."
    
    cd "$FRONTEND_DIR"
    
    if [[ ! -d "node_modules" ]]; then
        log "Installing dependencies..."
        pnpm install
    else
        log "Dependencies already installed"
    fi
    
    # Check for critical dependencies
    local critical_deps=("react" "vite" "@vitejs/plugin-react-swc")
    for dep in "${critical_deps[@]}"; do
        if [[ ! -d "node_modules/$dep" ]]; then
            warn "Critical dependency missing: $dep"
            log "Reinstalling dependencies..."
            pnpm install
            break
        fi
    done
    
    log "Dependencies verified"
}

# Set environment variables for optimal performance
set_environment() {
    log "Setting performance environment variables..."
    
    # Node.js optimizations
    export NODE_ENV=development
    export NODE_OPTIONS="--max-old-space-size=4096"
    
    # Vite optimizations
    export VITE_MINIMAL=false
    export VITE_PERFORMANCE_MODE=true
    
    # Development optimizations
    export FORCE_COLOR=1
    export CI=false
    
    log "Environment variables set"
}

# Start the frontend development server
start_frontend() {
    log "Starting frontend development server..."
    
    cd "$FRONTEND_DIR"
    
    # Choose the appropriate start command based on arguments
    local start_command="dev"
    
    case "$1" in
        "--fast")
            start_command="dev:fast"
            log "Using fast development mode"
            ;;
        "--minimal")
            start_command="dev:minimal"
            log "Using minimal development mode"
            ;;
        *)
            log "Using standard development mode"
            ;;
    esac
    
    log "Starting with command: pnpm run $start_command"
    log "Frontend will be available at: http://localhost:$FRONTEND_PORT"
    log "Press Ctrl+C to stop the server"
    echo ""
    
    # Start the development server
    exec pnpm run $start_command
}

# Display system information
show_system_info() {
    log "=== SYSTEM INFORMATION ==="
    echo "Node.js: $(node --version 2>/dev/null || echo 'Not found')"
    echo "pnpm: $(pnpm --version 2>/dev/null || echo 'Not found')"
    echo "CPU Cores: $(nproc)"
    echo "Memory: $(free -h | grep Mem | awk '{print $2 " total, " $7 " available"}')"
    echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
    echo ""
}

# Display usage information
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  --fast        Use fast development mode (skips dependency optimization)"
    echo "  --minimal     Use minimal development mode (skips heavy components)"
    echo "  --deep-clean  Perform deep clean (removes node_modules)"
    echo "  --help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Standard development mode"
    echo "  $0 --fast            # Fast startup mode"
    echo "  $0 --minimal         # Minimal mode for testing"
    echo "  $0 --deep-clean      # Clean install and start"
    echo ""
}

# Main execution
main() {
    # Handle help option
    if [[ "$1" == "--help" ]]; then
        show_usage
        exit 0
    fi
    
    log "Starting AtlasVPN Frontend (Optimized)..."
    
    show_system_info
    check_directory
    check_pnpm
    cleanup_processes
    clean_cache "$1"
    verify_dependencies
    set_environment
    check_backend
    
    log "=== STARTING FRONTEND SERVER ==="
    start_frontend "$1"
}

# Handle script interruption
trap 'echo -e "\n${YELLOW}Frontend server stopped${NC}"; exit 0' INT TERM

# Run main function with all arguments
main "$@"
