#!/bin/bash

# Nginx Configuration Checker for AtlasVPN
# This script validates and optimizes Nginx configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NGINX_SITES_AVAILABLE="/etc/nginx/sites-available"
NGINX_SITES_ENABLED="/etc/nginx/sites-enabled"
ATLAS_CONFIG="atlas-vpn-site.conf"
BACKEND_PORT=8000
FRONTEND_PORT=3000

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
        exit 1
    fi
}

# Check Nginx installation
check_nginx_installation() {
    log "=== CHECKING NGINX INSTALLATION ==="
    
    if ! command -v nginx &> /dev/null; then
        error "Nginx is not installed"
        log "Installing Nginx..."
        apt-get update >/dev/null 2>&1
        apt-get install -y nginx >/dev/null 2>&1
        log "Nginx installed successfully"
    else
        log "✅ Nginx is installed: $(nginx -v 2>&1)"
    fi
    
    # Check if Nginx is running
    if systemctl is-active --quiet nginx; then
        log "✅ Nginx is running"
    else
        warn "Nginx is not running, starting..."
        systemctl start nginx
        log "✅ Nginx started"
    fi
}

# Check current configuration
check_current_config() {
    log "=== CHECKING CURRENT CONFIGURATION ==="
    
    local config_file="$NGINX_SITES_AVAILABLE/$ATLAS_CONFIG"
    
    if [[ -f "$config_file" ]]; then
        log "✅ Atlas VPN configuration found: $config_file"
        
        # Check if it's enabled
        if [[ -L "$NGINX_SITES_ENABLED/$ATLAS_CONFIG" ]]; then
            log "✅ Configuration is enabled"
        else
            warn "Configuration exists but is not enabled"
            log "Enabling configuration..."
            ln -sf "$config_file" "$NGINX_SITES_ENABLED/$ATLAS_CONFIG"
            log "✅ Configuration enabled"
        fi
    else
        warn "Atlas VPN configuration not found"
        return 1
    fi
}

# Test Nginx configuration
test_nginx_config() {
    log "=== TESTING NGINX CONFIGURATION ==="
    
    if nginx -t 2>/dev/null; then
        log "✅ Nginx configuration is valid"
        return 0
    else
        error "Nginx configuration has errors:"
        nginx -t
        return 1
    fi
}

# Check backend connectivity
check_backend_connectivity() {
    log "=== CHECKING BACKEND CONNECTIVITY ==="
    
    if curl -s "http://localhost:$BACKEND_PORT/health" >/dev/null 2>&1; then
        log "✅ Backend is accessible on port $BACKEND_PORT"
    elif curl -s "http://localhost:$BACKEND_PORT/docs" >/dev/null 2>&1; then
        log "✅ Backend is accessible on port $BACKEND_PORT (docs endpoint)"
    else
        warn "Backend is not accessible on port $BACKEND_PORT"
        log "Checking if backend process is running..."
        
        if pgrep -f "uvicorn.*$BACKEND_PORT" >/dev/null; then
            log "✅ Backend process is running"
            warn "Backend may be starting up, please wait..."
        else
            error "Backend process is not running"
            log "Please start the backend first: cd /opt/atlasvpn/backend && source venv/bin/activate && uvicorn main:app --host 0.0.0.0 --port $BACKEND_PORT --reload"
        fi
    fi
}

# Check SSL certificates
check_ssl_certificates() {
    log "=== CHECKING SSL CERTIFICATES ==="
    
    local config_file="$NGINX_SITES_AVAILABLE/$ATLAS_CONFIG"
    
    if [[ -f "$config_file" ]]; then
        # Extract SSL certificate paths from config
        local cert_paths=$(grep -E "ssl_certificate" "$config_file" | grep -v "ssl_certificate_key" | awk '{print $2}' | tr -d ';')
        local key_paths=$(grep -E "ssl_certificate_key" "$config_file" | awk '{print $2}' | tr -d ';')
        
        if [[ -n "$cert_paths" ]]; then
            for cert_path in $cert_paths; do
                if [[ -f "$cert_path" ]]; then
                    log "✅ SSL certificate found: $cert_path"
                    
                    # Check certificate expiry
                    local expiry_date=$(openssl x509 -enddate -noout -in "$cert_path" 2>/dev/null | cut -d= -f2)
                    if [[ -n "$expiry_date" ]]; then
                        log "   Certificate expires: $expiry_date"
                    fi
                else
                    error "SSL certificate not found: $cert_path"
                fi
            done
            
            for key_path in $key_paths; do
                if [[ -f "$key_path" ]]; then
                    log "✅ SSL private key found: $key_path"
                else
                    error "SSL private key not found: $key_path"
                fi
            done
        else
            warn "No SSL certificates configured"
        fi
    fi
}

# Display configuration summary
show_config_summary() {
    log "=== CONFIGURATION SUMMARY ==="
    
    local config_file="$NGINX_SITES_AVAILABLE/$ATLAS_CONFIG"
    
    if [[ -f "$config_file" ]]; then
        echo "Configuration file: $config_file"
        
        # Extract server names
        local server_names=$(grep -E "server_name" "$config_file" | awk '{for(i=2;i<=NF;i++) printf "%s ", $i}' | tr -d ';')
        echo "Server names: $server_names"
        
        # Extract listen ports
        local listen_ports=$(grep -E "listen" "$config_file" | awk '{print $2}' | tr -d ';' | sort -u)
        echo "Listen ports: $(echo $listen_ports | tr '\n' ' ')"
        
        # Check proxy configurations
        echo "Proxy configurations:"
        grep -E "proxy_pass" "$config_file" | while read -r line; do
            local location=$(echo "$line" | grep -o "location [^{]*" | awk '{print $2}' || echo "unknown")
            local proxy_target=$(echo "$line" | awk '{print $2}' | tr -d ';')
            echo "  $location -> $proxy_target"
        done
    fi
}

# Create optimized configuration if needed
create_optimized_config() {
    log "=== CREATING OPTIMIZED CONFIGURATION ==="
    
    local config_file="$NGINX_SITES_AVAILABLE/$ATLAS_CONFIG"
    
    if [[ ! -f "$config_file" ]]; then
        log "Creating new Atlas VPN configuration..."
        
        cat > "$config_file" << 'EOF'
# Atlas VPN Site Configuration
# Optimized for performance and security

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

# Upstream backend
upstream backend {
    server 127.0.0.1:8000;
    keepalive 32;
}

server {
    listen 80;
    server_name dev.atlasvip.cloud;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name dev.atlasvip.cloud;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/dev.atlasvip.cloud/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/dev.atlasvip.cloud/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Frontend static files
    location / {
        root /var/www/atlasvpn/frontend;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://backend/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # WebSocket endpoint
    location /ws {
        proxy_pass http://backend/ws;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # API documentation
    location /docs {
        proxy_pass http://backend/docs;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /redoc {
        proxy_pass http://backend/redoc;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    location /openapi.json {
        proxy_pass http://backend/openapi.json;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF
        
        log "✅ Configuration created"
        
        # Enable the configuration
        ln -sf "$config_file" "$NGINX_SITES_ENABLED/$ATLAS_CONFIG"
        log "✅ Configuration enabled"
    else
        log "Configuration already exists"
    fi
}

# Main execution
main() {
    log "Starting Nginx Configuration Check..."
    
    check_root
    check_nginx_installation
    
    if ! check_current_config; then
        create_optimized_config
    fi
    
    if test_nginx_config; then
        log "Reloading Nginx configuration..."
        systemctl reload nginx
        log "✅ Nginx reloaded successfully"
    else
        error "Configuration test failed, not reloading"
        exit 1
    fi
    
    check_backend_connectivity
    check_ssl_certificates
    show_config_summary
    
    log "=== NGINX CHECK COMPLETED ==="
    log "Next steps:"
    echo "1. Ensure backend is running on port $BACKEND_PORT"
    echo "2. Start frontend development server on port $FRONTEND_PORT"
    echo "3. Test the application: https://dev.atlasvip.cloud"
    echo ""
}

# Run main function
main "$@"
