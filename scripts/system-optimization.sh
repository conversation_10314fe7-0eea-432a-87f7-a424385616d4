#!/bin/bash

# System Performance Optimization Script for AtlasVPN
# This script addresses high CPU usage and optimizes system performance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root"
        exit 1
    fi
}

# Display current system status
show_system_status() {
    log "=== CURRENT SYSTEM STATUS ==="
    echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
    echo "CPU Usage: $(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}')%"
    echo "Memory Usage: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
    echo "Active Processes: $(ps aux | wc -l)"
    echo "High CPU Processes:"
    ps aux --sort=-%cpu | head -10 | awk '{printf "  PID: %-8s CPU: %-6s CMD: %s\n", $2, $3"%", $11}'
    echo ""
}

# Kill problematic processes
kill_problematic_processes() {
    log "=== KILLING PROBLEMATIC PROCESSES ==="
    
    # Kill orphaned bash processes with high CPU usage
    warn "Killing orphaned bash processes with high CPU usage..."
    for pid in $(ps aux | awk '$3 > 20 && $11 ~ /bash/ && $2 != 1 {print $2}'); do
        if ps -p $pid > /dev/null 2>&1; then
            log "Killing bash process PID: $pid"
            kill -TERM $pid 2>/dev/null || true
            sleep 1
            if ps -p $pid > /dev/null 2>&1; then
                kill -KILL $pid 2>/dev/null || true
            fi
        fi
    done
    
    # Kill TypeScript compilation processes from /opt/void
    warn "Killing TypeScript compilation processes from /opt/void..."
    pkill -f "/opt/void.*tsc.*--watch" || true
    
    # Kill excessive VSCode server processes (keep only essential ones)
    warn "Cleaning up excessive VSCode server processes..."
    vscode_pids=$(ps aux | grep "vscode.*server" | grep -v grep | awk '{print $2}' | tail -n +3)
    for pid in $vscode_pids; do
        if ps -p $pid > /dev/null 2>&1; then
            log "Killing excess VSCode server process PID: $pid"
            kill -TERM $pid 2>/dev/null || true
        fi
    done
    
    sleep 3
    log "Process cleanup completed"
}

# Optimize system services
optimize_services() {
    log "=== OPTIMIZING SYSTEM SERVICES ==="
    
    # Disable unnecessary services
    local services_to_disable=(
        "snapd"
        "bluetooth"
        "cups"
        "avahi-daemon"
        "whoopsie"
        "apport"
    )
    
    for service in "${services_to_disable[@]}"; do
        if systemctl is-enabled $service >/dev/null 2>&1; then
            warn "Disabling service: $service"
            systemctl disable $service >/dev/null 2>&1 || true
            systemctl stop $service >/dev/null 2>&1 || true
        fi
    done
    
    # Optimize SSH (if running)
    if systemctl is-active ssh >/dev/null 2>&1; then
        log "Optimizing SSH configuration..."
        # Add SSH optimizations here if needed
    fi
    
    log "Service optimization completed"
}

# Set system limits and optimizations
set_system_limits() {
    log "=== SETTING SYSTEM LIMITS AND OPTIMIZATIONS ==="
    
    # Set file descriptor limits
    echo "* soft nofile 65536" >> /etc/security/limits.conf
    echo "* hard nofile 65536" >> /etc/security/limits.conf
    echo "root soft nofile 65536" >> /etc/security/limits.conf
    echo "root hard nofile 65536" >> /etc/security/limits.conf
    
    # Optimize kernel parameters
    cat >> /etc/sysctl.conf << EOF

# AtlasVPN Performance Optimizations
vm.swappiness=10
vm.dirty_ratio=15
vm.dirty_background_ratio=5
net.core.rmem_max=16777216
net.core.wmem_max=16777216
net.ipv4.tcp_rmem=4096 87380 16777216
net.ipv4.tcp_wmem=4096 65536 16777216
fs.file-max=2097152
EOF
    
    # Apply sysctl changes
    sysctl -p >/dev/null 2>&1
    
    log "System limits and optimizations applied"
}

# Clean up temporary files and caches
cleanup_system() {
    log "=== CLEANING UP SYSTEM ==="
    
    # Clean package cache
    apt-get clean >/dev/null 2>&1 || true
    
    # Clean temporary files
    find /tmp -type f -atime +7 -delete 2>/dev/null || true
    find /var/tmp -type f -atime +7 -delete 2>/dev/null || true
    
    # Clean log files older than 30 days
    find /var/log -name "*.log" -type f -mtime +30 -delete 2>/dev/null || true
    
    # Clean journal logs
    journalctl --vacuum-time=7d >/dev/null 2>&1 || true
    
    log "System cleanup completed"
}

# Install monitoring tools
install_monitoring_tools() {
    log "=== INSTALLING MONITORING TOOLS ==="
    
    # Update package list
    apt-get update >/dev/null 2>&1
    
    # Install essential monitoring tools
    apt-get install -y htop iotop nethogs iftop sysstat >/dev/null 2>&1
    
    log "Monitoring tools installed"
}

# Create monitoring script
create_monitoring_script() {
    log "=== CREATING MONITORING SCRIPT ==="
    
    cat > /opt/atlasvpn/scripts/monitor-system.sh << 'EOF'
#!/bin/bash

# System monitoring script
LOG_FILE="/opt/atlasvpn/logs/system-monitor.log"
mkdir -p "$(dirname "$LOG_FILE")"

# Function to log with timestamp
log_metric() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# Get system metrics
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4}')
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | tr -d '%')

# Log metrics
log_metric "CPU: ${CPU_USAGE}% | Memory: ${MEMORY_USAGE}% | Load: ${LOAD_AVG} | Disk: ${DISK_USAGE}%"

# Check for high resource usage
if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
    log_metric "HIGH CPU USAGE DETECTED: ${CPU_USAGE}%"
    ps aux --sort=-%cpu | head -10 >> "$LOG_FILE"
fi

if (( $(echo "$MEMORY_USAGE > 85" | bc -l) )); then
    log_metric "HIGH MEMORY USAGE DETECTED: ${MEMORY_USAGE}%"
    ps aux --sort=-%mem | head -10 >> "$LOG_FILE"
fi
EOF

    chmod +x /opt/atlasvpn/scripts/monitor-system.sh
    log "Monitoring script created"
}

# Main execution
main() {
    log "Starting AtlasVPN System Optimization..."
    
    check_root
    show_system_status
    
    kill_problematic_processes
    optimize_services
    set_system_limits
    cleanup_system
    install_monitoring_tools
    create_monitoring_script
    
    log "=== OPTIMIZATION COMPLETED ==="
    echo ""
    log "System has been optimized. Please check the new system status:"
    show_system_status
    
    log "Next steps:"
    echo "1. Run: cd /opt/atlasvpn/frontend && pnpm dev"
    echo "2. Check Nginx configuration: nginx -t"
    echo "3. Monitor system: /opt/atlasvpn/scripts/monitor-system.sh"
    echo ""
    log "Optimization script completed successfully!"
}

# Run main function
main "$@"
