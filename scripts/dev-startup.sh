#!/bin/bash

# Atlas VPN Development Startup Script
# Optimized for React 19 + Vite 6 + FastAPI

set -e  # Exit on any error

echo "🚀 Starting Atlas VPN Development Environment..."

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    nc -z localhost "$1" 2>/dev/null
}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check prerequisites
echo "📋 Checking prerequisites..."

# Check Node.js version (should be 18+)
if command_exists node; then
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        echo -e "${RED}❌ Node.js version $NODE_VERSION detected. Minimum required: 18${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ Node.js $(node -v)${NC}"
else
    echo -e "${RED}❌ Node.js not found${NC}"
    exit 1
fi

# Check pnpm
if command_exists pnpm; then
    echo -e "${GREEN}✅ pnpm $(pnpm -v)${NC}"
else
    echo -e "${YELLOW}⚠️  pnpm not found, installing...${NC}"
    npm install -g pnpm
fi

# Check Python version (should be 3.10+)
if command_exists python3; then
    PYTHON_VERSION=$(python3 -V 2>&1 | grep -Po '(?<=Python )([0-9]+\.[0-9]+)')
    echo -e "${GREEN}✅ Python $PYTHON_VERSION${NC}"
else
    echo -e "${RED}❌ Python3 not found${NC}"
    exit 1
fi

# Kill any existing processes on our ports
echo "🔄 Cleaning up existing processes..."
if port_in_use 3000; then
    echo -e "${YELLOW}⚠️  Port 3000 is in use, attempting to free it...${NC}"
    pkill -f "vite.*3000" || true
    pkill -f "node.*3000" || true
fi

if port_in_use 8000; then
    echo -e "${YELLOW}⚠️  Port 8000 is in use, attempting to free it...${NC}"
    pkill -f "uvicorn.*8000" || true
    pkill -f "python.*8000" || true
fi

# Wait a moment for processes to terminate
sleep 2

# Clear development caches
echo "🧹 Clearing development caches..."
cd frontend
if [ -d "node_modules/.vite" ]; then
    rm -rf node_modules/.vite
    echo -e "${GREEN}✅ Cleared Vite cache${NC}"
fi

if [ -d ".next" ]; then
    rm -rf .next
    echo -e "${GREEN}✅ Cleared Next.js cache${NC}"
fi

# Clear pnpm store if needed
if [ "$1" = "--clean" ]; then
    echo "🗑️  Deep cleaning..."
    rm -rf node_modules
    pnpm store prune
    echo -e "${GREEN}✅ Deep clean completed${NC}"
fi

# Install/update frontend dependencies
echo "📦 Installing frontend dependencies..."
if [ ! -d "node_modules" ] || [ "$1" = "--clean" ]; then
    pnpm install --frozen-lockfile
else
    pnpm install --prefer-offline
fi

# Backend setup
echo "🐍 Setting up backend..."
cd ../backend

# Activate virtual environment
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
    echo -e "${GREEN}✅ Activated Python virtual environment${NC}"
else
    echo -e "${RED}❌ Backend virtual environment not found at backend/venv${NC}"
    echo "Please run: python3 -m venv venv && source venv/bin/activate && pip install -r requirements.txt"
    exit 1
fi

# Check if backend dependencies are installed
if ! pip show fastapi >/dev/null 2>&1; then
    echo "📦 Installing backend dependencies..."
    pip install -r requirements.txt
fi

# Start backend in background
echo "🔥 Starting FastAPI backend..."
cd /opt/atlasvpn/backend
source venv/bin/activate
nohup uvicorn main:app --host 0.0.0.0 --port 8000 --reload > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo -e "${GREEN}✅ Backend started (PID: $BACKEND_PID)${NC}"

# Wait for backend to be ready
echo "⏳ Waiting for backend to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend is ready!${NC}"
        break
    fi
    sleep 1
    if [ $i -eq 30 ]; then
        echo -e "${RED}❌ Backend failed to start within 30 seconds${NC}"
        kill $BACKEND_PID 2>/dev/null || true
        exit 1
    fi
done

# Start frontend
echo "⚡ Starting Vite frontend..."
cd /opt/atlasvpn/frontend
pnpm dev &
FRONTEND_PID=$!
echo -e "${GREEN}✅ Frontend started (PID: $FRONTEND_PID)${NC}"

# Wait for frontend to be ready
echo "⏳ Waiting for frontend to be ready..."
for i in {1..60}; do
    if curl -s http://localhost:3000 >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Frontend is ready!${NC}"
        break
    fi
    sleep 1
    if [ $i -eq 60 ]; then
        echo -e "${YELLOW}⚠️  Frontend taking longer than expected...${NC}"
        break
    fi
done

# Create a cleanup function
cleanup() {
    echo -e "\n🛑 Shutting down development environment..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    pkill -f "vite.*3000" || true
    pkill -f "uvicorn.*8000" || true
    echo -e "${GREEN}✅ Cleanup completed${NC}"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Display startup information
echo ""
echo "🎉 Atlas VPN Development Environment Ready!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo -e "Frontend: ${GREEN}http://localhost:3000${NC}"
echo -e "Backend:  ${GREEN}http://localhost:8000${NC}"
echo -e "API Docs: ${GREEN}http://localhost:8000/docs${NC}"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Press Ctrl+C to stop all services"
echo ""

# Keep the script running
wait 