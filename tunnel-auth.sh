#!/bin/bash

# VS Code Tunnel GitHub Authentication Helper
echo "🔐 VS Code Tunnel GitHub Authentication"
echo "======================================="

# Get the latest authentication code from logs
AUTH_CODE=$(journalctl -u vscode-tunnel.service --no-pager -n 20 | grep "use code" | tail -1 | grep -o "[A-Z0-9]\{4\}-[A-Z0-9]\{4\}")

if [ -n "$AUTH_CODE" ]; then
    echo ""
    echo "✅ Found authentication code: $AUTH_CODE"
    echo ""
    echo "🔗 To complete authentication:"
    echo "   1. Open: https://github.com/login/device"
    echo "   2. Enter code: $AUTH_CODE"
    echo "   3. Authorize VS Code Server"
    echo ""
    echo "📱 Quick link: https://github.com/login/device"
    echo ""
    echo "⏱️  This code expires in 15 minutes"
    echo ""
    
    # Check if authentication is complete
    echo "🔄 Checking authentication status..."
    sleep 2
    
    if journalctl -u vscode-tunnel.service --no-pager -n 5 | grep -q "tunnel opened"; then
        echo "✅ Authentication successful! Tunnel is ready."
        echo "🌐 Access URL: https://vscode.dev/tunnel/atlasvpn-server"
    else
        echo "⏳ Waiting for authentication... Please complete the GitHub login."
        echo ""
        echo "💡 After completing authentication, run:"
        echo "   tunnel status"
        echo "   tunnel info"
    fi
else
    echo "❌ No authentication code found in recent logs."
    echo ""
    echo "🔄 Try restarting the tunnel service:"
    echo "   systemctl restart vscode-tunnel.service"
    echo "   sleep 5"
    echo "   $0"
fi

echo ""
echo "📋 For troubleshooting, check logs:"
echo "   tunnel logs" 