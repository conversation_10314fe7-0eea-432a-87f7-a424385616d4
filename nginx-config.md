# Nginx Configuration Reference

This file provides quick access to the nginx configuration for the AtlasVPN project.

## Configuration Location
- **Project Copy**: `./nginx-config/atlas-vpn-site.conf`
- **Active Config**: `/etc/nginx/sites-available/atlas-vpn-site.conf`
- **Documentation**: `./nginx-config/README.md`

## Quick Commands

### Deploy Configuration
```bash
sudo cp nginx-config/atlas-vpn-site.conf /etc/nginx/sites-available/atlas-vpn-site.conf
sudo nginx -t && sudo systemctl reload nginx
```

### Update Project Copy
```bash
cp /etc/nginx/sites-available/atlas-vpn-site.conf nginx-config/atlas-vpn-site.conf
```

## Frontend Access
- **React**: https://dev.atlasvip.cloud/
- **Astro**: https://dev.atlasvip.cloud/astro/

## Development Ports
- React: 3000
- Astro: 3005
- Backend: 8000
