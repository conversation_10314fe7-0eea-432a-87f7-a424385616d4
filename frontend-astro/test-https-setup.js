#!/usr/bin/env node

/**
 * HTTPS Setup Validation Script for VIPVerse Telegram WebApp
 * Tests all critical endpoints and functionality over HTTPS
 */

import https from 'https';
import { URL } from 'url';

const BASE_URL = 'https://dev.atlasvip.cloud';
const TIMEOUT = 10000;

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: method,
      timeout: TIMEOUT,
      headers: {
        'User-Agent': 'VIPVerse-HTTPS-Test/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testEndpoint(name, url, expectedStatus = 200) {
  try {
    log(`Testing ${name}...`, 'blue');
    const response = await makeRequest(url);
    
    if (response.statusCode === expectedStatus) {
      log(`✅ ${name}: OK (${response.statusCode})`, 'green');
      return true;
    } else {
      log(`❌ ${name}: Expected ${expectedStatus}, got ${response.statusCode}`, 'red');
      return false;
    }
  } catch (error) {
    log(`❌ ${name}: Error - ${error.message}`, 'red');
    return false;
  }
}

async function testTelegramWebAppFeatures(url) {
  try {
    log(`Testing Telegram WebApp features...`, 'blue');
    const response = await makeRequest(url);
    
    if (response.statusCode !== 200) {
      log(`❌ Page not accessible (${response.statusCode})`, 'red');
      return false;
    }

    const content = response.data;
    const checks = [
      {
        name: 'Telegram WebApp Script',
        test: content.includes('telegram-web-app.js'),
        required: true
      },
      {
        name: 'Telegram Initialization',
        test: content.includes('window.Telegram?.WebApp'),
        required: true
      },
      {
        name: 'React Islands',
        test: content.includes('client:only="react"') || content.includes('LandingPageInteractive'),
        required: true
      },
      {
        name: 'Astro Framework',
        test: content.includes('astro') || response.headers['x-powered-by']?.includes('astro'),
        required: false
      },
      {
        name: 'HTTPS Security Headers',
        test: response.headers['strict-transport-security'] !== undefined,
        required: true
      },
      {
        name: 'Content Security Policy',
        test: response.headers['content-security-policy'] !== undefined,
        required: true
      }
    ];

    let passed = 0;
    let total = 0;

    for (const check of checks) {
      total++;
      if (check.test) {
        log(`  ✅ ${check.name}`, 'green');
        passed++;
      } else {
        const symbol = check.required ? '❌' : '⚠️';
        const color = check.required ? 'red' : 'yellow';
        log(`  ${symbol} ${check.name}`, color);
        if (check.required) {
          // Don't increment passed for required failures
        } else {
          passed++; // Optional checks don't fail the test
        }
      }
    }

    log(`Telegram WebApp Features: ${passed}/${total} checks passed`, passed === total ? 'green' : 'yellow');
    return passed >= total - 1; // Allow 1 optional failure
  } catch (error) {
    log(`❌ Telegram WebApp Features: Error - ${error.message}`, 'red');
    return false;
  }
}

async function runTests() {
  log('🚀 Starting HTTPS Setup Validation for VIPVerse Telegram WebApp', 'bold');
  log('=' * 60, 'blue');

  const tests = [
    // Core pages
    { name: 'Landing Page', url: `${BASE_URL}/`, status: 200 },
    { name: 'New User Setup', url: `${BASE_URL}/new-user-setup`, status: 200 },
    
    // API endpoints
    { name: 'API Root', url: `${BASE_URL}/api/`, status: 404 }, // Expected 404
    { name: 'API Health Check', url: `${BASE_URL}/api/health`, status: 405 }, // Expected 405 for GET
    
    // Static assets
    { name: 'Favicon', url: `${BASE_URL}/favicon.svg`, status: 200 },
  ];

  let passed = 0;
  let total = tests.length;

  // Run basic endpoint tests
  for (const test of tests) {
    const result = await testEndpoint(test.name, test.url, test.status);
    if (result) passed++;
  }

  // Run Telegram WebApp feature tests
  log('\n📱 Testing Telegram WebApp Integration...', 'bold');
  const telegramTest = await testTelegramWebAppFeatures(`${BASE_URL}/`);
  if (telegramTest) passed++;
  total++;

  // Summary
  log('\n' + '=' * 60, 'blue');
  log(`📊 Test Results: ${passed}/${total} tests passed`, passed === total ? 'green' : 'red');
  
  if (passed === total) {
    log('🎉 All tests passed! HTTPS setup is working correctly.', 'green');
    log('✅ The Telegram WebApp is ready for testing.', 'green');
  } else {
    log('⚠️  Some tests failed. Please check the configuration.', 'yellow');
  }

  log('\n🔗 Test URLs:', 'bold');
  log(`   Landing Page: ${BASE_URL}/`, 'blue');
  log(`   New User Setup: ${BASE_URL}/new-user-setup`, 'blue');
  log(`   API Documentation: ${BASE_URL}/docs`, 'blue');

  process.exit(passed === total ? 0 : 1);
}

// Run the tests
runTests().catch((error) => {
  log(`💥 Test runner error: ${error.message}`, 'red');
  process.exit(1);
});
