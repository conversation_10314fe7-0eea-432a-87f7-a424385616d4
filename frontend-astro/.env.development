# Development Environment Configuration for Telegram WebApp
VITE_API_URL=https://dev.atlasvip.cloud/api
VITE_WS_BASE_URL=wss://dev.atlasvip.cloud/ws
VITE_ENVIRONMENT=development
VITE_DEBUG=true
VITE_APP_NAME=VIPVerse
VITE_APP_VERSION=1.0.0

# Telegram WebApp Configuration
VITE_TELEGRAM_BOT_USERNAME=VIPVerseBot
VITE_TELEGRAM_WEBAPP_URL=https://dev.atlasvip.cloud

# Development flags
VITE_ENABLE_REACT_QUERY_DEVTOOLS=true
VITE_ENABLE_CONSOLE_LOGS=true
