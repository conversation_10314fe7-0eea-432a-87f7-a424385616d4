/**
 * @file queryClient.ts
 * @description Shared QueryClient instance for TanStack Query in Astro + React
 * Based on best practices for Astro integration without QueryClientProvider
 */

import { QueryClient } from '@tanstack/react-query';

// Create a single QueryClient instance to be shared across all components
// This approach is recommended for Astro + React integration
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time: 5 minutes
      staleTime: 5 * 60 * 1000,
      // Cache time: 10 minutes  
      gcTime: 10 * 60 * 1000,
      // Don't refetch on window focus in Telegram WebApp
      refetchOnWindowFocus: false,
      // Don't refetch on mount by default (use stale data)
      refetchOnMount: false,
      // Refetch on reconnect
      refetchOnReconnect: true,
      // Retry configuration
      retry: (failureCount: number, error: any) => {
        // Don't retry on 4xx errors except 408, 429
        if (error?.status >= 400 && error?.status < 500) {
          if (error?.status === 408 || error?.status === 429) {
            return failureCount < 2;
          }
          return false;
        }
        // Retry on network errors and 5xx errors
        return failureCount < 3;
      },
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // Retry mutations once on failure
      retry: 1,
      // Retry delay for mutations
      retryDelay: 1000,
    },
  },
});

// Export default for convenience
export default queryClient;
