/**
 * @file Avatar3DViewer.tsx
 * @description Simple avatar component for user profile images
 * Placeholder for the 3D avatar viewer - can be enhanced later
 */

import React from 'react';

interface Avatar3DViewerProps {
  filename: string;
  size: { width: number; height: number };
  className?: string;
}

const Avatar3DViewer: React.FC<Avatar3DViewerProps> = ({ 
  filename, 
  size, 
  className = '' 
}) => {
  // Extract name from filename for fallback initials
  const getInitials = (filename: string): string => {
    const name = filename.replace('.webp', '').replace(/[^a-zA-Z\s]/g, '');
    const words = name.split(' ').filter(word => word.length > 0);
    if (words.length === 0) return 'U';
    if (words.length === 1) return words[0].charAt(0).toUpperCase();
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  };

  const initials = getInitials(filename);

  return (
    <div 
      className={`relative overflow-hidden rounded-full bg-gradient-to-br from-accent-500 to-purple-600 flex items-center justify-center ${className}`}
      style={{ 
        width: size.width, 
        height: size.height,
        minWidth: size.width,
        minHeight: size.height
      }}
    >
      {/* Fallback avatar with initials */}
      <span 
        className="text-white font-semibold select-none"
        style={{ 
          fontSize: Math.max(size.width * 0.4, 12) 
        }}
      >
        {initials}
      </span>
      
      {/* Subtle glow effect */}
      <div className="absolute inset-0 rounded-full bg-gradient-to-t from-transparent via-transparent to-white/10" />
    </div>
  );
};

export default Avatar3DViewer;
