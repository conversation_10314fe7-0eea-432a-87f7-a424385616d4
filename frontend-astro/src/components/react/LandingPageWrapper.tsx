import React from 'react';
import QueryProvider from './QueryProvider';
import ToastProvider from './ToastProvider';
import LandingPageInteractive from './LandingPageInteractive';

interface LandingPageWrapperProps {
  onNavigate: (path: string) => void;
}

const LandingPageWrapper: React.FC<LandingPageWrapperProps> = ({ onNavigate }) => {
  return (
    <QueryProvider>
      <ToastProvider>
        <LandingPageInteractive onNavigate={onNavigate} />
      </ToastProvider>
    </QueryProvider>
  );
};

export default LandingPageWrapper;
