import React from 'react';
import QueryClientWrapper from './QueryClientWrapper';
import ToastProvider from './ToastProvider';
import LandingPageInteractive from './LandingPageInteractive';

interface LandingPageWrapperProps {
  onNavigate: (path: string) => void;
}

const LandingPageWrapper: React.FC<LandingPageWrapperProps> = ({ onNavigate }) => {
  return (
    <QueryClientWrapper>
      <ToastProvider>
        <LandingPageInteractive onNavigate={onNavigate} />
      </ToastProvider>
    </QueryClientWrapper>
  );
};

export default LandingPageWrapper;
