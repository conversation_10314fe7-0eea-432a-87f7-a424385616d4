import React from 'react';
import ToastProvider from './ToastProvider';
import LandingPageInteractive from './LandingPageInteractive';

interface LandingPageWrapperProps {
  onNavigate: (path: string) => void;
}

const LandingPageWrapper: React.FC<LandingPageWrapperProps> = ({ onNavigate }) => {
  return (
    <ToastProvider>
      <LandingPageInteractive onNavigate={onNavigate} />
    </ToastProvider>
  );
};

export default LandingPageWrapper;
