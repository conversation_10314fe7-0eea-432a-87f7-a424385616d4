import React, { useEffect, useState, useCallback, useMemo, memo } from 'react';
import LoadingSpinner from './LoadingSpinner';
import { useTelegramLoginMutation, useCurrentUserQuery } from '../../hooks/authHooks';
import { getTelegramData, initializeTelegramWebApp, isTelegramWebApp, triggerHapticFeedback } from '../../utils/telegram';
import { useToast } from './ToastProvider';

interface LandingPageInteractiveProps {
  onNavigate: (path: string) => void;
}

const LandingPageInteractive = memo(({ onNavigate }: LandingPageInteractiveProps) => {
  const { showToast } = useToast();
  const telegramLoginMutation = useTelegramLoginMutation();
  const { data: currentUser, isLoading: isAuthLoading } = useCurrentUserQuery();
  const [isTelegramReady, setIsTelegramReady] = useState(false);
  const [isNonTelegramEnvironment, setIsNonTelegramEnvironment] = useState(false);
  const [initializationAttempts, setInitializationAttempts] = useState(0);
  const [showInitialLoading, setShowInitialLoading] = useState(true);
  const [showTelegramInitializing, setShowTelegramInitializing] = useState(false);

  // Animation classes replaced with CSS for better compatibility

  // Check if already authenticated - optimized with early return
  useEffect(() => {
    if (currentUser && !isAuthLoading) {
      console.log('[LandingPage] User already authenticated, redirecting');
      onNavigate('/dashboard');
    }
  }, [currentUser, isAuthLoading, onNavigate]);

  // Optimized Telegram WebApp initialization
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    const initializeTelegram = async () => {
      try {
        if (typeof window === 'undefined') return;
        
        console.log('[LandingPage] Starting Telegram initialization...');
        setShowTelegramInitializing(true);
        
        // Check if we're in a Telegram environment
        if (!isTelegramWebApp()) {
          console.log('[LandingPage] Not in Telegram environment');
          setIsNonTelegramEnvironment(true);
          setShowTelegramInitializing(false);
          setShowInitialLoading(false);
          return;
        }
        
        // Initialize Telegram WebApp
        const webApp = await initializeTelegramWebApp();
        
        if (webApp) {
          console.log('[LandingPage] Telegram WebApp initialized successfully');
          setIsTelegramReady(true);
          setIsNonTelegramEnvironment(false);
        } else {
          console.warn('[LandingPage] Failed to initialize Telegram WebApp');
          setIsNonTelegramEnvironment(true);
        }
        
      } catch (error) {
        console.error('[LandingPage] Telegram initialization error:', error);
        setIsNonTelegramEnvironment(true);
      } finally {
        setShowTelegramInitializing(false);
        setShowInitialLoading(false);
        setInitializationAttempts(prev => prev + 1);
      }
    };

    // Initial loading delay
    timeoutId = setTimeout(() => {
      setShowInitialLoading(false);
      initializeTelegram();
    }, 1500);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, []);

  const handleTelegramLogin = useCallback(async () => {
    try {
      triggerHapticFeedback('light');
      
      if (isNonTelegramEnvironment) {
        showToast('This app must be opened in Telegram to login', 'error');
        return;
      }

      const telegramData = getTelegramData();
      if (!telegramData?.initData) {
        showToast('Please open this app in Telegram to login', 'error');
        return;
      }

      console.log('[LandingPage] Starting Telegram login...');
      
      const result = await telegramLoginMutation.mutateAsync({
        init_data: telegramData.initData,
        auth_method: 'telegram'
      });

      if (result.isNewUser) {
        console.log('[LandingPage] New user, redirecting to setup');
        triggerHapticFeedback('success');
        onNavigate('/new-user-setup');
      } else if (result.user) {
        console.log('[LandingPage] Existing user, redirecting to dashboard');
        triggerHapticFeedback('success');
        onNavigate('/dashboard');
      }
    } catch (error: any) {
      console.error('[LandingPage] Login failed:', error);
      triggerHapticFeedback('error');
      
      // Show specific error messages
      const errorMessage = error?.message || 'Login failed. Please try again.';
      showToast(errorMessage, 'error');
    }
  }, [isNonTelegramEnvironment, telegramLoginMutation, onNavigate, showToast]);

  const handleCreateAccount = useCallback(() => {
    if (isNonTelegramEnvironment) {
      showToast('This app must be opened in Telegram to create an account', 'error');
      return;
    }

    const telegramData = getTelegramData();
    if (!telegramData?.initData) {
      showToast('Please open this app in Telegram to create an account', 'error');
      return;
    }
    
    triggerHapticFeedback('light');
    onNavigate('/new-user-setup');
  }, [isNonTelegramEnvironment, onNavigate, showToast]);

  const handleOpenInTelegram = useCallback(() => {
    const telegramUrl = 'https://t.me/VIPVerseBot/app';
    window.open(telegramUrl, '_blank');
  }, []);

  // Loading state component
  const LoadingComponent = useMemo(() => (
    <div
      className="flex flex-col items-center justify-center text-center p-5 animate-fade-in"
    >
      <div className="text-[3.5rem] mb-6 animate-[floatIcon_3s_ease-in-out_infinite]">🚀</div>
      <h1 className="text-[2.25rem] font-bold text-gray-100 mb-3 tracking-tight">VIPVerse</h1>
      <p className="text-base text-zinc-400 mb-8">Welcome to the future</p>
      <LoadingSpinner size="lg" />
      <p className="text-[0.95rem] text-purple-500 font-medium animate-[pulseText_1.8s_infinite_alternate] mt-4">
        {showInitialLoading ? 'Verifying session...' : 'Initializing Telegram...'}
      </p>
    </div>
  ), [showInitialLoading]);

  if (showInitialLoading || showTelegramInitializing) {
    return LoadingComponent;
  }

  // Non-Telegram environment component
  if (isNonTelegramEnvironment) {
    return (
      <div
        className="max-w-md w-full bg-gray-800 rounded-2xl p-8 text-center animate-fade-in"
      >
        <div className="text-4xl mb-6">⚠️</div>
        
        <h1 className="text-2xl font-bold text-white mb-4">
          Telegram Required
        </h1>
        
        <p className="text-gray-300 mb-6">
          VIPVerse is a Telegram Mini App and must be opened within Telegram to function properly.
        </p>

        <div className="space-y-4">
          <button
            onClick={handleOpenInTelegram}
            className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors hover:scale-105 active:scale-95"
          >
            Open in Telegram
          </button>

          {import.meta.env.DEV && (
            <button
              onClick={() => {
                setIsNonTelegramEnvironment(false);
                setIsTelegramReady(true);
              }}
              className="w-full py-2 px-4 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors hover:scale-105 active:scale-95"
            >
              Continue Anyway (Dev Mode)
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div
      className="max-w-sm w-full bg-gradient-to-br from-gray-900 via-gray-850 to-purple-900/30 rounded-2xl p-8 text-center shadow-2xl border border-purple-700/30 animate-fade-in"
    >
      <div className="text-5xl mb-6 text-purple-400">✨</div>
      
      <h1 className="text-3xl font-bold text-white mb-3">
        Welcome to VIPVerse
      </h1>
      
      <p className="text-gray-300/90 mb-8 text-sm">
        Enter the exclusive world of premium VPN services.
      </p>

      <div className="space-y-4">
        <button
          onClick={handleTelegramLogin}
          disabled={telegramLoginMutation.isPending || !isTelegramReady || isAuthLoading}
          className="w-full py-3 px-6 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-300 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900 hover:scale-105 active:scale-95"
        >
          {telegramLoginMutation.isPending ? (
            <div className="flex items-center justify-center space-x-2">
              <LoadingSpinner size="sm" />
              <span>Connecting...</span>
            </div>
          ) : !isTelegramReady ? (
            'Initializing Telegram...'
          ) : (
            'Login to VIPVerse'
          )}
        </button>

        <button
          onClick={handleCreateAccount}
          disabled={!isTelegramReady || isAuthLoading}
          className="w-full py-3 px-6 bg-gray-700/80 hover:bg-gray-600/90 border border-gray-600 text-white font-semibold rounded-xl shadow-md transition-all duration-300 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:ring-offset-gray-900 hover:scale-105 active:scale-95"
        >
          Create New Account
        </button>
      </div>

      <div className="mt-8 pt-6 border-t border-purple-700/20">
        <p className="text-xs text-gray-400/70">
          Secure • Anonymous • Premium
        </p>
      </div>

      {/* Debug info in development */}
      {import.meta.env.DEV && (
        <div className="mt-6 p-3 bg-black/30 rounded-lg text-left text-xs text-gray-500 overflow-x-auto">
          <p><strong>Dev Info:</strong></p>
          <p>TG WebApp: {String(!!window.Telegram?.WebApp)}</p>
          <p>TG Ready: {String(isTelegramReady)}</p>
          <p>TG InitData: {(window.Telegram?.WebApp?.initData?.length || 0) > 0 ? 'Available' : 'Missing'}</p>
          <p>Non-TG Env: {String(isNonTelegramEnvironment)}</p>
          <p>Auth Loading: {String(isAuthLoading)}</p>
          <p>Attempts: {initializationAttempts}</p>
        </div>
      )}
    </div>
  );
});

LandingPageInteractive.displayName = 'LandingPageInteractive';

export default LandingPageInteractive;
