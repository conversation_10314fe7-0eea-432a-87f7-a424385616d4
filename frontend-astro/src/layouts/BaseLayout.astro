---
export interface Props {
  title: string;
  description?: string;
  requireAuth?: boolean;
}

const { title, description = "VIPVerse - Secure VIP Experiences", requireAuth = false } = Astro.props;
// const user = Astro.locals.user;
// const isAuthenticated = Astro.locals.isAuthenticated;

// Redirect if auth required but user not authenticated (temporarily disabled)
// if (requireAuth && !isAuthenticated) {
//   return Astro.redirect('/');
// }
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no" />
  <title>{title}</title>
  <meta name="description" content={description} />
  
  <!-- Telegram WebApp Script - Critical -->
  <script src="https://telegram.org/js/telegram-web-app.js" is:inline></script>
  
  <!-- Telegram Initialization -->
  <script is:inline>
    // Initialize Telegram WebApp immediately
    if (window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;
      tg.ready();
      tg.expand();

      // Check if disableVerticalSwipes is available (deprecated in 6.0+)
      if (typeof tg.disableVerticalSwipes === 'function') {
        tg.disableVerticalSwipes();
      }

      // Store Telegram data globally for React components
      window.__TELEGRAM_DATA__ = {
        initData: tg.initData,
        initDataUnsafe: tg.initDataUnsafe,
        user: tg.initDataUnsafe?.user,
        isReady: true
      };
    } else {
      window.__TELEGRAM_DATA__ = { isReady: false };
    }
  </script>
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Global Styles -->
  <link rel="stylesheet" href="/src/styles/global.css">
  
  <!-- Meta tags for mobile optimization -->
  <meta name="theme-color" content="#000000" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="format-detection" content="telephone=no" />
  
  <!-- Telegram WebApp specific meta tags -->
  <meta name="telegram-web-app" content="true" />
</head>
<body class="bg-black text-white font-sans overflow-x-hidden">
  <div id="app" class="min-h-screen">
    <slot />
  </div>
  
  <!-- Global error handler -->
  <script is:inline>
    window.addEventListener('error', function(e) {
      console.error('Global error:', e.error);
    });
    
    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
    });
  </script>
</body>
</html>
