---
export interface Props {
  title: string;
  description?: string;
  requireAuth?: boolean;
}

const { title, description = "VIPVerse - Secure VIP Experiences", requireAuth = false } = Astro.props;
// const user = Astro.locals.user;
// const isAuthenticated = Astro.locals.isAuthenticated;

// Redirect if auth required but user not authenticated (temporarily disabled)
// if (requireAuth && !isAuthenticated) {
//   return Astro.redirect('/');
// }
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <!-- Telegram Mini App optimized viewport -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover, user-scalable=no, shrink-to-fit=no" />
  <title>{title}</title>
  <meta name="description" content={description} />

  <!-- Telegram WebApp Script - Critical (Latest Version) -->
  <script src="https://telegram.org/js/telegram-web-app.js?57" is:inline></script>
  
  <!-- Telegram Initialization -->
  <script is:inline>
    // Initialize Telegram WebApp immediately
    if (window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;
      tg.ready();
      tg.expand();

      // Disable vertical swipes for better UX
      if (typeof tg.disableVerticalSwipes === 'function') {
        tg.disableVerticalSwipes();
      }

      // Set up CSS variables for Telegram viewport and safe areas
      const updateTelegramCSSVars = () => {
        const root = document.documentElement;

        // Viewport dimensions
        root.style.setProperty('--tg-viewport-height', `${tg.viewportHeight || window.innerHeight}px`);
        root.style.setProperty('--tg-viewport-stable-height', `${tg.viewportStableHeight || tg.viewportHeight || window.innerHeight}px`);

        // Safe area insets (Bot API 8.0+)
        if (tg.safeAreaInset) {
          root.style.setProperty('--tg-safe-area-inset-top', `${tg.safeAreaInset.top || 0}px`);
          root.style.setProperty('--tg-safe-area-inset-right', `${tg.safeAreaInset.right || 0}px`);
          root.style.setProperty('--tg-safe-area-inset-bottom', `${tg.safeAreaInset.bottom || 0}px`);
          root.style.setProperty('--tg-safe-area-inset-left', `${tg.safeAreaInset.left || 0}px`);
        }

        // Content safe area insets (Bot API 8.0+)
        if (tg.contentSafeAreaInset) {
          root.style.setProperty('--tg-content-safe-area-inset-top', `${tg.contentSafeAreaInset.top || 0}px`);
          root.style.setProperty('--tg-content-safe-area-inset-right', `${tg.contentSafeAreaInset.right || 0}px`);
          root.style.setProperty('--tg-content-safe-area-inset-bottom', `${tg.contentSafeAreaInset.bottom || 0}px`);
          root.style.setProperty('--tg-content-safe-area-inset-left', `${tg.contentSafeAreaInset.left || 0}px`);
        }

        // Theme colors
        if (tg.themeParams) {
          root.style.setProperty('--tg-theme-bg-color', tg.themeParams.bg_color || '#000000');
          root.style.setProperty('--tg-theme-text-color', tg.themeParams.text_color || '#ffffff');
          root.style.setProperty('--tg-theme-hint-color', tg.themeParams.hint_color || '#999999');
          root.style.setProperty('--tg-theme-link-color', tg.themeParams.link_color || '#8B5CF6');
          root.style.setProperty('--tg-theme-button-color', tg.themeParams.button_color || '#8B5CF6');
          root.style.setProperty('--tg-theme-button-text-color', tg.themeParams.button_text_color || '#ffffff');
          root.style.setProperty('--tg-theme-secondary-bg-color', tg.themeParams.secondary_bg_color || '#1a1a1a');
        }

        // Color scheme
        root.style.setProperty('--tg-color-scheme', tg.colorScheme || 'dark');
      };

      // Initial setup
      updateTelegramCSSVars();

      // Listen for viewport and theme changes
      tg.onEvent('viewportChanged', updateTelegramCSSVars);
      tg.onEvent('themeChanged', updateTelegramCSSVars);
      tg.onEvent('safeAreaChanged', updateTelegramCSSVars);
      tg.onEvent('contentSafeAreaChanged', updateTelegramCSSVars);

      // Store Telegram data globally for React components
      window.__TELEGRAM_DATA__ = {
        initData: tg.initData,
        initDataUnsafe: tg.initDataUnsafe,
        user: tg.initDataUnsafe?.user,
        isReady: true
      };
    } else {
      // Fallback for non-Telegram environments
      const root = document.documentElement;
      root.style.setProperty('--tg-viewport-height', `${window.innerHeight}px`);
      root.style.setProperty('--tg-viewport-stable-height', `${window.innerHeight}px`);
      root.style.setProperty('--tg-safe-area-inset-top', '0px');
      root.style.setProperty('--tg-safe-area-inset-right', '0px');
      root.style.setProperty('--tg-safe-area-inset-bottom', '0px');
      root.style.setProperty('--tg-safe-area-inset-left', '0px');
      root.style.setProperty('--tg-content-safe-area-inset-top', '0px');
      root.style.setProperty('--tg-content-safe-area-inset-right', '0px');
      root.style.setProperty('--tg-content-safe-area-inset-bottom', '0px');
      root.style.setProperty('--tg-content-safe-area-inset-left', '0px');
      root.style.setProperty('--tg-theme-bg-color', '#000000');
      root.style.setProperty('--tg-theme-text-color', '#ffffff');
      root.style.setProperty('--tg-color-scheme', 'dark');

      window.__TELEGRAM_DATA__ = { isReady: false };
    }
  </script>
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- Global Styles -->
  <link rel="stylesheet" href="/src/styles/global.css">
  
  <!-- Meta tags for mobile optimization -->
  <meta name="theme-color" content="#000000" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="apple-mobile-web-app-title" content="VIPVerse" />
  <meta name="format-detection" content="telephone=no" />
  <meta name="msapplication-tap-highlight" content="no" />

  <!-- Telegram WebApp specific meta tags -->
  <meta name="telegram-web-app" content="true" />
  <meta name="telegram-web-app-version" content="8.0" />

  <!-- Prevent zoom and improve touch handling -->
  <meta name="HandheldFriendly" content="true" />
  <meta name="MobileOptimized" content="width" />
</head>
<body class="bg-black text-white font-sans overflow-x-hidden">
  <div id="app" class="min-h-screen">
    <slot />
  </div>
  
  <!-- Global error handler -->
  <script is:inline>
    window.addEventListener('error', function(e) {
      console.error('Global error:', e.error);
    });
    
    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
    });
  </script>
</body>
</html>
