---
/**
 * @file DashboardLayout.astro
 * @description Main dashboard layout for Astro, handling navigation and tab structure
 * Converted from React DashboardLayout.tsx to leverage Astro's islands architecture
 */

export interface Props {
  title: string;
  description?: string;
  activeTab?: 'home' | 'earn' | 'friends' | 'wallet' | 'premium';
}

const { 
  title, 
  description = "VIPVerse Dashboard - Manage your VIP experience", 
  activeTab = 'home' 
} = Astro.props;

// Get user data from middleware (temporarily disabled for testing)
// const user = Astro.locals.user;
// const isAuthenticated = Astro.locals.isAuthenticated;

// Redirect if not authenticated (temporarily disabled)
// if (!isAuthenticated) {
//   return Astro.redirect('/');
// }

// Import necessary components
import BaseLayout from './BaseLayout.astro';
import Header from '../components/astro/Header.astro';
import BottomNavigation from '../components/astro/BottomNavigation.astro';
import DashboardBackground from '../components/react/DashboardBackground';
---

<BaseLayout title={title} description={description} requireAuth={true}>
  <!-- Dashboard Background (React Island) -->
  <DashboardBackground client:load />
  
  <!-- Main Dashboard Container -->
  <div
    id="dashboard-container"
    class="relative flex flex-col overflow-hidden"
    style="
      height: var(--tg-viewport-height, 100vh);
      max-height: var(--tg-viewport-height, 100vh);
      background: var(--tg-theme-bg-color, #000000);
    "
  >
    <!-- Header (Astro Component) -->
    <Header
      activeTab={activeTab}
      class="fixed inset-x-0 z-10 h-[64px] bg-black/70 backdrop-blur-md shadow-lg shadow-black/40"
      style="
        top: var(--tg-content-safe-area-inset-top, var(--tg-safe-area-inset-top, env(safe-area-inset-top, 0px)));
        left: var(--tg-content-safe-area-inset-left, var(--tg-safe-area-inset-left, env(safe-area-inset-left, 0px)));
        right: var(--tg-content-safe-area-inset-right, var(--tg-safe-area-inset-right, env(safe-area-inset-right, 0px)));
      "
    />

    <!-- Main Content Area -->
    <main
      class="flex-1 overflow-hidden relative"
      style="
        padding-top: calc(64px + var(--tg-content-safe-area-inset-top, var(--tg-safe-area-inset-top, env(safe-area-inset-top, 0px))));
        padding-bottom: calc(64px + var(--tg-content-safe-area-inset-bottom, var(--tg-safe-area-inset-bottom, env(safe-area-inset-bottom, 0px))));
        padding-left: var(--tg-content-safe-area-inset-left, var(--tg-safe-area-inset-left, env(safe-area-inset-left, 0px)));
        padding-right: var(--tg-content-safe-area-inset-right, var(--tg-safe-area-inset-right, env(safe-area-inset-right, 0px)));
      "
    >
      <div class="h-full flex flex-col">
        <!-- Tab Content Slot -->
        <slot />
      </div>
    </main>

    <!-- Bottom Navigation (Astro Component) -->
    <BottomNavigation
      activeTab={activeTab}
      className="fixed bottom-0 inset-x-0 z-10 h-[64px] bg-black/80 backdrop-blur-md shadow-[0_-4px_16px_rgba(0,0,0,0.5)]"
      style="
        bottom: var(--tg-content-safe-area-inset-bottom, var(--tg-safe-area-inset-bottom, env(safe-area-inset-bottom, 0px)));
        left: var(--tg-content-safe-area-inset-left, var(--tg-safe-area-inset-left, env(safe-area-inset-left, 0px)));
        right: var(--tg-content-safe-area-inset-right, var(--tg-safe-area-inset-right, env(safe-area-inset-right, 0px)));
      "
    />
  </div>

  <!-- Telegram WebApp Viewport Management -->
  <script>
    // Enhanced Telegram WebApp viewport management
    if (window.Telegram?.WebApp) {
      const tg = window.Telegram.WebApp;

      // Expand to full height
      tg.expand();

      // Disable vertical swipes for better UX
      if (typeof tg.disableVerticalSwipes === 'function') {
        tg.disableVerticalSwipes();
      }

      // Handle viewport and safe area changes
      const updateDashboardLayout = () => {
        const container = document.getElementById('dashboard-container');
        if (!container) return;

        const height = tg.viewportStableHeight || tg.viewportHeight || window.innerHeight;
        container.style.height = `${height}px`;
        container.style.maxHeight = `${height}px`;

        // Update CSS variables for this specific dashboard
        const root = document.documentElement;
        root.style.setProperty('--dashboard-height', `${height}px`);

        // Apply theme colors to dashboard
        if (tg.themeParams) {
          container.style.background = tg.themeParams.bg_color || '#000000';
        }
      };

      // Initial setup
      updateDashboardLayout();

      // Listen for all relevant events
      tg.onEvent('viewportChanged', updateDashboardLayout);
      tg.onEvent('themeChanged', updateDashboardLayout);
      tg.onEvent('safeAreaChanged', updateDashboardLayout);
      tg.onEvent('contentSafeAreaChanged', updateDashboardLayout);

      // Cleanup on page unload
      window.addEventListener('beforeunload', () => {
        tg.offEvent('viewportChanged', updateDashboardLayout);
        tg.offEvent('themeChanged', updateDashboardLayout);
        tg.offEvent('safeAreaChanged', updateDashboardLayout);
        tg.offEvent('contentSafeAreaChanged', updateDashboardLayout);
      });
    } else {
      // Fallback for non-Telegram environments
      const handleResize = () => {
        const container = document.getElementById('dashboard-container');
        if (container) {
          container.style.height = `${window.innerHeight}px`;
          container.style.maxHeight = `${window.innerHeight}px`;
        }
      };

      handleResize();
      window.addEventListener('resize', handleResize);
      window.addEventListener('beforeunload', () => {
        window.removeEventListener('resize', handleResize);
      });
    }
  </script>

  <!-- Dashboard-specific styles -->
  <style>
    /* Telegram Mini App optimized dashboard */
    #dashboard-container {
      height: var(--tg-viewport-height, 100vh);
      height: var(--tg-viewport-stable-height, var(--tg-viewport-height, 100vh));
      max-height: var(--tg-viewport-height, 100vh);
      max-height: var(--tg-viewport-stable-height, var(--tg-viewport-height, 100vh));
      background: var(--tg-theme-bg-color, #000000);
      color: var(--tg-theme-text-color, #ffffff);
    }

    /* Enhanced safe area handling */
    @supports (padding: env(safe-area-inset-top)) {
      #dashboard-container {
        /* Use Telegram safe areas first, fallback to env() */
        padding-top: var(--tg-content-safe-area-inset-top, var(--tg-safe-area-inset-top, env(safe-area-inset-top, 0px)));
        padding-right: var(--tg-content-safe-area-inset-right, var(--tg-safe-area-inset-right, env(safe-area-inset-right, 0px)));
        padding-bottom: var(--tg-content-safe-area-inset-bottom, var(--tg-safe-area-inset-bottom, env(safe-area-inset-bottom, 0px)));
        padding-left: var(--tg-content-safe-area-inset-left, var(--tg-safe-area-inset-left, env(safe-area-inset-left, 0px)));
      }
    }

    /* Prevent overscroll and improve mobile performance */
    #dashboard-container {
      overscroll-behavior: none;
      -webkit-overflow-scrolling: touch;
      /* Hardware acceleration for smooth animations */
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      will-change: transform;
    }

    /* Hide scrollbars but keep functionality */
    main {
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
    }

    main::-webkit-scrollbar {
      display: none; /* Chrome, Safari, Opera */
    }

    /* Optimize for Telegram WebApp */
    @media (max-width: 768px) {
      #dashboard-container {
        /* Disable text selection on mobile for better UX */
        -webkit-user-select: none;
        user-select: none;
        -webkit-touch-callout: none;
        -webkit-tap-highlight-color: transparent;
      }
    }

    /* Theme-aware styling */
    @media (prefers-color-scheme: dark) {
      #dashboard-container {
        background: var(--tg-theme-bg-color, #000000);
        color: var(--tg-theme-text-color, #ffffff);
      }
    }

    @media (prefers-color-scheme: light) {
      #dashboard-container {
        background: var(--tg-theme-bg-color, #ffffff);
        color: var(--tg-theme-text-color, #000000);
      }
    }
  </style>
</BaseLayout>
