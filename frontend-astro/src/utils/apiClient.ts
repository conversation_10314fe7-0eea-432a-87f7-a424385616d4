import axios from 'axios';
import type { AxiosInstance } from 'axios';

const getApiBaseUrl = (): string => {
  // Use environment variables for API URL configuration
  console.log('[ApiClient] 🔧 Getting API base URL from environment...');

  if (typeof window !== 'undefined') {
    // Client-side: Use VITE environment variables
    const clientUrl = import.meta.env.VITE_API_URL || import.meta.env.PUBLIC_API_URL;
    console.log('[ApiClient] 🌐 CLIENT-SIDE URL:', clientUrl);

    // Fallback to localhost for development
    if (!clientUrl && (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1')) {
      console.log('[ApiClient] 🔧 FALLBACK: Using localhost:8000 for development');
      return 'http://localhost:8000/api';
    }

    return clientUrl || 'https://dev.atlasvip.cloud/api';
  }

  // Server-side: Use process environment variables
  const serverUrl = process.env.API_URL || process.env.PUBLIC_API_URL;
  console.log('[ApiClient] 🖥️ SERVER-SIDE URL:', serverUrl);

  // Development fallback
  if (import.meta.env.DEV) {
    return serverUrl || 'http://localhost:8000/api';
  }

  return serverUrl || 'https://dev.atlasvip.cloud/api';
};

// Create a function to get fresh API instance
const createApiInstance = (): AxiosInstance => {
  const baseURL = getApiBaseUrl();
  console.log('[ApiClient] 🚀 Creating NEW axios instance with baseURL:', baseURL);

  return axios.create({
    baseURL: baseURL,
    withCredentials: true,
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
  });
};

// Export the API instance
export const api: AxiosInstance = createApiInstance();

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  const fullUrl = config.baseURL + config.url;
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Making request to:', fullUrl);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Base URL:', config.baseURL);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Endpoint:', config.url);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Timestamp:', new Date().toISOString());

  // CRITICAL CHECK: If this is still going to production, something is wrong
  if (fullUrl.includes('dev.atlasvip.cloud')) {
    console.error('🚨🚨🚨 ERROR: Still calling production API!', fullUrl);
    console.error('🚨🚨🚨 This should not happen!');
  } else {
    console.log('✅✅✅ SUCCESS: Calling local API:', fullUrl);
  }

  // Add CSRF token if available (only for production)
  if (typeof document !== 'undefined' && !config.baseURL?.includes('localhost')) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }
  }

  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

// Helper function for server-side requests
export const createServerApiClient = (cookies?: string): AxiosInstance => {
  return axios.create({
    baseURL: process.env.API_URL || 'https://dev.atlasvip.cloud/api',
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies || '',
    },
    withCredentials: true,
  });
};

export default api;
