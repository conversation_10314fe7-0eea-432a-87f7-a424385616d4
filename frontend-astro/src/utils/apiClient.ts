import axios from 'axios';
import type { AxiosInstance } from 'axios';

const getApiBaseUrl = (): string => {
  // 🚨 TEMPORARY HARDCODE FOR DEBUGGING - ALWAYS USE LOCAL - VERSION 2.0
  console.log('[ApiClient] 🚨 HARDCODED TO LOCAL BACKEND - VERSION 2.0');
  console.log('[ApiClient] 🚨 TIMESTAMP:', new Date().toISOString());

  // Client-side alert only
  if (typeof window !== 'undefined') {
    console.log('[ApiClient] 🚨 CLIENT-SIDE: Using localhost:8000');
  }

  return 'http://localhost:8000/api';
};

// Create a function to get fresh API instance
const createApiInstance = (): AxiosInstance => {
  const baseURL = getApiBaseUrl();
  console.log('[ApiClient] 🚀 Creating NEW axios instance with baseURL:', baseURL);

  return axios.create({
    baseURL: baseURL,
    withCredentials: true,
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
  });
};

// Export the API instance
export const api: AxiosInstance = createApiInstance();

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  const fullUrl = config.baseURL + config.url;
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Making request to:', fullUrl);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Base URL:', config.baseURL);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Endpoint:', config.url);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Timestamp:', new Date().toISOString());

  // CRITICAL CHECK: If this is still going to production, something is wrong
  if (fullUrl.includes('dev.atlasvip.cloud')) {
    console.error('🚨🚨🚨 ERROR: Still calling production API!', fullUrl);
    console.error('🚨🚨🚨 This should not happen!');
  } else {
    console.log('✅✅✅ SUCCESS: Calling local API:', fullUrl);
  }

  // Add CSRF token if available (only for production)
  if (typeof document !== 'undefined' && !config.baseURL?.includes('localhost')) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }
  }

  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

// Helper function for server-side requests
export const createServerApiClient = (cookies?: string): AxiosInstance => {
  return axios.create({
    baseURL: process.env.API_URL || 'https://dev.atlasvip.cloud/api',
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies || '',
    },
    withCredentials: true,
  });
};

export default api;
