import axios from 'axios';
import type { AxiosInstance } from 'axios';

const getApiBaseUrl = (): string => {
  console.log('[ApiClient] 🔧 Smart API URL detection...');

  if (typeof window !== 'undefined') {
    // Client-side: Smart domain detection
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;

    console.log('[ApiClient] 🌐 Current domain:', `${protocol}//${hostname}`);

    // If accessing from production domain, use production API
    if (hostname === 'dev.atlasvip.cloud') {
      console.log('[ApiClient] 🚀 PRODUCTION: Using production API');
      return 'https://dev.atlasvip.cloud';
    }

    // If accessing from localhost, use local API
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      console.log('[ApiClient] 🔧 LOCALHOST: Using local API');
      return 'http://localhost:8000/api';
    }

    // Fallback to environment variable or production
    const envUrl = import.meta.env.VITE_API_URL || import.meta.env.PUBLIC_API_URL;
    console.log('[ApiClient] 🔄 FALLBACK: Using env URL:', envUrl);
    return envUrl || 'https://dev.atlasvip.cloud';
  }

  // Server-side: Use environment variables
  const serverUrl = process.env.API_URL || process.env.PUBLIC_API_URL;
  console.log('[ApiClient] 🖥️ SERVER-SIDE URL:', serverUrl);

  return serverUrl || 'https://dev.atlasvip.cloud';
};

// Create a function to get fresh API instance
const createApiInstance = (): AxiosInstance => {
  const baseURL = getApiBaseUrl();
  console.log('[ApiClient] 🚀 Creating NEW axios instance with baseURL:', baseURL);

  return axios.create({
    baseURL: baseURL,
    withCredentials: true,
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
  });
};

// Export the API instance
export const api: AxiosInstance = createApiInstance();

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  const fullUrl = config.baseURL + config.url;
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Making request to:', fullUrl);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Base URL:', config.baseURL);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Endpoint:', config.url);
  console.log('🚨🚨🚨 [ApiClient] INTERCEPTOR - Timestamp:', new Date().toISOString());

  // CRITICAL CHECK: If this is still going to production, something is wrong
  if (fullUrl.includes('dev.atlasvip.cloud')) {
    console.error('🚨🚨🚨 ERROR: Still calling production API!', fullUrl);
    console.error('🚨🚨🚨 This should not happen!');
  } else {
    console.log('✅✅✅ SUCCESS: Calling local API:', fullUrl);
  }

  // Add CSRF token if available (only for production)
  if (typeof document !== 'undefined' && !config.baseURL?.includes('localhost')) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }
  }

  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

// Helper function for server-side requests
export const createServerApiClient = (cookies?: string): AxiosInstance => {
  return axios.create({
    baseURL: process.env.API_URL || 'https://dev.atlasvip.cloud/api',
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies || '',
    },
    withCredentials: true,
  });
};

export default api;
