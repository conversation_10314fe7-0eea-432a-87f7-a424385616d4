import axios from 'axios';
import type { AxiosInstance } from 'axios';

const getApiBaseUrl = (): string => {
  // Force local development API
  if (typeof window !== 'undefined') {
    // Client-side: check if we're in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('[ApiClient] DEVELOPMENT MODE: Using local backend');
      return 'http://localhost:8000/api';
    }
    console.log('[ApiClient] PRODUCTION MODE: Using production API');
    return import.meta.env.VITE_API_URL || 'https://dev.atlasvip.cloud/api';
  }

  // Server-side detection
  if (import.meta.env.DEV) {
    console.log('[ApiClient] SERVER-SIDE DEV: Using local backend');
    return 'http://localhost:8000/api';
  }

  console.log('[ApiClient] SERVER-SIDE PROD: Using production API');
  return process.env.API_URL || 'https://dev.atlasvip.cloud/api';
};

const baseURL = getApiBaseUrl();
console.log('[ApiClient] Creating axios instance with baseURL:', baseURL);

export const api: AxiosInstance = axios.create({
  baseURL: baseURL,
  withCredentials: true,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  console.log('[ApiClient] Making request to:', config.baseURL + config.url);

  // Add CSRF token if available (only for production)
  if (typeof document !== 'undefined' && !config.baseURL?.includes('localhost')) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }
  }

  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

// Helper function for server-side requests
export const createServerApiClient = (cookies?: string): AxiosInstance => {
  return axios.create({
    baseURL: process.env.API_URL || 'https://dev.atlasvip.cloud/api',
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies || '',
    },
    withCredentials: true,
  });
};

export default api;
