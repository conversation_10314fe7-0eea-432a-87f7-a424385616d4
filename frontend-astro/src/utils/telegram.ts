import type { TelegramWebApp, TelegramData, TelegramUser } from '../types/telegram';
import { DEV_CONFIG } from './constants';

// Get Telegram WebApp instance
export function getTelegramWebApp(): TelegramWebApp | null {
  if (typeof window === 'undefined') return null;
  return window.Telegram?.WebApp || null;
}

// Get Telegram data from global window object
export function getTelegramData(): TelegramData | null {
  if (typeof window === 'undefined') return null;
  
  // Check if we have stored Telegram data
  if (window.__TELEGRAM_DATA__) {
    return window.__TELEGRAM_DATA__;
  }
  
  // Fallback to direct WebApp access
  const webApp = getTelegramWebApp();
  if (webApp && webApp.initDataUnsafe?.user) {
    return {
      initData: webApp.initData,
      initDataUnsafe: webApp.initDataUnsafe,
      user: webApp.initDataUnsafe.user,
      isReady: true,
    };
  }
  
  // Development fallback
  if (import.meta.env.DEV) {
    return DEV_CONFIG.MOCK_TELEGRAM_DATA;
  }
  
  return null;
}

// Check if running in Telegram WebApp
export function isTelegramWebApp(): boolean {
  if (typeof window === 'undefined') return false;
  
  // Development mode always returns true
  if (import.meta.env.DEV) return true;
  
  return !!(window.Telegram?.WebApp);
}

// Initialize Telegram WebApp
export function initializeTelegramWebApp(): Promise<TelegramWebApp | null> {
  return new Promise((resolve) => {
    if (typeof window === 'undefined') {
      resolve(null);
      return;
    }
    
    // Development mode
    if (import.meta.env.DEV) {
      // Mock Telegram WebApp for development
      const mockWebApp = {
        initData: DEV_CONFIG.MOCK_TELEGRAM_DATA.initData,
        initDataUnsafe: DEV_CONFIG.MOCK_TELEGRAM_DATA.initDataUnsafe,
        ready: () => {},
        expand: () => {},
        close: () => {},
        enableClosingConfirmation: () => {},
        disableClosingConfirmation: () => {},
        enableVerticalSwipes: () => {},
        disableVerticalSwipes: () => {},
        setHeaderColor: () => {},
        setBackgroundColor: () => {},
        showAlert: (message: string, callback?: () => void) => {
          alert(message);
          callback?.();
        },
        showConfirm: (message: string, callback?: (confirmed: boolean) => void) => {
          const confirmed = confirm(message);
          callback?.(confirmed);
        },
        HapticFeedback: {
          impactOccurred: () => {},
          notificationOccurred: () => {},
          selectionChanged: () => {},
        },
      } as any;
      
      resolve(mockWebApp);
      return;
    }
    
    // Check if Telegram WebApp is already available
    if (window.Telegram?.WebApp) {
      const webApp = window.Telegram.WebApp;
      webApp.ready();
      webApp.expand();
      webApp.disableVerticalSwipes();
      resolve(webApp);
      return;
    }
    
    // Wait for Telegram WebApp to load
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait
    
    const checkTelegram = () => {
      attempts++;
      
      if (window.Telegram?.WebApp) {
        const webApp = window.Telegram.WebApp;
        webApp.ready();
        webApp.expand();
        webApp.disableVerticalSwipes();
        resolve(webApp);
      } else if (attempts < maxAttempts) {
        setTimeout(checkTelegram, 100);
      } else {
        console.warn('Telegram WebApp not available after timeout');
        resolve(null);
      }
    };
    
    checkTelegram();
  });
}

// Get current user from Telegram
export function getTelegramUser(): TelegramUser | null {
  const telegramData = getTelegramData();
  return telegramData?.user || null;
}

// Validate Telegram init data
export function validateTelegramInitData(initData: string): boolean {
  if (!initData) return false;
  
  // In development, always return true
  if (import.meta.env.DEV) return true;
  
  // Basic validation - check if it contains required fields
  try {
    const params = new URLSearchParams(initData);
    return !!(params.get('user') && params.get('auth_date') && params.get('hash'));
  } catch {
    return false;
  }
}

// Show Telegram alert
export function showTelegramAlert(message: string): Promise<void> {
  return new Promise((resolve) => {
    const webApp = getTelegramWebApp();
    
    if (webApp?.showAlert) {
      webApp.showAlert(message, resolve);
    } else {
      // Fallback to browser alert
      alert(message);
      resolve();
    }
  });
}

// Show Telegram confirm dialog
export function showTelegramConfirm(message: string): Promise<boolean> {
  return new Promise((resolve) => {
    const webApp = getTelegramWebApp();
    
    if (webApp?.showConfirm) {
      webApp.showConfirm(message, resolve);
    } else {
      // Fallback to browser confirm
      resolve(confirm(message));
    }
  });
}

// Trigger haptic feedback
export function triggerHapticFeedback(type: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' = 'light'): void {
  const webApp = getTelegramWebApp();
  
  if (!webApp?.HapticFeedback) return;
  
  switch (type) {
    case 'success':
      webApp.HapticFeedback.notificationOccurred('success');
      break;
    case 'warning':
      webApp.HapticFeedback.notificationOccurred('warning');
      break;
    case 'error':
      webApp.HapticFeedback.notificationOccurred('error');
      break;
    case 'light':
    case 'medium':
    case 'heavy':
      webApp.HapticFeedback.impactOccurred(type);
      break;
  }
}

// Close Telegram WebApp
export function closeTelegramWebApp(): void {
  const webApp = getTelegramWebApp();
  webApp?.close();
}

// Set Telegram WebApp header color
export function setTelegramHeaderColor(color: string): void {
  const webApp = getTelegramWebApp();
  webApp?.setHeaderColor(color);
}

// Set Telegram WebApp background color
export function setTelegramBackgroundColor(color: string): void {
  const webApp = getTelegramWebApp();
  webApp?.setBackgroundColor(color);
}

// Generate referral link
export function generateReferralLink(referralCode: string): string {
  const botUsername = 'VIPVerseBot'; // Replace with actual bot username
  return `https://t.me/${botUsername}?start=${referralCode}`;
}

// Share content via Telegram
export function shareTelegramContent(url: string, text: string): void {
  const shareUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
  
  if (typeof window !== 'undefined') {
    window.open(shareUrl, '_blank');
  }
}

// Get Telegram theme parameters
export function getTelegramTheme(): Record<string, string> {
  const webApp = getTelegramWebApp();
  
  if (!webApp?.themeParams) {
    return {
      bg_color: '#000000',
      text_color: '#ffffff',
      hint_color: '#999999',
      link_color: '#8B5CF6',
      button_color: '#8B5CF6',
      button_text_color: '#ffffff',
    };
  }
  
  return webApp.themeParams;
}

// Apply Telegram theme to document
export function applyTelegramTheme(): void {
  if (typeof document === 'undefined') return;
  
  const theme = getTelegramTheme();
  const root = document.documentElement;
  
  // Apply CSS custom properties
  Object.entries(theme).forEach(([key, value]) => {
    if (value) {
      root.style.setProperty(`--tg-${key.replace(/_/g, '-')}`, value);
    }
  });
  
  // Add telegram-web-app class to body
  document.body.classList.add('telegram-web-app');
}

// Check if user is premium Telegram user
export function isTelegramPremium(): boolean {
  const user = getTelegramUser();
  return user?.is_premium || false;
}

// Get user's language code
export function getTelegramLanguage(): string {
  const user = getTelegramUser();
  return user?.language_code || 'en';
}

// Format Telegram user display name
export function formatTelegramUserName(user: TelegramUser): string {
  if (user.username) {
    return `@${user.username}`;
  }
  
  const parts = [user.first_name, user.last_name].filter(Boolean);
  return parts.join(' ') || 'Anonymous';
}

// Check if WebApp is expanded
export function isTelegramExpanded(): boolean {
  const webApp = getTelegramWebApp();
  return webApp?.isExpanded || false;
}

// Get viewport height
export function getTelegramViewportHeight(): number {
  const webApp = getTelegramWebApp();
  return webApp?.viewportHeight || window.innerHeight;
}

// Send data to Telegram bot
export function sendDataToTelegramBot(data: string): void {
  const webApp = getTelegramWebApp();
  webApp?.sendData(data);
}

// Check if closing confirmation is enabled
export function isTelegramClosingConfirmationEnabled(): boolean {
  const webApp = getTelegramWebApp();
  return webApp?.isClosingConfirmationEnabled || false;
}

// Enable/disable closing confirmation
export function setTelegramClosingConfirmation(enabled: boolean): void {
  const webApp = getTelegramWebApp();
  
  if (!webApp) return;
  
  if (enabled) {
    webApp.enableClosingConfirmation();
  } else {
    webApp.disableClosingConfirmation();
  }
}
