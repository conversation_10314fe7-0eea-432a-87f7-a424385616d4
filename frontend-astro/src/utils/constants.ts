// API Configuration
export const API_CONFIG = {
  BASE_URL: {
    DEVELOPMENT: 'http://localhost:8000/api',
    PRODUCTION: '/api',
  },
  WEBSOCKET_URL: {
    DEVELOPMENT: 'ws://localhost:8000/ws',
    PRODUCTION: 'wss://dev.atlasvip.cloud/ws',
  },
  TIMEOUT: 15000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Application Routes
export const ROUTES = {
  HOME: '/',
  NEW_USER_SETUP: '/new-user-setup',
  DASHBOARD: '/dashboard',
  DASHBOARD_HOME: '/dashboard/home',
  DASHBOARD_EARN: '/dashboard/earn',
  DASHBOARD_WALLET: '/dashboard/wallet',
  DASHBOARD_FRIENDS: '/dashboard/friends',
  DASHBOARD_PREMIUM: '/dashboard/premium',
  VERSE: '/verse',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'vipverse_user_preferences',
  GAME_STATE: 'vipverse_game_state',
  NOTIFICATION_SETTINGS: 'vipverse_notifications',
  THEME_PREFERENCE: 'vipverse_theme',
  LANGUAGE_PREFERENCE: 'vipverse_language',
  TUTORIAL_COMPLETED: 'vipverse_tutorial_completed',
  LAST_VISIT: 'vipverse_last_visit',
} as const;

// Query Keys for React Query
export const QUERY_KEYS = {
  // User related
  CURRENT_USER: ['currentUser'],
  USER_PROFILE: ['userProfile'],
  DASHBOARD_STATS: ['dashboardStats'],
  
  // Cards related
  USER_CARDS: ['userCards'],
  CARD_CATALOG: ['cardCatalog'],
  CARD_CATEGORIES: ['cardCategories'],
  
  // Tasks related
  TASKS: ['tasks'],
  TASK_HISTORY: ['taskHistory'],
  DAILY_TASKS: ['dailyTasks'],
  
  // Transactions
  TRANSACTIONS: ['transactions'],
  TRANSACTION_HISTORY: ['transactionHistory'],
  
  // Referrals
  REFERRAL_INFO: ['referralInfo'],
  REFERRED_USERS: ['referredUsers'],
  REFERRAL_STATS: ['referralStats'],
  
  // VPN
  VPN_SUBSCRIPTIONS: ['vpnSubscriptions'],
  VPN_PACKAGES: ['vpnPackages'],
  
  // Verse (3D World)
  VERSE_LANDS: ['verseLands'],
  VERSE_CHAT: ['verseChat'],
  
  // Chat
  CHAT_MESSAGES: ['chatMessages'],
  CHAT_HISTORY: ['chatHistory'],
} as const;

// Game Configuration
export const GAME_CONFIG = {
  // Card system
  MAX_CARD_LEVEL: 50,
  CARD_UPGRADE_MULTIPLIER: 1.5,
  PROFIT_COLLECTION_INTERVAL: 3600000, // 1 hour in milliseconds
  
  // Task system
  DAILY_TASK_RESET_HOUR: 0, // UTC hour for daily task reset
  MAX_ACTIVE_TASKS: 10,
  TASK_COMPLETION_TIMEOUT: 300000, // 5 minutes in milliseconds
  
  // Referral system
  REFERRAL_BONUS_PERCENTAGE: 0.1, // 10% of referred user's earnings
  MAX_REFERRAL_LEVELS: 3,
  
  // Chat system
  MAX_MESSAGE_LENGTH: 500,
  CHAT_HISTORY_LIMIT: 100,
  MESSAGE_RATE_LIMIT: 5, // messages per minute
  
  // 3D World
  WORLD_SIZE: 1000,
  LAND_PLOT_SIZE: 10,
  MAX_ZOOM: 100,
  MIN_ZOOM: 10,
} as const;

// UI Configuration
export const UI_CONFIG = {
  // Animation durations (in milliseconds)
  ANIMATION_DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  
  // Breakpoints (in pixels)
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
  },
  
  // Z-index layers
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    TOAST: 1080,
  },
  
  // Toast configuration
  TOAST: {
    DURATION: {
      SHORT: 3000,
      NORMAL: 5000,
      LONG: 8000,
    },
    POSITION: 'top-right',
    MAX_TOASTS: 5,
  },
  
  // Loading states
  LOADING: {
    SKELETON_ANIMATION_DURATION: 1500,
    SPINNER_SIZE: {
      SM: 16,
      MD: 24,
      LG: 32,
    },
  },
} as const;

// Telegram WebApp Configuration
export const TELEGRAM_CONFIG = {
  BOT_USERNAME: 'VIPVerseBot',
  WEBAPP_URL: 'https://t.me/VIPVerseBot/app',
  SHARE_URL_TEMPLATE: 'https://t.me/share/url?url={url}&text={text}',
  
  // Theme colors
  THEME_COLORS: {
    PRIMARY: '#8B5CF6', // purple-500
    SECONDARY: '#3B82F6', // blue-500
    SUCCESS: '#10B981', // emerald-500
    WARNING: '#F59E0B', // amber-500
    ERROR: '#EF4444', // red-500
    BACKGROUND: '#000000',
    SURFACE: '#1F2937', // gray-800
  },
  
  // Haptic feedback patterns
  HAPTIC: {
    LIGHT: 'light',
    MEDIUM: 'medium',
    HEAVY: 'heavy',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  AUTHENTICATION_FAILED: 'Authentication failed. Please try logging in again.',
  INSUFFICIENT_BALANCE: 'Insufficient balance to complete this action.',
  TASK_ALREADY_COMPLETED: 'This task has already been completed.',
  CARD_MAX_LEVEL: 'This card is already at maximum level.',
  INVALID_INPUT: 'Please check your input and try again.',
  SERVER_ERROR: 'Server error occurred. Please try again later.',
  TELEGRAM_NOT_AVAILABLE: 'This app must be opened in Telegram.',
  PERMISSION_DENIED: 'Permission denied. Please check your access rights.',
  RATE_LIMIT_EXCEEDED: 'Too many requests. Please wait before trying again.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  TASK_COMPLETED: 'Task completed successfully!',
  CARD_UPGRADED: 'Card upgraded successfully!',
  CARD_PURCHASED: 'Card purchased successfully!',
  REWARD_CLAIMED: 'Reward claimed successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!',
  REFERRAL_SENT: 'Referral invitation sent!',
  VPN_SUBSCRIBED: 'VPN subscription activated!',
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_3D_WORLD: true,
  ENABLE_CHAT: true,
  ENABLE_VPN: true,
  ENABLE_BIOMETRIC_AUTH: false,
  ENABLE_CLOUD_STORAGE: true,
  ENABLE_HAPTIC_FEEDBACK: true,
  ENABLE_PUSH_NOTIFICATIONS: false,
  ENABLE_ANALYTICS: true,
  ENABLE_DEBUG_MODE: false,
} as const;

// Development Configuration
export const DEV_CONFIG = {
  ENABLE_REACT_QUERY_DEVTOOLS: true,
  ENABLE_CONSOLE_LOGS: true,
  MOCK_TELEGRAM_DATA: {
    initData: 'mock_init_data',
    initDataUnsafe: {
      user: {
        id: 123456789,
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser',
        language_code: 'en',
      },
      auth_date: Date.now(),
      hash: 'mock_hash',
    },
    isReady: true,
  },
} as const;

// Export all constants as a single object for easier importing
export const CONSTANTS = {
  API_CONFIG,
  ROUTES,
  STORAGE_KEYS,
  QUERY_KEYS,
  GAME_CONFIG,
  UI_CONFIG,
  TELEGRAM_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  FEATURE_FLAGS,
  DEV_CONFIG,
} as const;
