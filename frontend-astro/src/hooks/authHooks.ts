import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '../utils/apiClient';
import type { User, ApiResponse } from '../types/api';
import { QUERY_KEYS } from '../utils/constants';

// Local type definitions
export interface VerifySessionData {
  status: string;
  data?: any;
}

export interface LoginParams {
  username: string;
  password: string;
}

export interface TelegramLoginParams {
  init_data: string;
  auth_method: 'telegram';
}

export interface AuthError extends Error {
  response?: {
    data?: {
      message?: string;
      detail?: string;
    };
    status?: number;
  };
}

// Single source of truth for current user query
const CURRENT_USER_QUERY_KEY = QUERY_KEYS.CURRENT_USER;

export function useCurrentUserQuery() {
  return useQuery({
    queryKey: CURRENT_USER_QUERY_KEY,
    queryFn: async (): Promise<VerifySessionData> => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log('[Auth] Request timeout - aborting');
        controller.abort();
      }, 15000);
      
      try {
        console.log('[Auth] Starting session verification...');
        
        const response = await api.post('/auth/verify-session', {}, {
          signal: controller.signal,
          timeout: 12000,
        });
        
        clearTimeout(timeoutId);
        
        if (response.data?.status === 'authenticated') {
          console.log('[Auth] Session verified successfully');
          return response.data;
        }
        
        console.log('[Auth] Session not authenticated');
        return { status: 'unauthenticated', data: null };
        
      } catch (error: any) {
        clearTimeout(timeoutId);
        
        if (error.name === 'AbortError') {
          console.log('[Auth] Request was aborted');
          throw new Error('Request timeout');
        }
        
        if (error.response?.status === 401) {
          console.log('[Auth] Session expired or invalid');
          return { status: 'unauthenticated', data: null };
        }
        
        console.error('[Auth] Session verification failed:', error);
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false,
    retryOnMount: false,
    networkMode: 'online',
    structuralSharing: false,
    select: (verifySessionData: VerifySessionData): User | null => {
      if (verifySessionData?.status === 'authenticated' && verifySessionData.data) {
        return verifySessionData.data as User;
      }
      return null;
    },
  });
}

export function useTelegramLoginMutation() {
  const queryClient = useQueryClient();
  
  type TelegramLoginResult = {
    isNewUser: boolean;
    user?: User;
    telegram_data?: any;
  };
  
  return useMutation<TelegramLoginResult, AuthError, TelegramLoginParams>({
    mutationFn: async (variables: TelegramLoginParams): Promise<TelegramLoginResult> => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000);
      
      try {
        console.log('[TelegramLogin] Starting login process');
        
        const response = await api.post('/auth/telegram-login', {
          init_data: variables.init_data,
          auth_method: variables.auth_method
        }, {
          signal: controller.signal,
          timeout: 7000,
        });
        
        clearTimeout(timeoutId);
        
        // If we get a successful response, it means existing user
        if (response?.data?.data) {
          console.log('[TelegramLogin] Existing user login successful');
          return {
            isNewUser: false,
            user: response.data.data
          };
        }
        
        throw new Error('Invalid response from server');
      } catch (error: any) {
        clearTimeout(timeoutId);
        console.error('[TelegramLogin] Error:', error);
        
        // Handle timeout
        if (error.name === 'AbortError' || error.message?.includes('timeout')) {
          throw new Error('Login request timed out. Please try again.');
        }
        
        // Handle 404 - new user
        if (error.response?.status === 404) {
          console.log('[TelegramLogin] New user detected');
          return {
            isNewUser: true,
            telegram_data: error.response?.data?.telegram_data || null
          };
        }
        
        // Handle other errors
        const errorMessage = error.response?.data?.message || 
                           error.response?.data?.detail || 
                           error.message || 
                           'Login failed';
        
        throw new Error(errorMessage);
      }
    },
    onSuccess: (data: TelegramLoginResult) => {
      if (data?.user) {
        // Existing user - update cache immediately
        const verifyData: VerifySessionData = {
          status: 'authenticated',
          data: data.user,
        };
        queryClient.setQueryData(CURRENT_USER_QUERY_KEY, verifyData);
        console.log('[TelegramLogin] Updated auth cache for existing user');
        
        // Clear any stale setup data
        sessionStorage.removeItem('telegram_setup_data');
      } else if (data?.isNewUser && data.telegram_data) {
        // New user - store setup data
        sessionStorage.setItem(
          'telegram_setup_data',
          JSON.stringify({ 
            telegramData: data.telegram_data, 
            timestamp: Date.now() 
          })
        );
        console.log('[TelegramLogin] Stored setup data for new user');
      }
    },
    onError: (error) => {
      console.error('[TelegramLogin] Authentication failed:', error);
      // Ensure we don't leave stale auth data
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
    },
  });
}

export function useLogoutMutation() {
  const queryClient = useQueryClient();
  return useMutation<unknown, AuthError, void>({
    mutationFn: async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      try {
        const result = await api.post('/auth/logout', {}, {
          signal: controller.signal,
          timeout: 4000,
        });
        clearTimeout(timeoutId);
        return result;
      } catch (error: any) {
        clearTimeout(timeoutId);
        // Even if logout fails on server, we still want to clear local state
        console.warn('[Logout] Server logout failed, but clearing local state:', error);
        return null;
      }
    },
    onSuccess: () => {
      // Clear all auth-related data
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
      queryClient.removeQueries({ queryKey: CURRENT_USER_QUERY_KEY });
      sessionStorage.removeItem('telegram_setup_data');
      localStorage.removeItem('isFreshLogin');
      console.log('[Logout] Cleared auth state');
    },
    onError: () => {
      // Even on error, clear local auth state
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
      queryClient.removeQueries({ queryKey: CURRENT_USER_QUERY_KEY });
      sessionStorage.removeItem('telegram_setup_data');
      localStorage.removeItem('isFreshLogin');
      console.log('[Logout] Cleared auth state after error');
    },
  });
}
