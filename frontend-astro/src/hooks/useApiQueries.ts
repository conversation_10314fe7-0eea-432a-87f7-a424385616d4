/**
 * @file useApiQueries.ts
 * @description React Query hooks for VIPVerse Telegram Mini App
 * Provides type-safe data fetching with caching, background refetching, and error handling
 */

import { useQuery, useMutation } from '@tanstack/react-query';
import { api, handleAPIError, type User, type UserStats, type Card, type Task, type ReferralInfo, type ReferredUser } from '../services/api';
import { QUERY_KEYS } from '../utils/constants';
import { telegramWebApp } from '../utils/telegramWebApp';
import { queryClient } from '../lib/queryClient';

// Query options for consistent configuration
const DEFAULT_QUERY_OPTIONS = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  refetchOnReconnect: true,
  retry: (failureCount: number, error: any) => {
    // Don't retry on 4xx errors except 408, 429
    if (error?.status >= 400 && error?.status < 500) {
      if (error?.status === 408 || error?.status === 429) {
        return failureCount < 2;
      }
      return false;
    }
    // Retry on network errors and 5xx errors
    return failureCount < 3;
  },
  retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
};

// User Queries
export function useCurrentUserQuery() {
  return useQuery({
    queryKey: QUERY_KEYS.CURRENT_USER,
    queryFn: async (): Promise<User> => {
      try {
        return await api.getCurrentUser();
      } catch (error) {
        console.error('Failed to fetch current user:', error);
        throw error;
      }
    },
    ...DEFAULT_QUERY_OPTIONS,
  }, queryClient);
}

export function useUserStatsQuery() {
  return useQuery({
    queryKey: QUERY_KEYS.DASHBOARD_STATS,
    queryFn: async (): Promise<UserStats> => {
      try {
        return await api.getUserStats();
      } catch (error) {
        console.error('Failed to fetch user stats:', error);
        throw error;
      }
    },
    ...DEFAULT_QUERY_OPTIONS,
  }, queryClient);
}

// Cards Queries
export function useCardsQuery() {
  return useQuery({
    queryKey: QUERY_KEYS.USER_CARDS,
    queryFn: async (): Promise<Card[]> => {
      try {
        return await api.getAllCards();
      } catch (error) {
        console.error('Failed to fetch cards:', error);
        throw error;
      }
    },
    ...DEFAULT_QUERY_OPTIONS,
  }, queryClient);
}

// Tasks Queries
export function useTasksQuery() {
  return useQuery({
    queryKey: QUERY_KEYS.TASKS,
    queryFn: async (): Promise<Task[]> => {
      try {
        return await api.getTasks();
      } catch (error) {
        console.error('Failed to fetch tasks:', error);
        throw error;
      }
    },
    ...DEFAULT_QUERY_OPTIONS,
  }, queryClient);
}

// Referrals Queries
export function useReferralInfoQuery() {
  return useQuery({
    queryKey: QUERY_KEYS.REFERRAL_INFO,
    queryFn: async (): Promise<ReferralInfo> => {
      try {
        return await api.getReferralInfo();
      } catch (error) {
        console.error('Failed to fetch referral info:', error);
        throw error;
      }
    },
    ...DEFAULT_QUERY_OPTIONS,
  }, queryClient);
}

export function useReferredUsersQuery() {
  return useQuery({
    queryKey: QUERY_KEYS.REFERRED_USERS,
    queryFn: async (): Promise<ReferredUser[]> => {
      try {
        return await api.getReferredUsers();
      } catch (error) {
        console.error('Failed to fetch referred users:', error);
        throw error;
      }
    },
    ...DEFAULT_QUERY_OPTIONS,
  }, queryClient);
}

// Mutations
export function useClaimProfitsMutation() {

  return useMutation({
    mutationFn: async (): Promise<{ success: boolean; amount: number; message: string }> => {
      try {
        // Add haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('medium');
        }

        const result = await api.claimAllProfits();

        // Success haptic feedback
        if (telegramWebApp.isAvailable() && result.success) {
          telegramWebApp.hapticFeedback('heavy');
        }

        return result;
      } catch (error) {
        // Error haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('rigid');
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        // Invalidate and refetch user stats to reflect new balance
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_STATS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CURRENT_USER });
      }
    },
    onError: (error) => {
      console.error('Failed to claim profits:', error);
    },
  });
}

export function useUpgradeCardMutation() {

  return useMutation({
    mutationFn: async (cardId: number): Promise<{ success: boolean; message: string }> => {
      try {
        // Add haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('medium');
        }

        const result = await api.upgradeCard(cardId);

        // Success haptic feedback
        if (telegramWebApp.isAvailable() && result.success) {
          telegramWebApp.hapticFeedback('heavy');
        }

        return result;
      } catch (error) {
        // Error haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('rigid');
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        // Invalidate and refetch cards and user stats
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USER_CARDS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_STATS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CURRENT_USER });
      }
    },
    onError: (error) => {
      console.error('Failed to upgrade card:', error);
    },
  });
}

export function useBuyCardMutation() {

  return useMutation({
    mutationFn: async (cardId: number): Promise<{ success: boolean; message: string }> => {
      try {
        // Add haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('medium');
        }

        const result = await api.buyCard(cardId);

        // Success haptic feedback
        if (telegramWebApp.isAvailable() && result.success) {
          telegramWebApp.hapticFeedback('heavy');
        }

        return result;
      } catch (error) {
        // Error haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('rigid');
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        // Invalidate and refetch cards and user stats
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.USER_CARDS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_STATS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CURRENT_USER });
      }
    },
    onError: (error) => {
      console.error('Failed to buy card:', error);
    },
  });
}

export function useStartTaskMutation() {

  return useMutation({
    mutationFn: async (taskId: number): Promise<{ success: boolean; message: string }> => {
      try {
        // Add haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('light');
        }

        const result = await api.startTask(taskId);

        return result;
      } catch (error) {
        // Error haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('rigid');
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        // Invalidate and refetch tasks
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TASKS });
      }
    },
    onError: (error) => {
      console.error('Failed to start task:', error);
    },
  });
}

export function useCompleteTaskMutation() {

  return useMutation({
    mutationFn: async (taskId: number): Promise<{ success: boolean; reward: number; message: string }> => {
      try {
        // Add haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('medium');
        }

        const result = await api.claimTask(taskId);

        // Success haptic feedback
        if (telegramWebApp.isAvailable() && result.success) {
          telegramWebApp.hapticFeedback('heavy');
        }

        return result;
      } catch (error) {
        // Error haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('rigid');
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        // Invalidate and refetch tasks and user stats
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TASKS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.DASHBOARD_STATS });
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.CURRENT_USER });
      }
    },
    onError: (error) => {
      console.error('Failed to complete task:', error);
    },
  });
}

export function useVerifyTaskMutation() {

  return useMutation({
    mutationFn: async ({ taskId, data }: { taskId: number; data?: any }): Promise<{ success: boolean; message: string }> => {
      try {
        // Add haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('light');
        }

        const result = await api.verifyTask(taskId, data);

        return result;
      } catch (error) {
        // Error haptic feedback
        if (telegramWebApp.isAvailable()) {
          telegramWebApp.hapticFeedback('rigid');
        }
        throw error;
      }
    },
    onSuccess: (data) => {
      if (data.success) {
        // Invalidate and refetch tasks
        queryClient.invalidateQueries({ queryKey: QUERY_KEYS.TASKS });
      }
    },
    onError: (error) => {
      console.error('Failed to verify task:', error);
    },
  });
}

// Utility hook for error handling
export function useApiErrorHandler() {
  return (error: unknown): string => {
    return handleAPIError(error);
  };
}
