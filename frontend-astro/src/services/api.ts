/**
 * @file api.ts
 * @description API service for VIPVerse Telegram Mini App
 * Fetch-based implementation with automatic Telegram WebApp authentication
 */

import { telegramWebApp } from '../utils/telegramWebApp';

// Types
export interface User {
  id: number;
  username: string;
  email?: string;
  telegram_id?: number;
  telegram_photo_url?: string;
  wallet_balance: number;
  total_passive_hourly_income: number;
  last_passive_claim_at?: string;
  created_at: string;
  updated_at: string;
}

export interface UserStats {
  wallet_balance: number;
  total_accumulated_card_profit: number;
  total_passive_hourly_income: number;
  active_subscriptions: number;
  total_earned: number;
  pending_rewards: number;
  total_data_used?: number;
  total_data_limit?: number;
  last_passive_claim_at?: string;
}

export interface Card {
  id: number;
  name: string;
  level: number;
  profit_per_hour: number;
  upgrade_cost: number;
  image?: string;
  category: string;
  is_owned: boolean;
  description?: string;
  max_level?: number;
}

export interface Task {
  id: number;
  title: string;
  description: string;
  reward: number;
  status: 'available' | 'in_progress' | 'completed';
  type: 'daily' | 'social' | 'achievement';
  requirements?: any;
  url?: string;
  verification_type?: string;
}

export interface ReferralInfo {
  total_referred: number;
  total_earned: number;
  pending_rewards: number;
  referral_code: string;
  referral_link: string;
}

export interface ReferredUser {
  id: number;
  username: string;
  joined_at: string;
  earned_from: number;
  status: 'active' | 'pending';
}

export interface ApiResponse<T> {
  data: T;
  status: string;
  message?: string;
}

export interface HealthCheck {
  status: string;
  timestamp: string;
  version: string;
}

// API Error handling
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// API Configuration
const API_BASE_URL = import.meta.env.PUBLIC_API_URL || 'https://dev.atlasvip.cloud/api';

const getBaseURL = (): string => {
  if (typeof window !== 'undefined') {
    // Client-side: check if we're in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      // Development: use local backend
      return 'http://localhost:8000/api';
    }
    // Production: use the current domain
    return `${window.location.protocol}//${window.location.host}/api`;
  }
  // Server-side: use configured URL
  return API_BASE_URL;
};

// API Client class with automatic Telegram authentication
class APIClient {
  private baseURL: string;
  private token: string | null = null;
  private isAuthenticating = false;
  private authPromise: Promise<void> | null = null;

  constructor() {
    this.baseURL = getBaseURL();
    this.initializeAuth();
  }

  private async initializeAuth(): Promise<void> {
    // Try to get token from localStorage first
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }

    // If no token and Telegram WebApp is available, authenticate with Telegram
    if (!this.token && telegramWebApp.isAvailable()) {
      try {
        await this.authenticateWithTelegram();
      } catch (error) {
        console.warn('Failed to authenticate with Telegram:', error);
      }
    }
  }

  private async authenticateWithTelegram(): Promise<void> {
    if (this.isAuthenticating) {
      return this.authPromise || Promise.resolve();
    }

    this.isAuthenticating = true;
    this.authPromise = this._performTelegramAuth();

    try {
      await this.authPromise;
    } finally {
      this.isAuthenticating = false;
      this.authPromise = null;
    }
  }

  private async _performTelegramAuth(): Promise<void> {
    const webApp = telegramWebApp.getWebApp();
    if (!webApp?.initData) {
      throw new Error('No Telegram init data available');
    }

    const response = await fetch(`${this.baseURL}/auth/telegram/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        init_data: webApp.initData,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new APIError(
        errorData.detail || 'Authentication failed',
        response.status,
        errorData.code
      );
    }

    const data = await response.json();
    this.token = data.access_token;

    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', this.token!);
    }
  }

  private async ensureAuthenticated(): Promise<void> {
    if (this.token) return;

    if (telegramWebApp.isAvailable()) {
      await this.authenticateWithTelegram();
    } else {
      throw new APIError('No authentication method available', 401);
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    await this.ensureAuthenticated();

    const url = `${this.baseURL}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Token expired, clear it and try to re-authenticate
        this.token = null;
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token');
        }

        // Try to re-authenticate with Telegram
        if (telegramWebApp.isAvailable()) {
          await this.authenticateWithTelegram();
          // Retry the original request
          return this.request<T>(endpoint, options);
        }
      }

      const errorData = await response.json().catch(() => ({}));
      throw new APIError(
        errorData.detail || errorData.message || `HTTP ${response.status}`,
        response.status,
        errorData.code
      );
    }

    return response.json();
  }

  // User endpoints
  async getCurrentUser(): Promise<User> {
    return this.request<User>('/user/me');
  }

  async getUserStats(): Promise<UserStats> {
    return this.request<UserStats>('/user/dashboard/stats');
  }

  async updateUserProfile(data: Partial<User>): Promise<User> {
    return this.request<User>('/user/profile/update', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Cards endpoints
  async getAllCards(): Promise<Card[]> {
    return this.request<Card[]>('/cards/all');
  }

  async buyCard(cardCatalogId: number): Promise<{ success: boolean; message: string }> {
    return this.request(`/cards/buy/${cardCatalogId}`, {
      method: 'POST',
    });
  }

  async upgradeCard(userCardId: number): Promise<{ success: boolean; message: string }> {
    return this.request(`/cards/${userCardId}/upgrade`, {
      method: 'POST',
    });
  }

  async claimAllProfits(): Promise<{ success: boolean; amount: number; message: string }> {
    return this.request('/cards/claim-all', {
      method: 'POST',
    });
  }

  // Tasks endpoints
  async getAvailableTasks(): Promise<Task[]> {
    return this.request<Task[]>('/tasks/available');
  }

  async startTask(taskId: number): Promise<{ success: boolean; message: string }> {
    return this.request(`/tasks/${taskId}/start`, {
      method: 'POST',
    });
  }

  async claimTaskReward(taskId: number): Promise<{ success: boolean; reward: number; message: string }> {
    return this.request(`/tasks/${taskId}/claim`, {
      method: 'POST',
    });
  }

  async verifyTask(taskId: number, data?: any): Promise<{ success: boolean; message: string }> {
    return this.request(`/tasks/${taskId}/verify`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    });
  }

  // Referrals endpoints
  async getReferralInfo(): Promise<ReferralInfo> {
    return this.request<ReferralInfo>('/referrals/info');
  }

  async getReferredUsers(): Promise<ReferredUser[]> {
    return this.request<ReferredUser[]>('/referrals/users');
  }

  async checkReferralCode(code: string): Promise<{ valid: boolean; user?: any }> {
    return this.request<{ valid: boolean; user?: any }>(`/referrals/check/${code}`);
  }

  // VPN endpoints
  async getAvailablePackages(): Promise<any[]> {
    return this.request<any[]>('/user/available-packages');
  }

  async getUserSubscriptions(): Promise<any[]> {
    return this.request<any[]>('/user/subscriptions');
  }

  async purchasePackage(packageId: number): Promise<{ success: boolean; message: string }> {
    return this.request(`/user/purchase/${packageId}`, {
      method: 'POST',
    });
  }

  // Transactions endpoints
  async getUserTransactions(): Promise<any[]> {
    return this.request<any[]>('/user/transactions');
  }

  // Health check
  async healthCheck(): Promise<{ status: string }> {
    return this.request<{ status: string }>('/health');
  }
}

// Export singleton instance
export const apiClient = new APIClient();

// Convenience functions for common operations
export const api = {
  // User operations
  getCurrentUser: () => apiClient.getCurrentUser(),
  getUserStats: () => apiClient.getUserStats(),
  updateProfile: (data: Partial<User>) => apiClient.updateUserProfile(data),

  // Cards operations
  getAllCards: () => apiClient.getAllCards(),
  buyCard: (cardId: number) => apiClient.buyCard(cardId),
  upgradeCard: (cardId: number) => apiClient.upgradeCard(cardId),
  claimAllProfits: () => apiClient.claimAllProfits(),

  // Tasks operations
  getTasks: () => apiClient.getAvailableTasks(),
  startTask: (taskId: number) => apiClient.startTask(taskId),
  claimTask: (taskId: number) => apiClient.claimTaskReward(taskId),
  verifyTask: (taskId: number, data?: any) => apiClient.verifyTask(taskId, data),

  // Referrals operations
  getReferralInfo: () => apiClient.getReferralInfo(),
  getReferredUsers: () => apiClient.getReferredUsers(),
  checkReferralCode: (code: string) => apiClient.checkReferralCode(code),

  // VPN operations
  getPackages: () => apiClient.getAvailablePackages(),
  getSubscriptions: () => apiClient.getUserSubscriptions(),
  purchasePackage: (packageId: number) => apiClient.purchasePackage(packageId),

  // Transactions
  getTransactions: () => apiClient.getUserTransactions(),

  // Health
  healthCheck: () => apiClient.healthCheck(),
};

// Error handling utilities
export const handleAPIError = (error: unknown): string => {
  if (error instanceof APIError) {
    switch (error.status) {
      case 401:
        return 'Authentication required. Please log in again.';
      case 403:
        return 'Access denied. You do not have permission for this action.';
      case 404:
        return 'Resource not found.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unexpected error occurred.';
};

// Development mode helpers
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV || import.meta.env.VITE_ENVIRONMENT === 'development';
};

// Export the configured API client
export default apiClient;
