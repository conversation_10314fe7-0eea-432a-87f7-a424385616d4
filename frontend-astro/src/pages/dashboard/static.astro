---
/**
 * @file dashboard/static.astro
 * @description Static dashboard page without React components
 */

// Page metadata
const title = "VIPVerse Dashboard - Static";
const description = "Static dashboard for testing";
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>{title}</title>
  <meta name="description" content={description} />
  <style>
    body {
      background: #000;
      color: #fff;
      font-family: system-ui, sans-serif;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
    }
    .card {
      background: rgba(168, 85, 247, 0.1);
      border: 1px solid rgba(168, 85, 247, 0.3);
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1rem;
    }
    .grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
    }
    .btn {
      background: #8b5cf6;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: bold;
      cursor: pointer;
      transition: background 0.2s;
    }
    .btn:hover {
      background: #7c3aed;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 style="font-size: 2.5rem; margin-bottom: 1rem;">
      WELCOME TO <span style="color: #a855f7;">VIPVERSE</span>
    </h1>
    <p style="color: #9ca3af; margin-bottom: 2rem;">Your cyberpunk gaming dashboard</p>
    
    <div class="grid">
      <div class="card">
        <h2 style="color: #fbbf24; margin-bottom: 1rem;">💰 PROFIT CORE</h2>
        <div style="font-size: 2rem; font-weight: bold; color: #fbbf24;">$89.50</div>
        <p style="color: #9ca3af; font-size: 0.875rem;">Available to claim</p>
        <button class="btn" style="background: #f59e0b; color: #000; margin-top: 1rem;">
          CLAIM ALL PROFITS
        </button>
      </div>
      
      <div class="card">
        <h2 style="color: #06b6d4; margin-bottom: 1rem;">📊 ANALYTICS HUB</h2>
        <div style="font-size: 2rem; font-weight: bold; color: #06b6d4;">3 Active</div>
        <p style="color: #9ca3af; font-size: 0.875rem;">VPN Subscriptions</p>
        <div style="margin-top: 1rem; display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
          <div style="background: rgba(0,0,0,0.3); padding: 0.75rem; border-radius: 6px; text-align: center;">
            <div style="font-size: 0.75rem; color: #9ca3af;">Daily</div>
            <div style="font-weight: bold;">$78.00</div>
          </div>
          <div style="background: rgba(0,0,0,0.3); padding: 0.75rem; border-radius: 6px; text-align: center;">
            <div style="font-size: 0.75rem; color: #9ca3af;">Weekly</div>
            <div style="font-weight: bold;">$546.00</div>
          </div>
        </div>
      </div>
    </div>
    
    <div style="margin-top: 2rem;">
      <h2 style="text-align: center; margin-bottom: 1.5rem;">🎮 COMMAND CENTER</h2>
      <div class="grid">
        <button class="btn" onclick="window.location.href='/dashboard/earn'">
          🎯 CARD NEXUS
        </button>
        <button class="btn" onclick="window.location.href='/dashboard/premium'">
          🛡️ VPN CORE
        </button>
        <button class="btn" onclick="window.location.href='/dashboard/friends'">
          👥 NETWORK
        </button>
        <button class="btn" onclick="window.location.href='/dashboard/wallet'">
          💎 VAULT
        </button>
      </div>
    </div>
  </div>
</body>
</html>
