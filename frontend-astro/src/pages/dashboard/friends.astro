---
/**
 * @file dashboard/friends.astro
 * @description Dashboard friends page - referral system
 */

import DashboardLayout from '../../layouts/DashboardLayout.astro';
import QueryClientWrapper from '../../components/react/QueryClientWrapper';
import FriendsTabContent from '../../components/react/FriendsTabContent';

// Page metadata
const title = "VIPVerse Dashboard - Friends";
const description = "Invite friends and manage your referral network";
---

<DashboardLayout title={title} description={description} activeTab="friends">
  <!-- Friends Tab Content (React Island with Query Client) -->
  <QueryClientWrapper client:load>
    <FriendsTabContent />
  </QueryClientWrapper>
</DashboardLayout>
