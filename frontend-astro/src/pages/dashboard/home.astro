---
/**
 * @file dashboard/home.astro
 * @description Dashboard home page - main dashboard view
 */

import DashboardLayout from '../../layouts/DashboardLayout.astro';
import QueryClientWrapper from '../../components/react/QueryClientWrapper';
import HomeTabContent from '../../components/react/HomeTabContent';

// Page metadata
const title = "VIPVerse Dashboard - Home";
const description = "Your VIPVerse dashboard home with stats, cards, and activities";
---

<DashboardLayout title={title} description={description} activeTab="home">
  <!-- Home Tab Content (React Island with Query Client) -->
  <QueryClientWrapper client:load>
    <HomeTabContent />
  </QueryClientWrapper>
</DashboardLayout>
