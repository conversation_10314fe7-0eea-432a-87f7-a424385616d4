---
import BaseLayout from '../layouts/BaseLayout.astro';
import NewUserSetupWrapper from '../components/react/NewUserSetupWrapper';
---

<BaseLayout title="VIPVerse - Account Setup" description="Complete your VIPVerse account setup">
  <div class="min-h-screen bg-[#030303] flex items-center justify-center p-4">
    <NewUserSetupWrapper
      client:only="react"
      onNavigate={(path) => {
        window.location.href = path;
      }}
    />
  </div>

  <!-- Custom animations for setup page -->
  <style>
    @keyframes typewriter {
      from { width: 0; }
      to { width: 100%; }
    }
    
    @keyframes blink {
      0%, 50% { border-color: transparent; }
      51%, 100% { border-color: #8B5CF6; }
    }
    
    @keyframes glitch {
      0% { transform: translate(0); }
      20% { transform: translate(-2px, 2px); }
      40% { transform: translate(-2px, -2px); }
      60% { transform: translate(2px, 2px); }
      80% { transform: translate(2px, -2px); }
      100% { transform: translate(0); }
    }
    
    .typewriter {
      overflow: hidden;
      border-right: 2px solid #8B5CF6;
      white-space: nowrap;
      animation: typewriter 1s steps(40, end), blink 1s infinite;
    }
    
    .glitch-effect {
      animation: glitch 0.3s ease-in-out;
    }
    
    .terminal-cursor::after {
      content: '█';
      animation: blink 1s infinite;
    }
  </style>
</BaseLayout>
