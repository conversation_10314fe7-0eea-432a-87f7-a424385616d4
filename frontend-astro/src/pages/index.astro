---
import BaseLayout from '../layouts/BaseLayout.astro';
import LandingPageWrapper from '../components/react/LandingPageWrapper';
---

<BaseLayout title="VIPVerse - Welcome" description="Enter the exclusive world of premium VPN services">
  <div class="min-h-screen bg-[#030303] flex items-center justify-center p-4">
    <LandingPageWrapper
      client:only="react"
      onNavigate={(path) => {
        window.location.href = path;
      }}
    />
  </div>

  <!-- Custom animations for landing page -->
  <style>
    @keyframes floatIcon {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
    }

    @keyframes pulseText {
      0% { opacity: 0.6; }
      100% { opacity: 1; }
    }

    .animate-floatIcon {
      animation: floatIcon 3s ease-in-out infinite;
    }

    .animate-pulseText {
      animation: pulseText 1.8s infinite alternate;
    }
  </style>
</BaseLayout>
