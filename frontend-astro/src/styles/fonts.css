/* Gaming & Cyberpunk Fonts */

/* Orbitron - Futuristic/Sci-fi font for headings */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap');

/* Rajdhani - Modern tech font for UI elements */
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* JetBrains Mono - Monospace for code/data displays */
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');

/* Exo 2 - Gaming/tech font for body text */
@import url('https://fonts.googleapis.com/css2?family=Exo+2:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Font Variables */
:root {
  /* Primary Gaming Fonts */
  --font-display: 'Orbitron', 'Arial Black', sans-serif;
  --font-heading: 'Rajdhani', 'Arial', sans-serif;
  --font-body: 'Exo 2', 'Helvetica Neue', sans-serif;
  --font-mono: 'JetBrains Mono', 'Courier New', monospace;
  
  /* Font Weights */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
  
  /* Letter Spacing for Gaming Feel */
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;
}

/* Font Classes */
.font-display {
  font-family: var(--font-display);
  font-weight: var(--font-weight-bold);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
}

.font-heading {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--letter-spacing-normal);
}

.font-body {
  font-family: var(--font-body);
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-normal);
}

.font-mono {
  font-family: var(--font-mono);
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-normal);
}

/* Gaming Text Effects */
.text-glow {
  text-shadow: 
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor;
}

.text-glow-neon {
  text-shadow: 
    0 0 5px #00ffff,
    0 0 10px #00ffff,
    0 0 15px #00ffff,
    0 0 20px #00ffff;
}

.text-glow-purple {
  text-shadow: 
    0 0 5px #a855f7,
    0 0 10px #a855f7,
    0 0 15px #a855f7,
    0 0 20px #a855f7;
}

.text-glow-gold {
  text-shadow: 
    0 0 5px #fbbf24,
    0 0 10px #fbbf24,
    0 0 15px #fbbf24,
    0 0 20px #fbbf24;
}

/* Cyberpunk Text Styles */
.text-cyberpunk {
  font-family: var(--font-display);
  font-weight: var(--font-weight-bold);
  letter-spacing: var(--letter-spacing-wider);
  text-transform: uppercase;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: cyberpunk-gradient 3s ease infinite;
}

@keyframes cyberpunk-gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Gaming UI Text */
.text-gaming-title {
  font-family: var(--font-display);
  font-weight: var(--font-weight-extrabold);
  letter-spacing: var(--letter-spacing-wider);
  text-transform: uppercase;
  background: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gaming-subtitle {
  font-family: var(--font-heading);
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-wide);
  color: #94a3b8;
}

.text-gaming-body {
  font-family: var(--font-body);
  font-weight: var(--font-weight-normal);
  letter-spacing: var(--letter-spacing-normal);
  line-height: 1.6;
}

.text-gaming-mono {
  font-family: var(--font-mono);
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--letter-spacing-normal);
  color: #00ff88;
}

/* Responsive Font Sizes */
@media (max-width: 640px) {
  .font-display {
    letter-spacing: var(--letter-spacing-normal);
  }
  
  .text-cyberpunk {
    letter-spacing: var(--letter-spacing-wide);
  }
  
  .text-gaming-title {
    letter-spacing: var(--letter-spacing-wide);
  }
}
